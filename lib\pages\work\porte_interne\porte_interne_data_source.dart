import 'dart:developer';
import 'package:data_table_2/data_table_2.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/NAMaterial.dart';
import 'package:newarc_platform/pages/work/rivestimenti/rivestimenti_controller.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:intl/intl.dart';
import '../../../widget/UI/image_dropdown.dart';
import '../../../widget/UI/tab/status_widget.dart';
import 'package:path/path.dart' as path;

class RivestimentiDataSource extends DataTableSource {
  final List<NAMaterial> naMaterial;
  final BuildContext context;
  final Function() initialFetchNAMaterial;
  final Function(String type,NAMaterial naMaterial)onOpenAzioniDialog;
  final controller = Get.put<RivestimentiController>(RivestimentiController());

  NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);

  RivestimentiDataSource({
    required this.context,
    required this.naMaterial,
    required this.onOpenAzioniDialog,
    required this.initialFetchNAMaterial,
  });

  @override
  DataRow? getRow(int index) {
    if (index < naMaterial.length) {
      return DataRow2(
        specificRowHeight: 50,
        cells: [
          //?---- Codice
          DataCell(StatusWidget(
            statusColor: (naMaterial[index].isAnySupplierPriceAvailable ?? false) ? Color(0xff39C14F) : Color(0xffDD0000),
          )),
          DataCell(FutureBuilder<ImageProvider>(
            future: getImage(naMaterial[index].coverImagePath?["location"] ?? "",naMaterial[index].coverImagePath?["filename"] ?? ""),
            builder: (context, snapshot) {
              if(snapshot.connectionState == ConnectionState.waiting){
                return SizedBox(
                  width: 30,
                  height: 30,
                  child: CircularProgressIndicator(),
                );
              } else if (snapshot.hasData) {
                return ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image(
                    image: snapshot.data!,
                    width: 40,
                    height: 40,
                    fit: BoxFit.cover,
                  ),
                );
              } else if (snapshot.hasError) {
                return SizedBox();
              }
              return SizedBox();
            },
          )),
          DataCell(
            NarFormLabelWidget(
              label: naMaterial[index].code ?? "",
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            ),
          ),
          //?---- Nome
          DataCell(
            NarFormLabelWidget(
              label: naMaterial[index].name ?? "",
              fontSize: 12,
              fontWeight: '600',
              overflow: TextOverflow.visible,
              textAlign: TextAlign.start,
              textColor: Colors.black,
            ),
          ),
          //? ---- Collezione
          DataCell(
            NarFormLabelWidget(
              label: naMaterial[index].collectionName ?? "",
              fontSize: 12,
              fontWeight: '600',
              overflow: TextOverflow.visible,
              textAlign: TextAlign.start,
              textColor: Colors.black,
            ),
          ),
          //? ---- Produttore
          DataCell(NarFormLabelWidget(
            label: naMaterial[index].manufacturerName ?? "",
            fontSize: 12,
            fontWeight: '600',
            textAlign: TextAlign.start,
            textColor: Colors.black,
          )),
          //---- Action
          DataCell(_buildActionsDropdown(naMaterial: naMaterial[index])),
        ],
      );
    }

    return null;
  }

  Widget _buildActionsDropdown({required NAMaterial naMaterial}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 5.0),
      child: NarImageDropdown(
        controller: TextEditingController(),
        customButton: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NarFormLabelWidget(
                label: "Azioni",
                fontSize: 11,
                fontWeight: '600',
                textColor: AppColor.greyColor,
              ),
              Icon(
                Icons.keyboard_arrow_down,
                color: AppColor.iconGreyColor,
                size: 15,
              )
            ],
          ),
        ),
        options: [
          {
            'value': 'edit',
            'label': 'Modifica',
            'image': 'assets/icons/edit.svg',
            'iconColor': AppColor.greyColor
          },
          {
            'value': 'archivia',
            'label': 'Archivia',
            'image': 'assets/icons/archive.png',
            'iconColor': AppColor.redColor,
            'labelColor': AppColor.redColor
          },
        ],
        hintText: "Azioni",
        onChanged: (value) {
          if (value == null || value['value'] == null) return;
          onOpenAzioniDialog(value['value'].toString().trim(),naMaterial);
        },
      ),
    );
  }

  Future<ImageProvider> getImage(String imagePath,String imageName) async {
    if(imagePath.isNotEmpty && imageName.isNotEmpty){
      final ext = path.extension(imageName);
      final base = path.basenameWithoutExtension(imageName);
      final newFileName =  '${base}_thumbnail$ext';
      final ref = FirebaseStorage.instance.ref().child("$imagePath/$newFileName");
      try {
        final url = await ref.getDownloadURL();
        return NetworkImage(url);
      } catch (error) {
        log("Error fetching image for ${"$imagePath/$newFileName"}: $error");
        return const AssetImage('assets/empty_cover_img.png');
      }
    }else{
      return const AssetImage('assets/empty_cover_img.png');
    }
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => naMaterial.length;

  @override
  int get selectedRowCount => 0;
}
