import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/supplier.dart';

class ContractorsController extends GetxController {
  bool loading = false;
  // suggested contractors to be shown in table
  List<Supplier> contractors = [];
  List<String> categories = [];
  TextEditingController activitiesFilterController = new TextEditingController();
  String activitiesSelectedFilter = "";

  TextEditingController contractorName = new TextEditingController();
  TextEditingController formationType = new TextEditingController();
  List selectedActivities = [];
  TextEditingController contactName = new TextEditingController();
  TextEditingController contactSurname = new TextEditingController();
  TextEditingController contactPhone = new TextEditingController();
  TextEditingController contactEmail = new TextEditingController();

  TextEditingController contractorLegalEntity = new TextEditingController();
  TextEditingController contractorIban = new TextEditingController();
  TextEditingController contractorBillingCode = new TextEditingController();
  TextEditingController contractorVat = new TextEditingController();
  TextEditingController contractorFiscalCode = new TextEditingController();
  BaseAddressInfo contractorOperativeAddress = BaseAddressInfo.empty();
  BaseAddressInfo contractorLegalAddress = BaseAddressInfo.empty();
  TextEditingController contractorPhone = new TextEditingController();
  TextEditingController contractorEmail = new TextEditingController();
  TextEditingController contractorPEC = new TextEditingController();
  TextEditingController contractorNationality = new TextEditingController();

  TextEditingController legalRepresentativeName = new TextEditingController();
  TextEditingController legalRepresentativeSurname = new TextEditingController();
  TextEditingController legalRepresentativePhone = new TextEditingController();
  TextEditingController legalRepresentativeEmail = new TextEditingController();
  TextEditingController legalRepresentativePEC = new TextEditingController();
  TextEditingController legalRepresentativeFiscalCode = new TextEditingController();
  BaseAddressInfo legalRepresentativeAddress = BaseAddressInfo.empty();
  TextEditingController legalRepresentativeBirthCountry = new TextEditingController();
  TextEditingController legalRepresentativeBirthProvince = new TextEditingController();
  TextEditingController legalRepresentativeBirthCity = new TextEditingController();
  TextEditingController legalRepresentativeSex = new TextEditingController();
  TextEditingController legalRepresentativeBirthDate = new TextEditingController();
  int? selectedLegalRepresentativeBirthDate;

  List jobOptions = [];

  String validationMessage = "";
  String progressMessage = "";
  List<String> fileProgressMessage = [""];
  List<String> formMessages = [""];

  // Set non-TextEditingController values
  initInsideViewController(Supplier contractor){
    // Reset state values
    validationMessage = "";
    progressMessage = "";
    fileProgressMessage = [""];
    formMessages = [""];
  }

  clearInsideViewController(){
    selectedActivities.clear();
    jobOptions.clear();
    contactName.clear();
    contactSurname.clear();
    contactPhone.clear();
    contactEmail.clear();
    contractorName.clear();
    formationType.clear();
    contractorLegalEntity.clear();
    contractorIban.clear();
    contractorBillingCode.clear();
    contractorVat.clear();
    contractorFiscalCode.clear();
    contractorOperativeAddress = BaseAddressInfo.empty();
    contractorLegalAddress = BaseAddressInfo.empty();
    contractorPhone.clear();
    contractorEmail.clear();
    contractorPEC.clear();
    contractorNationality.clear();
    legalRepresentativeName.clear();
    legalRepresentativeSurname.clear();
    legalRepresentativePhone.clear();
    legalRepresentativeEmail.clear();
    legalRepresentativePEC.clear();
    legalRepresentativeFiscalCode.clear();
    legalRepresentativeAddress = BaseAddressInfo.empty();
    legalRepresentativeBirthCountry.clear();
    legalRepresentativeBirthProvince.clear();
    legalRepresentativeBirthCity.clear();
    legalRepresentativeSex.clear();
    legalRepresentativeBirthDate.clear();
    selectedLegalRepresentativeBirthDate = null;

    validationMessage = "";
    progressMessage = "";
    fileProgressMessage = [""];
    formMessages = [""];
  }

  clearFilter() {
    activitiesSelectedFilter = '';
    activitiesFilterController.clear();
  }
}
