import 'dart:ui';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/input.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/agency/agency_comment_popup.dart';
import 'package:newarc_platform/widget/agency/assegna_immobile_popup.dart';
import 'package:newarc_platform/widget/agency/contact_popup.dart';
import 'package:newarc_platform/widget/agency/custom_dropdown_button_2.dart';
import 'package:newarc_platform/widget/agency/immobile_popup.dart';
import '../../utils/various.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/widget/UI/form-label.dart';

class AcquiredContactsView extends StatefulWidget {
  final Agency agency;
  final AgencyUser agencyUser;
  final responsive;

  const AcquiredContactsView({
    Key? key,
    required this.agency,
    required this.agencyUser,
    required this.responsive,
    //this.receivedContactsPageFilters
  }) : super(key: key);

  @override
  State<AcquiredContactsView> createState() => _AcquiredContactsViewState();
}

class _AcquiredContactsViewState extends State<AcquiredContactsView> {
  bool loadingContacts = true;
  List<AcquiredContact> contacts = [];
  List<AcquiredContact> displayContacts = [];
  String query = "";
  String filterFieldWhere = "";
  String filterValueWhere = "";
  bool isValueNull = false;
  Key? paddingKey;

  List<Map> filters = [];

  List _testList = [
    {'no': 0, 'keyword': 'Filtra per stato'},
    {'no': 1, 'keyword': 'Da contattare'},
    {'no': 2, 'keyword': 'Contattato --'},
    {'no': 3, 'keyword': 'Non interessato'},
    {'no': 4, 'keyword': 'Acquisito'}
  ];
  List<DropdownMenuItem>? _dropdownTestItems;
  var _selectedStatus;

  late List<DocumentSnapshot> documentList;
  late int totalRecords = 0;
  String currentlyShowing = '';
  final recordsPerPage = 20;
  late int pageCounter;
  int totalPages = 0;
  bool disablePreviousButton = true;
  bool disableNextButton = false;
  List<dynamic> cacheFirestore = [];
  TextEditingController assignmentController = new TextEditingController();
  TextEditingController agencyController = new TextEditingController();
  TextEditingController searchTextController = new TextEditingController();
  TextEditingController filterCity = new TextEditingController();
  TextEditingController filterNewarcType = new TextEditingController();

  NewarcUser? newarcUser;

  List<Agency> agencyList = [];

  @override
  void initState() {
    _selectedStatus = _testList.first;
    _dropdownTestItems = buildDropdownTestItems(_testList);
    //print( widget.receivedContactsPageFilters);

    // print( filterTypeWhere.runtimeType );
    getMasterFilter();
    getAgencies();
    getNewarcUser();

    super.initState();
  }

  @override
  void dispose() {
    setState(() {
      contacts = [];
    });
    super.dispose();
  }

  @protected
  void didUpdateWidget(AcquiredContactsView oldWidget) {
    super.didUpdateWidget(oldWidget);
    getMasterFilter();
  }

  getNewarcUser() async {
    DocumentSnapshot<Map<String, dynamic>> collectionSnapshot;

    collectionSnapshot = await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_USERS)
        .doc(widget.agencyUser.id)
        .get();

    if (collectionSnapshot.exists) {
      newarcUser = NewarcUser.fromDocument(
          collectionSnapshot.data()!, collectionSnapshot.id);
    }
  }

  getAgencies() async {
    QuerySnapshot<Map<String, dynamic>> collectionSnapshot;

    collectionSnapshot =
        await FirebaseFirestore.instance.collection('agencies').get();

    documentList = collectionSnapshot.docs;
    // print({ 'documentlist', documentList });

    for (var element in collectionSnapshot.docs) {
      Agency agency = Agency.fromDocument(element.data(), element.id);

      agencyList.add(agency);
      /*
      var _tmp = element.data();
      if (_tmp['name'] != null &&
          _tmp['name'] != '' &&
          agencyList.indexOf(_tmp['name']) == -1) agencyList.add(_tmp['name']);*/
    }

    setState(() {});
  }

  getMasterFilter() {
    /*if (widget.receivedContactsPageFilters ==
        ReceivedContactsPageFilters.vendiNewarc) {
      filters.clear();
      filters.add({
        'field': 'additionalInfo.sellerType',
        'value': 'privato',
        'search': 'equal'
      });
      filters.add({
        'field': 'additionalInfo.vendiType',
        'value': 'subito/insieme',
        'search': 'equal'
      });
    } else if (widget.receivedContactsPageFilters ==
        ReceivedContactsPageFilters.vendiAgenzie) {
      filters.clear();
      filters.add({
        'field': 'additionalInfo.sellerType',
        'value': 'privato',
        'search': 'equal'
      });
      filters.add({
        'field': 'additionalInfo.vendiType',
        'value': 'agenzie',
        'search': 'equal'
      });
    } else if (widget.receivedContactsPageFilters ==
        ReceivedContactsPageFilters.professionista) {
      filters.clear();
      filters.add({
        'field': 'additionalInfo.sellerType',
        'value': 'professionista',
        'search': 'equal'
      });
      filters.add({
        'field': 'additionalInfo.requestType',
        'value': 'vendi',
        'search': 'equal'
      });
    } else if (widget.receivedContactsPageFilters ==
        ReceivedContactsPageFilters.valutaCompra) {
      filters.clear();
      filters.add({
        'field': 'additionalInfo.valutaType',
        'value': 'compra',
        'search': 'equal'
      });
    } else if (widget.receivedContactsPageFilters ==
        ReceivedContactsPageFilters.valutaCuriosita) {
      filters.clear();
      filters.add({
        'field': 'additionalInfo.valutaType',
        'value': 'curiosity',
        'search': 'equal'
      });
      isValueNull = true;
    } else if (widget.receivedContactsPageFilters ==
        ReceivedContactsPageFilters.valutaProfessionista) {
      filters.clear();
      filters.add({
        'field': 'additionalInfo.sellerType',
        'value': 'professionista',
        'search': 'equal'
      });
      filters.add({
        'field': 'additionalInfo.requestType',
        'value': 'valuta',
        'search': 'equal'
      });
    }*/

    initialFetchContacts(widget.agency);
  }

  getColor(String status) {
    switch (status) {
      case 'Da contattare':
        return Color(0xff5FBCEC);
      case 'Contattato':
        return Color(0xffFFC633);
      case 'Non interessato':
        return Color(0xffFF5E53);
      case 'Acquisito':
        return Color(0xff489B79);
    }
  }

  List<DropdownMenuItem> buildDropdownTestItems(List _testList) {
    List<DropdownMenuItem> items = [];
    for (var i in _testList) {
      items.add(
        DropdownMenuItem(
          value: i,
          child: Container(
            decoration: BoxDecoration(
              color: getColor(i['keyword']),
              borderRadius: BorderRadius.circular(8),
            ),
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    i['keyword'],
                    style: TextStyle(
                        color: Colors.white, fontWeight: FontWeight.w600),
                  ),
                ]),
          ),
        ),
      );
    }
    return items;
  }

  onChangeDropdownTests(String selectedStatus) {
    int i =
        _testList.indexWhere((element) => element['keyword'] == selectedStatus);

    setState(() {
      _selectedStatus = _testList.elementAt(i);
    });
  }

  Widget dataTablePagination() {
    return Padding(
        key: paddingKey,
        padding: EdgeInsets.symmetric(vertical: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            NarFormLabelWidget(
              label: "Pagina ${pageCounter.toString()} di " +
                  totalPages.toString() +
                  " pagine, contatti $currentlyShowing di  ${totalRecords.toString()}",
              textColor: Color.fromRGBO(131, 131, 131, 1),
              fontSize: 13,
            ),
            SizedBox(width: 5),
            TextButton(
              child: Icon(Icons.arrow_back_ios,
                  size: 20,
                  color: disablePreviousButton ? Colors.grey : Colors.black),
              onPressed: () {
                if (disablePreviousButton == true) return;
                fetchPrevContacts(widget.agency);
              },
              style: ButtonStyle(
                  overlayColor: MaterialStateProperty.all(Colors.transparent)),
            ),
            // SizedBox(width: 5),
            TextButton(
              child: Icon(Icons.arrow_forward_ios,
                  size: 20,
                  color: disableNextButton ? Colors.grey : Colors.black),
              onPressed: () {
                if (disableNextButton == true) return;
                fetchNextContacts(widget.agency);
              },
              style: ButtonStyle(
                  overlayColor: MaterialStateProperty.all(Colors.transparent)),
            ),
            TextButton(
              child: Icon(Icons.refresh,
                  size: 20,
                  color: disableNextButton ? Colors.grey : Colors.black),
              onPressed: () {
                // if (disableNextButton == true) return;
                // fetchNextContacts(widget.agency);
                cacheFirestore.clear();
                initialFetchContacts(widget.agency);
              },
              style: ButtonStyle(
                  overlayColor: MaterialStateProperty.all(Colors.transparent)),
            )
          ],
        ));
  }

  /*List<DataRow> loadingRow() {
    List<DataCell> list = [];

    list.add(DataCell(Text('')));
    list.add(DataCell(Text('')));
    list.add(DataCell(Text('')));
    list.add(DataCell(Padding(
        padding: EdgeInsets.symmetric(vertical: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(
              color: Theme.of(context).primaryColor,
            ),
            Text(
              'Loading',
              style: TextStyle(fontSize: 15, fontWeight: FontWeight.w900),
              textAlign: TextAlign.center,
            )
          ],
        ))));
    list.add(DataCell(Text('')));
    // if (widget.agencyUser.role == 'master') list.add(DataCell(Text('')));
    list.add(DataCell(Text('')));
    if (widget.agencyUser.role == 'master') {
      list.add(DataCell(Text('')));
    }

    // list.add(DataCell(Text('')));
    // if (widget.agencyUser.role == 'master') list.add(DataCell(Text('')));
    // if (widget.agencyUser.role == 'master') list.add(DataCell(Text('')));

    return [DataRow(cells: list)];
  }*/

  Future<void> initialFetchContacts(Agency agency) async {
    //TODO Rimuovere la collectionSnapshotCounterQuery, ha duplicato tutto555
    pageCounter = 1;

    setState(() {
      contacts = [];
      loadingContacts = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshotCounter;
      Query<Map<String, dynamic>> collectionSnapshotCounterQuery;

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      if (widget.agencyUser.role != 'master') {
        collectionSnapshotQuery = FirebaseFirestore.instance
            .collection(appConfig.COLLECT_VALUATOR_SUBMISSIONS);
        collectionSnapshotCounterQuery =
            FirebaseFirestore.instance.collection('valuatorSubmissions');

        if (filters.length > 0) {
          for (var i = 0; i < filters.length; i++) {
            if (filters[i]['search'] == 'equal') {
              collectionSnapshotQuery = collectionSnapshotQuery
                  .where(filters[i]['field'], isEqualTo: filters[i]['value']);
              collectionSnapshotCounterQuery = collectionSnapshotCounterQuery
                  .where(filters[i]['field'], isEqualTo: filters[i]['value']);
            } else if (filters[i]['search'] == 'like') {
              collectionSnapshotQuery
                  .where(filters[i]['field'],
                      isGreaterThanOrEqualTo: filters[i]['value'])
                  .where(filters[i]['field'],
                      isLessThanOrEqualTo: filters[i]['value'] + "\uf7ff");
            }
          }
        }

        collectionSnapshotQuery = collectionSnapshotQuery
            .where('assignedAgencyId', isEqualTo: agency.id);
        collectionSnapshotCounterQuery = collectionSnapshotCounterQuery
            .where('assignedAgencyId', isEqualTo: agency.id);

        collectionSnapshot = await collectionSnapshotQuery
            .orderBy('insertion_timestamp', descending: true)
            .limit(recordsPerPage)
            .get();

        collectionSnapshotCounter = await collectionSnapshotCounterQuery
            .orderBy('insertion_timestamp', descending: true)
            .get();
      } else {
        collectionSnapshotQuery =
            FirebaseFirestore.instance.collection('valuatorSubmissions');
        collectionSnapshotCounterQuery =
            FirebaseFirestore.instance.collection('valuatorSubmissions');

        //print({'master filter', filters.length, filters});
        if (filters.length > 0) {
          for (var i = 0; i < filters.length; i++) {
            // collectionSnapshotQuery = collectionSnapshotQuery
            //     .where(filters[i]['field'], isEqualTo: filters[i]['value']);
            //collectionSnapshotCounterQuery = collectionSnapshotCounterQuery
            //    .where(filters[i]['field'], isEqualTo: filters[i]['value']);
            if (filters[i]['search'] == 'equal') {
              collectionSnapshotQuery = collectionSnapshotQuery
                  .where(filters[i]['field'], isEqualTo: filters[i]['value']);
              collectionSnapshotCounterQuery = collectionSnapshotCounterQuery
                  .where(filters[i]['field'], isEqualTo: filters[i]['value']);
            } else if (filters[i]['search'] == 'like') {
              collectionSnapshotQuery = collectionSnapshotQuery
                  .where(filters[i]['field'],
                      isGreaterThanOrEqualTo: filters[i]['value'])
                  .where(filters[i]['field'],
                      isLessThanOrEqualTo: filters[i]['value'] + "\uf7ff");
              collectionSnapshotCounterQuery
                  .where(filters[i]['field'],
                      isGreaterThanOrEqualTo: filters[i]['value'])
                  .where(filters[i]['field'],
                      isLessThanOrEqualTo: filters[i]['value'] + "\uf7ff");
            }
          }
        }

        collectionSnapshot = await collectionSnapshotQuery
            .orderBy('insertion_timestamp', descending: true)
            .limit(recordsPerPage)
            .get();
        //await collectionSnapshotQuery.limit(recordsPerPage).get();

        collectionSnapshotCounter = await collectionSnapshotCounterQuery.get();
      }

      //totalRecords = collectionSnapshot.docs.length;
      totalRecords = collectionSnapshotCounter.docs.length;

      totalPages = (totalRecords / recordsPerPage).ceil();

      if (totalRecords > recordsPerPage) {
        currentlyShowing = '1-' + recordsPerPage.toString();
      } else {
        currentlyShowing = '1-' + recordsPerPage.toString();
      }

      documentList = collectionSnapshot.docs;

      await generateContacts(collectionSnapshot);

      setState(() {
        loadingContacts = false;
      });
    } catch (e) {
      setState(() {
        loadingContacts = false;
      });
      print(e.toString());
    }
  }

  fetchNextContacts(Agency agency) async {
    cacheFirestore.clear();

    setState(() {
      loadingContacts = true;
    });

    pageCounter++;

    try {
      // updateIndicator(true);
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;
      int indexOfSnapshot = isRecordExists(pageCounter);

      if (indexOfSnapshot > -1) {
        collectionSnapshot = cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        if (widget.agencyUser.role != 'master') {
          collectionSnapshotQuery =
              FirebaseFirestore.instance.collection('valuatorSubmissions');

          if (filters.length > 0) {
            for (var i = 0; i < filters.length; i++) {
              collectionSnapshotQuery = collectionSnapshotQuery
                  .where(filters[i]['field'], isEqualTo: filters[i]['value']);
            }
          }
          collectionSnapshotQuery = collectionSnapshotQuery
              .where('assignedAgencyId', isEqualTo: agency.id);

          collectionSnapshot = await collectionSnapshotQuery
              .orderBy('insertion_timestamp', descending: true)
              .limit(recordsPerPage)
              .startAfterDocument(documentList[documentList.length - 1])
              .get();
        } else {
          collectionSnapshotQuery =
              FirebaseFirestore.instance.collection('valuatorSubmissions');

          if (filters.length > 0) {
            for (var i = 0; i < filters.length; i++) {
              collectionSnapshotQuery = collectionSnapshotQuery
                  .where(filters[i]['field'], isEqualTo: filters[i]['value']);
            }
          }

          collectionSnapshot = await collectionSnapshotQuery
              .orderBy('insertion_timestamp', descending: true)
              .limit(recordsPerPage)
              .startAfterDocument(documentList[documentList.length - 1])
              .get();
        }
      }

      // List<DocumentSnapshot> newDocumentList = collectionSnapshot.docs;
      documentList = collectionSnapshot.docs;

      // documentList.addAll(newDocumentList);

      generateContacts(collectionSnapshot);
    } catch (e) {
      setState(() {
        loadingContacts = false;
      });
      print(e.toString());
      // movieController.sink.addError(e);
    }
  }

  fetchPrevContacts(Agency agency) async {
    cacheFirestore.clear();

    setState(() {
      loadingContacts = true;
    });

    pageCounter--;

    //print('Prev Page');

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      int indexOfSnapshot = isRecordExists(pageCounter);
      if (indexOfSnapshot > -1) {
        collectionSnapshot = cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        if (widget.agencyUser.role != 'master') {
          collectionSnapshotQuery =
              FirebaseFirestore.instance.collection('valuatorSubmissions');

          if (filters.length > 0) {
            for (var i = 0; i < filters.length; i++) {
              collectionSnapshotQuery = collectionSnapshotQuery
                  .where(filters[i]['field'], isEqualTo: filters[i]['value']);
            }
          }
          collectionSnapshotQuery = collectionSnapshotQuery
              .where('assignedAgencyId', isEqualTo: agency.id);

          collectionSnapshot = await collectionSnapshotQuery
              .orderBy('insertion_timestamp', descending: true)
              .limit(recordsPerPage)
              .endBeforeDocument(documentList[documentList.length - 1])
              .get();
        } else {
          collectionSnapshotQuery =
              FirebaseFirestore.instance.collection('valuatorSubmissions');

          if (filters.length > 0) {
            for (var i = 0; i < filters.length; i++) {
              collectionSnapshotQuery = collectionSnapshotQuery
                  .where(filters[i]['field'], isEqualTo: filters[i]['value']);
            }
          }

          collectionSnapshot = await collectionSnapshotQuery
              .orderBy('insertion_timestamp', descending: true)
              .limit(recordsPerPage)
              .endBeforeDocument(documentList[documentList.length - 1])
              .get();
        }
        // List<DocumentSnapshot> newDocumentList = collectionSnapshot.docs;
      }

      // updateIndicator(true);

      // documentList.addAll(newDocumentList);
      documentList = collectionSnapshot.docs;
      generateContacts(collectionSnapshot);
    } catch (e) {
      setState(() {
        loadingContacts = false;
      });
    }
  }

  int isRecordExists(pageCounter) {
    int isRecordExists =
        cacheFirestore.indexWhere((record) => record['key'] == pageCounter);
    return isRecordExists;
  }

  generateContacts(collectionSnapshot) async {
    // If a record already doesn't exists then store
    if (isRecordExists(pageCounter) < 0) {
      cacheFirestore.add({'key': pageCounter, 'snapshot': collectionSnapshot});
    }

    if (pageCounter == 1) {
      disablePreviousButton = true;
    } else {
      disablePreviousButton = false;
    }

    if (pageCounter == totalPages) {
      disableNextButton = true;
    } else {
      disableNextButton = false;
    }

    List<AcquiredContact> _contacts = [];
    for (var element in collectionSnapshot.docs) {
      try {
        var _tmp = AcquiredContact.fromDocument(element.data(), element.id);

        if (_tmp.assignedAgencyId != null && _tmp.assignedAgencyId != '') {
          DocumentSnapshot<Map<String, dynamic>> collectionSnapshot;
          collectionSnapshot = await FirebaseFirestore.instance
              .collection('agencies')
              .doc(_tmp.assignedAgencyId)
              .get();

          _tmp.agencyUser = Agency.fromDocument(
              collectionSnapshot.data()!, collectionSnapshot.id);
          // contacts[contacts.indexOf(contact)].agencyUser = Agency.fromDocument(collectionSnapshot.data()!
          // , collectionSnapshot.id);

          // print(contact.agencyUser);
        }

        _contacts.add(_tmp);
      } catch (e) {
        print("error in document ${element.id}");
        print(e);
      }
    }

    _contacts
        .sort((a, b) => b.insertionTimestamp!.compareTo(a.insertionTimestamp!));

    int lastRecordNumber = pageCounter * recordsPerPage;
    if (_contacts.length == recordsPerPage) {
      currentlyShowing = (lastRecordNumber - (recordsPerPage - 1)).toString() +
          '-' +
          lastRecordNumber.toString();
    } else if (_contacts.length > 0 && _contacts.length < recordsPerPage) {
      int prevLastRecordNumber = (pageCounter - 1) * recordsPerPage;

      currentlyShowing = (lastRecordNumber - (recordsPerPage - 1)).toString() +
          '-' +
          (prevLastRecordNumber + _contacts.length).toString();
    }

    setState(() {
      contacts = _contacts;
      displayContacts = _contacts;
      loadingContacts = false;
    });
  }

  /*showHideContacts() {
    if (agencyController.text != '' &&
        agencyController.text != 'Mostra tutti') {
      displayContacts = contacts.where((element) {
        if (element.agencyUser != null &&
            element.agencyUser!.name == agencyController.text) {
          return true;
        }
        return false;
      }).toList();
    } else {
      displayContacts = contacts.where((element) {
        return true;
      }).toList();
    }

    setState(() {});
  }*/

  updateContactStatus(AcquiredContact acquiredContact, String stage) async {
    acquiredContact.contactStage = stage;
    await updateDocument(appConfig.COLLECT_VALUATOR_SUBMISSIONS,
        acquiredContact.firebaseId!, acquiredContact.toMap());
    setState(() {
      displayContacts
          .where(filterFunction)
          .elementAt(displayContacts
              .where(filterFunction)
              .toList()
              .indexOf(acquiredContact))
          .contactStage = stage;
    });
  }

  void showHousePopup(AcquiredContact acquiredContact) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (_, __, ___) {
        return Center(
            child: ImmobilePopup(
          acquiredContact: acquiredContact,
        ));
      },
    );
  }

  void showContactPopup(AcquiredContact acquiredContact) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: false,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (_, __, ___) {
        return Center(
            child: BaseNewarcPopup(
          title: "Info Contatto",
          column: ContactPopup(acquiredContact: acquiredContact),
        ));
      },
    );
  }

  void showCommentPopup(AcquiredContact acquiredContact) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: false,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (_, __, ___) {
        return Center(
          child: AgencyCommentPopup(
              acquiredContact: acquiredContact,
              agency: widget.agency,
              newarcUser: newarcUser),
        );
      },
    );
  }

  void showAgencyStatusPopup(AcquiredContact acquiredContact, Agency agency) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.5),
      useRootNavigator: true,
      pageBuilder: (_, __, ___) {
        return Center(
            child: AssegnaImmobilePopup(
                acquiredContact: acquiredContact,
                agency: agency,
                initialFetchContacts: initialFetchContacts));
      },
    );
  }

  /*void showAgencyCommentPopup(
      AcquiredContact acquiredContact, Agency agency, AgencyUser agencyUser) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (_, __, ___) {
        return Center(
            child: AgencyCommentPopup(
                acquiredContact: acquiredContact,
                agency: agency,
                agencyUser: agencyUser
              ));
      },
    );
  }*/

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }
    if (widget.agencyUser.role != 'master') {
      pageWidth *= 1.45;
    }

    return Column(
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            NarFormLabelWidget(
              label: 'Contatti ricevuti',
              fontSize: 22,
              fontWeight: 'bold',
              overflow: TextOverflow.ellipsis,
              textColor: Colors.black,
            ),
            !widget.responsive
                ? Container()
                : Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      /* Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 5),
                        child: NarInputWidget(
                          hintText: "Cerca",
                          borderColor: Color.fromRGBO(227,227,227,1),
                          hintFontColor: Color.fromRGBO(227,227,227,1),
                          borderWidth: 2,
                          fontWeight: FontWeight.bold,
                          height: 0.7,
                          fontSize: 12,
                          onChanged: ( String _query ){
                            setState(() {
                              query = _query.toLowerCase();
                            });
                          },
                        ),*/

                      SizedBox(width: 30),
                      Container(
                        width: 180,
                        child: Column(
                          children: [
                            NarFormLabelWidget(
                              label: '',
                              fontSize: 10,
                              fontWeight: 'bold',
                              height: 1.5,
                            ),
                            NarInputWidget(
                              hintText: "Cerca contatto",
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              controller: searchTextController,
                              borderWidth: 1,
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: 10),
                      Column(
                        children: [
                          NarFormLabelWidget(
                            label: '',
                            fontSize: 10,
                            fontWeight: 'bold',
                            height: 1.5,
                          ),
                          Container(
                            height: 35,
                            width: 61,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(7),
                              color: Theme.of(context).primaryColor,
                            ),
                            child: TextButton(
                              onPressed: () {
                                if (searchTextController.text == '') {
                                  filters.removeWhere((element) {
                                    return element['field'] ==
                                        'additionalInfo.submitterName';
                                  });
                                } else {
                                  // var indexoffilter = filters.indexOf({ 'field': 'additionalInfo.submitterName' });

                                  filters.removeWhere((element) {
                                    return element['field'] ==
                                        'additionalInfo.submitterName';
                                  });

                                  filters.add({
                                    'field': 'additionalInfo.submitterName',
                                    'value': searchTextController.text,
                                    'search': 'equal'
                                  });
                                }

                                setState(() {
                                  cacheFirestore.clear();
                                  initialFetchContacts(widget.agency);
                                });
                              },
                              child: Center(
                                child: NarFormLabelWidget(
                                  label: "Cerca",
                                  textColor: Colors.white,
                                  fontSize: 13,
                                  fontWeight: 'bold',
                                ),
                              ),
                              style: ButtonStyle(
                                  overlayColor: MaterialStateProperty.all(
                                      Colors.transparent)),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
            widget.agencyUser.role != 'master'
                ? Container()
                : Expanded(
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Container(
                          width: 145,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Tipo',
                                fontSize: 10,
                                fontWeight: 'bold',
                                height: 1.5,
                              ),
                              NarSelectBoxWidget(
                                options: [
                                  "Tutti",
                                  "Subito/Insieme",
                                  "Agenzia",
                                  "Compra",
                                  "Curiosità"
                                ],
                                controller: filterNewarcType,
                                onChanged: (value) {
                                  if (filterNewarcType.text == 'Tutti') {
                                    filters.removeWhere((element) {
                                      return element['field'] ==
                                          'additionalInfo.newarcType';
                                    });
                                  } else {
                                    String tipo = filterNewarcType.text;
                                    if (tipo == 'Agenzia') {
                                      tipo = 'agency'; //vendi
                                    } else if (tipo == 'Subito/Insieme') {
                                      tipo = 'subito/insieme'; //vendi
                                    } else if (tipo == 'Curiosità') {
                                      tipo = 'curiosity'; //valuta
                                    } else if (tipo == 'Compra') {
                                      tipo = 'buy'; //valuta
                                    }

                                    filters.removeWhere((element) {
                                      return element['field'] ==
                                          'additionalInfo.newarcType';
                                    });

                                    filters.add({
                                      'field': 'additionalInfo.newarcType',
                                      'value': tipo,
                                      'search': 'equal'
                                    });
                                  }

                                  setState(() {
                                    cacheFirestore.clear();
                                    initialFetchContacts(widget.agency);
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: 5),
                        Container(
                          width: 145,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Città',
                                fontSize: 10,
                                fontWeight: 'bold',
                                height: 1.5,
                              ),
                              NarSelectBoxWidget(
                                options: [
                                  "Tutte",
                                  "Torino",
                                  "Milano",
                                  "Roma",
                                ],
                                controller: filterCity,
                                onChanged: (value) {
                                  if (filterCity.text == 'Tutte') {
                                    filters.removeWhere((element) {
                                      return element['field'] ==
                                          'addressObject.city';
                                    });
                                  } else {
                                    // var indexoffilter = filters.indexOf({ 'field': 'additionalInfo.submitterName' });

                                    filters.removeWhere((element) {
                                      return element['field'] ==
                                          'addressObject.city';
                                    });

                                    filters.add({
                                      'field': 'addressObject.city',
                                      'value': filterCity.text,
                                      'search': 'equal'
                                    });
                                  }

                                  setState(() {
                                    cacheFirestore.clear();
                                    initialFetchContacts(widget.agency);
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: 5),
                        Container(
                          width: 145,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Agenzia',
                                fontSize: 10,
                                fontWeight: 'bold',
                                height: 1.5,
                              ),
                              NarSelectBoxWidget(
                                options: ['Mostra tutti']
                                  ..addAll(agencyList.map((e) => e.name!)),
                                controller: agencyController,
                                onChanged: (value) {
                                  if (agencyController.text == 'Mostra tutti') {
                                    filters.removeWhere((element) {
                                      return element['field'] ==
                                          'assignedAgencyId';
                                    });
                                  } else {
                                    Agency _selectedAgency = agencyList
                                        .where((agency) =>
                                            agency.name! ==
                                            agencyController.text)
                                        .first;
                                    filters.removeWhere((element) {
                                      return element['field'] ==
                                          'assignedAgencyId';
                                    });

                                    filters.add({
                                      'field': 'assignedAgencyId',
                                      'value': _selectedAgency.id,
                                      'search': 'equal'
                                    });
                                  }

                                  setState(() {
                                    cacheFirestore.clear();
                                    initialFetchContacts(widget.agency);
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  )
          ],
        ),
        SizedBox(height: 20),
        loadingContacts
            ? Center(
                child: CircularProgressIndicator(
                color: Theme.of(context).primaryColor,
              ))
            : Expanded(
                child: Container(
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                        width: 1, color: Color.fromRGBO(219, 219, 219, 1))),
                padding: EdgeInsets.symmetric(horizontal: 0),
                child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 10),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Expanded(
                          child: DataTable2(
                            dataRowHeight: loadingContacts ? 300 : 70,
                            isHorizontalScrollBarVisible: true,
                            minWidth: 1500,
                            columnSpacing: 10,
                            horizontalMargin: 0,
                            headingRowHeight: 25,
                            columns: getColumns(pageWidth),
                            border: TableBorder(
                              bottom: BorderSide.none,
                              top: BorderSide.none,
                              left: BorderSide.none,
                              right: BorderSide.none,
                            ),
                            empty: Text('Nessun record trovato!'),
                            rows: List.generate(
                                displayContacts.where(filterFunction).length,
                                (int index) {
                              return DataRow(
                                key: ValueKey(contacts
                                    .where(filterFunction)
                                    .elementAt(index)
                                    .firebaseId),
                                color:
                                    MaterialStateProperty.resolveWith((states) {
                                  // If the button is pressed, return green, otherwise blue
                                  if (displayContacts
                                      .where(filterFunction)
                                      .elementAt(index)
                                      .wantsAgencyValuation!) {
                                    return Colors.white;
                                  }
                                  return Colors.white;
                                }),
                                cells: getDataRow(
                                  displayContacts
                                      .where(filterFunction)
                                      .elementAt(index),
                                ),
                              );
                            }),
                          ),
                        ),
                        dataTablePagination(),
                      ],
                    )),
              )),
      ],
    );
  }

  bool filterFunction(AcquiredContact contact) {
    bool _show = true;

    if (!contact.contactFullName!.toLowerCase().contains(query) &&
        !(contact.address! + " " + contact.streetNumber + "," + contact.city)
            .toLowerCase()
            .contains(query) &&
        !contact.contactEmail!.toLowerCase().contains(query)) {
      _show = false;
    }

    if (_show && _selectedStatus['keyword'] != "Filtra per stato") {
      if (contact.contactStage! != _selectedStatus['keyword']) {
        _show = false;
      }
    }

    return _show;
  }

  bool _showBlur(AcquiredContact contact, Agency agency) {
    return contact.unlocked! || widget.agencyUser.role == 'master';
  }

  //Why not a stream?
  Future getCommentCount(String firebaseId) async {
    QuerySnapshot<Map<String, dynamic>>? collectionSnapshot;
    collectionSnapshot = await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_VALUATOR_SUBMISSION_COMMENTS)
        .where('contactId', isEqualTo: firebaseId)
        .orderBy('date', descending: true)
        .get();

    return collectionSnapshot!.docs.length;
  }

  List<DataCell> getDataRow(AcquiredContact contact) {
    List<DataCell> list = [];

    if (contact.agencyUser != null) {
      // print(contact.agencyUser!.name);
    }

    // 1. Immobile
    var address =
        contact.address! + " " + contact.streetNumber + "," + contact.city;
    list.add(
      DataCell(
        Stack(
          children: [
            ImageFiltered(
              imageFilter: ImageFilter.blur(
                  sigmaX: _showBlur(contact, widget.agency) ? 0 : 7,
                  sigmaY: _showBlur(contact, widget.agency) ? 0 : 7),
              child: MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                    onTap: () {
                      if (!_showBlur(contact, widget.agency)) {
                        return;
                      }
                      showHousePopup(contact);
                    },
                    child: Padding(
                      padding: const EdgeInsets.only(left: 10),
                      child: NarFormLabelWidget(
                        label: address,
                        textAlign: TextAlign.start,
                        textColor: Colors.black,
                        fontWeight: 'bold',
                        fontSize: 12,
                        height: 2,
                        textDecoration: TextDecoration.underline,
                        overflow: TextOverflow.ellipsis,
                      ),
                    )),
              ),
            )
          ],
        ),
      ),
    );

    int millisecondsSinceEpoch = (widget.agencyUser.role == 'master')
        ? contact.insertionTimestamp!
        : contact.assignedAgencyTimestamp!;

    // 2. Data
    var date = DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch)
            .day
            .toString() +
        '/' +
        DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch)
            .month
            .toString() +
        '/' +
        DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch)
            .year
            .toString();
    list.add(
      DataCell(Container(
        child: NarFormLabelWidget(
          label: date,
          fontSize: 12,
          fontWeight: '800',
          textColor: Colors.black,
        ),
      )),
    );

    if (widget.agencyUser.role == 'master') {
      String? tipo;
      if (contact.newarcType != null) {
        tipo = contact.newarcType;
      } else {
        tipo = contact.valutaType ?? contact.vendiType;
      }

      if (tipo == 'nok' || tipo == 'agency') {
        tipo = 'Agenzia'; //vendi
      } else if (tipo == 'ok' || tipo == 'subito/insieme') {
        tipo = 'Subito/Insieme'; //vendi
      } else if (tipo == 'curiosity') {
        tipo = 'Curiosità'; //valuta
      } else if (tipo == 'compra' || tipo == 'buy') {
        tipo = 'Compra'; //valuta
      }

      list.add(
        DataCell(Container(
          child: NarFormLabelWidget(
            label: tipo,
            fontSize: 12,
            fontWeight: '800',
            textColor: Colors.black,
          ),
        )),
      );
    }
    if (widget.agencyUser.role == 'master') {
      String professione = contact.sellerProfession ?? 'Privato';

      if (professione == 'Agente immobiliare') {
        professione = 'Agente';
      }

      list.add(
        DataCell(Container(
          child: NarFormLabelWidget(
            label: professione,
            fontSize: 12,
            fontWeight: '800',
            textColor: Colors.black,
          ),
        )),
      );
    }

    // 3. contact
    // list.add(DataCell(
    //   IconButton(
    //     icon: Icon(Icons.contact_page_rounded),
    //     onPressed: () {
    //       showContactPopup(contact);
    //     },
    //   ),

    // ));

    list.add(
      DataCell(
        !_showBlur(contact, widget.agency)
            ? NarFormLabelWidget(
                label: contact.contactFullName!,
                fontSize: 12,
                fontWeight: '800',
                textColor: Colors.black,
              )
            : Container(
                child: TextButton(
                  child: Container(
                    child:
                        Image.asset('assets/icons/user-icon.png', height: 20),
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Color.fromRGBO(227, 227, 227, 1),
                      borderRadius: BorderRadius.circular(7.0),
                    ),
                  ),
                  onPressed: () {
                    !_showBlur(contact, widget.agency)
                        ? () => {}
                        : showContactPopup(contact);
                  },
                  style: ButtonStyle(
                      overlayColor:
                          MaterialStateProperty.all(Colors.transparent)),
                ),
              ),
      ),
    );

    /*if (widget.agencyUser.role == 'master') {
      list.add(DataCell(Icon(
          contact.wantsAgencyValuation == false
              ? Icons.cancel_outlined
              : Icons.check,
          color: contact.wantsAgencyValuation == false
              ? Colors.red
              : Colors.green[700],
          size: 20)));
    }*/

    if (widget.agencyUser.role == 'master') {
      list.add(DataCell(Icon(Icons.circle,
          color: contact.wantsAgencyValuation == false
              ? Colors.grey
              : Colors.green[700],
          size: 15)));
    }

    if (widget.agencyUser.role == 'master') {
      Color lockedColor = Color.fromRGBO(146, 146, 146, 1);
      Color unlockedColor = Color.fromRGBO(72, 155, 121, 1);

      list.add(
        DataCell(
          TextButton(
            child: Row(
              children: [
                Container(
                  height: 30,
                  width: 30,
                  child: SvgPicture.asset('assets/icons/plane.svg',
                      height: 20,
                      color: contact.assignedAgencyId == null
                          ? Color(0xff5b5b5b)
                          : Colors.white),
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: contact.assignedAgencyId == null
                        ? Colors.transparent
                        : contact.dateUnlocked != 'Not unlocked'
                            ? Color.fromRGBO(72, 155, 121, 1)
                            : Color.fromRGBO(147, 147, 147, 1),
                    borderRadius: BorderRadius.circular(7.0),
                  ),
                ),
              ],
            ),
            style: ButtonStyle(
                overlayColor: MaterialStateProperty.all(Colors.transparent)),
            onPressed: () {
              showAgencyStatusPopup(contact, widget.agency);
            },
          ),
        ),
      );
    }

    list.add(
      DataCell(!_showBlur(contact, widget.agency)
          ? GestureDetector(
              child: MouseRegion(
                cursor: SystemMouseCursors.click,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Color(0xffe82525),
                  ),
                  height: 32,
                  width: 150,
                  padding: EdgeInsets.symmetric(horizontal: 8),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        NarFormLabelWidget(
                            textColor: Colors.white,
                            fontWeight: 'bold',
                            fontSize: 13,
                            label: 'Da Sbloccare'),
                        Expanded(
                          child: Icon(
                            Icons.lock,
                            color: Colors.white,
                            size: 15,
                          ),
                        )
                      ]),
                ),
              ),
              onTap: () async {
                setState(() {
                  loadingContacts = true;
                });
                bool unlock = await showAlertDialog(context, "Sblocca immobile",
                    "Cliccando su sblocca visualizzerai i dati dell’immobile e del proprietario.\nConfermando la provenienza degli stessi dati da Newarc Srl come da termini contrattuali.",
                    addCancel: true);
                if (!unlock) {
                  setState(() {
                    loadingContacts = false;
                  });
                  return;
                }
                await updateDocument(
                    'valuatorSubmissions', contact.firebaseId!, {
                  'unlocked': true,
                  'contact_stage': 'Da contattare',
                  'dateUnlocked': DateTime.now().toLocal().toString()
                });
                var _c = displayContacts
                    .where(filterFunction)
                    .where((c) => c.firebaseId == contact.firebaseId)
                    .first;
                int index =
                    displayContacts.where(filterFunction).toList().indexOf(_c);
                setState(() {
                  displayContacts
                      .where(filterFunction)
                      .elementAt(index)
                      .unlocked = true;
                  displayContacts
                      .where(filterFunction)
                      .elementAt(index)
                      .contactStage = "Da contattare";

                  loadingContacts = false;
                });
              },
            )
          : contact.assignedAgencyId == null
              ? Container()
              : CustomDropdown2(
                  isMaster: widget.agencyUser.role == 'master',
                  acquiredContact: contact,
                  updateStage: updateContactStatus,
                )),
    );

    list.add(DataCell(
      ImageFiltered(
        imageFilter: ImageFilter.blur(
            sigmaX: _showBlur(contact, widget.agency) ? 0 : 7,
            sigmaY: _showBlur(contact, widget.agency) ? 0 : 7),
        child: Stack(
          children: [
            Align(
              alignment: AlignmentDirectional.centerStart,
              child: TextButton(
                child: Container(
                  height: 30,
                  width: 30,
                  padding: EdgeInsets.all(4),
                  child: SvgPicture.asset('assets/icons/comment.svg',
                      height: 12, color: Color(0xff5b5b5b)),
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(231, 231, 231, 1),
                    borderRadius: BorderRadius.circular(7.0),
                  ),
                ),
                onPressed: () {
                  !_showBlur(contact, widget.agency)
                      ? () => {}
                      : showCommentPopup(contact);
                },
                style: ButtonStyle(
                    overlayColor:
                        MaterialStateProperty.all(Colors.transparent)),
              ),
            ),
            Positioned(
              bottom: 15,
              left: 35,
              child: Container(
                height: 15,
                width: 15,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(15.0),
                    border: Border.all(
                        color: Color.fromRGBO(197, 197, 197, 1), width: 1)),
                child: StreamBuilder(
                  stream: FirebaseFirestore.instance
                      .collection(
                          appConfig.COLLECT_VALUATOR_SUBMISSION_COMMENTS)
                      .where('contactId', isEqualTo: contact.firebaseId)
                      .snapshots(),
                  builder: (BuildContext context,
                      AsyncSnapshot<QuerySnapshot> snapshot) {
                    if (!snapshot.hasData) {
                      return Center(
                        child: Container(),
                      );
                    }

                    return NarFormLabelWidget(
                      label: snapshot.data!.docs.length.toString(),
                      fontWeight: 'bold',
                      fontSize: 10,
                      height: 1.3,
                      textColor: Colors.black,
                      textAlign: TextAlign.center,
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    ));
    // list.add(DataCell(
    //   !contact.wantsAgencyValuation!
    //       ? Container()
    //       : ImageFiltered(
    //           imageFilter: ImageFilter.blur(
    //               sigmaX: _showBlur(contact, widget.agency) ? 0 : 7,
    //               sigmaY: _showBlur(contact, widget.agency) ? 0 : 7),
    //           child: IconButton(
    //             icon: Icon(Icons.comment),
    //             onPressed: () {
    //               !_showBlur(contact, widget.agency)
    //                   ? () => {}
    //                   : showCommentPopup(contact);
    //             },
    //           ),
    //         ),
    // ));

    return list;
  }

  List<DataColumn> getColumns(double pageWidth) {
    List<DataColumn> list = [];

    list.add(DataColumn2(
        fixedWidth: 0.14 * pageWidth,
        label: Padding(
          padding: const EdgeInsets.only(left: 10.0),
          child: NarFormLabelWidget(
            label: 'Immobile',
            fontSize: 13,
            fontWeight: 'bold',
            textColor: Color.fromRGBO(131, 131, 131, 1),
            height: 1.15,
          ),
        )));

    list.add(DataColumn2(
        fixedWidth: 0.07 * pageWidth,
        label: NarFormLabelWidget(
          label: 'Data',
          fontSize: 13,
          fontWeight: 'bold',
          textColor: Color.fromRGBO(131, 131, 131, 1),
          height: 1.15,
        )));

    if (widget.agencyUser.role == 'master') {
      list.add(DataColumn2(
          fixedWidth: 0.07 * pageWidth,
          label: NarFormLabelWidget(
            label: 'Tipo',
            fontSize: 13,
            fontWeight: 'bold',
            textColor: Color.fromRGBO(131, 131, 131, 1),
            height: 1.15,
          )));
    }
    if (widget.agencyUser.role == 'master') {
      list.add(DataColumn2(
          fixedWidth: 0.07 * pageWidth,
          label: NarFormLabelWidget(
            label: 'Professione',
            fontSize: 13,
            fontWeight: 'bold',
            textColor: Color.fromRGBO(131, 131, 131, 1),
            height: 1.15,
          )));
    }
    list.add(DataColumn2(
        fixedWidth: 0.07 * pageWidth,
        label: NarFormLabelWidget(
          label: 'Contatto',
          fontSize: 13,
          fontWeight: 'bold',
          textColor: Color.fromRGBO(131, 131, 131, 1),
          height: 1.15,
        )));

    /*if (widget.agencyUser.role == 'master') {
      list.add(DataColumn2(
          size: ColumnSize.L,
          fixedWidth: 130,
          label: NarFormLabelWidget(
            label: 'Consenso dati?',
            fontSize: 13,
            fontWeight: '700',
            textColor: Color.fromRGBO(131, 131, 131, 1),
          )));
    }*/

    if (widget.agencyUser.role == 'master') {
      list.add(
        DataColumn2(
          fixedWidth: 0.05 * pageWidth,
          label: NarFormLabelWidget(
            label: 'Agenzia',
            fontSize: 13,
            fontWeight: 'bold',
            textColor: Color.fromRGBO(131, 131, 131, 1),
            height: 1.15,
          ),
        ),
      );
    }

    if (widget.agencyUser.role == 'master') {
      list.add(DataColumn2(
          fixedWidth: 0.07 * pageWidth,
          label: NarFormLabelWidget(
            label: 'Assegnazione',
            fontSize: 13,
            fontWeight: 'bold',
            textColor: Color.fromRGBO(131, 131, 131, 1),
            height: 1.15,
          )));
    }

    list.add(DataColumn2(
        fixedWidth: 0.1 * pageWidth,
        label: NarFormLabelWidget(
          label: 'Stato',
          fontSize: 13,
          fontWeight: 'bold',
          textColor: Color.fromRGBO(131, 131, 131, 1),
          height: 1.15,
        )));

    list.add(DataColumn2(
        fixedWidth: 0.07 * pageWidth,
        label: NarFormLabelWidget(
          label: 'Commenti',
          fontSize: 13,
          fontWeight: 'bold',
          textColor: Color.fromRGBO(131, 131, 131, 1),
          height: 1.15,
        )));
    return list;
  }
}
