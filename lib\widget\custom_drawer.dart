import 'package:flutter/material.dart';
import 'package:newarc_platform/widget/custom_icon_button.dart';

class CustomDrawer extends StatefulWidget {
  CustomDrawer({Key? key}) : super(key: key);

  @override
  _CustomDrawerState createState() => _CustomDrawerState();
}

class _CustomDrawerState extends State<CustomDrawer> {
  double borderRadius = 10;
  TextStyle menuItemStyle = TextStyle(color: Colors.white, fontSize: 18);
  bool reduced = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: reduced ? 72 : 260,
      color: Theme.of(context).primaryColorDark,
      child: Column(
        children: [
          Row(
            mainAxisAlignment:
                reduced ? MainAxisAlignment.center : MainAxisAlignment.end,
            children: [
              GestureDetector(
                child: Container(
                  height: 21,
                  width: 21,
                  decoration: BoxDecoration(
                    color: Color(0xff606060),
                    borderRadius: BorderRadius.circular(5),
                  ),
                  margin: EdgeInsets.only(right: reduced ? 0 : 25, top: 20),
                  padding: EdgeInsets.only(left: 5),
                  child: Icon(
                    Icons.arrow_back_ios,
                    color: Colors.white,
                    size: 15,
                  ),
                ),
                onTap: () {
                  setState(() {
                    reduced = !reduced;
                  });
                },
              )
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(top: 35, bottom: 94),
            child: Image.asset(
              reduced ? 'assets/icon.png' : 'assets/logo.png',
              width: reduced ? 20 : 139,
            ),
          ),
          _getTile("Ricerca immobili", Icons.search, reduced),
          _getTile("Inserisci immobile", Icons.add, reduced),
          SizedBox(height: 20),
          _getTile("Immobili salvati", Icons.favorite, reduced),
          _getTile("Appuntamenti", Icons.watch_later_outlined, reduced),
          _getTile("Trattative", Icons.business, reduced),
          _getTile("Immobili acquistati", Icons.privacy_tip, reduced),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: CustomIconButton(
                    leadingIcon: true,
                    label: reduced ? "" : "Case Newarc",
                    textStyle: TextStyle(color: Colors.white),
                    width: reduced ? 47 : 220,
                    height: 47,
                    icon: Padding(
                      padding: EdgeInsets.only(right: reduced ? 0 : 10),
                      child: Image.asset(
                        'assets/icon.png',
                        color: Colors.white,
                      ),
                    ),
                    color: Theme.of(context).primaryColor,
                    function: () => {},
                  ),
                )
              ],
            ),
          ),
          SizedBox(height: 40)
        ],
      ),
    );
  }

  Widget _getTile(String label, IconData icon, bool reduced) {
    return GestureDetector(
      onTap: () {},
      child: Padding(
        padding: const EdgeInsets.only(bottom: 23.0),
        child: reduced
            ? Icon(
                icon,
                color: Color(0xff646464),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  SizedBox(width: 15),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Icon(
                        icon,
                        color: Color(0xff646464),
                      ),
                    ],
                  ),
                  SizedBox(width: 15),
                  Text(
                    label,
                    style: menuItemStyle,
                  ),
                ],
              ),
      ),
    );
  }
}
