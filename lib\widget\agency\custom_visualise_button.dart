import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/pages/agency/acquired_contacts_view.dart';

class CustomVisualizeButton extends StatefulWidget {
  final Function showPopupCallback;
  final AcquiredContact acquiredContact;
  final IconData iconData;
  Agency? agency;
  final bool disabled;
  Color? color;

  CustomVisualizeButton({
    Key? key,
    required this.showPopupCallback,
    required this.acquiredContact,
    required this.iconData,
    required this.disabled,
    this.agency,
    this.color,
  }) : super(key: key);

  @override
  State<CustomVisualizeButton> createState() => _CustomVisualizeButtonState();
}

class _CustomVisualizeButtonState extends State<CustomVisualizeButton> {
  Color? color;
  @override
  void initState() {
    if (widget.color == null) {
      color = Color(0xff489B79);
    } else {
      color = widget.color;
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (!widget.disabled) {
          if (widget.agency == null) {
            widget.showPopupCallback(widget.acquiredContact);
          } else {
            widget.showPopupCallback(widget.acquiredContact, widget.agency);
          }
        }
      },
      child: Container(
        height: 50,
        width: 50,
        margin: EdgeInsets.symmetric(vertical: 10),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: widget.disabled ? Colors.grey : color!),
            color: widget.disabled ? Colors.grey : color,
            boxShadow: [
              BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  blurRadius: 5,
                  spreadRadius: 2)
            ]),
        child: Icon(widget.iconData, color: Colors.white),
      ),
    );
  }
}
