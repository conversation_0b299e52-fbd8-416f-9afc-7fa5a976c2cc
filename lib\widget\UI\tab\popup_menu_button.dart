import 'package:flutter/material.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/tab/common_icon_button.dart';
import 'package:newarc_platform/widget/UI/tab/text_style.dart';
import 'package:url_launcher/url_launcher.dart';

import '../link.dart';

class PopupMenuOnHover extends StatelessWidget {
  final GlobalKey _popupKey = GlobalKey();

  final String? icon, title, infoTitle, note;
  final String reason;
  final bool? isForProfessionals;
  final Widget? children;

  PopupMenuOnHover({
    super.key,
    this.icon,
    this.title,
    this.infoTitle,
    this.isForProfessionals,
    this.children, this.note,
    required this.reason
  });

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      child: PopupMenuButton<int>(
        key: _popupKey,
        tooltip: '',
        shadowColor: AppColor.black.withOpacity(0.6),
        elevation: 2,
        offset: Offset(0, 40),
        color: AppColor.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        child: IconButtonWidget(
          isOnlyBorder: true,
          width: 20,
          height: 20,
          borderColor: AppColor.buttonBorderColor,
          backgroundColor: AppColor.white,
          isSvgIcon: true,
          borderRadius: 5,
          iconPadding: EdgeInsets.all(3),
          icon: icon ?? 'assets/icons/question_mark.svg',
          iconColor: AppColor.drawerButtonColor,
        ),
        itemBuilder: (BuildContext context) => [
          PopupMenuItem<int>(
            enabled: false,
            value: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    NarFormLabelWidget(
                      label: title ?? 'Richiesta bloccata',
                      fontSize: 13,
                      fontWeight: '700',
                      textColor:Color(0xffE82525),
                    ),
                    NarFormLabelWidget(
                      label: infoTitle ?? 'Perchè?',
                      fontSize: 11,
                      fontWeight: '600',
                      textColor: Color(0xFFB6B6B6),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Divider(
                    height: 1,
                    color: Color(0xffE2E2E2),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: children ??
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            overflow: TextOverflow.visible,
                            label: reason == "Planimetrie non conformi" ? 'Le planimetrie inviate non sono consone alla realizzazione del progetto.' : 'Le fotografie inviate non sono consone alla realizzazione del progetto.',
                            fontSize: 12,
                            fontWeight: '600',
                            textColor: AppColor.black,
                          ),
                          NarFormLabelWidget(
                            overflow: TextOverflow.visible,
                            label: 'Entra nella richiesta e sostituiscile.',
                            fontSize: 12,
                            fontWeight: '700',
                            textColor: AppColor.black,
                          ),
                          SizedBox(
                            height: 10,

                          ),
                          NarFormLabelWidget(
                            overflow: TextOverflow.visible,
                            label: 'NOTE DA NEWARC: $note',
                            fontSize: 12,
                            fontWeight: '700',
                            textColor: AppColor.black,
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          if (!(isForProfessionals ?? false)) ...[
                            NarFormLabelWidget(
                              overflow: TextOverflow.visible,
                              label: reason == "Planimetrie non conformi" ? 'Per sapere come realizzare le planimetrie nel modo giusto segui questa guida' : 'Per sapere come realizzare le fotografie nel modo giusto',
                              fontSize: 12,
                              fontWeight: '600',
                              textColor: AppColor.black,
                            ),
                            NarLinkWidget(
                              text: 'segui questa guida',
                              fontSize: 12,
                              fontWeight: '600',
                              textColor: Theme.of(context).primaryColor,
                              textDecoration: TextDecoration.underline,
                              onClick: ()async{
                                if(reason == "Planimetrie non conformi"){
                                  await launchUrl(
                                    Uri.parse("https://www.newarc.it/guida-agenzie/#planimetria"),
                                  );
                                }else{
                                  await launchUrl(
                                    Uri.parse("https://www.newarc.it/guida-agenzie/#immagini"),
                                  );
                                }
                              },
                            ),
                          ]
                        ],
                      ),
                ),
              ],
            ),
          ),
        ],
        onSelected: (int value) {
          print('Selected: $value');
        },
      ),
    );
  }
}
