import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:country_picker/country_picker.dart';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/utils/inputFormatters.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/classes/supplier.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/basePersonInfo.dart';
import 'package:newarc_platform/pages/work/gestione/contractors/contractors_controller.dart';
import 'package:newarc_platform/widget/UI/address_completion_google.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/file-picker.dart';
import 'package:newarc_platform/widget/UI/multi-select-dropdown.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/app_config.dart' as appConfig;


class ContractorsInsideView extends StatefulWidget {
  final Function updateViewCallback;
  final Supplier contractor;
  const ContractorsInsideView({super.key, required this.updateViewCallback, required this.contractor});

  @override
  State<ContractorsInsideView> createState() => _ContractorsInsideViewState();
}

class _ContractorsInsideViewState extends State<ContractorsInsideView> {
  final controller = Get.put<ContractorsController>(ContractorsController());
  final List<String> documents=[];
  final _formKey = GlobalKey<FormState>();

  Future<bool> updateContractorActiveStatus(Supplier contractor) async {
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    return await _db
        .collection(appConfig.COLLECT_SUPPLIERS)
        .doc(contractor.id)
        .update(contractor.toMap())
        .then((value) {
      return true;
    }).onError((error, stackTrace) {
      // print({error, stackTrace});
      return false;
    });
  }

  Future<bool> updateData(BuildContext context) async {
    setState(() {
      controller.validationMessage = "";
    });
    widget.contractor.firstName = controller.contactName.text;
    widget.contractor.lastName = controller.contactSurname.text;
    widget.contractor.phone = controller.contractorPhone.text;
    widget.contractor.email = controller.contractorEmail.text;
    widget.contractor.pec = controller.contractorPEC.text;
    widget.contractor.nationality= controller.contractorNationality.text;
    widget.contractor.contactPersonInfo = BasePersonInfo.fromMap(
        {
          'name': controller.contactName.text,
          'surname': controller.contactSurname.text,
          'phone': controller.contactPhone.text,
          'email': controller.contactEmail.text
        }
    );
    widget.contractor.name = controller.contractorName.text;
    widget.contractor.formationType = controller.formationType.text;
    widget.contractor.city = controller.contractorOperativeAddress.city;
    widget.contractor.addressAndCivicNumber = controller.contractorOperativeAddress.fullAddress;
    widget.contractor.iban = controller.contractorIban.text;
    widget.contractor.billingCode = controller.contractorBillingCode.text;
    widget.contractor.iva = controller.contractorVat.text;
    widget.contractor.legalAddress = controller.contractorLegalAddress.fullAddress;
    widget.contractor.operativeAddressInfo = controller.contractorOperativeAddress;
    widget.contractor.legalAddressInfo = controller.contractorLegalAddress;
    widget.contractor.legalEntity = controller.contractorLegalEntity.text;
    widget.contractor.fiscalCode = controller.contractorFiscalCode.text;
    widget.contractor.modificationTimestamp = DateTime.now().millisecondsSinceEpoch;

    widget.contractor.legalRepresentativePersonInfo = LegalRepresentativePersonInfo.fromMap({
      'name': controller.legalRepresentativeName.text,
      'surname': controller.legalRepresentativeSurname.text,
      'phone': controller.legalRepresentativePhone.text,
      'email': controller.legalRepresentativeEmail.text,
      'pec': controller.legalRepresentativePEC.text,
      'fiscalCode': controller.legalRepresentativeFiscalCode.text,
      'birthCountry': controller.legalRepresentativeBirthCountry.text,
      'birthProvince': controller.legalRepresentativeBirthProvince.text,
      'birthCity': controller.legalRepresentativeBirthCity.text,
      'sex': controller.legalRepresentativeSex.text,
      'birthDate': controller.legalRepresentativeBirthDate.text,
      'residentialAddressInfo': controller.legalRepresentativeAddress.toMap()
    });

    widget.contractor.activities!.clear();
    controller.selectedActivities.map((element) {
      widget.contractor.activities!.add(element['value']);
    }).toList();

    widget.contractor.documents!.clear();
    for (var i = 0; i < documents.length; i++) {
      widget.contractor.documents!.add(documents[i]);
    }
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    await _db
        .collection(appConfig.COLLECT_SUPPLIERS)
        .doc(widget.contractor.id)
        .update(widget.contractor.toMap());

    return true;
  }

  Future onVendorFileUploadasync() async {

    final FirebaseFirestore _db = FirebaseFirestore.instance;
    // widget.supplier.documents!.clear();
    for (var i = 0; i < documents.length; i++) {
      widget.contractor.documents!.add(documents[i]);
    }

    try {
      await _db
          .collection(appConfig.COLLECT_SUPPLIERS)
          .doc(widget.contractor.id)
          .update(widget.contractor.toMap());

      // documents.clear();

      if (mounted) {
        setState(() {
          controller.fileProgressMessage.clear();
          controller.fileProgressMessage.add('Saved!');
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          controller.fileProgressMessage.clear();
          controller.fileProgressMessage.add('Error');
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();

    // Initialize form fields directly for immediate display
    controller.selectedActivities.clear();
    controller.contractorOperativeAddress = widget.contractor.operativeAddressInfo ?? BaseAddressInfo.empty();
    controller.contractorLegalAddress = widget.contractor.legalAddressInfo ?? BaseAddressInfo.empty();

    // Process activities for dropdown
    if (widget.contractor.activities != null && widget.contractor.activities!.isNotEmpty) {
      for (var activity in widget.contractor.activities!) {
        var matchingOption = controller.jobOptions.firstWhere(
          (option) => option['value'] == activity,
          orElse: () => {"value": "", "label": ""}
        );
        if (matchingOption["label"] != "") {
          controller.selectedActivities.add(matchingOption);
        }
      }
    }

    // Handle documents safely
    documents.clear();
    if (widget.contractor.documents != null) {
      for (var doc in widget.contractor.documents!) {
        if (doc is String) {
          documents.add(doc);
        }
      }
    }

    // Use addPostFrameCallback for TextEditingController values to avoid build-time setState
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Set text field values
      controller.contactName.text = widget.contractor.contactPersonInfo?.name ?? widget.contractor.firstName ?? "";
      controller.contactSurname.text = widget.contractor.contactPersonInfo?.surname ?? widget.contractor.lastName ?? "";
      controller.contactPhone.text = widget.contractor.contactPersonInfo?.phone ?? widget.contractor.phone ?? "";
      controller.contactEmail.text = widget.contractor.contactPersonInfo?.email ?? widget.contractor.email ?? "";
      controller.contractorName.text = widget.contractor.name ?? "";
      controller.formationType.text = widget.contractor.formationType ?? "";
      controller.contractorLegalEntity.text = widget.contractor.legalEntity ?? "";
      controller.contractorIban.text = widget.contractor.iban ?? "";
      controller.contractorBillingCode.text = widget.contractor.billingCode ?? "";
      controller.contractorVat.text = widget.contractor.iva ?? "";
      controller.contractorFiscalCode.text = widget.contractor.fiscalCode ?? "";
      controller.contractorPhone.text = widget.contractor.phone ?? "";
      controller.contractorEmail.text = widget.contractor.email ?? "";
      controller.contractorPEC.text = widget.contractor.pec ?? "";
      controller.contractorNationality.text = widget.contractor.nationality ?? "";

      controller.legalRepresentativeName.text = widget.contractor.legalRepresentativePersonInfo?.name ?? "";
      controller.legalRepresentativeSurname.text = widget.contractor.legalRepresentativePersonInfo?.surname ?? "";
      controller.legalRepresentativePhone.text = widget.contractor.legalRepresentativePersonInfo?.phone ?? "";
      controller.legalRepresentativeEmail.text = widget.contractor.legalRepresentativePersonInfo?.email ?? "";
      controller.legalRepresentativePEC.text = widget.contractor.legalRepresentativePersonInfo?.pec ?? "";
      controller.legalRepresentativeFiscalCode.text = widget.contractor.legalRepresentativePersonInfo?.fiscalCode ?? "";
      controller.legalRepresentativeBirthCountry.text = widget.contractor.legalRepresentativePersonInfo?.birthCountry ?? "";
      controller.legalRepresentativeBirthProvince.text = widget.contractor.legalRepresentativePersonInfo?.birthProvince ?? "";
      controller.legalRepresentativeBirthCity.text = widget.contractor.legalRepresentativePersonInfo?.birthCity ?? "";
      controller.legalRepresentativeSex.text = widget.contractor.legalRepresentativePersonInfo?.sex ?? "";
      controller.legalRepresentativeBirthDate.text = widget.contractor.legalRepresentativePersonInfo?.birthDate ?? "";

      // Force a rebuild to ensure all values are displayed
      if (mounted) setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    var containerWidth = MediaQuery.of(context).size.width * .75;
    return SingleChildScrollView(
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      hoverColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      onPressed: () {
                        controller.clearInsideViewController();
                        documents.clear();
                        widget.updateViewCallback('contractors');
                      },
                      icon: SvgPicture.asset('assets/icons/arrow_left.svg',
                          height: 20, color: Colors.black),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    NarFormLabelWidget(
                      label:
                          widget.contractor.name! + ' ' + widget.contractor.formationType!,
                      fontSize: 20,
                      fontWeight: 'bold',
                    ),
                  ],
                ),
                Row(
                  children: [
                    NarFormLabelWidget(
                      label: 'Attiva/Disattiva',
                      fontSize: 15,
                      fontWeight: '500',
                    ),
                    Switch(
                      // This bool value toggles the switch.
                      value: widget.contractor.isActive!,
                      activeColor: Theme.of(context).primaryColor,
                      onChanged: (bool value) async {
                        // This is called when the user toggles the switch.
                        setState(() {
                          widget.contractor.isActive = value;
                        });
                        await updateContractorActiveStatus(widget.contractor);
                      },
                    ),
                  ],
                )
              ],
            ),
            SizedBox(height: 35),
            NarFormLabelWidget(
              label: 'Dati Ditta',
              fontSize: 18,
              fontWeight: 'bold',
            ),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomTextFormField(
                  label: 'Nome Ditta',
                  controller: controller.contractorName,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Obbligatorio';
                    }
                    return null;
                  },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            AddressSearchBar(
              label: "Indirizzo sede operativa",
              initialAddress: widget.contractor.operativeAddressInfo?.fullAddress ?? widget.contractor.addressAndCivicNumber ?? "",
              // validator: (value) {
              //   if (value == null || value.isEmpty) {
              //     return 'Obbligatorio';
              //   }
              //   return null;
              // },
              onPlaceSelected: (selectedPlace){
                log('Selected place: \n$selectedPlace');
                BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace['place']);
                if (selectedAddress.isValidAddress()){
                  controller.contractorOperativeAddress = selectedAddress;
                } else {
                  controller.contractorOperativeAddress = BaseAddressInfo.empty();
                }
              }
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      NarFormLabelWidget(
                        label: "Attività",
                        textColor: Color(0xff696969),
                        fontSize: 13,
                        fontWeight: '600',
                      ),
                      SizedBox(height: 4),
                      MultiSelectDropdownWidget(
                        options: controller.jobOptions,
                        initialValue: controller.selectedActivities,
                        // validationType: 'required',
                        // parametersValidate: 'Obbligatorio',
                        onChanged: (List<dynamic> selectedValues) {
                          controller.selectedActivities = selectedValues;
                          setState(() {});
                        },
                      )
                    ],
                  ),
                ),
                SizedBox(
                  width: 15,
                ),
                Expanded(flex: 1, child: Container(),),
                SizedBox(
                  height: 0,
                )
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomTextFormField(
                  label: 'E-mail (login)',
                  controller: controller.contractorEmail,
                  readOnly: true,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15
                ),
                CustomTextFormField(
                  label: 'PEC',
                  controller: controller.contractorPEC,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisAlignment:
                  MainAxisAlignment.center,
              children: [
                Container(
                  width: containerWidth * .525,
                  child: CustomTextFormField(
                    isExpanded: false,
                    textAlign: TextAlign.left,
                    isHaveBorder: true,
                    isCenterLabel: false,
                    flex: 0,
                    suffixIcon: Container(
                      padding: const EdgeInsets.all(10),
                      height: 20,
                      width: 20,
                      // child: Icon(Icons.web),
                    ),
                    // readOnly: true,
                    label: "Nazionalità",
                    controller: controller.contractorNationality,
                    onTap: () {
                      showCountryPicker(
                        context: context,
                        showPhoneCode: false, // Set to true if you want dial codes
                        onSelect: (Country country) {
                          setState(() {
                            controller.contractorNationality.text = country.name;
                          });
                        },
                      );
                    },
                  ),
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label:
                      "Telefono",
                  controller: controller.contractorPhone,
                  inputFormatters: [phoneNumberMaskFormatterInternational],
                  // validator: (value) {
                  //   if (value == '') {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(height:15),
            Divider(
              color: Color(0xffd7d7d7d7),
              thickness: 1,
            ),
            SizedBox(
              height: 15,
            ),
            NarFormLabelWidget(
              label: 'Persona di riferimento',
              fontSize: 18,
              fontWeight: 'bold',
            ),
            SizedBox(
              height: 20,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Nome',
                  controller: controller.contactName,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'Cognome',
                  controller: controller.contactSurname,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Telefono',
                  controller: controller.contactPhone,
                  inputFormatters: [phoneNumberMaskFormatterInternational],
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'E-mail',
                  controller: controller.contactEmail,
                  // readOnly: true,
                ),
              ],
            ),
            SizedBox(height:15),
            Divider(
              color: Color(0xffd7d7d7d7),
              thickness: 1,
            ),
            SizedBox(
              height: 15,
            ),
            NarFormLabelWidget(
              label: 'Legale Rappresentante',
              fontSize: 18,
              fontWeight: 'bold',
            ),
            SizedBox(
              height: 20,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Nome',
                  controller: controller.legalRepresentativeName,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'Cognome',
                  controller: controller.legalRepresentativeSurname,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Telefono',
                  controller: controller.legalRepresentativePhone,
                  inputFormatters: [phoneNumberMaskFormatterInternational],
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'E-mail',
                  controller: controller.legalRepresentativeEmail,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'PEC',
                  controller: controller.legalRepresentativePEC,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'Codice Fiscale',
                  controller: controller.legalRepresentativeFiscalCode,
                  inputFormatters: [codiceFiscaleMaskFormatter],
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisAlignment:
                  MainAxisAlignment.center,
              children: [
                Container(
                  width: containerWidth * .525,
                  child: CustomTextFormField(
                    isExpanded: false,
                    textAlign: TextAlign.left,
                    isHaveBorder: true,
                    isCenterLabel: false,
                    flex: 0,
                    suffixIcon: Container(
                      padding: const EdgeInsets.all(10),
                      height: 20,
                      width: 20,
                      // child: Icon(Icons.web),
                    ),
                    // readOnly: true,
                    label: "Stato di nascita",
                    controller: controller.legalRepresentativeBirthCountry,
                    onTap: () {
                      showCountryPicker(
                        context: context,
                        showPhoneCode: false, // Set to true if you want dial codes
                        onSelect: (Country country) {
                          setState(() {
                            controller.legalRepresentativeBirthCountry.text = country.name;
                          });
                        },
                      );
                    },
                  ),
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label:
                      "Provincia di nascita",
                  controller: controller.legalRepresentativeBirthProvince,
                  // validator: (value) {
                  //   if (value == '') {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Città di nascita',
                  controller: controller.legalRepresentativeBirthCity,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'Sesso',
                  controller: controller.legalRepresentativeSex,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            AddressSearchBar(
              label: "Indirizzo di residenza",
              initialAddress: widget.contractor.legalRepresentativePersonInfo?.residentialAddressInfo?.fullAddress ?? "",
              // validator: (value) {
              //   if (value == null || value.isEmpty) {
              //     return 'Obbligatorio';
              //   }
              //   return null;
              // },
              onPlaceSelected: (selectedPlace){
                log('Selected place: \n$selectedPlace');
                BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace['place']);
                if (selectedAddress.isValidAddress()){
                  controller.legalRepresentativeAddress = selectedAddress;
                } else {
                  controller.legalRepresentativeAddress = BaseAddressInfo.empty();
                }
              }
            ),
            SizedBox(
              height: 15,
            ),
            CustomTextFormField(
              textAlign: TextAlign.left,
              isHaveBorder: true,
              isCenterLabel: false,
              flex: 0,
              suffixIcon: Container(
                padding: const EdgeInsets.all(10),
                height: 20,
                width: 20,
                child: Image.asset('assets/icons/calendar.png'),
              ),
              readOnly: true,
              label: "Data di nascita legale rappresentante",
              controller: controller.legalRepresentativeBirthDate,
              onTap: () async {
                DateTime? pickedDate = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(1930),
                  lastDate: DateTime(DateTime.now().year + 1),
                );
                if (pickedDate != null) {
                  controller.selectedLegalRepresentativeBirthDate = pickedDate.millisecondsSinceEpoch;
                  String formattedDate = DateFormat('dd/MM/yyyy').format(pickedDate);
                  controller.legalRepresentativeBirthDate.text = formattedDate;
                } else {
                  log("No Date Selected");
                }
              },
            ),
            SizedBox(
              height: 15,
            ),
            Divider(
              color: Color(0xffd7d7d7d7),
              thickness: 1,
            ),
            SizedBox(
              height: 15,
            ),
            NarFormLabelWidget(
              label: 'Fatturazione e pagamenti',
              fontSize: 18,
              fontWeight: 'bold',
            ),
            SizedBox(
              height: 20,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Denominazione',
                  controller: controller.contractorLegalEntity,
                  textCapitalization: TextCapitalization.words,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      NarFormLabelWidget(
                        label: "Forma Societaria",
                        textColor: Color(0xff696969),
                        fontSize: 13,
                        fontWeight: '600',
                      ),
                      SizedBox(height: 4),
                      NarSelectBoxWidget(
                        options: appConst.supplierFormationTypesList,
                        controller: controller.formationType,
                        // validationType: 'required',
                        // parametersValidate: 'Required!',
                        onChanged: (value){
                          setState((){});
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'IBAN',
                  controller: controller.contractorIban,
                  textCapitalization: TextCapitalization.words,
                  inputFormatters: [ibanMaskFormatter],
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'Codice Fatturazione Elettronico',
                  controller: controller.contractorBillingCode,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'P.IVA',
                  controller: controller.contractorVat,
                  inputFormatters: [ivaMaskFormatter],
                  // validator: (value) {
                  //   if (value == null || value.isEmpty || !RegExp(r"^(IT)?[0-9]{11}$").hasMatch(value)) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'Codice Fiscale',
                  enabled : controller.formationType.text == 'Impresa individuale',
                  controller: controller.contractorFiscalCode,
                  inputFormatters: [codiceFiscaleMaskFormatter],
                  // validator: (value) {
                  //   if (controller.formationType.text != 'Impresa individuale') {
                  //     return null;
                  //   }
                  //   if (value == null || value.isEmpty || value.length < 16) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            AddressSearchBar(
              label: "Indirizzo sede legale",
              initialAddress: widget.contractor.legalAddressInfo?.fullAddress ?? widget.contractor.legalAddress ?? "",
              // validator: (value) {
              //   if (value == null || value.isEmpty) {
              //     return 'Obbligatorio';
              //   }
              //   return null;
              // },
              onPlaceSelected: (selectedPlace){
                log('Selected place: \n$selectedPlace');
                BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace['place']);
                if (selectedAddress.isValidAddress()){
                  controller.contractorLegalAddress = selectedAddress;
                } else {
                  controller.contractorLegalAddress = BaseAddressInfo.empty();
                }
              }
            ),
            SizedBox(
              height: 15,
            ),
            Divider(
              color: Color(0xffd7d7d7d7),
              thickness: 1,
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              children: [
                Expanded(
                  child: ListView(
                    shrinkWrap: true,
                    // crossAxisAlignment: CrossAxisAlignment.start,
                    // mainAxisSize: MainAxisSize.max,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          NarFormLabelWidget(
                            label: 'Documenti',
                            fontSize: 18,
                            fontWeight: 'bold',
                          ),
                          NarFilePickerWidget(
                            allowMultiple: true,
                            displayFormat: 'inline-button',
                            borderRadius: 7,
                            fontSize: 11,
                            fontWeight: '600',
                            text: 'Carica documenti',
                            borderSideColor: Theme.of(context).primaryColor,
                            hoverColor: Color.fromRGBO(133, 133, 133, 1),
                            allFiles: documents,
                            pageContext: context,
                            storageDirectory:
                                'suppliers/${widget.contractor.id}/',
                            progressMessage: controller.fileProgressMessage,
                            onUploadCompleted: onVendorFileUploadasync,
                          )
                        ],
                      ),
                      SizedBox(height: 30),
                      Container(
                        width: 350,
                        child: ListView.builder(
                            shrinkWrap: true,
                            itemCount: 1,
                            itemBuilder: (context, index) {
                              return Column(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    NarFilePickerWidget(
                                      allowMultiple: false,
                                      filesToDisplayInList: 0,
                                      removeButton: true,
                                      isDownloadable: true,
                                      removeButtonText: 'Elimina',
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-widget',
                                      containerWidth: 110,
                                      containerHeight: 110,
                                      containerBorderRadius: 13,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica Progetto',
                                      borderSideColor:
                                          Theme.of(context).primaryColor,
                                      hoverColor:
                                          Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: documents,
                                      pageContext: context,
                                      storageDirectory:
                                          'suppliers/${widget.contractor.id}/',
                                      removeExistingOnChange: true,
                                      progressMessage: controller.fileProgressMessage,
                                      onUploadCompleted: onVendorFileUploadasync,
                                    )
                                  ]);
                            }),
                      )
                    ],
                  ),
                ),
              ],
            ),
            NarFormLabelWidget(
              label: controller.validationMessage != '' ? controller.validationMessage : '',
              fontSize: 12,
              textColor: Colors.red,
            ),
            SizedBox(
              height: 50,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              mainAxisSize: MainAxisSize.max,
              children: [
                NarFormLabelWidget(
                  label: controller.progressMessage != '' ? controller.progressMessage : '',
                  fontSize: 12,
                )
              ],
            ),
            SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                BaseNewarcButton(
                  buttonText: "Salva",
                  onPressed: () async {
                    if (_formKey.currentState!.validate()) {
                      setState(() {
                        controller.progressMessage = 'Salvataggio in corso...';
                      });
                      bool response = await updateData(context);
                      if (response == true) {
                        setState(() {
                          controller.progressMessage = '';
                        });
                        await showAlertDialog(context, "Salvataggio",
                            "Informazioni ditta salvate con successo");
                      } else {
                        setState(() {
                          controller.progressMessage =
                              'Si è verificato un errore. Contatta l\'assistenza.';
                        });
                      }
                    }
                  }
                )
              ],
            )
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   mainAxisSize: MainAxisSize.max,
            //   children: [
            //     BaseNewarcButton(
            //         notAccent: true,
            //         buttonText: "Elimina ditta",
            //         onPressed: () async {
            //           setState(() {
            //             progressMessage = 'Eliminazione in corso';
            //           });
            //           bool unlock = await showAlertDialog(
            //               context,
            //               "Conferma eliminazione",
            //               "Sei sicuro di voler eliminare questa ditta?",
            //               addCancel: true);
            //           if (unlock) {
            //             bool response = await updateData(context, isArchive: true);
            //             if (response == true) {
            //               widget.fetchSupplier!();
            //             } else {
            //               setState(() {
            //                 progressMessage =
            //                     'Si è verificato un errore. Contatta l\'assistenza.';
            //               });
            //             }
            //           }
            //         }),
            //     BaseNewarcButton(
            //         buttonText: "Salva",
            //         onPressed: () async {
            //           setState(() {
            //             progressMessage = 'Salvataggio in corso...';
            //           });
            //           bool response = await updateData(context, isArchive: false);
            //           if (response == true) {
            //             setState(() {
            //               progressMessage = '';
            //               //widget.getProfilePicture!();
            //             });
            //             await showAlertDialog(context, "Salvataggio",
            //                 "Informazioni ditta salvate con successo");

            //             widget.updateSelectedSupplier!();
            //           } else {
            //             setState(() {
            //               progressMessage =
            //                   'Si è verificato un errore. Contatta l\'assistenza.';
            //             });
            //           }
            //         })
            //   ],
            // )
          ],
        ),
      ),
    );
  }
}