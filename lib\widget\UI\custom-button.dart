import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

Widget ui_button(BuildContext context, String buttonText, callback,
    String buttonTheme, String buttonSize, dynamic buttonIcon) {
  EdgeInsets buttonPadding;
  double buttonFontSize;

  if (buttonSize == 'lg') {
    buttonPadding = EdgeInsets.symmetric(vertical: 25, horizontal: 25);
    buttonFontSize = 28;
  } else if (buttonSize == 'md') {
    buttonPadding = EdgeInsets.symmetric(vertical: 20, horizontal: 20);
    buttonFontSize = 20;
  } else {
    buttonPadding = EdgeInsets.symmetric(vertical: 10, horizontal: 10);
    buttonFontSize = 10;
  }

  Color buttonThemeColor;
  Color buttonTextColor;

  if (buttonTheme == 'primary') {
    buttonThemeColor = Theme.of(context).primaryColor;
    buttonTextColor = Colors.white;
  } else if (buttonTheme == 'secondary') {
    buttonThemeColor = Color.fromARGB(255, 77, 78, 77);
    buttonTextColor = Color.fromARGB(255, 0, 0, 0);
  } else if (buttonTheme == 'danger') {
    buttonThemeColor = Color.fromARGB(255, 238, 0, 0);
    buttonTextColor = Colors.white;
  } else {
    buttonThemeColor = Theme.of(context).primaryColor;
    buttonTextColor = Colors.white;
  }

  Widget buttonIcons;
  if (buttonIcon != null) {
    buttonIcons = Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          buttonText,
          style: TextStyle(
              color: buttonTextColor,
              fontWeight: FontWeight.w900,
              fontSize: buttonFontSize),
        ),
        SizedBox(width: 15),
        Icon(
          buttonIcon,
          color: Colors.white,
        )
      ],
    );
  } else {
    buttonIcons = Text(
      buttonText,
      style: TextStyle(
          color: buttonTextColor,
          fontWeight: FontWeight.w900,
          fontSize: buttonFontSize),
    );
  }

  return TextButton(
    style: TextButton.styleFrom(
        backgroundColor: buttonThemeColor, padding: buttonPadding),
    onPressed: callback,
    child: Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [buttonIcons],
    ),
  );
}
