import 'package:clipboard/clipboard.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:newarc_platform/classes/property.dart';
import 'package:newarc_platform/pages/agency/newarc_property_manage.dart';
import 'package:newarc_platform/widget/work/project/add_property_form.dart';
import 'package:newarc_platform/widget/UI/button.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
// import 'package:newarc_platform/utils/firestore.dart';
import 'package:newarc_platform/widget/agency/assegna_immobile_popup.dart';
import 'package:newarc_platform/widget/agency/custom_filter_dropdown.dart';
import 'package:newarc_platform/widget/agency/immobile_popup.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import '../../utils/various.dart';
import 'package:newarc_platform/widget/custom_drawer.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/alert.dart';

import 'package:newarc_platform/utils/storage.dart';

class NewarcPropertiesView extends StatefulWidget {
  // final Agency agency;
  // final AgencyUser agencyUser;
  // final responsive;

  // const NewarcPropertiesView(
  //     {Key? key,
  //     required this.agency,
  //     required this.agencyUser,
  //     required this.responsive})
  //     : super(key: key);

  static const String route = '/property/index';

  @override
  State<NewarcPropertiesView> createState() => _NewarcPropertiesViewState();
}

class _NewarcPropertiesViewState extends State<NewarcPropertiesView> {
  bool loadingProperties = true;
  List<Property> properties = [];

  String query = "";

  var _selectedStatus;

  @override
  void initState() {
    initialFetchProperties();
    super.initState();
  }

  void reloadAfterPop() {
    cacheFirestore.clear();
    initialFetchProperties();
  }

  getColor(String status) {
    switch (status) {
      case 'Da contattare':
        return Color(0xff5FBCEC);
      case 'Contattato':
        return Color(0xffFFC633);
      case 'Non interessato':
        return Color(0xffFF5E53);
      case 'Acquisito':
        return Color(0xff489B79);
    }
  }

  List<DropdownMenuItem> buildDropdownTestItems(List _testList) {
    List<DropdownMenuItem> items = [];
    for (var i in _testList) {
      items.add(
        DropdownMenuItem(
          value: i,
          child: Container(
            decoration: BoxDecoration(
              color: getColor(i['keyword']),
              borderRadius: BorderRadius.circular(8),
            ),
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    i['keyword'],
                    style: TextStyle(
                        color: Colors.white, fontWeight: FontWeight.w600),
                  ),
                ]),
          ),
        ),
      );
    }
    return items;
  }

  late List<DocumentSnapshot> documentList;
  late int totalRecords = 0;
  String currentlyShowing = '';
  final recordsPerPage = 12;
  late int pageCounter;
  int totalPages = 0;
  bool disablePreviousButton = true;
  bool disableNextButton = false;
  List<dynamic> cacheFirestore = [];

  Widget dataTablePagination() {
    return Padding(
        padding: EdgeInsets.symmetric(vertical: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
                "Page ${pageCounter.toString()} of ${totalPages} Pages, Showing ${currentlyShowing} of Total  ${totalRecords.toString()}"),
            SizedBox(width: 5),
            TextButton(
              child: Icon(Icons.arrow_back_ios,
                  size: 20,
                  color: disablePreviousButton ? Colors.grey : Colors.black),
              onPressed: () {
                if (disablePreviousButton == true) return;
                fetchPrevProperties();
              },
            ),
            // SizedBox(width: 5),
            TextButton(
              child: Icon(Icons.arrow_forward_ios,
                  size: 20,
                  color: disableNextButton ? Colors.grey : Colors.black),
              onPressed: () {
                if (disableNextButton == true) return;
                fetchNextProperties();
              },
            ),
            TextButton(
              child: Icon(Icons.refresh,
                  size: 20,
                  color: disableNextButton ? Colors.grey : Colors.black),
              onPressed: () {
                // if (disableNextButton == true) return;
                // fetchNextContacts(widget.agency);
                cacheFirestore.clear();
                initialFetchProperties();
              },
            )
          ],
        ));
  }

  /*<DataRow> loadingRow() {
    List<DataCell> list = [];

    list.add(DataCell(Text('')));
    list.add(DataCell(Text('')));
    list.add(DataCell(Text('')));
    list.add(DataCell(Text('')));
    list.add(DataCell(Text('')));
    list.add(DataCell(Text('')));
    // list.add(DataCell(Text('')));

    return [DataRow(cells: list)];
  }*/

  Future<void> initialFetchProperties() async {
    pageCounter = 1;

    loadingProperties = true;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshotCounter;

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;

      // print(appConfig.COLLECT_NEWARC_HOME);

      collectionSnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARC_HOME)
          .limit(recordsPerPage)
          .get();

      collectionSnapshotCounter = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARC_HOME)
          .get();

      totalRecords = collectionSnapshotCounter.docs.length;

      totalPages = (totalRecords / recordsPerPage).ceil();

      if (totalRecords > recordsPerPage) {
        currentlyShowing = '1-' + recordsPerPage.toString();
      }

      documentList = collectionSnapshot.docs;
      // print(collectionSnapshot );
      generateProperties(collectionSnapshot);

      setState(() {});
    } catch (e) {
      setState(() {
        loadingProperties = false;
      });
      print(e.toString());
    }
  }

  fetchNextProperties() async {
    setState(() {
      loadingProperties = true;
    });

    pageCounter++;

    try {
      // updateIndicator(true);
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      int indexOfSnapshot = isRecordExists(pageCounter);

      if (indexOfSnapshot > -1) {
        collectionSnapshot = cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshot = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_NEWARC_HOME)
            .startAfterDocument(documentList[documentList.length - 1])
            .limit(recordsPerPage)
            .get();
      }

      // List<DocumentSnapshot> newDocumentList = collectionSnapshot.docs;
      documentList = collectionSnapshot.docs;

      // documentList.addAll(newDocumentList);

      generateProperties(collectionSnapshot);
    } catch (e) {
      setState(() {
        loadingProperties = false;
      });
      print(e.toString());
      // movieController.sink.addError(e);
    }
  }

  fetchPrevProperties() async {
    setState(() {
      loadingProperties = true;
    });

    pageCounter--;

    //print('Prev Page');

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      int indexOfSnapshot = isRecordExists(pageCounter);
      if (indexOfSnapshot > -1) {
        collectionSnapshot = cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshot = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_NEWARC_HOME)
            .endBeforeDocument(documentList[documentList.length - 1])
            .limit(recordsPerPage)
            .get();
      }

      documentList = collectionSnapshot.docs;
      generateProperties(collectionSnapshot);
    } catch (e) {
      setState(() {
        loadingProperties = false;
      });
      print(e.toString());
      // movieController.sink.addError(e);
    }
  }

  int isRecordExists(pageCounter) {
    int isRecordExists =
        cacheFirestore.indexWhere((record) => record['key'] == pageCounter);
    return isRecordExists;
  }

  generateProperties(collectionSnapshot) {
    // If a record already doesn't exists then store
    if (isRecordExists(pageCounter) < 0) {
      cacheFirestore.add({'key': pageCounter, 'snapshot': collectionSnapshot});
    }

    if (pageCounter == 1) {
      disablePreviousButton = true;
    } else {
      disablePreviousButton = false;
    }

    if (pageCounter == totalPages) {
      disableNextButton = true;
    } else {
      disableNextButton = false;
    }

    List<Property> _properties = [];
    for (var element in collectionSnapshot.docs) {
      try {
        var _tmp = Property.fromDocument(element);

        _properties.add(_tmp);
      } catch (e) {
        print("error in document ${element.id}");
        print(e);
      }
    }
    // print(_properties.length);

    // property
    //     .sort((a, b) => b.insertionTimestamp!.compareTo(a.insertionTimestamp!));

    int lastRecordNumber = pageCounter * recordsPerPage;
    if (_properties.length == recordsPerPage) {
      currentlyShowing = (lastRecordNumber - (recordsPerPage - 1)).toString() +
          '-' +
          lastRecordNumber.toString();
    } else if (_properties.length > 0 && _properties.length < recordsPerPage) {
      int prevLastRecordNumber = (pageCounter - 1) * recordsPerPage;

      currentlyShowing = (lastRecordNumber - (recordsPerPage - 1)).toString() +
          '-' +
          (prevLastRecordNumber + _properties.length).toString();
    }

    setState(() {
      properties = _properties;
      loadingProperties = false;
    });
  }

  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      key: scaffoldKey,
      drawer: CustomDrawer(),
      body: LayoutBuilder(
          builder: (BuildContext context, BoxConstraints constraints) {
        return SingleChildScrollView(
          // scrollDirection: Axis.vertical,

          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  NarFormLabelWidget(
                    label: 'Progetti in corso',
                    fontSize: 22,
                    fontWeight: 'bold',
                    textColor: Colors.black,
                  ),
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () async {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => AddPropertyForm(
                                  initialFetchProperties:
                                      initialFetchProperties)),
                        );
                      },
                      child: Container(
                        height: 40,
                        width: 180,
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "Aggiungi annuncio",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Icon(
                                Icons.add,
                                color: Colors.white,
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),

              /*NarLinkWidget(
                text: 'Add Newarc Home',
                fontWeight: '800',
                onClick: () {
                  Navigator.of(context).pushNamed(AddPropertyForm.route);
                },
              ),*/
              dataTablePagination(),
              Container(
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                        width: 1, color: Color.fromRGBO(219, 219, 219, 1))),
                height: constraints.maxHeight / 1.2,
                // width: 300,
                child: loadingProperties
                    ? Center(child: CircularProgressIndicator())
                    : DataTable2(
                        dataRowHeight: loadingProperties ? 300 : 50,
                        minWidth: constraints.maxWidth,
                        columnSpacing: 20,
                        columns: getColumns(),
                        empty: NarFormLabelWidget(
                          label: 'Nessun record trovato!',
                          fontWeight: '800',
                          fontSize: 15,
                          textColor: Colors.black,
                        ),
                        rows: List.generate(
                            properties.where(filterFunction).length,
                            (int index) {
                          return DataRow(
                            cells: getDataRow(
                              properties.where(filterFunction).elementAt(index),
                            ),
                          );
                        }),
                      ),
              ),
              dataTablePagination(),
            ],
          ),
        );
      }),
    );
  }

  bool filterFunction(Property property) {
    bool _show = true;

    // if (!property.contactFullName!.toLowerCase().contains(query) &&
    //     !(contact.address! + " " + contact.streetNumber + "," + contact.city)
    //         .toLowerCase()
    //         .contains(query) &&
    //     !contact.contactEmail!.toLowerCase().contains(query)) {
    //   _show = false;
    // }

    // if (_show && _selectedStatus['keyword'] != "Filtra per stato") {
    //   if (contact.contactStage! != _selectedStatus['keyword']) {
    //     _show = false;
    //   }
    // }

    return _show;
  }

  Future<bool> updateHouseActiveStatus(Property property) async {
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    return await _db
        .collection(appConfig.COLLECT_NEWARC_HOME)
        .doc(property.firebaseId)
        .update(property.toMap())
        .then((value) {
      return true;
    }).onError((error, stackTrace) {
      print({error, stackTrace});
      return false;
    });
  }

  Color? activeColor;

  List<DataCell> getDataRow(Property property) {
    Color colorBlack = Color.fromRGBO(0, 0, 0, 1);
    Color colorGrey = Color.fromRGBO(131, 131, 131, 1);
    activeColor = property.isActive == true ? colorBlack : colorGrey;

    List<DataCell> list = [];
    var rooms = '';
    switch (int.tryParse(property.bedrooms.toString())) {
      case 1:
        rooms = 'Monolocale';
        break;
      case 2:
        rooms = 'Bilocale';
        break;
      case 3:
        rooms = 'Trilocale';
        break;
      case 4:
        rooms = 'Quadrilocale';
        break;
      default:
        rooms = 'Plurilocale';
        break;
    }

    list.add(DataCell(NarFormLabelWidget(
        label: rooms + ' in ' + property.propertyName!,
        fontWeight: '800',
        fontSize: 14)));

    list.add(DataCell(NarFormLabelWidget(
        label: property.zone, fontWeight: '800', fontSize: 14)));

    list.add(DataCell(NarFormLabelWidget(
        label: property.type, fontWeight: '800', fontSize: 14)));

    list.add(DataCell(NarFormLabelWidget(
        label: property.city, fontWeight: '800', fontSize: 14)));

    list.add(DataCell(
      Switch(
        // This bool value toggles the switch.
        value: property.isActive!,
        activeColor: Theme.of(context).primaryColor,
        onChanged: (bool value) async {
          // This is called when the user toggles the switch.

          property.isActive = value;
          setState(() {
            property.isActive = value;
            activeColor = value == true ? colorBlack : colorGrey;
          });

          bool status = await updateHouseActiveStatus(property);

          if (status == false) {
            NarAlertDialog(context, 'Error',
                'Error while updating the Agency status! Try agian later!', []);
            setState(() {
              property.isActive = !value;
              activeColor = value == true ? colorGrey : colorBlack;
            });
          }
        },
      ),
    ));

    list.add(DataCell(Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        NarLinkWidget(
          text: 'Elimina',
          textColor: Colors.black,
          fontWeight: '800',
          fontSize: 15,
          onClick: () async {
            loadingProperties = true;

            List<Widget> actionArray = [];

            actionArray.add(
              GestureDetector(
                child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 14, vertical: 10),
                    decoration: BoxDecoration(
                      color: Color(0xffB32F2B),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: NarFormLabelWidget(
                      label: 'Annulla',
                      fontWeight: '800',
                      textColor: Colors.white,
                    )),
                onTap: () {
                  Navigator.of(context).pop(false);
                },
              ),
            );

            actionArray.add(
              GestureDetector(
                child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 14, vertical: 10),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: NarFormLabelWidget(
                        label: 'Procedi',
                        fontWeight: '800',
                        textColor: Colors.white)),
                onTap: () async {
                  loadingProperties = true;
                  Navigator.of(context).pop(false);
                  property.deleteProperty().then((value) async {
                    await deleteDirectory("newarcHomes/"+property.firebaseId!);

                    setState(() {
                      loadingProperties = false;

                      cacheFirestore.clear();
                      initialFetchProperties();
                    });
                  });
                },
              ),
            );

            NarAlertDialog(context, 'Eliminazione annuncio',
                'Sei sicuro di voler cancellare l\'annuncio?', actionArray);
          },
        ),
        SizedBox(width: 8),
        NarLinkWidget(
          text: 'Modifica',
          textColor: Colors.black,
          fontWeight: '800',
          fontSize: 15,
          onClick: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => NewarcPropertyManage(
                        property: property,
                      )),
            );
            // NarAlertDialog(context, 'Caricamento in corso', 'In arrivo...', []);
          },
        )
      ],
    )));

    return list;
  }

  List<DataColumn> getColumns() {
    List<DataColumn> list = [];

    list.add(DataColumn2(
        size: ColumnSize.L,
        fixedWidth: 250,
        label: NarFormLabelWidget(
          label: 'Titolo',
          fontSize: 13,
          textColor: Color.fromRGBO(131, 131, 131, 1),
        )));

    list.add(DataColumn2(
        size: ColumnSize.M,
        // fixedWidth: 250,
        label: NarFormLabelWidget(
          label: 'Zona',
          fontSize: 13,
          textColor: Color.fromRGBO(131, 131, 131, 1),
        )));

    list.add(DataColumn2(
      size: ColumnSize.M,
      label: NarFormLabelWidget(
        label: 'Tipo',
        fontSize: 13,
        textColor: Color.fromRGBO(131, 131, 131, 1),
      ),
    ));

    list.add(DataColumn2(
      size: ColumnSize.M,
      // fixedWidth: 150,
      label: NarFormLabelWidget(
        label: 'Città',
        fontSize: 13,
        textColor: Color.fromRGBO(131, 131, 131, 1),
      ),
    ));

    list.add(DataColumn2(
        size: ColumnSize.L,
        label: NarFormLabelWidget(
          label: 'Attiva/Disattiva',
          fontSize: 13,
          textColor: Color.fromRGBO(131, 131, 131, 1),
        )));

    list.add(DataColumn2(
      size: ColumnSize.L,
      fixedWidth: 200,
      label: NarFormLabelWidget(
        label: 'Azione',
        fontSize: 13,
        textColor: Color.fromRGBO(131, 131, 131, 1),
      ),
    ));

    return list;
  }
}
