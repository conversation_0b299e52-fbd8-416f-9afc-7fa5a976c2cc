
import 'package:newarc_platform/classes/basePersonInfo.dart';

class AgencyPersone extends BasePersonInfo{
  String? firebaseId;
  String? agencyId;
  int? insertTimestamp;
  Map? profilePicturePath;
  List? profilePicture;


  Map<String, Object?> toMap()  {
    Map<String, Object?> map = super.toMap();
    map['insertTimestamp'] = this.insertTimestamp;
    map['agencyId'] = this.agencyId;
    map['profilePicturePath'] = this.profilePicturePath;
    return map;
  }



  AgencyPersone.empty() : super.empty() {
    this.firebaseId = '';
    this.insertTimestamp = null;
    this.agencyId = '';
    this.profilePicturePath = {};
    this.profilePicture = [];
  }

  AgencyPersone.fromDocument(Map<String, dynamic> data, String id) : super.fromMap(data){
    this.firebaseId = id;
    try {
      this.insertTimestamp = data['insertTimestamp'];
      this.agencyId = data['agencyId'];
      this.profilePicturePath = data['profilePicturePath'];
      this.profilePicture = [];
      if (data['profilePicturePath'] != null && data['profilePicturePath'] ["filename"] != null && data['profilePicturePath'] ["filename"] != "") {
        this.profilePicture?.add(data['profilePicturePath']["filename"]);
      }
    } catch (e, s) {
      print({ 'AgencyPersone Class Error ------->', e, s});
    }
  }
}