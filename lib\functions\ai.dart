import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:newarc_platform/classes/comparabile.dart';
import 'package:newarc_platform/functions/various.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

String endpoint = appConfig.AI_ENDPOINT;

Future<List<Comparabile>> predictSalePrice(
    List<Comparabile> comparabili) async {
  try {
    List<Map<String, dynamic>> listaComparabili = [];

    comparabili.forEach((element) {
      listaComparabili.add(element.toJsonPredict());
    });

    http.Response response = await http.post(
      Uri.parse('$endpoint/predict'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
      },
      body: jsonEncode(<String, List>{
        'comparabili': listaComparabili,
      }),
    );

    Map<String, dynamic> result = jsonDecode(response.body);
    if (result['status'] == 200) {
      List<double> predictedPrices =
          List<double>.from(jsonDecode(result['predicted_sale_price']));

      int i = 0;
      comparabili.forEach((element) {
        element.predictedPrice = predictedPrices.elementAt(i);
        element.offerPrice = [
          computeMinimumOfferPrice(element.predictedPrice!),
          computeMaximumOfferPrice(element.predictedPrice!)
        ];

        i++;
      });
    }

    return comparabili;
  } catch (e) {
    print("Error in AI call");
    print(e);
    return comparabili;
  }
}
