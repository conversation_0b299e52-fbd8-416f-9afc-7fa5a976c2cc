import 'package:flutter/material.dart';


class BarToggleButton extends StatefulWidget {
  final bool? startingState; // true for firstState, false for secondState
  final Function(bool)? onStateChanged;
  final String? trueStateText;
  final String? falseStateText;

  const BarToggleButton ({ 
    Key? key, 
    this.startingState, 
    this.onStateChanged,
    this.trueStateText,
    this.falseStateText,
    }): super(key: key);
  @override
  _BarToggleButtonState createState() => _BarToggleButtonState();
}

class _BarToggleButtonState extends State<BarToggleButton> {
  late bool _isSelected;
  @override
  void initState() {
    super.initState();
    setState(() {
      _isSelected = widget.startingState ?? true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return 
    Container(
      decoration: BoxDecoration(
        shape: BoxShape.rectangle,
        borderRadius: BorderRadius.circular(15),
        color: Color(0xffE5E5E5),
        border: Border.all(color: Color(0xffE5E5E5))
      ),
      child: Padding(
        padding: const EdgeInsets.all(4.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                  _isSelected = true;
                  widget.onStateChanged?.call(_isSelected);
                  });
                },
                child: 
                  MouseRegion(
                    cursor: !_isSelected? SystemMouseCursors.click: MouseCursor.defer,
                    child: Container(
                      height: double.infinity,
                      alignment: Alignment.center,
                      decoration: 
                        BoxDecoration(
                          shape: BoxShape.rectangle, 
                          // borderRadius: BorderRadius.only(topLeft: Radius.circular(10), bottomLeft: Radius.circular(10)),
                          borderRadius: BorderRadius.circular(10),
                          color: _isSelected ? Theme.of(context).unselectedWidgetColor: Color(0xffE5E5E5),
                        ),
                        child: 
                          Text(
                            widget.trueStateText ?? "TRUE",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontFamily: 'Raleway-700',
                              fontSize: 13,
                              color: _isSelected ? Theme.of(context).primaryColor: Theme.of(context).primaryColorLight,
                              )
                            ),
                    ),
                  )
              ),
            ),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                  _isSelected = false;
                  widget.onStateChanged?.call(_isSelected);
                  });
                },
                child: 
                  MouseRegion(
                    cursor: _isSelected? SystemMouseCursors.click: MouseCursor.defer,
                    child: Container(
                      height: double.infinity,
                      alignment: Alignment.center,
                      decoration: 
                        BoxDecoration(
                          shape: BoxShape.rectangle,
                          // borderRadius: BorderRadius.only(topRight: Radius.circular(10), bottomRight: Radius.circular(10)), 
                          borderRadius: BorderRadius.circular(10),
                          color: !_isSelected ? Theme.of(context).unselectedWidgetColor: Color(0xffE5E5E5),
                        ),
                      child: 
                        Text(
                            widget.falseStateText ?? "FALSE",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontFamily: 'Raleway-700',
                              fontSize: 13,
                              color: !_isSelected ? Theme.of(context).primaryColor: Theme.of(context).primaryColorLight,
                              )
                            ),
                    ),
                  )
              ),
            ),
          ],
        ),
      ),
    );
  }
}