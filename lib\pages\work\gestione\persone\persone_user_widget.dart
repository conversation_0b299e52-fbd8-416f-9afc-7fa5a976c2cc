import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/widget/UI/image-picker.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:path/path.dart' as p;
import 'package:firebase_storage/firebase_storage.dart';

class PersoneUserWidget extends StatefulWidget {
  final NewarcUser user;
  final Function? updateViewCallback;

  const PersoneUserWidget(
      {Key? key,
      required this.user,
      this.updateViewCallback})
      : super(key: key);

  @override
  State<PersoneUserWidget> createState() => _PersoneUserWidgetState();
}

class _PersoneUserWidgetState extends State<PersoneUserWidget> {
  NewarcUser? user;

  TextEditingController firstName = new TextEditingController();
  TextEditingController lastName = new TextEditingController();
  TextEditingController userEmail = new TextEditingController();
  TextEditingController userType = new TextEditingController();
  final TextEditingController userRole = new TextEditingController();

  final FirebaseFirestore _db = FirebaseFirestore.instance;
  final profilePicture = [];
  String? profilePictureFilename;
  String? validationMessage;

  String? progressMessage;
  bool savingData = false;
  String tmpUserRole = '';
  Map<String, dynamic> userMenuControls = {};
  Map<String, dynamic> userProjectTabControls = {};
  dynamic sortedEntries;
  dynamic sortedProjectTabEntries;
  bool isFilterPerAccountEnabled = true;

  double containerWidth = 0;
  @override
  void initState() {
    super.initState();

    getProfileImageUrl();
    setInitValues();

    tmpUserRole = appConst.userRolesDict[widget.user.role!]!;
    
    if( widget.user.menuAccess != null ) {
      userMenuControls = widget.user.menuAccess as Map<String, dynamic>; 
    } else if( widget.user.menuAccess == null && widget.user.role == '' ) {
      userMenuControls = appConst.workMenuControls; 
    } else if( widget.user.role != '' ) {
      userMenuControls = getDefaultMenuAccessByRole(widget.user.role);
    }

    if( widget.user.projectTabAccess != null ) {
      userProjectTabControls = widget.user.projectTabAccess as Map<String, dynamic>; 
    } else if( widget.user.projectTabAccess == null && widget.user.role == '' ) {
      userProjectTabControls = appConst.projectSectionAccess; 
    } else if( widget.user.role != '' ) {
      userProjectTabControls = getDefaultProjectTabByRole(widget.user.role);
    }

    sortedEntries = userMenuControls.entries.toList()
    ..sort((a, b) => a.key.compareTo(b.key)); 

    sortedProjectTabEntries = userProjectTabControls.entries.toList()
    ..sort((a, b) => a.key.compareTo(b.key)); 
    
  }

  @protected
  void didUpdateWidget(PersoneUserWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    setInitValues();
  }

  setInitValues() {
    String _userType = widget.user.type!;
    _userType = _userType.replaceAll('_', ' ').toTitleCase();

    String _userRole = appConst.userRolesDict[widget.user.role!]!;

    firstName.text = widget.user.firstName!;
    lastName.text = widget.user.lastName!;
    userEmail.text = widget.user.email!;
    isFilterPerAccountEnabled = widget.user.isFilterPerAccountEnabled!;
    userType.text = _userType;
    userRole.text = _userRole;

    
    


    profilePictureFilename = widget.user.profilePicture;
    profilePicture.clear();
  }

  getProfileImageUrl() async {
    if (savingData == true) return;

    setState(() {
      if (widget.user.profilePicture != '') {
        profilePictureFilename = widget.user.profilePicture;
      }
    });
  }

  Future<bool> updateData(BuildContext context,
      {bool isArchive = false}) async {
    setState(() {
      validationMessage = null;
    });

    var _userType = userType.text;
    _userType = _userType.replaceAll(' ', '_');
    _userType = _userType.toLowerCase();

    var _userRole = userRole.text;
    _userRole = appConst.userRolesDict.entries.firstWhere((entry) => entry.value == _userRole).key;

    widget.user.firstName = firstName.text;
    widget.user.lastName = lastName.text;
    widget.user.email = userEmail.text;
    widget.user.type = _userType;
    widget.user.role = _userRole;
    widget.user.menuAccess = userMenuControls;
    widget.user.isFilterPerAccountEnabled = isFilterPerAccountEnabled;
    widget.user.projectTabAccess = userProjectTabControls;
    
    if (isArchive) {
      widget.user.isArchived = true;
    }

    setState(() {
      savingData = true;
    });
    if (profilePicture.length > 0) {
      String _profilePictureFilename =
          'profile' + p.extension(profilePicture[0].name);
      setState(() {
        profilePictureFilename = '';
      });

      await uploadProfilePicture('users/', widget.user.id!,
              _profilePictureFilename, profilePicture[0])
          .then((uploadTask) async {
        try {
          uploadTask!.snapshotEvents.listen((TaskSnapshot taskSnapshot) async {
            switch (taskSnapshot.state) {
              case TaskState.success:
                progressMessage = '';
                widget.user.profilePicture = _profilePictureFilename;
                await _db
                    .collection(appConfig.COLLECT_USERS)
                    .doc(widget.user.id)
                    .update(widget.user.toMap());
                savingData = false;
                getProfileImageUrl();
                break;
            }
          });
        } catch (e) {
        }
      });

      return true;
    } else {
      setState(() {
        profilePictureFilename = '';
      });

      try {
        await _db
            .collection(appConfig.COLLECT_USERS)
            .doc(widget.user.id)
            .update(widget.user.toMap());

        setState(() {
          progressMessage = '';
          savingData = false;
        });

        getProfileImageUrl();
      } catch (e) {
        progressMessage = 'Qualcosa è andato storto!';
      }
      return true;
    }
  }

  Future<bool> updateUserActiveStatus(NewarcUser user) async {
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    return await _db
        .collection(appConfig.COLLECT_USERS)
        .doc(user.id)
        .update(user.toMap())
        .then((value) {
      return true;
    }).onError((error, stackTrace) {
      return false;
    });
  }

  @override
  Widget build(BuildContext context) {
    containerWidth = MediaQuery.of(context).size.width * .75;

    return Container(
      height: double.infinity,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: ListView(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    NarFormLabelWidget(
                      label:
                          widget.user.firstName! + ' ' + widget.user.lastName!,
                      fontSize: 20,
                      fontWeight: 'bold',
                    ),
                    Row(
                      children: [
                        NarFormLabelWidget(
                          label: 'Attiva/Disattiva',
                          fontSize: 15,
                          fontWeight: '500',
                        ),
                        Switch(
                          // This bool value toggles the switch.
                          value: widget.user.isActive!,
                          activeColor: Theme.of(context).primaryColor,
                          onChanged: (bool value) async {
                            // This is called when the user toggles the switch.

                            // widget.user.isActive = value;
                            setState(() {
                              widget.user.isActive = value;
                            });

                            // bool status = false; //
                            await updateUserActiveStatus(widget.user);

                            // if (status == false) {
                            //   await showAlertDialog(context, "Errore",
                            //       "Si è verificato un errore nell\'aggiornare lo stato.");

                            // }
                          },
                        ),
                      ],
                    )
                  ],
                ),
                SizedBox(height: 35),

                NarFormLabelWidget(
                  label: 'Dati professionista',
                  fontSize: 16,
                  fontWeight: 'bold',
                ),
                SizedBox(height: 15),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    CustomTextFormField(
                      label: 'Nome',
                      hintText: '',
                      controller: firstName,
                    ),
                    SizedBox(
                      width: 15,
                    ),
                    CustomTextFormField(
                      label: 'Cognome',
                      hintText: '',
                      controller: lastName,
                    ),
                  ],
                ),
                SizedBox(
                  height: 15,
                ),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label: "Professione",
                            fontSize: 13,
                            fontWeight: '600',
                            textColor: const Color(0xff696969),
                          ),
                          SizedBox(height: 4),
                          NarSelectBoxWidget(
                            options: appConst.userRolesDict.values.toList(),
                            controller: userRole,
                            validationType: 'required',
                            parametersValidate: '',
                            onChanged: ( value ){
                              String selectedRole = appConst.userRolesDict.entries
                              .firstWhere((entry) => entry.value == value, orElse: () => const MapEntry('', ''))
                              .key;
                              setState(() {
                                userMenuControls = getDefaultMenuAccessByRole(selectedRole);  

                                sortedEntries = userMenuControls.entries.toList()
                                ..sort((a, b) => a.key.compareTo(b.key)); 

                                userProjectTabControls = getDefaultProjectTabByRole(selectedRole);  

                                sortedProjectTabEntries = userProjectTabControls.entries.toList()
                                ..sort((a, b) => a.key.compareTo(b.key)); 
                                
                              });
                              
                            },
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      width: 15,
                    ),
                    CustomTextFormField(
                      label: 'E-mail',
                      hintText: '',
                      controller: userEmail,
                    ),
                    SizedBox(
                      height: 0,
                    )
                  ],
                ),

                SizedBox(
                  height: 15,
                ),

                Container(
                  width: containerWidth * .80,
                  height: 1,
                  decoration: BoxDecoration(
                    color: Color(0xFFDCDCDC),
                  ),
                  child: SizedBox(height: 0),
                ),
                SizedBox(
                  height: 15,
                ),

                // Logo
                NarFormLabelWidget(
                  label: 'Immagine profilo',
                  fontSize: 16,
                  fontWeight: 'bold',
                ),
                SizedBox(
                  height: 15,
                ),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    NarImagePickerWidget(
                      allowMultiple: false,
                      imagesToDisplayInList: 0,
                      removeButton: false,
                      removeButtonText: 'rimuovi',
                      uploadButtonPosition: 'back',
                      showMoreButtonText: '+ espandi',
                      removeButtonPosition: 'bottom',
                      displayFormat: 'row',
                      imageDimension: 100,
                      imageBorderRadius: 50,
                      borderRadius: 7,
                      fontSize: 14,
                      fontWeight: '600',
                      text: 'Carica immagine profilo',
                      borderSideColor: Theme.of(context).primaryColor,
                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                      images: profilePicture,
                      pageContext: context,
                      storageDirectory: 'users/',
                      preloadedImages: profilePictureFilename == null ||
                              profilePictureFilename == ''
                          ? []
                          : [profilePictureFilename],
                      firebaseId: widget.user.id,
                      removeExistingOnChange: true,
                    )
                  ],
                ),

                Container(
                  width: containerWidth * .80,
                  height: 1,
                  decoration: BoxDecoration(
                    color: Color(0xFFDCDCDC),
                  ),
                  child: SizedBox(height: 0),
                ),
                SizedBox(
                  height: 15,
                ),

                Row(
                  children: [
                    Container(
                      width: 900,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          NarFormLabelWidget(
                            label: 'Assegna risorse',
                            fontSize: 16,
                            fontWeight: 'bold',
                          ),
                          Row(
                            children: [
                              Transform.scale(
                                scale: 0.7,
                                child: SizedBox(
                                  width: 50,
                                  child: Switch(
                                    activeColor: Theme.of(context).primaryColor,
                                    value: isFilterPerAccountEnabled, 
                                    onChanged: ( value ) {
                                      setState((){
                                        isFilterPerAccountEnabled = value;
                                      });
                                    }
                                  ),
                                ),
                              ),
                              NarFormLabelWidget(
                                label: 'filtra per account',
                                fontSize: 13,
                                letterSpacing: 0.1,
                                textColor: Colors.black,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 15,
                ),

                Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          width: 1,
                          color: Color(0xffCACACA)
                        ),
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                        
                      ),
                      width: 900,
                      child: Table(
                    
                        border: TableBorder.symmetric(
                          inside: BorderSide(width: 1, color: Color(0xffCACACA)),
                        ),
                          children: [
                            TableRow(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric( vertical: 10, horizontal: 10),
                                  child: NarFormLabelWidget(
                                    label: 'Sezioni',
                                    fontSize: 14,
                                    letterSpacing: 0.1,
                                    textColor: Colors.black,
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.symmetric( vertical: 10, horizontal: 10),
                                  child: NarFormLabelWidget(
                                    label: 'Sotto-sezioni',
                                    fontSize: 14,
                                    letterSpacing: 0.1,
                                    textColor: Colors.black,
                                  ),
                                )
                              ],
                            ),
                            ...sortedEntries.map((entry) {
                              final section = entry.key;
                              // final subSections = entry.value.keys.join(', '); // Adjust as needed
                      
                              final subSections = entry.value['children'] as Map<String, dynamic>;
                      
                              final sortedSubSections = subSections.entries.toList()
                              ..sort((a, b) => a.key.compareTo(b.key));
                      
                              return TableRow(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.symmetric( vertical: 10, horizontal: 0),
                                    child: Row(
                                      children: [
                                        Transform.scale(
                                          scale: 0.7,
                                          child: SizedBox(
                                            width: 50,
                                            child: Switch(
                                              activeColor: Theme.of(context).primaryColor,
                                              value: userMenuControls[section]!['status'] == 'show', 
                                              onChanged: ( value ) {
                                                setState((){
                                                  userMenuControls[section]!['status'] = value ? 'show' : 'hide';
                                                });
                                              }
                                            ),
                                          ),
                                        ),
                                        NarFormLabelWidget(
                                          label: appConst.workMenuControlLabel[section],
                                          fontSize: 14,
                                          letterSpacing: 0.1,
                                          textColor: Colors.black,
                                        ),
                                      ],
                                    ),
                                  ),
                      
                                  Padding(
                                    padding: const EdgeInsets.symmetric( vertical: 10, horizontal: 0),
                                    child: Column(
                                      children: [
                                        ...sortedSubSections.map((subentry) {
                                          String submenu_key = subentry.key;
                                          bool isVisible = subentry.value == 'show';
                                                          
                                          return Row(
                                            children: [
                                              Transform.scale(
                                                scale: 0.7,
                                                child: Switch(
                                                  activeColor: Theme.of(context).primaryColor,
                                                  value: isVisible,
                                                  onChanged: (value) {
                                                    setState(() {
                                                      (userMenuControls[section]!['children'] as Map<String, dynamic>)[submenu_key] =
                                                          value ? 'show' : 'hide';
                                                    });
                                                  },
                                                ),
                                              ),
                                              NarFormLabelWidget(
                                                label: appConst.workMenuControlLabel[submenu_key],
                                                fontSize: 14,
                                                letterSpacing: 0.1,
                                                textColor: Colors.black,
                                              ),
                                            ],
                                          );
                                        }).toList(),
                                      ],
                                    ),
                                  )
                                  
                                  
                                ],
                              );
                            }).toList(),
                      
                          ],
                      
                      ),
                    ),
                  ],
                ),

                SizedBox(
                  height: 15,
                ),

                Container(
                  width: containerWidth * .80,
                  height: 1,
                  decoration: BoxDecoration(
                    color: Color(0xFFDCDCDC),
                  ),
                  child: SizedBox(height: 0),
                ),
                SizedBox(
                  height: 15,
                ),

                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    NarFormLabelWidget(
                      label: 'Project Tabs',
                      fontSize: 16,
                      fontWeight: 'bold',
                    ),
                    
                  ],
                ),
                SizedBox(
                  height: 15,
                ),

                Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          width: 1,
                          color: Color(0xffCACACA)
                        ),
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                        
                      ),
                      width: 900,
                      child: Table(
                    
                        border: TableBorder.symmetric(
                          inside: BorderSide(width: 1, color: Color(0xffCACACA)),
                        ),
                          children: [
                            TableRow(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric( vertical: 10, horizontal: 10),
                                  child: NarFormLabelWidget(
                                    label: 'Sezioni',
                                    fontSize: 14,
                                    letterSpacing: 0.1,
                                    textColor: Colors.black,
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.symmetric( vertical: 10, horizontal: 10),
                                  child: NarFormLabelWidget(
                                    label: 'Sotto-sezioni',
                                    fontSize: 14,
                                    letterSpacing: 0.1,
                                    textColor: Colors.black,
                                  ),
                                )
                              ],
                            ),
                            ...sortedProjectTabEntries.map((entry) {
                              final section = entry.key;
                              // final subSections = entry.value.keys.join(', '); // Adjust as needed
                      
                              final subSections = entry.value['children'] as Map<String, dynamic>;
                      
                              final sortedSubSections = subSections.entries.toList()
                              ..sort((a, b) => a.key.compareTo(b.key));
                      
                              return TableRow(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.symmetric( vertical: 10, horizontal: 0),
                                    child: Row(
                                      children: [
                                        Transform.scale(
                                          scale: 0.7,
                                          child: SizedBox(
                                            width: 50,
                                            child: Switch(
                                              activeColor: Theme.of(context).primaryColor,
                                              value: userProjectTabControls[section]!['status'] == 'show', 
                                              onChanged: ( value ) {
                                                setState((){
                                                  userProjectTabControls[section]!['status'] = value ? 'show' : 'hide';
                                                });
                                              }
                                            ),
                                          ),
                                        ),
                                        NarFormLabelWidget(
                                          label: section.toString().toCapitalized(),
                                          fontSize: 14,
                                          letterSpacing: 0.1,
                                          textColor: Colors.black,
                                        ),
                                      ],
                                    ),
                                  ),
                      
                                  Padding(
                                    padding: const EdgeInsets.symmetric( vertical: 10, horizontal: 0),
                                    child: Column(
                                      children: [
                                        ...sortedSubSections.map((subentry) {
                                          String submenu_key = subentry.key;
                                          bool isVisible = subentry.value == 'show';
                                                          
                                          return Row(
                                            children: [
                                              Transform.scale(
                                                scale: 0.7,
                                                child: Switch(
                                                  activeColor: Theme.of(context).primaryColor,
                                                  value: isVisible,
                                                  onChanged: (value) {
                                                    setState(() {
                                                      (userProjectTabControls[section]!['children'] as Map<String, dynamic>)[submenu_key] =
                                                          value ? 'show' : 'hide';
                                                    });
                                                  },
                                                ),
                                              ),
                                              NarFormLabelWidget(
                                                label: submenu_key,
                                                fontSize: 14,
                                                letterSpacing: 0.1,
                                                textColor: Colors.black,
                                              ),
                                            ],
                                          );
                                        }).toList(),
                                      ],
                                    ),
                                  )
                                  
                                  
                                ],
                              );
                            }).toList(),
                      
                          ],
                      
                      ),
                    ),
                  ],
                ),

                NarFormLabelWidget(
                  label: validationMessage != '' ? validationMessage : '',
                  fontSize: 12,
                  textColor: Colors.red,
                ),

                SizedBox(
                  height: 10,
                ),

                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    NarFormLabelWidget(
                      label: progressMessage != '' ? progressMessage : '',
                      fontSize: 12,
                    )
                  ],
                ),
                SizedBox(height: 15),
              ],
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            mainAxisSize: MainAxisSize.max,
            children: [
              BaseNewarcButton(
                  notAccent: true,
                  buttonText: "Elimina professionista",
                  onPressed: () async {
                    setState(() {
                      progressMessage = 'Eliminazione in corso';
                    });
                    bool unlock = await showAlertDialog(
                        context,
                        "Conferma eliminazione",
                        "Sei sicuro di voler eliminare questo utente?",
                        addCancel: true);
                    if (unlock) {
                      bool response =
                          await updateData(context, isArchive: true);
                      if (response == true) {
                        widget.updateViewCallback!('team');
                      } else {
                        setState(() {
                          progressMessage =
                              'Si è verificato un errore. Contatta l\'assistenza.';
                        });
                      }
                    } else {
                      setState(() {
                      progressMessage = '';
                    });
                    }
                  }),
              Row(
                children: [
                  NarFormLabelWidget(
                    label: progressMessage != '' ? progressMessage : '',
                    fontSize: 12,
                  ),
                  SizedBox(width: 10,),
                  BaseNewarcButton(
                    buttonText: "Salva",
                    onPressed: () async {
                      setState(() {
                        progressMessage = 'Salvataggio in corso...';
                      });
                      bool response = await updateData(context);
                      if (response == true) {
                      } else {
                        setState(() {
                          progressMessage =
                              'Si è verificato un errore. Contatta l\'assistenza.';
                        });
                      }
                    },
                  ),
                ],
              )
            ],
          ),
        ],
      ),
    );
  }
}
