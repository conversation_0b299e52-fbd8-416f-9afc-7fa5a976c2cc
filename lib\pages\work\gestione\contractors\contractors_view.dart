import 'dart:developer';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:country_picker/country_picker.dart';

import 'package:newarc_platform/classes/supplier.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/basePersonInfo.dart';
import 'package:newarc_platform/utils/inputFormatters.dart';
import 'package:newarc_platform/widget/UI/address_completion_google.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/image-select-box.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/multi-select-dropdown.dart';
import 'package:newarc_platform/pages/work/gestione/contractors/contractors_controller.dart';
import 'package:newarc_platform/pages/work/gestione/contractors/contractors_data_source.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;


class ContractorsView extends StatefulWidget {
  final responsive;
  final Function updateViewCallback;

  ContractorsView({
    Key? key,
    required this.responsive,
    required this.updateViewCallback,
    }) : super(key: key);

  @override
  State<ContractorsView> createState() =>
      _ContractorsViewState();
}

class _ContractorsViewState extends State<ContractorsView> {

  final controller = Get.put<ContractorsController>(ContractorsController());
  Key? paddingKey;

  @override
  void initState() {
    controller.activitiesFilterController.text = '';
    initialFetchContractors();
    fetchActivityCategories();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> fetchActivityCategories() async {
    try {
      setState(() {
        controller.categories = [];
        controller.loading = true;
      });

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_CATEGORY_ACTIVITY);
      collectionSnapshot = await collectionSnapshotQuery.get();
      for (var i = 0; i < collectionSnapshot.docs.length; i++) {
        controller.categories.add(collectionSnapshot.docs[i].data()['categoryName']);
      }
      controller.jobOptions = (controller.categories..sort()).map((toElement) => {
        'label': toElement,
        'value': toElement,
      }).toList();

      setState(() {
        controller.loading = false;
      });
    } catch (e, s) {
      setState(() {
        controller.loading = false;
      });
      log('fetchActivityCategories error', error: e, stackTrace: s);
    }
  }

  Future<void> initialFetchContractors() async {

    setState(() {
      controller.contractors = [];
      controller.loading = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
        .collection(appConfig.COLLECT_SUPPLIERS);

      if (controller.activitiesFilterController.text != '') {
        collectionSnapshotQuery = collectionSnapshotQuery.where('activities',
            arrayContains: controller.activitiesFilterController.text);
      }
      collectionSnapshotQuery = collectionSnapshotQuery
        .where('isArchived', isEqualTo: false);
        // .orderBy('insertTimestamp', descending: true);
      collectionSnapshot = await collectionSnapshotQuery.get();

      for (var i = 0; i < collectionSnapshot.docs.length; i++) {
        Supplier tmpCont = Supplier.fromDocument(
            collectionSnapshot.docs[i].data(), collectionSnapshot.docs[i].id);
        controller.contractors.add(tmpCont);
      }
      setState(() {
        controller.loading = false;
      });
    } catch (e, s) {
      setState(() {
        controller.loading = false;
      });
      log('Following error', error: e, stackTrace: s);
    }
  }

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }

    return LayoutBuilder(builder: (context, constraints) {
      return Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              NarFormLabelWidget(
                label: 'Ditte e Professionisti',
                fontSize: 19,
                fontWeight: '700',
              ),
              Expanded(
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    BaseNewarcButton(
                      disableButton: controller.loading,
                      buttonText: "Aggiungi Ditta",
                      onPressed: () {
                        return showDialog(
                            context: context,
                            builder: (_context) {
                              return StatefulBuilder(builder: (__context, _setState) {
                                return Center(
                                  child: BaseNewarcPopup(
                                    formErrorMessage: controller.formMessages,
                                    buttonText: 'Aggiungi ditta',
                                    onPressed: () async {
                                      _setState(() {
                                        controller.formMessages.clear();
                                        controller.formMessages
                                            .add('Salvataggio in corso...');
                                        controller.loading = true;
                                      });

                                      BasePersonInfo contactData = BasePersonInfo.fromMap({
                                        'name': controller.contactName.text,
                                        'surname': controller.contactSurname.text,
                                        'email': controller.contactEmail.text,
                                        'phone': controller.contactPhone.text,
                                      });

                                      LegalRepresentativePersonInfo legalRepresentativeData = LegalRepresentativePersonInfo.fromMap({
                                        'name': controller.legalRepresentativeName.text,
                                        'surname': controller.legalRepresentativeSurname.text,
                                        'email': controller.legalRepresentativeEmail.text,
                                        'phone': controller.legalRepresentativePhone.text,
                                        'fiscalCode': controller.legalRepresentativeFiscalCode.text,
                                        'birthCountry': controller.legalRepresentativeBirthCountry.text,
                                        'birthProvince': controller.legalRepresentativeBirthProvince.text,
                                        'birthCity': controller.legalRepresentativeBirthCity.text,
                                        'sex': controller.legalRepresentativeSex.text,
                                        'birthDate': controller.legalRepresentativeBirthDate.text,
                                        'pec': controller.legalRepresentativePEC.text,
                                        'residentialAddressInfo': controller.legalRepresentativeAddress.toMap(),
                                      });

                                      Supplier supplierData = Supplier.empty();

                                      supplierData.name = controller.contractorName.text;
                                      supplierData.formationType =
                                          controller.formationType.text;
                                      supplierData.city = controller.contractorOperativeAddress.city;
                                      supplierData.addressAndCivicNumber =
                                          controller.contractorOperativeAddress.toShortAddress();
                                      supplierData.firstName = controller.contactName.text;
                                      supplierData.lastName = controller.contactSurname.text;
                                      // supplierData.phone = controller.contactPhone.text;
                                      // supplierData.email = controller.contactEmail.text;
                                      supplierData.phone = controller.contractorPhone.text;
                                      supplierData.email = controller.contractorEmail.text;
                                      supplierData.pec = controller.contractorPEC.text;
                                      supplierData.nationality = controller.contractorNationality.text;
                                      supplierData.iban = controller.contractorIban.text;
                                      supplierData.billingCode =
                                          controller.contractorBillingCode.text;
                                      supplierData.iva = controller.contractorVat.text;
                                      supplierData.legalAddress =
                                          controller.contractorLegalAddress.toShortAddress();
                                      supplierData.legalEntity = controller.contractorLegalEntity.text;
                                      supplierData.fiscalCode = controller.contractorFiscalCode.text;
                                      supplierData.activities!.clear();
                                      supplierData.activities = controller.selectedActivities.map((element) {
                                        return element['value'];
                                      }).toList();
                                      supplierData.operativeAddressInfo = controller.contractorOperativeAddress;
                                      supplierData.legalAddressInfo = controller.contractorLegalAddress;
                                      supplierData.contactPersonInfo = contactData;
                                      supplierData.legalRepresentativePersonInfo = legalRepresentativeData;

                                      final FirebaseFirestore _db = FirebaseFirestore.instance;
                                      try {
                                        // create firebase user with default password
                                        final result = await FirebaseFunctions.instance
                                            .httpsCallable('createUser')
                                            .call({
                                              'email': supplierData.email,
                                              'password': 'cambiami!',
                                            });

                                        // create supplier object
                                        DocumentReference supplierFirebaseId = await _db
                                            .collection(
                                                appConfig.COLLECT_SUPPLIERS)
                                            .add(supplierData.toMap());
                                        supplierData.id = supplierFirebaseId.id;

                                        // create user object with id inherited from firebase user's
                                        SupplierUser userData = SupplierUser.empty();
                                        userData.id = result.data['uid'];
                                        userData.name = controller.contactName.text;
                                        userData.surname = controller.contactSurname.text;
                                        userData.email = controller.contractorEmail.text;
                                        userData.phone = controller.contractorPhone.text;
                                        userData.supplierId = supplierFirebaseId.id;
                                        userData.role = 'supplier';
                                        userData.type = 'supplier';
                                        userData.isActive = true;
                                        userData.isArchived = false;

                                        await _db
                                            .collection(
                                                appConfig.COLLECT_USERS)
                                            .doc(result.data['uid'])
                                            .set(userData.toMap());

                                        _setState(() {
                                          controller.formMessages.clear();
                                          controller.formMessages
                                              .add('Ditta creata!');
                                          controller.loading = false;
                                        });

                                        Future.delayed(Duration(milliseconds: 100), () {
                                          widget.updateViewCallback(
                                            "contractors-inside-view",
                                            projectArguments: {
                                              'contractor': supplierData
                                            }
                                          );
                                        });
                                        return true;
                                      } on FirebaseFunctionsException catch (e) {
                                        print('FirebaseFunctionExceptions:');
                                        print("ERROR: $e");
                                        print("DETAILS:");
                                        print(e.details);
                                        if (e.details['originalError']['code'] == 'auth/email-already-exists') {
                                          _setState(() {
                                            controller.formMessages.clear();
                                            controller.formMessages.add('Esiste già un account associato a questa email');
                                            controller.loading = false;
                                          });
                                        }
                                        if (e.details['originalError']['code'] == 'auth/invalid-email') {
                                          _setState(() {
                                            controller.formMessages.clear();
                                            controller.formMessages.add("L'email inserita non è valida");
                                            controller.loading = false;
                                          });
                                        }
                                        return false;
                                      } catch (e) {
                                        _setState(() {
                                          controller.formMessages.clear();
                                          controller.formMessages.add(
                                              'Qualcosa è andato storto!' +
                                                  e.toString());
                                          controller.loading = false;
                                        });
                                        return false;
                                      }
                                    },
                                    title: "Aggiungi una ditta",
                                    column: Container(
                                      width: 600,
                                      height: 400,
                                      child: controller.loading
                                        ? Center(child: CircularProgressIndicator(color: Theme.of(context).primaryColor))
                                        : SingleChildScrollView(
                                        child: Column(
                                          children: [
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.start,
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                CustomTextFormField(
                                                  label: 'Nome Ditta',
                                                  controller: controller.contractorName,
                                                  validator: (value) {
                                                    if (value == '') {
                                                      return 'Obbligatorio';
                                                    }
                                                    return null;
                                                  },
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 10),
                                            Column(
                                              mainAxisSize: MainAxisSize.max,
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              mainAxisAlignment: MainAxisAlignment.start,
                                              children: [
                                                NarFormLabelWidget(
                                                  label: "Attività",
                                                  textColor: Color(0xff696969),
                                                  fontSize: 13,
                                                  fontWeight: '600',
                                                ),
                                                SizedBox(height: 4),
                                                MultiSelectDropdownWidget(
                                                  options: controller.jobOptions,
                                                  initialValue: controller.selectedActivities,
                                                  // validationType: 'required',
                                                  // parametersValidate: 'Obbligatorio',
                                                  onChanged: (List<dynamic> selectedValues) {
                                                    _setState(() {controller.selectedActivities = selectedValues;});
                                                  },
                                                )
                                              ],
                                            ),
                                            SizedBox(height: 10),
                                            AddressSearchBar(
                                              label: "Indirizzo sede operativa",
                                              onPlaceSelected: (selectedPlace){
                                                BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace["place"]);
                                                if (selectedAddress.isValidAddress()){
                                                  controller.contractorOperativeAddress = selectedAddress;
                                                } else {
                                                  controller.contractorOperativeAddress = BaseAddressInfo.empty();
                                                }
                                              },
                                              // validator: (value) {
                                              //   if (value == null || value.isEmpty) {
                                              //     return 'Obbligatorio';
                                              //   }
                                              //   return null;
                                              // },
                                            ),
                                            SizedBox(height: 10),
                                            AddressSearchBar(
                                              label: 'Indirizzo sede legale',
                                              onPlaceSelected: (selectedPlace){
                                                BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace["place"]);
                                                if (selectedAddress.isValidAddress()){
                                                  controller.contractorLegalAddress = selectedAddress;
                                                } else {
                                                  controller.contractorLegalAddress = BaseAddressInfo.empty();
                                                }
                                              },
                                              // validator: (value) {
                                              //   if (value == null || value.isEmpty) {
                                              //     return 'Obbligatorio';
                                              //   }
                                              //   return null;
                                              // },
                                            ),
                                            SizedBox(height: 10),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                CustomTextFormField(
                                                  label: "Denominazione",
                                                  controller: controller.contractorLegalEntity,
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                Expanded(
                                                  child: Column(
                                                    mainAxisSize:
                                                        MainAxisSize.max,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.start,
                                                    children: [
                                                      NarFormLabelWidget(
                                                        label:
                                                            "Forma Societaria",
                                                        textColor:
                                                            Color(0xff696969),
                                                        fontSize: 13,
                                                        fontWeight: '600',
                                                      ),
                                                      SizedBox(height: 4),
                                                      NarSelectBoxWidget(
                                                        options: appConst.supplierFormationTypesList,
                                                        controller:
                                                            controller.formationType,
                                                        // validationType:
                                                        //     'required',
                                                        // parametersValidate:
                                                        //     'Obbligatorio',
                                                        onChanged: (value){
                                                          _setState((){});
                                                        },
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 10),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                CustomTextFormField(
                                                  label: "P.Iva",
                                                  controller: controller.contractorVat,
                                                  inputFormatters: [ivaMaskFormatter],
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                CustomTextFormField(
                                                  label: "Codice Fiscale",
                                                  enabled: controller.formationType.text == 'Impresa individuale',
                                                  controller: controller.contractorFiscalCode,
                                                  inputFormatters: [codiceFiscaleMaskFormatter],
                                                  textCapitalization: TextCapitalization.characters,
                                                  // validator: (value) {
                                                  //   if (controller.formationType.text != 'Impresa individuale') {
                                                  //     return null;
                                                  //   }
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   if (value.length < 16) {
                                                  //     return 'Codice fiscale non valido';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 10),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                CustomTextFormField(
                                                  label: "IBAN",
                                                  controller: controller.contractorIban,
                                                  inputFormatters: [ibanMaskFormatter],
                                                  textCapitalization:
                                                      TextCapitalization.words,
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                CustomTextFormField(
                                                  label:
                                                      "Codice Fatturazione Elettronico",
                                                  controller: controller.contractorBillingCode,
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 10),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Container(
                                                  width: 293,
                                                  child: CustomTextFormField(
                                                    isExpanded: false,
                                                    textAlign: TextAlign.left,
                                                    isHaveBorder: true,
                                                    isCenterLabel: false,
                                                    flex: 0,
                                                    suffixIcon: Container(
                                                      padding: const EdgeInsets.all(10),
                                                      height: 20,
                                                      width: 20,
                                                      // child: Icon(Icons.web),
                                                    ),
                                                    // readOnly: true,
                                                    label: "Nazionalità",
                                                    controller: controller.contractorNationality,
                                                    onTap: () {
                                                      showCountryPicker(
                                                        context: context,
                                                        showPhoneCode: false, // Set to true if you want dial codes
                                                        onSelect: (Country country) {
                                                          setState(() {
                                                            controller.contractorNationality.text = country.name;
                                                          });
                                                        },
                                                      );
                                                    },
                                                  ),
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                CustomTextFormField(
                                                  label:
                                                      "Telefono",
                                                  controller: controller.contractorPhone,
                                                  inputFormatters: [phoneNumberMaskFormatterInternational],
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 10),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                CustomTextFormField(
                                                  label: "E-mail (login)",
                                                  controller: controller.contractorEmail,
                                                  validator: (value) {
                                                    if (value == '') {
                                                      return 'Obbligatorio';
                                                    }
                                                    final emailRegexp = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                                                    if (!emailRegexp.hasMatch(value)) {
                                                      return 'Inserisci un indirizzo email valido';
                                                    }
                                                    return null;
                                                  },
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                CustomTextFormField(
                                                  label: "PEC",
                                                  controller: controller.contractorPEC,
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   final emailRegexp = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                                                  //   if (!emailRegexp.hasMatch(value)) {
                                                  //     return 'Inserisci un indirizzo email valido';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 20),
                                            Divider(color: Color(0xffd7d7d7d7), thickness: 1,),
                                            SizedBox(height: 20),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                CustomTextFormField(
                                                  label:
                                                      "Nome persona di riferimento",
                                                  controller: controller.contactName,
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                CustomTextFormField(
                                                  label:
                                                      "Cognome persona di riferimento",
                                                  controller: controller.contactSurname,
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 10),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                CustomTextFormField(
                                                  label: "Telefono persona di riferimento",
                                                  controller: controller.contactPhone,
                                                  inputFormatters: [phoneNumberMaskFormatterInternational],
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                CustomTextFormField(
                                                  label: "E-mail persona di riferimento",
                                                  controller: controller.contactEmail,
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   final emailRegexp = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                                                  //   if (!emailRegexp.hasMatch(value)) {
                                                  //     return 'Inserisci un indirizzo email valido';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 20),
                                            Divider(color: Color(0xffd7d7d7d7), thickness: 1,),
                                            SizedBox(height: 20),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                CustomTextFormField(
                                                  label:
                                                      "Nome legale rappresentante",
                                                  controller: controller.legalRepresentativeName,
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                CustomTextFormField(
                                                  label:
                                                      "Cognome legale rappresentante",
                                                  controller: controller.legalRepresentativeSurname,
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 10),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                CustomTextFormField(
                                                  label:
                                                      "E-mail legale rappresentante",
                                                  controller: controller.legalRepresentativeEmail,
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                CustomTextFormField(
                                                  label:
                                                      "Telefono legale rappresentante",
                                                  controller: controller.legalRepresentativePhone,
                                                  inputFormatters: [phoneNumberMaskFormatterInternational],
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 10),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                CustomTextFormField(
                                                  label:
                                                      "PEC legale rappresentante",
                                                  controller: controller.legalRepresentativePEC,
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                CustomTextFormField(
                                                  label:
                                                      "CF legale rappresentante",
                                                  controller: controller.legalRepresentativeFiscalCode,
                                                  inputFormatters: [codiceFiscaleMaskFormatter],
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 10),
                                            AddressSearchBar(
                                              label: 'Residenza legale rappresentante',
                                              onPlaceSelected: (selectedPlace){
                                                BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace["place"]);
                                                if (selectedAddress.isValidAddress()){
                                                  controller.legalRepresentativeAddress = selectedAddress;
                                                } else {
                                                  controller.legalRepresentativeAddress = BaseAddressInfo.empty();
                                                }
                                              },
                                              // validator: (value) {
                                              //   if (value == null || value.isEmpty) {
                                              //     return 'Obbligatorio';
                                              //   }
                                              //   return null;
                                              // },
                                            ),
                                            SizedBox(height: 10),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Container(
                                                  width: 293,
                                                  child: CustomTextFormField(
                                                    isExpanded: false,
                                                    textAlign: TextAlign.left,
                                                    isHaveBorder: true,
                                                    isCenterLabel: false,
                                                    flex: 0,
                                                    suffixIcon: Container(
                                                      padding: const EdgeInsets.all(10),
                                                      height: 20,
                                                      width: 20,
                                                      // child: Icon(Icons.web),
                                                    ),
                                                    // readOnly: true,
                                                    label: "Stato di nascita legale rappresentante",
                                                    controller: controller.legalRepresentativeBirthCountry,
                                                    onTap: () {
                                                      showCountryPicker(
                                                        context: context,
                                                        showPhoneCode: false, // Set to true if you want dial codes
                                                        onSelect: (Country country) {
                                                          setState(() {
                                                            controller.legalRepresentativeBirthCountry.text = country.name;
                                                          });
                                                        },
                                                      );
                                                    },
                                                  ),
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                CustomTextFormField(
                                                  label:
                                                      "Provincia di nascita legale rappresentante",
                                                  controller: controller.legalRepresentativeBirthProvince,
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 10),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                CustomTextFormField(
                                                  label:
                                                      "Città di nascita legale rappresentante",
                                                  controller: controller.legalRepresentativeBirthCity,
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                CustomTextFormField(
                                                  label:
                                                      "Sesso legale rappresentante",
                                                  controller: controller.legalRepresentativeSex,
                                                  // validator: (value) {
                                                  //   if (value == '') {
                                                  //     return 'Obbligatorio';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 10),
                                            // data di nascita legale rappresentante
                                            CustomTextFormField(
                                              textAlign: TextAlign.left,
                                              isHaveBorder: true,
                                              isCenterLabel: false,
                                              flex: 0,
                                              suffixIcon: Container(
                                                padding: const EdgeInsets.all(10),
                                                height: 20,
                                                width: 20,
                                                child: Image.asset('assets/icons/calendar.png'),
                                              ),
                                              readOnly: true,
                                              label: "Data di nascita legale rappresentante",
                                              controller: controller.legalRepresentativeBirthDate,
                                              onTap: () async {
                                                DateTime? pickedDate = await showDatePicker(
                                                  context: context,
                                                  initialDate: DateTime.now(),
                                                  firstDate: DateTime(1930),
                                                  lastDate: DateTime(DateTime.now().year + 1),
                                                );
                                                if (pickedDate != null) {
                                                  controller.selectedLegalRepresentativeBirthDate = pickedDate.millisecondsSinceEpoch;
                                                  String formattedDate = DateFormat('dd/MM/yyyy').format(pickedDate);
                                                  controller.legalRepresentativeBirthDate.text = formattedDate;
                                                } else {
                                                  log("No Date Selected");
                                                }
                                              },
                                            ),
                                            SizedBox(height: 10),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              });
                            });
                      })
                  ],
                ),
              )
            ],
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              controller.loading
                  ? Container()
                  : NarFilter(
                showSearchInput: false,
                textEditingControllers: [
                  controller.activitiesFilterController,
                ],
                selectedFilters: [
                  controller.activitiesSelectedFilter,
                ],
                filterFields: [
                  {
                    'Categoria': NarImageSelectBoxWidget(
                      // must be sorted in alphabetical order
                      options: (controller.categories..sort()).map((toElement) => {
                        'label': toElement,
                        'value': toElement,
                      }).toList(),
                      onChanged: (dynamic val) {
                        controller.activitiesSelectedFilter = val['label'];
                        setState(() {});
                      },
                      controller: controller.activitiesFilterController,
                    ),
                  },
                ],
                onSubmit: () async {
                  await initialFetchContractors();
                },
                onReset: () async {
                  controller.clearFilter();
                  await initialFetchContractors();
                },
              ),
            ],
          ),
          SizedBox(height: 10),
          Container(
            height: constraints.maxHeight / 1.2,
            child: Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Opacity(
                        opacity: controller.loading ? 0.5 : 1,
                        child: NewarcDataTable(
                          rowsPerPage: 20,
                          dividerThickness: 1,
                          columns: [
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Ditta',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.L,
                              label: Text(
                                'Categorie',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Provincia base',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Prog. assegnati',
                              ),
                            ),
                          ],
                          source: ContractorsDataSource(
                            onContractorTap: (Supplier cont) {
                              // Use Future.microtask to ensure we're not in the build phase
                              Future.microtask(() {
                                widget.updateViewCallback(
                                  "contractors-inside-view",
                                  projectArguments: {
                                    'contractor': cont
                                  }
                                );
                              });
                            },
                            contractors: controller.contractors,
                            context: context,
                          ),
                        ),
                      ),
                      if (controller.loading)
                        Positioned.fill(
                          child: Center(
                            child: CircularProgressIndicator(
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }
}
