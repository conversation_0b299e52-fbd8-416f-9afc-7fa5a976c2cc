import 'package:flutter/material.dart';

class AgencyTermsAndConditions extends StatefulWidget {
  AgencyTermsAndConditions({Key? key}) : super(key: key);

  static const String route = '/terms-condition';

  @override
  _AgencyTermsAndConditionsState createState() =>
      _AgencyTermsAndConditionsState();
}

class _AgencyTermsAndConditionsState extends State<AgencyTermsAndConditions> {
  @override
  Widget build(BuildContext context) {
    return Text('Hello');
  }
}
