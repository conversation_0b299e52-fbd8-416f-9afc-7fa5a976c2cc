import 'package:flutter/material.dart';
import 'package:path/path.dart' as p;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';

import 'package:newarc_platform/classes/professionals.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/basePersonInfo.dart';

import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/image-picker.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/address_completion_google.dart';

import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/utils/inputFormatters.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;


class ProfessionalsSetting extends StatefulWidget {
  final ProfessionalsUser professionalsUser;
  final Function? getProfilePicture;

  const ProfessionalsSetting(
      {Key? key, required this.professionalsUser, this.getProfilePicture})
      : super(key: key);

  @override
  State<ProfessionalsSetting> createState() => ProfessionalsSettingState();
}

class ProfessionalsSettingState extends State<ProfessionalsSetting> {
  TextEditingController confirmPassword = new TextEditingController();
  TextEditingController newPassword = new TextEditingController();

  bool isNewPasswordVisible = false;
  bool isConfirmPasswordVisible = false;

  final controller = Get.put<ProfessionalsSettingsController>(ProfessionalsSettingsController());

  final FirebaseFirestore _db = FirebaseFirestore.instance;
  final profilePicture = [];
  String? profilePictureFilename;
  String? validationMessage;
  String? progressMessage;
  double containerWidth = 0;

  @override
  void initState() {
    confirmPassword.text = '';
    newPassword.text = '';
    getProfileImageUrl();
    controller.initInsideViewController(widget.professionalsUser.professional!);
    super.initState();
  }

  @protected
  void didUpdateWidget(ProfessionalsSetting oldWidget) {
    super.didUpdateWidget(oldWidget);
    getProfileImageUrl();
  }

  getProfileImageUrl() async {
    profilePicture.clear();
    setState(() {
      profilePictureFilename = widget.professionalsUser.profilePicture;
    });
  }

  Future<bool> updateData() async {
    setState(() {
      validationMessage = null;
    });
    // Update password
    if (confirmPassword.text != '' && newPassword.text != '') {
      if (newPassword.text == confirmPassword.text) {
        setState(() {
          validationMessage = null;
        });
        updatePassword();
      } else {
        setState(() {
          validationMessage = 'Password mismatch!';
        });
        return false;
      }
    }
    // Update profile picture
    if (profilePicture.length > 0) {
      String __profilePictureFilename =
          'professional-profile' + p.extension(profilePicture[0].name);
      await uploadProfilePicture("${appConfig.COLLECT_PROFESSIONALS}/", widget.professionalsUser.id!,
              __profilePictureFilename, profilePicture[0])
          .then((uploadTask) {
        try {
          // files.add(filename);
        } catch (e, s) {
          // print({e,s});
        }
      });
      widget.professionalsUser.profilePicture = __profilePictureFilename;
      profilePicture.clear();
      profilePictureFilename = __profilePictureFilename;
    } else {
      profilePictureFilename = widget.professionalsUser.profilePicture;
    }
    // Update professional info
    controller.updateProfessional(widget.professionalsUser.professional!);

    setState(() {
      progressMessage = "Saved!";
    });

    await _db
        .collection(appConfig.COLLECT_USERS)
        .doc(widget.professionalsUser.id)
        .update(widget.professionalsUser.toMap());
    
    await _db
        .collection(appConfig.COLLECT_PROFESSIONALS)
        .doc(widget.professionalsUser.professional!.id)
        .update(widget.professionalsUser.professional!.toMap());

    return true;
  }

  updatePassword() async {
    User? user = FirebaseAuth.instance.currentUser;

    user!.updatePassword(newPassword.text).then((_) {
      print('password changed');

      newPassword.text = '';
      confirmPassword.text = '';
    }).catchError((error) {
      // print({'password no changed', error});
      //Error, show something
    });
  }

  @override
  Widget build(BuildContext context) {
    containerWidth = MediaQuery.of(context).size.width * .75;

    return ListView(
      shrinkWrap: true,
      // mainAxisSize: MainAxisSize.max,
      // mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.max,
          children: [
            NarFormLabelWidget(
              label: 'Impostazioni',
              fontSize: 20,
              fontWeight: 'bold',
            ),
            SizedBox(height: 15),
            Row(
              children: [
                Container(
                  width: containerWidth,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(13),
                    border: Border.all(
                      color: Color(0xFFE7E7E7),
                      width: 1.0,
                    ),
                  ),
                  padding: EdgeInsets.all(15),
                  child: Column(
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label: 'Dati Professionista',
                            fontSize: 18,
                            fontWeight: 'bold',
                          ),
                          SizedBox(height: 20),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Switch(
                                value: widget.professionalsUser.professional!.hasPIva,
                                activeTrackColor: Theme.of(context).primaryColor,
                                inactiveTrackColor: Theme.of(context).primaryColorLight,
                                thumbColor: WidgetStateProperty.all(Colors.white),
                                onChanged: (bool value) {
                                  setState(() {
                                    widget.professionalsUser.professional!.hasPIva = value;
                                  });
                                },
                              ),
                              SizedBox(width: 10),
                              NarFormLabelWidget(
                                label: 'Registrazione con p.iva',
                                fontSize: 14,
                                fontWeight: '600',
                                textColor: widget.professionalsUser.professional!.hasPIva ? Colors.black : Colors.grey,
                              ),
                            ],
                          ),
                          SizedBox(height: 15),
                          widget.professionalsUser.professional!.hasPIva
                          ? Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomTextFormField(
                                label: 'Nome Azienda',
                                controller: controller.professionalName,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Obbligatorio';
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ) : SizedBox.shrink(),
                          widget.professionalsUser.professional!.hasPIva
                          ? SizedBox(
                            height: 15,
                          ) : SizedBox.shrink(),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Expanded(
                                child: NarSelectBoxWidget(
                                  label: 'Professione',
                                  options: appConst.professionalsTypesList,
                                  controller: controller.professionalProfession,
                                  onChanged: (){
                                    setState((){});
                                  },
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 15),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomTextFormField(
                                label: 'E-mail (login)',
                                controller: controller.professionalEmail,
                                readOnly: true,
                                // validator: (value) {
                                //   if (value == null || value.isEmpty) {
                                //     return 'Obbligatorio';
                                //   }
                                //   return null;
                                // },
                              ),
                              SizedBox(
                                width: 15
                              ),
                              CustomTextFormField(
                                label: 'Telefono Clienti',
                                controller: controller.professionalClientPhone,
                                // validator: (value) {
                                //   if (value == null || value.isEmpty) {
                                //     return 'Obbligatorio';
                                //   }
                                //   return null;
                                // },
                              ),
                            ],
                          ),
                          SizedBox(height: 15),
                          Divider(
                            color: Color(0xffd7d7d7d7),
                            thickness: 1,
                          ),
                          SizedBox(height: 15),
                          NarFormLabelWidget(
                            label: 'Persona di riferimento',
                            fontSize: 18,
                            fontWeight: 'bold',
                          ),
                          SizedBox(height: 20),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              CustomTextFormField(
                                label: 'Nome',
                                controller: controller.contactName,
                                // validator: (value) {
                                //   if (value == null || value.isEmpty) {
                                //     return 'Obbligatorio';
                                //   }
                                //   return null;
                                // },
                              ),
                              SizedBox(
                                width: 15,
                              ),
                              CustomTextFormField(
                                label: 'Cognome',
                                controller: controller.contactSurname,
                                // validator: (value) {
                                //   if (value == null || value.isEmpty) {
                                //     return 'Obbligatorio';
                                //   }
                                //   return null;
                                // },
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 15,
                          ),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              CustomTextFormField(
                                label: 'Telefono',
                                controller: controller.contactPhone,
                                inputFormatters: [phoneNumberMaskFormatterInternational],
                                // validator: (value) {
                                //   if (value == null || value.isEmpty) {
                                //     return 'Obbligatorio';
                                //   }
                                //   return null;
                                // },
                              ),
                              SizedBox(
                                width: 15,
                              ),
                              CustomTextFormField(
                                label: 'E-mail',
                                controller: controller.contactEmail,
                                // readOnly: true,
                              ),
                            ],
                          ),
                          SizedBox(height: 15),
                          Divider(
                            color: Color(0xffd7d7d7d7),
                            thickness: 1,
                          ),
                          SizedBox(height: 15),
                          NarFormLabelWidget(
                            label: 'Fatturazione',
                            fontSize: 18,
                            fontWeight: 'bold',
                          ),
                          SizedBox(height: 20),
                          widget.professionalsUser.professional!.hasPIva
                          ? Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              CustomTextFormField(
                                label: 'Denominazione',
                                controller: controller.professionalLegalEntity,
                                textCapitalization: TextCapitalization.words,
                                // validator: (value) {
                                //   if (value == null || value.isEmpty) {
                                //     return 'Obbligatorio';
                                //   }
                                //   return null;
                                // },
                              ),
                              SizedBox(
                                width: 15,
                              ),
                              Expanded(
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    NarFormLabelWidget(
                                      label: "Forma Societaria",
                                      textColor: Color(0xff696969),
                                      fontSize: 13,
                                      fontWeight: '600',
                                    ),
                                    SizedBox(height: 4),
                                    NarSelectBoxWidget(
                                      options: appConst.supplierFormationTypesList,
                                      controller: controller.formationType,
                                      // validationType: 'required',
                                      // parametersValidate: 'Required!',
                                      onChanged: (value){
                                        setState((){});
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ) : SizedBox.shrink(),
                          widget.professionalsUser.professional!.hasPIva
                          ? SizedBox(
                            height: 15,
                          ) : SizedBox.shrink(),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Expanded(
                                child: AddressSearchBar(
                                  label: "Indirizzo sede legale", 
                                  initialAddress: widget.professionalsUser.professional!.legalAddressInfo.fullAddress ?? "",
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Obbligatorio';
                                    }
                                    return null;
                                  },
                                  onPlaceSelected: (selectedPlace){
                                    BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace['place']);
                                    if (selectedAddress.isValidAddress()){
                                      controller.professionalLegalAddress = selectedAddress;
                                    } else {
                                      controller.professionalLegalAddress = BaseAddressInfo.empty();
                                    }
                                  }
                                ),
                              ),
                              SizedBox(
                                width: 15,
                              ),
                              CustomTextFormField(
                                label: 'Codice Fiscale',
                                enabled : widget.professionalsUser.professional!.hasPIva ? controller.formationType.text == 'Impresa individuale' : true,
                                controller: controller.professionalFiscalCode,
                                inputFormatters: [codiceFiscaleMaskFormatter],
                                // validator: (value) {
                                //   if (controller.formationType.text != 'Impresa individuale') {
                                //     return null;
                                //   }
                                //   if (value == null || value.isEmpty || value.length < 16) {
                                //     return 'Obbligatorio';
                                //   }
                                //   return null;
                                // },
                              ),
                            ],
                          ),
                          SizedBox(height: 15),
                          widget.professionalsUser.professional!.hasPIva
                          ? Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              CustomTextFormField(
                                label: 'P.IVA',
                                controller: controller.professionalVat,
                                inputFormatters: [ivaMaskFormatter],
                                // validator: (value) {
                                //   if (value == null || value.isEmpty || !RegExp(r"^(IT)?[0-9]{11}$").hasMatch(value)) {
                                //     return 'Obbligatorio';
                                //   }
                                //   return null;
                                // },
                              ),
                              SizedBox(
                                width: 15,
                              ),
                              CustomTextFormField(
                                label: 'Codice Fatturazione Elettronico',
                                controller: controller.professionalBillingCode,
                                // validator: (value) {
                                //   if (value == null || value.isEmpty) {
                                //     return 'Obbligatorio';
                                //   }
                                //   return null;
                                // },
                              ),
                            ],
                          ) : SizedBox.shrink(),
                          widget.professionalsUser.professional!.hasPIva
                          ? SizedBox(
                            height: 15,
                          ) : SizedBox.shrink(),
                          Divider(
                            color: Color(0xffd7d7d7d7),
                            thickness: 1,
                          ),
                          SizedBox(height: 15),
                          // Logo
                          NarFormLabelWidget(
                            label: 'Immagine profilo',
                            fontSize: 16,
                            fontWeight: 'bold',
                          ),
                          SizedBox(height: 20),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              NarImagePickerWidget(
                                  allowMultiple: false,
                                  imagesToDisplayInList: 0,
                                  removeButton: false,
                                  removeButtonText: 'rimuovi',
                                  uploadButtonPosition: 'back',
                                  showMoreButtonText: '+ espandi',
                                  removeButtonPosition: 'bottom',
                                  displayFormat: 'row',
                                  imageDimension: 100,
                                  imageBorderRadius: 50,
                                  borderRadius: 7,
                                  fontSize: 14,
                                  fontWeight: '600',
                                  text: 'Carica immagine profilo',
                                  borderSideColor:
                                      Theme.of(context).primaryColor,
                                  hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                  images: profilePicture,
                                  pageContext: context,
                                  storageDirectory: "${appConfig.COLLECT_PROFESSIONALS}/",
                                  preloadedImages:
                                      profilePictureFilename == null ||
                                              profilePictureFilename == ''
                                          ? []
                                          : [profilePictureFilename],
                                  firebaseId: widget.professionalsUser.id,
                                  removeExistingOnChange: true)
                            ],
                          ),
                          SizedBox(height: 15),
                          Container(
                            width: containerWidth * .80,
                            height: 1,
                            decoration: BoxDecoration(
                              color: Color(0xFFDCDCDC),
                            ),
                            child: SizedBox(height: 0),
                          ),
                          SizedBox(height: 15),
                          NarFormLabelWidget(
                            label: 'Modifica password',
                            fontSize: 16,
                            fontWeight: 'bold',
                          ),
                          SizedBox(height: 20),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              CustomTextFormField(
                                  label: 'Forza nuova password',
                                  hintText: '',
                                  controller: confirmPassword,
                                  isObscureText: !isNewPasswordVisible,
                                  suffixIcon: IconButton(
                                    icon: isNewPasswordVisible
                                        ? Icon(
                                            Icons.visibility_rounded,
                                            color:
                                                Theme.of(context).primaryColor,
                                          )
                                        : Icon(Icons.visibility_off_rounded,
                                            color: Colors.grey),
                                    onPressed: () {
                                      setState(() {
                                        isNewPasswordVisible =
                                            !isNewPasswordVisible;
                                      });
                                    },
                                  )),
                              SizedBox(
                                width: 15,
                              ),
                              CustomTextFormField(
                                  label: 'Ripeti nuova password',
                                  hintText: '',
                                  controller: newPassword,
                                  isObscureText: !isConfirmPasswordVisible,
                                  suffixIcon: IconButton(
                                    icon: isConfirmPasswordVisible
                                        ? Icon(
                                            Icons.visibility_rounded,
                                            color:
                                                Theme.of(context).primaryColor,
                                          )
                                        : Icon(Icons.visibility_off_rounded,
                                            color: Colors.grey),
                                    onPressed: () {
                                      setState(() {
                                        isConfirmPasswordVisible =
                                            !isConfirmPasswordVisible;
                                      });
                                    },
                                  )),
                              Expanded(
                                  flex: 1,
                                  child: SizedBox(
                                    height: 0,
                                  ))
                            ],
                          ),
                          NarFormLabelWidget(
                            label: validationMessage != ''
                                ? validationMessage
                                : '',
                            fontSize: 12,
                            textColor: Colors.red,
                          ),
                        ],
                      ),
                      Column(
                        children: [
                          SizedBox(
                            height: 50,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              NarFormLabelWidget(
                                label: progressMessage != ''
                                    ? progressMessage
                                    : '',
                                fontSize: 12,
                              )
                            ],
                          ),
                          SizedBox(height: 10),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              BaseNewarcButton(
                                  buttonText: "Salva",
                                  onPressed: () async {
                                    setState(() {
                                      profilePictureFilename = '';
                                      progressMessage =
                                          'Salvataggio in corso...';
                                    });
                                    bool response = await updateData();

                                    if (response == true) {
                                      setState(() {
                                        progressMessage = '';
                                        widget.getProfilePicture!();
                                        profilePicture.clear();
                                      });
                                      await showAlertDialog(
                                          context,
                                          "Salvataggio",
                                          "Informazioni professionista salvate con successo");
                                    } else {
                                      setState(() {
                                        progressMessage =
                                            'Si è verificato un errore. Contatta l\'assistenza.';
                                      });
                                    }
                                  })
                            ],
                          )
                        ],
                      )
                    ],
                  ),
                ),
              ],
            ),
          ],
        )
      ],
    );
  }
}

class ProfessionalsSettingsController extends GetxController {

  TextEditingController professionalName = new TextEditingController();
  TextEditingController professionalProfession = new TextEditingController();
  TextEditingController professionalEmail = new TextEditingController();
  TextEditingController professionalClientPhone = new TextEditingController();
  TextEditingController contactName = new TextEditingController();
  TextEditingController contactSurname = new TextEditingController();
  TextEditingController contactPhone = new TextEditingController();
  TextEditingController contactEmail = new TextEditingController();
  TextEditingController professionalLegalEntity = new TextEditingController();
  TextEditingController formationType = new TextEditingController();
  BaseAddressInfo professionalLegalAddress = BaseAddressInfo.empty();
  TextEditingController professionalFiscalCode = new TextEditingController();
  TextEditingController professionalVat = new TextEditingController();
  TextEditingController professionalBillingCode = new TextEditingController();

  initInsideViewController(Professional professional){
    professionalName.text = professional.companyName ?? "";
    professionalProfession.text = professional.profession ?? "";
    professionalEmail.text = professional.email ?? "";
    professionalClientPhone.text = professional.phone ?? "";
    contactName.text = professional.contactPersonInfo.name ?? "";
    contactSurname.text = professional.contactPersonInfo.surname ?? "";
    contactPhone.text = professional.contactPersonInfo.phone ?? "";
    contactEmail.text = professional.contactPersonInfo.email ?? "";
    professionalLegalEntity.text = professional.legalEntity ?? "";
    formationType.text = professional.formationType ?? "";
    professionalLegalAddress = professional.legalAddressInfo;
    professionalFiscalCode.text = professional.fiscalCode ?? "";
    professionalVat.text = professional.vat ?? "";
    professionalBillingCode.text = professional.sdi ?? "";
  }

  updateProfessional(Professional professional){
    professional.companyName = professionalName.text;
    professional.profession = professionalProfession.text;
    professional.email = professionalEmail.text;
    professional.phone = professionalClientPhone.text;
    professional.contactPersonInfo = BasePersonInfo.fromMap(
        {
          'name': contactName.text,
          'surname': contactSurname.text,
          'phone': contactPhone.text,
          'email': contactEmail.text
        }
    );
    professional.legalEntity = professionalLegalEntity.text;
    professional.formationType = formationType.text;
    professional.legalAddressInfo = professionalLegalAddress;
    professional.fiscalCode = professionalFiscalCode.text;
    professional.vat = professionalVat.text;
    professional.sdi = professionalBillingCode.text;
    professional.modificationTimestamp = DateTime.now().millisecondsSinceEpoch;
  }

  clearInsideViewController(){
    professionalName.clear();
    professionalProfession.clear();
    professionalEmail.clear();
    professionalClientPhone.clear();
    contactName.clear();
    contactSurname.clear();
    contactPhone.clear();
    contactEmail.clear();
    professionalLegalEntity.clear();
    formationType.clear();
    professionalLegalAddress = BaseAddressInfo.empty();
    professionalFiscalCode.clear();
    professionalVat.clear();
    professionalBillingCode.clear();
  }
}
