import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:html' as html;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:image_picker/image_picker.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/professionals.dart';
import 'package:newarc_platform/functions/various.dart';
import 'package:newarc_platform/pages/login_page.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:newarc_platform/utils/inputFormatters.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/address_completion_google.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/image-picker.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/agency/register_form_tab.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:path/path.dart' as p;
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/utils/inputFormatters.dart';

import '../utils/resizeXFile.dart';


class RegisterPage extends StatefulWidget {

  final String origin;
  static const String route = '/register';

  RegisterPage({Key? key, required this.origin}) : super(key: key);

  @override
  _RegisterPageState createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  late String origin;
  late List userEmails = [];
  String? projectId;
  //String? email;
  String? password;
  User? user;
  bool loading = false;
  bool pwdVisible = false;
  Agency? agency;
  AgencyUser? agencyUser;
  bool useRefAgencyEmail = false;
  bool useAgencyRefTelephone = false;
  List<XFile> profilePicture = [];
  String? agencyProfilePicture;
  String? profilePictureFilename;

  
  Professional? professional;
  ProfessionalsUser? professionalsUser;
  bool professionalHasPIva = true;

  bool registrationComplete = false;
  bool hasClicked = false;
  final _telephoneFormKey = GlobalKey<FormState>();
  final _agencyInfoFormKey = GlobalKey<FormState>();
  final _personaFormKey = GlobalKey<FormState>();
  final _fatturazioneFormKey = GlobalKey<FormState>();
  final _credFormKey = GlobalKey<FormState>();
  final _privacyFormKey = GlobalKey<FormState>();

  final PageController _pageController = PageController();

  // Project linking variables
  bool isProjectLinked = false;

  //TextControllers
  TextEditingController filterCity = new TextEditingController();
  TextEditingController nomeAgenziaController = TextEditingController();
  TextEditingController indirizzoAgenziaController = TextEditingController();
  TextEditingController numeroCivicoController = TextEditingController();
  TextEditingController numeroTelefonoController = TextEditingController();
  TextEditingController emailAgenziaController = TextEditingController();

  TextEditingController refPersonName = new TextEditingController();
  TextEditingController refPersonLastName = new TextEditingController();
  TextEditingController refPersonMail = new TextEditingController();
  TextEditingController billingFullName = new TextEditingController();
  TextEditingController billingFormationType = new TextEditingController();
  TextEditingController billingFiscalCode = new TextEditingController();
  TextEditingController billingIva = new TextEditingController();
  TextEditingController billingSedeLegal = new TextEditingController();
  TextEditingController billingInvoiceCode = new TextEditingController();
  TextEditingController newPassword = new TextEditingController();
  TextEditingController confirmPassword = new TextEditingController();
  TextEditingController refPersonPhone = new TextEditingController();
  TextEditingController billingIban = new TextEditingController();

  TextEditingController professionController = new TextEditingController();

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    origin = widget.origin;
    // Extract projectId from URL parameters for agency registration linking
    projectId = Uri.base.queryParameters["projectId"];

    if (origin == 'professionals') {
      professional = Professional.empty();
      professionalsUser = ProfessionalsUser.empty();
    } else {
      agency = Agency.empty();
      agencyUser = AgencyUser.empty();
    }
    fetchUserEmails();
    super.initState();
  }

  fetchUserEmails() async {
    try {
      QuerySnapshot querySnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_USERS)
          .get();

      setState(() {
        userEmails = querySnapshot.docs.map((doc) => doc['email']).toList();
      });
    } catch (e) {
      print(e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).primaryColorDark,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(origin == 'agency' ? "assets/various/agency-register-full.jpg" : "assets/various/professionals-register-full.jpg"),
            fit: BoxFit.cover,
          ),
        ),
        child: loading
            ? Center(
                child: CircularProgressIndicator(
                  color: Theme.of(context).primaryColor,
                ),
              )
            : registrationComplete
                ? _registrationComplete()
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        flex: 2,
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 40, vertical: 40),
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage(
                                  origin == "agency" ? "assets/various/agency-register-lateral.jpg" : "assets/various/professionals-register-lateral.jpg"),
                              fit: BoxFit.cover,
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Image.asset(
                                origin == "agency" ? "assets/logo-agenzie-white.png" : "assets/newarc_professionals_white.png",
                                width: 150,
                              ),
                              NarFormLabelWidget(
                                label:
                                    "Non vediamo l’ora\ndi fare grandi cose\ninsieme.",
                                fontWeight: 'bold',
                                fontSize: 30,
                                textColor: Colors.white,
                                textAlign: TextAlign.start,
                              ),
                              SizedBox(height: 20),
                              Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  NarFormLabelWidget(
                                    label: "Hai già un account?",
                                    fontWeight: "400",
                                    fontSize: 16,
                                    textColor: Colors.white,
                                    textAlign: TextAlign.start,
                                  ),
                                  
                                  TextButton(
                                    onPressed: () {
                                      Navigator.of(context)
                                          .pushNamedAndRemoveUntil(
                                        LoginPage.route,
                                        (route) => false,
                                      );
                                    },
                                    style: TextButton.styleFrom(
                                      padding: EdgeInsets.zero, // Removes default padding
                                    ),
                                    child: NarFormLabelWidget(
                                      label: "Accedi ora",
                                      textColor: Colors.white,
                                      fontSize: 16,
                                      fontWeight: '700',
                                      textDecoration: TextDecoration.underline,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 5,
                        child: Container(
                          padding: EdgeInsets.only(
                              left: 40, right: 40, top: 40, bottom: 0),
                          color: Colors.white,
                          child: PageView(
                            controller: _pageController,
                            physics: NeverScrollableScrollPhysics(),
                            children: [
                              RegisterFormTab(
                                formKey: _agencyInfoFormKey,
                                title: origin == "agency" ? "La tua agenzia" : "Crea il tuo account professionista",
                                isFirstTab: true,
                                isFinalTab: false,
                                isSkipable: false,
                                content: origin == "agency" ? _getSezioneDatiAgenzia() : _getSezioneDatiProfessionista(),
                                pageController: _pageController,
                                disabledNext:
                                    isNotFormValid(_agencyInfoFormKey),
                              ),
                              RegisterFormTab(
                                formKey: _credFormKey,
                                title: "Crea un account",
                                isFirstTab: false,
                                isFinalTab: false,
                                isSkipable: false,
                                content: _getSezioneAgencyCredentials(),
                                pageController: _pageController,
                                disabledNext: isNotFormValid(_credFormKey),
                              ),
                              RegisterFormTab(
                                formKey: _telephoneFormKey,
                                title: origin == "agency" ? "Telefono agenzia" : "Telefono per clienti",
                                isFirstTab: false,
                                isFinalTab: false,
                                isSkipable: false,
                                content: _getSezioneTelephone(),
                                pageController: _pageController,
                                disabledNext: isNotFormValid(_telephoneFormKey),
                              ),
                              RegisterFormTab(
                                formKey: new GlobalKey<FormState>(),
                                title: origin == "agency" ? "Logo agenzia" : "Logo aziendale",
                                isFirstTab: false,
                                isFinalTab: false,
                                isSkipable: true,
                                content: _getSezioneLogoAgenzia(),
                                pageController: _pageController,
                                disabledNext: isPictureUploaded(),
                              ),
                              RegisterFormTab(
                                formKey: _personaFormKey,
                                title: "Persona di riferimento",
                                isFirstTab: false,
                                isFinalTab: false,
                                isSkipable: false,
                                content: _getSezionePersonaRiferimento(),
                                pageController: _pageController,
                                disabledNext: isNotFormValid(_personaFormKey),
                              ),
                              RegisterFormTab(
                                formKey: _fatturazioneFormKey,
                                title: origin == "agency" ? "Fatturazione e pagamenti" : "Fatturazione",
                                isFirstTab: false,
                                isFinalTab: false,
                                isSkipable: origin == "agency" ? true : false,
                                content: _getSezioneFatturazione(),
                                pageController: _pageController,
                                disabledNext:
                                    isNotFormValid(_fatturazioneFormKey),
                              ),
                              RegisterFormTab(
                                formKey: _privacyFormKey,
                                title: "Pronto a iniziare?",
                                isFirstTab: false,
                                isFinalTab: true,
                                isSkipable: false,
                                disabledNext: origin == "agency" ? (!agency!.consentedToPrivacyPolicy || !agency!.acceptedTermsConditions) : (!professional!.consentedToPrivacyPolicy || !professional!.acceptedTermsConditions),
                                content: _getSezionePrivacyConsent(),
                                beforeNext: () async {
                                  if (!_privacyFormKey.currentState!
                                      .validate()) {
                                    return false;
                                  }
                                  print("Register completed");
                                  registrationComplete = await register();
                                  if (!registrationComplete) {
                                    Navigator.of(context)
                                          .pushNamedAndRemoveUntil(
                                        LoginPage.route,
                                        (route) => false,
                                      );
                                  } else {
                                    // If projectId exists and this is an agency registration, link the agency to the project
                                    if (projectId != null && origin == 'agency' && agency?.id != null) {
                                      bool linkingSuccess = await linkAgencyToProject(projectId!, agency!.id!);
                                      if (linkingSuccess) {
                                        isProjectLinked = true;
                                        print('Successfully linked agency ${agency!.id} to project $projectId');
                                      } else {
                                        print('Failed to link agency to project');
                                      }
                                    }
                                    registrationComplete = true;
                                  }
                                  setState(() {});
                                  return registrationComplete;
                                },
                                pageController: _pageController,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
      ),
    );
  }

  bool isPictureUploaded() {
    if (kDebugMode) {
      print(
          'profilePictureFilename: $profilePictureFilename - profilePicture: ${profilePicture.length}');
    }
    return profilePicture.length == 0;
  }

  bool isNotFormValid(GlobalKey<FormState> key) {
    return !(key.currentState?.validate() ?? false);
  }

  Widget _getSezionePrivacyConsent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Checkbox(
                value: origin == "agency" ? agency!.consentedToPrivacyPolicy : professional!.consentedToPrivacyPolicy,
                onChanged: (bool? value) {
                  if (origin == "agency") {
                    agency!.consentedToPrivacyPolicy = value!;
                    if (agency!.consentedToPrivacyPolicy) {
                      _privacyFormKey.currentState!.reset();
                    }
                  } else {
                    professional!.consentedToPrivacyPolicy = value!;
                    if (professional!.consentedToPrivacyPolicy) {
                      _privacyFormKey.currentState!.reset();
                    }
                  }
                  setState(() {});
                },
              ),
              const SizedBox(width: 10),
              NarFormLabelWidget(
                label:
                    "Acconsento all’utilizzo dei dati inseriti per la realizzazione di un account agenzia \nall’interno della piattaforma Newarc.",
                fontWeight: '600',
                fontSize: 14,
                textColor: AppColor.iconGreyColor,
              ),
            ],
          ),
        ),
        SizedBox(height: 10),
        Padding(
          padding: EdgeInsets.only(left:8),
          child: Row(
            children: [
              NarFormLabelWidget(
                label: "Leggi la ",
                fontWeight: '800',
                fontSize: 14,
                textColor: AppColor.iconGreyColor,
              ),
              NarLinkWidget(
                onClick: () {
                  html.window.open(
                    origin == "agency"
                      ? "https://www.newarc.it/privacy-policy-piattaforma-agenzie/"
                      : "https://www.newarc.it/privacy-policy-piattaforma-professionals/", 
                    "Newarc privacy policy");
                },
                text: "Privacy Policy",
                textDecoration: TextDecoration.underline,
                textColor: AppColor.iconGreyColor,
                fontSize: 14,
                fontWeight: '800',
              ),
            ],
          ),
        ),
        SizedBox(height:30),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Checkbox(
              value: origin == "agency" ? agency!.acceptedTermsConditions : professional!.acceptedTermsConditions,
              onChanged: (bool? value) {
                if (origin == "agency") {
                  agency!.acceptedTermsConditions = value!;
                  if (agency!.acceptedTermsConditions) {
                    _privacyFormKey.currentState!.reset();
                  }
                } else {
                  professional!.acceptedTermsConditions = value!;
                  if (professional!.acceptedTermsConditions) {
                    _privacyFormKey.currentState!.reset();
                  }
                }
                setState(() {});
              },
            ),
            const SizedBox(width: 10),
            NarFormLabelWidget(
              label:
                  "Ho preso visione e accettato i Termini e Condizioni di utilizzo della piattaforma Newarc.",
              fontWeight: '600',
              fontSize: 14,
              textColor: AppColor.iconGreyColor,
            ),
          ],
        ),
        SizedBox(height: 10),
        Padding(
          padding: EdgeInsets.only(left:8),
          child: Row(
            children: [
              NarFormLabelWidget(
                label: "Leggi i ",
                fontWeight: '800',
                fontSize: 14,
                textColor: AppColor.iconGreyColor,
              ),
              NarLinkWidget(
                onClick: () {
                  html.window.open(
                    origin == "agency" 
                      ? "https://www.newarc.it/termini-e-condizioni-piattaforma-agenzie/" 
                      : "https://www.newarc.it/termini-e-condizioni-piattaforma-professionals/", 
                    "Newarc terms conditions");
                },
                text: "Termini & Condizioni",
                textDecoration: TextDecoration.underline,
                textColor: AppColor.iconGreyColor,
                fontSize: 14,
                fontWeight: '800',
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<bool> register() async {
    dynamic toBeRegistered = origin == "agency" ? agency : professional;
    if(toBeRegistered == null){
      await showToastNotification(context, "Errore durante la registrazione. Riprova più tardi",
        duration: 5,
        error: true);
      return false;
    }


    if (!toBeRegistered.consentedToPrivacyPolicy || !toBeRegistered.acceptedTermsConditions) {
      await showToastNotification(context, "Devi accettare la privacy policy",
        duration: 5,
        error: true);
      return false;
    } else {
      setState(() {
        loading = true;
      });

      // if (kDebugMode) {
      //   print(agency!.toMap());
      //   agencyUser!.type = 'agency';
      //   agencyUser!.role = 'agency';
      //   print({'agency user', agencyUser!.toMap()});
      // }

      try {
        UserCredential userCredential = await FirebaseAuth.instance
            .createUserWithEmailAndPassword(
                email: origin == "agency" ? agency!.email! : professional!.email!, 
                password: password!);
        bool verification_response =
            await sendVerificationLinkEmail(userCredential.user!, origin);

        if (!verification_response) {
          await showToastNotification(context,
              "Si è verificato un errore nell'invio della mail di verifica. Riprova più tardi.",
              duration: 5,
              error: true);
          setState(() {
            loading = false;
          });
          return false;
        }
        if (origin == "agency") {
          String agencyId = await writeDocument('agencies', agency!.toMap());
          agency!.id = agencyId;
          agencyUser!.id = userCredential.user!.uid;
          agencyUser!.email = userCredential.user!.email;
          agencyUser!.phone = agency!.phone;
          agencyUser!.agencyId = agencyId;
          agencyUser!.agency = agency;
          agencyUser!.type = 'agency';
          agencyUser!.role = 'agency';
          if (profilePicture.length > 0) {
            String __profilePictureFilename =
                'agency-profile' + p.extension(profilePicture[0].name);
            await uploadProfilePicture('agencies/', agency!.id!, __profilePictureFilename, profilePicture[0]);

            final resizedFile = await resizeXFile(profilePicture[0],width: 240,height: 240,quality: 60,customFileName: "agency-profile_thumbnail");
            await uploadProfilePicture('${appConfig.COLLECT_AGENCIES}/',agency!.id!, resizedFile.name, resizedFile);
            agencyUser!.profilePicture = __profilePictureFilename;
            profilePicture.clear();
            profilePictureFilename = __profilePictureFilename;
          } else {
            profilePictureFilename = agencyUser!.profilePicture;
          }

          String agencyUserId = await writeDocument('users', agencyUser!.toMap(),
              id: agencyUser!.id);
          agencyUser!.id = agencyUserId;
        } else {
          String professionalId = await writeDocument('professionals', professional!.toMap());
          professional!.id = professionalId;
          professionalsUser!.id = userCredential.user!.uid;
          professionalsUser!.email = userCredential.user!.email;
          professionalsUser!.phone = professional!.phone;
          professionalsUser!.professionalId = professionalId;
          professionalsUser!.professional = professional;
          professionalsUser!.type = 'professionals';
          professionalsUser!.role = 'professionals';
          professionalsUser!.isActive = true;
          professionalsUser!.isArchived = false;
          if (profilePicture.length > 0) {
            String __profilePictureFilename =
                'professional-profile' + p.extension(profilePicture[0].name);
            await uploadProfilePicture('professionals/', professionalsUser!.id!, __profilePictureFilename, profilePicture[0]);
            professionalsUser!.profilePicture = __profilePictureFilename;
            profilePicture.clear();
            profilePictureFilename = __profilePictureFilename;
          } else {
            profilePictureFilename = professionalsUser!.profilePicture;
          }
          String professionalUserId = await writeDocument('users', professionalsUser!.toMap(),
              id: professionalsUser!.id);
          professionalsUser!.id = professionalUserId;
        }
        await showToastNotification(
          context,
          "Registrazione completata. Controlla la tua mail per confermare l'account",
          duration: 5
        );

        setState(() {
          loading = false;
        });
        return true;
      } on FirebaseAuthException catch (e) {
        if (e.code == 'email-already-in-use') {
          await showToastNotification(context,
              "Esiste già un account associato a questa mail. Effettua il login o crea un nuovo account.",
              duration: 5,
              error: true);
        }
        if (e.code == 'invalid-email') {
          await showToastNotification(context, "La mail fornita non è valida",
              duration: 5,
              error: true);
        } else {
          await showToastNotification(
              context, 'Si è verificato un errore interno. Riprova più tardi',
              duration: 5,
              error: true);
        }
        setState(() {
          loading = false;
        });
      } catch (e) {
        await showToastNotification(
          context, 
          e.toString(), 
          error: true,
          duration: 5);
        setState(() {
          loading = false;
        });
      }
    }
    return false;
  }

  /// Links the newly created agency to the project specified in the URL parameter
  Future<bool> linkAgencyToProject(String projectId, String agencyId) async {
    try {
      // First, validate that the project exists
      DocumentSnapshot projectDoc = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_IMMAGINA_PROJECTS)
          .doc(projectId)
          .get();

      if (!projectDoc.exists) {
        print('Project with ID $projectId does not exist');
        return false;
      }

      // Update the project with the agency ID and clear suggested agency
      await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_IMMAGINA_PROJECTS)
          .doc(projectId)
          .update({
        'agencyId': agencyId,
        'suggestedAgency.converted': true,
        'suggestedAgency.conversiontimestamp': DateTime.now().millisecondsSinceEpoch,
      });

      return true;
    } catch (e) {
      print('Error linking agency to project: $e');
      return false;
    }
  }

  void goLogin() {
    Navigator.of(context).pushNamed(LoginPage.route);
  }

  Widget _registrationComplete() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 20),
        Image.asset(
          origin == "agency" ? 'assets/logo-agenzie-white.png' : 'assets/newarc_professionals_white.png',
          height: 50,
        ),
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                origin == "agency" ?'assets/various/Livello_3.png' : 'assets/various/professional_success_icon.png',
                height: 100,
              ),
              SizedBox(height: 40),
              NarFormLabelWidget(
                label: "Benvenuto in Newarc!",
                fontWeight: 'bold',
                fontSize: 35,
                textColor: Colors.white,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 40),
              NarFormLabelWidget(
                label:
                    "Clicca sul pulsante nell’email che ti abbiamo inviato \nper confermare la creazione del tuo account.",
                fontSize: 14,
                textColor: Colors.white,
                fontWeight: '500',
                textAlign: TextAlign.center,
              ),
              // // Show project linking success message if applicable
              // if (isProjectLinked && projectId != null) ...[
              //   SizedBox(height: 20),
              //   Container(
              //     padding: EdgeInsets.all(16),
              //     margin: EdgeInsets.symmetric(horizontal: 40),
              //     decoration: BoxDecoration(
              //       color: Colors.green.withOpacity(0.1),
              //       borderRadius: BorderRadius.circular(8),
              //       border: Border.all(color: Colors.green.withOpacity(0.3)),
              //     ),
              //     child: NarFormLabelWidget(
              //       label: "✓ La tua agenzia è stata collegata con successo al progetto!",
              //       fontSize: 14,
              //       textColor: Colors.white,
              //       fontWeight: '600',
              //       textAlign: TextAlign.center,
              //     ),
              //   ),
              // ],
              SizedBox(height: 40),
              SizedBox(
                width: 250,
                child: BaseNewarcButton(
                  buttonText: "Vai al login",
                  onPressed: goLogin,
                  color: origin == "agency" ? Theme.of(context).primaryColor : Color(0xff499B79),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _getSezioneDatiAgenzia() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 100,
          width: MediaQuery.of(context).size.width * 0.35,
          child: CustomTextFormField(
              isExpanded: false,
              label: "Nome agenzia",
              controller: nomeAgenziaController,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Inserisci una denominazione valida';
                }
                return null;
              },
              onChangedCallback: (String value) {
                setState(() {
                  agency!.name = value;
                });
              }),
        ),
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.35,
          child: AddressSearchBar(
            controller: indirizzoAgenziaController,
            label: "Inserisci l’indirizzo completo",
            initialAddress: agency?.fullAddress,
            onPlaceSelected: (selectedPlace) {
              debugPrint('Selected place: \n$selectedPlace');
              final Map<String, dynamic> address = selectedPlace['place'] ?? {};
              final adr = BaseAddressInfo.fromMap(address);
              var status = adr.isValidAddress();
              if (kDebugMode) {
                print('Status Addrs: $status');
              }
              Copy(agency!, adr);
              agency!.fullAddress = adr.fullAddress;
              agency!.address = adr.fullAddress;
              indirizzoAgenziaController.text = adr.fullAddress??'';
              setState(() {});
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Inserisci un indirizzo valido';
              }
              // if (!agency!.isValidAddress()) {
              //   return 'Inserisci un indirizzo valido, includendo almeno il nome della via e il numero civico';
              // }
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _getSezioneDatiProfessionista() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 100,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Switch(
                value: professionalHasPIva,
                activeTrackColor: Theme.of(context).primaryColor,
                inactiveTrackColor: Theme.of(context).primaryColorLight,
                thumbColor: WidgetStateProperty.all(Colors.white),
                onChanged: (bool value) {
                  setState(() {
                    professionalHasPIva = value;
                    professional!.hasPIva = value;
                  });
                },
              ),
              SizedBox(width: 10),
              NarFormLabelWidget(
                label: "Registrazione con p.iva",
                fontSize: 14,
                fontWeight: '500',
                textColor: professionalHasPIva ? AppColor.black : AppColor.iconGreyColor,
              ),
            ],
          ),
        ),
        professionalHasPIva
        ? SizedBox(
          height: 100,
          width: MediaQuery.of(context).size.width * 0.35,
          child: CustomTextFormField(
            isExpanded: false,
            label: "Nome azienda",
            controller: nomeAgenziaController,
            validator: (value) {
              if (professionalHasPIva) {
                if (value == null || value.isEmpty) {
                  return 'Inserisci una denominazione valida';
                }
                return null;
              }
            },
            onChangedCallback: (String value) {
              setState(() {
                professional!.companyName = value;
              });
            }),
        )
        : SizedBox.shrink(),
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.35,
          child: NarSelectBoxWidget(
            label: "Professione",
            options: appConst.professionalsTypesList,
            controller: professionController,
            validationType: 'required',
            parametersValidate: 'Required!',
            onChanged: (String value) {
              setState(() {
                professional!.profession = value;
              });
            },
          )
        ),
      ],
    );
  }

  Widget _getSezionePersonaRiferimento() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.35,
          child: CustomTextFormField(
            isExpanded: false,
            controller: refPersonName,
            label: "Nome persona riferimento",
            onChangedCallback: (String value) {
              if (origin == "agency") {
                setState(() {
                  agencyUser!.name = value.toCapitalized();
                  agency!.referencePerson.name = value.toCapitalized();
                });
              } else {
                setState(() {
                  professionalsUser!.name = value.toCapitalized();
                  professional!.contactPersonInfo.name = value.toCapitalized();
                });
              }
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Inserisci un nome valido';
              }
              return null;
            },
          ),
        ),
        SizedBox(height: 10),
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.35,
          child: CustomTextFormField(
            isExpanded: false,
            controller: refPersonLastName,
            label: "Cognome persona riferimento",
            onChangedCallback: (String value) {
              if (origin == "agency") {
                setState(() {
                  agencyUser!.surname = value.toCapitalized();
                  agency!.referencePerson.surname = value.toCapitalized();
                });
              } else {
                setState(() {
                  professionalsUser!.surname = value.toCapitalized();
                  professional!.contactPersonInfo.surname = value.toCapitalized();
                });
              }
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Inserisci un cognome valido';
              }
              return null;
            },
          ),
        ),
        SizedBox(height: 20),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.35,
              child: CustomTextFormField(
                isExpanded: false,
                controller: refPersonPhone,
                label: "Telefono persona riferimento",
                isNullable: !useAgencyRefTelephone,
                enabled: !useAgencyRefTelephone,
                onChangedCallback: (String value) {
                  if (origin == "agency") {
                    setState(() {
                      agency!.referencePerson.phone = value;
                      agencyUser!.phone = value;
                    });
                  } else {
                    setState(() {
                      professional!.contactPersonInfo.phone = value;
                      professionalsUser!.phone = value;
                    });
                  }
                },
                inputFormatters: [phoneNumberMaskFormatterIt],
                validator: (value) {
                  //^(([+]|00)39|0)?((3[1-6][0-9]))(\d{7})$
                  if (value == null || value.isEmpty || value.length < 13) {
                    return 'Inserisci un numero di telefono valido';
                  }
                },
              ),
            ),
            Expanded(
              child: _buildSwitch(
                origin == "Agency" ? "Usa telefono agenzia" : "Usa telefono clienti",
                useAgencyRefTelephone,
                (bool value) {
                  if (origin == "agency") {
                    setState(() {
                      useAgencyRefTelephone = value;
                      if (value) {
                        agency!.referencePerson.phone = agency!.phone;
                        refPersonPhone.text = agency!.referencePerson.phone ?? '';
                      } else {
                        agency!.referencePerson.phone = refPersonPhone.text;
                      }
                    });
                  } else {
                    setState(() {
                      useAgencyRefTelephone = value;
                      if (value) {
                        professional!.contactPersonInfo.phone = professional!.phone;
                        refPersonPhone.text = professional!.contactPersonInfo.phone ?? '';
                      } else {
                        professional!.contactPersonInfo.phone = refPersonPhone.text;
                      }
                    });
                  }
                },
              ),
            ),
          ],
        ),
        SizedBox(height: 20),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.35,
              child: CustomTextFormField(
                isExpanded: false,
                controller: refPersonMail,
                label: "Email persona riferimento",
                enabled: !useRefAgencyEmail,
                isNullable: !useRefAgencyEmail,
                onChangedCallback: (String value) {
                  if (origin == "agency") {
                    setState(() {
                      agency!.referencePerson.email = value;
                    });
                  } else {
                    setState(() {
                      professional!.contactPersonInfo.email = value;
                    });
                  }
                },
                validator: emailValidator,
              ),
            ),
            Expanded(
              child: _buildSwitch(
                origin == "agency" ? "Usa Email agenzia" : "Usa Email account",
                useRefAgencyEmail,
                (bool value) {
                  if (origin == "agency") {
                    setState(() {
                      useRefAgencyEmail = value;
                      if (value) {
                        agency!.referencePerson.email = agency!.email;
                        refPersonMail.text = agency!.referencePerson.email ?? '';
                      } else {
                        agency!.referencePerson.email = refPersonMail.text;
                      }
                    });
                  } else {
                    setState(() {
                      useRefAgencyEmail = value;
                      if (value) {
                        professional!.contactPersonInfo.email = professional!.email;
                        refPersonMail.text = professional!.contactPersonInfo.email ?? '';
                      } else {
                        professional!.contactPersonInfo.email = refPersonMail.text;
                      }
                    });
                  }
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  emailValidator(value) {
    if (value == null || value.isEmpty) {
      return 'Inserisci una mail valida';
    }

    if (!RegExp(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
        .hasMatch(value)) {
      return 'Inserisci una mail valida';
    }

    if (userEmails.contains(value)) {
      return 'Esiste già un account associato a questa mail. Effettua il login o crea un nuovo account.';
    }

    return null;
  }

  Widget _buildSwitch(String label, bool value, void Function(bool) onChanged) {
    return SizedBox(
      width: label.length * 10.0 + 50,
      child: Opacity(
        opacity: value ? 1 : 0.5,
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Switch(
                value: value,
                activeTrackColor: Theme.of(context).primaryColor,
                inactiveTrackColor: Theme.of(context).primaryColorLight,
                thumbColor: WidgetStateProperty.all(Colors.white),
                onChanged: onChanged,
              ),
              NarFormLabelWidget(
                label: label,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _getSezioneFatturazione() {
    var semicolonWidth = MediaQuery.of(context).size.width * 0.25;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: origin == "agency" ? [
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                SizedBox(
                  width: semicolonWidth*.64,
                  child: CustomTextFormField(
                    isExpanded: false,
                    controller: billingFullName,
                    label: "Denominazione",
                    onChangedCallback: (String value) {
                      setState(() {
                        agency!.legalEntity = value;
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Inserisci una denominazione valida';
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(width: semicolonWidth * 0.01),
                SizedBox(
                  width: semicolonWidth * 0.35,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      NarFormLabelWidget(
                        label: "Forma giuridica",
                        fontWeight: "500",
                        fontSize: 13,
                        textColor: const Color(0xff696969)
                      ),
                      SizedBox(height: 4),
                      NarSelectBoxWidget(
                        options: appConst.supplierFormationTypesList,
                        controller: billingFormationType,
                        validationType: 'required',
                        parametersValidate: 'Required!',
                        onChanged: (String value) {
                          setState(() {
                            agency!.formationType = value;
                          });
                        },
                      ),
                    ],
                  )
                )
              ],
            ),
            SizedBox(height: 20),
            SizedBox(
              width: semicolonWidth,
              child: CustomTextFormField(
                isExpanded: false,
                controller: billingFiscalCode,
                label: "Codice Fiscale",
                inputFormatters: [codiceFiscaleMaskFormatter],
                enabled: billingFormationType.text == 'Impresa individuale',
                validator: (value) {
                  if (billingFormationType.text != 'Impresa individuale') {
                    return null;
                  } else if (value == null || value.isEmpty) {
                    return 'Inserisci un codice fiscale valido';
                  }
                  return null;
                },
                onChangedCallback: (String value) {
                  setState(() {
                    agency!.fiscalCode = value;
                  });
                },
              )
            ),
            SizedBox(height: 20),
            SizedBox(
              width: semicolonWidth,
              child: CustomTextFormField(
                isExpanded: false,
                controller: billingIva,
                label: "Partita IVA",
                inputFormatters: [ivaMaskFormatter],
                validator: (value) {
                  if (value == null ||
                      value.isEmpty ||
                      !RegExp(r"^(IT)?[0-9]{11}$").hasMatch(value)) {
                    return 'Inserisci una partita IVA valida';
                  }
        
                  return null;
                },
                onChangedCallback: (String value) {
                  setState(() {
                    agency!.vat = value;
                  });
                },
              ),
            ),
          ],
        ),
        SizedBox(width: 30),
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: semicolonWidth,
              child: AddressSearchBar(
                controller: billingSedeLegal,
                label: "Sede legale",
                onPlaceSelected: (selectedPlace) {
                  debugPrint('Selected place: \n$selectedPlace');
                  Map<String, dynamic> address = selectedPlace['place'] ?? {};
                  final adr = BaseAddressInfo.fromMap(address);
                  agency!.sedeLegaleFull = adr;
                  agency!.sedeLegale = adr.fullAddress;
                  billingSedeLegal.text = adr.fullAddress??'';
                  setState(() {});
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Inserisci un indirizzo valido';
                  }
                  // if (agency?.sedeLegaleFull?.isValidAddress() != true) {
                  //   return 'Inserisci un indirizzo valido, includendo almeno il nome della via e il numero civico';
                  // }
                  return null;
                },
              ),
            ),
            SizedBox(height: 20),
            SizedBox(
              width: semicolonWidth,
              child: CustomTextFormField(
                isExpanded: false,
                controller: billingInvoiceCode,
                label: "Codice fatturazione elettronico (non obbligatorio)",
                onChangedCallback: (String value) {
                  setState(() {
                    agency!.sdi = value;
                  });
                },
                validator: (value) {
                  // if (value == null || value.isEmpty) {
                  //   return 'Inserisci un codice valido';
                  // }
                  debugPrint('Value: not required $value');
                  return null;
                },
              ),
            ),
            SizedBox(height: 20),
            SizedBox(
              width: semicolonWidth,
              child: CustomTextFormField(
                isExpanded: false,
                controller: billingIban,
                label: "IBAN",
                inputFormatters: [ibanMaskFormatter],
                onChangedCallback: (String value) {
                  setState(() {
                    agency!.iban = value.toUpperCase();
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Inserisci un IBAN valido';
                  }
                  return null;
                },
              ),
            ),
          ],
        )
      ] : professionalHasPIva ? [
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: semicolonWidth,
              child: CustomTextFormField(
                isExpanded: false,
                controller: billingFullName,
                label: "Denominazione",
                onChangedCallback: (String value) {
                  setState(() {
                    professional!.legalEntity = value;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Inserisci una denominazione valida';
                  }
                  return null;
                },
              ),
            ),
            /*Row(
              children: [
                SizedBox(
                  width: semicolonWidth*.64,
                  child: CustomTextFormField(
                    isExpanded: false,
                    controller: billingFullName,
                    label: "Denominazione",
                    onChangedCallback: (String value) {
                      setState(() {
                        professional!.legalEntity = value;
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Inserisci una denominazione valida';
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(width: semicolonWidth * 0.01),
                SizedBox(
                  width: semicolonWidth * 0.35,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      NarFormLabelWidget(
                        label: "Forma giuridica",
                        fontWeight: "500",
                        fontSize: 13,
                        textColor: const Color(0xff696969)
                      ),
                      SizedBox(height: 4),
                      NarSelectBoxWidget(
                        options: appConst.supplierFormationTypesList,
                        controller: billingFormationType,
                        validationType: 'required',
                        parametersValidate: 'Required!',
                        onChanged: (String value) {
                          setState(() {
                            professional!.formationType = value;
                          });
                        },
                      ),
                    ],
                  )
                )
              ],
            ),*/
            SizedBox(height: 20),
            SizedBox(
              width: semicolonWidth,
              child: CustomTextFormField(
                isExpanded: false,
                controller: billingFiscalCode,
                label: "Codice Fiscale",
                inputFormatters: [codiceFiscaleMaskFormatter],
                enabled: billingFormationType.text == 'Impresa individuale',
                validator: (value) {
                  if (billingFormationType.text != 'Impresa individuale') {
                    return null;
                  } else if (value == null || value.isEmpty) {
                    return 'Inserisci un codice fiscale valido';
                  }
                  return null;
                },
                onChangedCallback: (String value) {
                  setState(() {
                    professional!.fiscalCode = value;
                  });
                },
              )
            ),
            SizedBox(height: 20),
            SizedBox(
              width: semicolonWidth,
              child: CustomTextFormField(
                isExpanded: false,
                controller: billingIva,
                label: "Partita IVA",
                inputFormatters: [ivaMaskFormatter],
                validator: (value) {
                  if (value == null ||
                      value.isEmpty ||
                      !RegExp(r"^(IT)?[0-9]{11}$").hasMatch(value)) {
                    return 'Inserisci una partita IVA valida';
                  }
        
                  return null;
                },
                onChangedCallback: (String value) {
                  setState(() {
                    professional!.vat = value;
                  });
                },
              ),
            ),
            SizedBox(height: 20),
            Visibility(
              visible: false,
              maintainAnimation: true,
              maintainState: true,
              maintainSize: true,
              child: SizedBox(
                width: semicolonWidth,
                child: CustomTextFormField(
                  isExpanded: false,
                  controller: billingIban,
                  label: "IBAN",
                  inputFormatters: [ibanMaskFormatter],
                  onChangedCallback: (String value) {
                  },
                  validator: (value) {
                    return null;
                  },
                ),
              ),
            ),
          ],
        ),
        SizedBox(width: 30),
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: semicolonWidth,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  NarFormLabelWidget(
                    label: "Forma giuridica",
                    fontWeight: "500",
                    fontSize: 13,
                    textColor: const Color(0xff696969)
                  ),
                  SizedBox(height: 4),
                  NarSelectBoxWidget(
                    options: appConst.supplierFormationTypesList,
                    controller: billingFormationType,
                    validationType: 'required',
                    parametersValidate: 'Required!',
                    onChanged: (String value) {
                      setState(() {
                        professional!.formationType = value;
                      });
                    },
                  ),
                ],
              )
            ),
            SizedBox(height: 20),
            SizedBox(
              width: semicolonWidth,
              child: AddressSearchBar(
                controller: billingSedeLegal,
                label: "Sede legale",
                onPlaceSelected: (selectedPlace) {
                  debugPrint('Selected place: \n$selectedPlace');
                  Map<String, dynamic> address = selectedPlace['place'] ?? {};
                  final adr = BaseAddressInfo.fromMap(address);
                  professional!.legalAddressInfo = adr;
                  billingSedeLegal.text = adr.fullAddress ?? '';
                  setState(() {});
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Inserisci un indirizzo valido';
                  }
                  // if (professional?.legalAddressInfo?.isValidAddress() != true) {
                  //   return 'Inserisci un indirizzo valido, includendo almeno il nome della via e il numero civico';
                  // }
                  return null;
                },
              ),
            ),
            SizedBox(height: 20),
            SizedBox(
              width: semicolonWidth,
              child: CustomTextFormField(
                isExpanded: false,
                controller: billingInvoiceCode,
                label: "Codice fatturazione elettronico (non obbligatorio)",
                onChangedCallback: (String value) {
                  setState(() {
                    professional!.sdi = value;
                  });
                },
                validator: (value) {
                  debugPrint('Value: not required $value');
                  return null;
                },
              ),
            ),
            SizedBox(height: 20),
            Visibility(
              visible: false,
              maintainAnimation: true,
              maintainState: true,
              maintainSize: true,
              child: SizedBox(
                width: semicolonWidth,
                child: CustomTextFormField(
                  isExpanded: false,
                  controller: billingIban,
                  label: "IBAN",
                  inputFormatters: [ibanMaskFormatter],
                  onChangedCallback: (String value) {
                  },
                  validator: (value) {
                    return null;
                  },
                ),
              ),
            ),
          ],
        )
      ] : [
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: semicolonWidth,
              child: CustomTextFormField(
                isExpanded: false,
                controller: billingFiscalCode,
                label: "Codice Fiscale",
                inputFormatters: [codiceFiscaleMaskFormatter],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Inserisci un codice fiscale valido';
                  }
                  return null;
                },
                onChangedCallback: (String value) {
                  setState(() {
                    professional!.fiscalCode = value;
                  });
                },
              )
            ),
            SizedBox(height: 20),
            SizedBox(
              width: semicolonWidth,
              child: AddressSearchBar(
                controller: billingSedeLegal,
                label: "Indirizzo di residenza",
                onPlaceSelected: (selectedPlace) {
                  debugPrint('Selected place: \n$selectedPlace');
                  Map<String, dynamic> address = selectedPlace['place'] ?? {};
                  final adr = BaseAddressInfo.fromMap(address);
                  professional!.legalAddressInfo = adr;
                  billingSedeLegal.text = adr.fullAddress ?? '';
                  setState(() {});
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Inserisci un indirizzo valido';
                  }
                  // if (professional?.legalAddressInfo?.isValidAddress() != true) {
                  //   return 'Inserisci un indirizzo valido, includendo almeno il nome della via e il numero civico';
                  // }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _getSezioneTelephone() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(
            bottom: 20.0, 
            left: 8.0, 
            // right: 8.0
          ),
          child: SizedBox(
            width: MediaQuery.of(context).size.width * 0.35,
            child: CustomTextFormField(
              isExpanded: false,
              controller: numeroTelefonoController,
              label: "Dove potranno chiamarti i clienti?",
              inputFormatters: [phoneNumberMaskFormatterIt],
              onChangedCallback: (String value) {
                setState(() {
                  origin == "agency" ? agency!.phone = value : professional!.phone = value;
                });
              },
              validator: (value) {
                //^(([+]|00)39|0)?((3[1-6][0-9]))(\d{7})$
                if (value == null || value.isEmpty || value.length < 13) {
                  return 'Inserisci un numero di telefono valido';
                }
              },
            ),
          ),
        ),
        origin == "professionals" 
        ? Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.35,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Theme.of(context).primaryColor.withOpacity(0.1),
            ),
            padding: EdgeInsets.all(12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/icons/info_icon.png',
                  height: 30,
                ),
                SizedBox(width: 10),
                Expanded(
                  child: NarFormLabelWidget(
                    label:
                        "Questo numero sarà visibile ai clienti per richiederti informazioni sugli immobili in vendita sul nostro sito.",
                    fontSize: 14,
                    fontWeight: '500',
                    textColor: Theme.of(context).primaryColor,
                    overflow: TextOverflow.clip,
                  ),
                ),
              ],
            ),
          ),
        )
        : SizedBox.shrink(),
      ],
    );
  }

  Widget _getSezioneLogoAgenzia() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            NarImagePickerWidget(
                allowMultiple: false,
                imagesToDisplayInList: 0,
                removeButton: false,
                removeButtonText: 'rimuovi',
                uploadButtonPosition: 'back',
                showMoreButtonText: '+ espandi',
                removeButtonPosition: 'bottom',
                displayFormat: 'row',
                imageDimension: 120,
                imageBorderRadius: 120,
                borderRadius: 7,
                fontSize: 14,
                fontWeight: '600',
                text: 'Carica logo',
                borderSideColor: Theme.of(context).primaryColor,
                hoverColor: Color.fromRGBO(133, 133, 133, 1),
                images: profilePicture,
                pageContext: context,
                storageDirectory: origin == "agency" ? 'agencies/' : 'professionals/',
                preloadedImages: [],
                firebaseId: origin == "agency" ? agency!.id : professional!.id,
                onImageChange: (images) {
                  setState(() {
                    debugPrint('Images: ${images.length}');
                    profilePicture = images;
                    profilePictureFilename = images[0].name;
                  });
                },
                removeExistingOnChange: true),
          ],
        ),
        SizedBox(height: 30),
        Container(
          width: MediaQuery.of(context).size.width * 0.35,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Theme.of(context).primaryColor.withOpacity(0.1),
          ),
          padding: EdgeInsets.all(12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset(
                'assets/icons/thumbs-up.png',
                height: 30,
              ),
              SizedBox(width: 10),
              Expanded(
                child: NarFormLabelWidget(
                  label:
                      "Se possibile, carica un logo con fondale bianco o trasparente.",
                  fontWeight: '500',
                  textColor: Theme.of(context).primaryColor,
                  overflow: TextOverflow.clip,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _getSezioneAgencyCredentials() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 40.0, left: 8.0, right: 8.0),
          child: SizedBox(
            width: MediaQuery.of(context).size.width * 0.35,
            child: CustomTextFormField(
              isExpanded: false,
              controller: emailAgenziaController,
              label: "Email",
              validator: emailValidator,
              onChangedCallback: (String value) {
                setState(() {
                  origin == "agency" ? agency!.email = value : professional!.email = value;
                });
              },
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: SizedBox(
            width: MediaQuery.of(context).size.width * 0.35,
            child: CustomTextFormField(
              isExpanded: false,
              controller: newPassword,
              isObscureText: !pwdVisible,
              label: "Password",
              suffixIcon: IconButton(
                icon: Icon(
                  !pwdVisible
                      ? Icons.visibility_outlined
                      : Icons.visibility_off_outlined,
                  color: Theme.of(context).primaryColorDark,
                ),
                onPressed: () {
                  setState(() {
                    pwdVisible = !pwdVisible;
                  });
                },
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Inserisci una password valida';
                }

                if (!RegExp(
                        r"^(?=.*[a-z])(?=.*[A-Z])(?=.*[@$!%*?&._#:~+^°§<>-])[A-Za-z\d@$!%*?&._#:~+^°§<>-]{8,}$")
                    .hasMatch(value)) {
                  return 'Minimo 8 caratteri, uno maiuscolo,\nuno minuscolo ed uno speciale';
                }

                return null;
              },
              onChangedCallback: (String _password) {
                setState(() {
                  password = _password;
                });
              },
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: SizedBox(
            width: MediaQuery.of(context).size.width * 0.35,
            child: CustomTextFormField(
              isExpanded: false,
              controller: confirmPassword,
              isObscureText: !pwdVisible,
              label: "Ripeti password",
              suffixIcon: IconButton(
                icon: Icon(
                  !pwdVisible
                      ? Icons.visibility_outlined
                      : Icons.visibility_off_outlined,
                  color: Theme.of(context).primaryColorDark,
                ),
                onPressed: () {
                  setState(() {
                    pwdVisible = !pwdVisible;
                  });
                },
              ),
              onChangedCallback: (String value) {
                setState(() {});
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Inserisci una password valida';
                }

                if (value != password) {
                  return 'Le password non combaciano';
                }

                return null;
              },
            ),
          ),
        ),
      ],
    );
  }
}
