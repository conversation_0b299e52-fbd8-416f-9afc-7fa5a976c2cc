import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

class ProjectVirtualTourDetails extends StatefulWidget {
  final NewarcProject? project;
  //final List<bool>? isInputChangeDetected;

  const ProjectVirtualTourDetails({Key? key, this.project}) : super(key: key);

  @override
  State<ProjectVirtualTourDetails> createState() =>
      _ProjectVirtualTourDetails();
}

class _ProjectVirtualTourDetails extends State<ProjectVirtualTourDetails> {
  bool loading = false;
  TextEditingController? virtualTourLinkController =
      new TextEditingController();

  String progressMessage = '';

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    setInitialValues();

    //virtualTourLinkController!.addListener(_checkForChanges);
  }

  @override
  void dispose() {
    //virtualTourLinkController!.removeListener(_checkForChanges);

    super.dispose();
  }

  @protected
  void didUpdateWidget(ProjectVirtualTourDetails oldWidget) {
    super.didUpdateWidget(oldWidget);
    setInitialValues();
  }

  setInitialValues() {
    try {
      virtualTourLinkController!.text = widget.project!.virtualTourLink!;
    } catch (e, s) {
      print({e, s});
    }
  }

  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      child: loading == true
          ? NarFormLabelWidget(label: 'Loading')
          : Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: Form(
                    key: _formKey,
                    child: ListView(
                      children: [
                        SizedBox(height: 20),
                        NarFormLabelWidget(
                          label: 'Tour virtuale',
                          fontSize: 20,
                          fontWeight: 'bold',
                        ),
                        SizedBox(height: 30),
                        Container(
                            // color: Colors.grey,
                            // width: 200,
                            padding: EdgeInsets.symmetric(
                                vertical: 20, horizontal: 15),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                width: 1,
                                color: Color.fromRGBO(230, 230, 230, 1),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    CustomTextFormField(
                                        flex: 1,
                                        label: 'Link',
                                        hintText: "",
                                        controller: virtualTourLinkController,
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'Required';
                                          }
                                          return null;
                                        }),
                                  ],
                                ),
                              ],
                            )),
                        SizedBox(height: 15),
                        SizedBox(height: 25),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            NarFormLabelWidget(label: progressMessage),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                BaseNewarcButton(
                                  buttonText: "Salva",
                                  onPressed: () async {
                                    setState(() {
                                      progressMessage =
                                          'Salvataggio in corso...';
                                    });

                                    widget.project!.virtualTourLink =
                                        virtualTourLinkController!.text;
                                    final FirebaseFirestore _db =
                                        FirebaseFirestore.instance;

                                    try {
                                      await _db
                                          .collection(
                                              appConfig.COLLECT_NEWARC_PROJECTS)
                                          .doc(widget.project!.id)
                                          .update(widget.project!.toMap());

                                      setState(() {
                                        progressMessage = 'Saved!';
                                      });
                                    } catch (e) {
                                      setState(() {
                                        progressMessage =
                                            'Errore: ' + e.toString();
                                      });
                                    }
                                  },
                                )
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
