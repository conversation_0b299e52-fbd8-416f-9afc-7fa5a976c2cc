import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:image_picker/image_picker.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/file-picker.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class ProjectVideos extends StatefulWidget {
  final NewarcProject? project;
  final List? allVideoFiles;

  const ProjectVideos({
    Key? key,
    this.project,
    this.allVideoFiles,
  }) : super(key: key);

  @override
  State<ProjectVideos> createState() => _ProjectVideosState();
}

class _ProjectVideosState extends State<ProjectVideos> {
  TextEditingController? contScouter = new TextEditingController();
  // String progressMessage = '';
  // final List mediaFiles = [];
  bool openDialog = false;
  final List<Widget>? displayInlineWidget = [
    new NarFormLabelWidget(
      label: 'load',
    )
  ];
  final List<String> progressMessage = [''];

  @override
  void initState() {
    super.initState();
    setInitialValues();
  }

  @protected
  void didUpdateWidget(ProjectVideos oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  setInitialValues() {
    contScouter!.text = '';
  }

  onPreRennoUploadCompleted() async {
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    widget.project!.videoFiles = widget.allVideoFiles;

    try {
      await _db
          .collection(appConfig.COLLECT_NEWARC_PROJECTS)
          .doc(widget.project!.id)
          .update(widget.project!.toMap());

      // print({'saved', widget.allVideoFiles});

      if (mounted) {
        setState(() {
          progressMessage.clear();
          progressMessage.add('Saved!');
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          progressMessage.clear();
          progressMessage.add('Error');
        });
      }
    }
  }

  onPostRennoUploadCompleted() async {
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    widget.project!.videoFiles = widget.allVideoFiles;

    try {
      await _db
          .collection(appConfig.COLLECT_NEWARC_PROJECTS)
          .doc(widget.project!.id)
          .update(widget.project!.toMap());

      // print({'saved', widget.allVideoFiles});

      if (mounted) {
        setState(() {
          progressMessage.clear();
          progressMessage.add('Saved!');
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          progressMessage.clear();
          progressMessage.add('Error');
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              Expanded(
                child: ListView(
                  // crossAxisAlignment: CrossAxisAlignment.start,
                  // mainAxisSize: MainAxisSize.max,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        NarFormLabelWidget(
                          label: 'Video',
                          fontSize: 20,
                          fontWeight: 'bold',
                        ),
                        NarFilePickerWidget(
                          allowMultiple: true,
                          displayFormat: 'inline-button',
                          borderRadius: 7,
                          fontSize: 11,
                          fontWeight: '600',
                          text: 'Carica video',
                          uploadPopupText: 'Carica video',
                          borderSideColor: Theme.of(context).primaryColor,
                          hoverColor: Color.fromRGBO(133, 133, 133, 1),
                          allFiles: widget.allVideoFiles,
                          pageContext: context,
                          storageDirectory:
                              'projects/${widget.project!.id}/videos/',
                          progressMessage: progressMessage,
                          // displayInlineWidget: displayInlineWidget,
                          onUploadCompleted: onPreRennoUploadCompleted,
                        )
                      ],
                    ),
                    SizedBox(height: 30),
                    Container(
                      width: 350,
                      child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: 1,
                          itemBuilder: (context, index) {
                            return Column(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  NarFilePickerWidget(
                                    allowMultiple: false,
                                    filesToDisplayInList: 0,
                                    removeButton: true,
                                    isDownloadable: true,
                                    removeButtonText: 'Elimina',
                                    uploadButtonPosition: 'back',
                                    showMoreButtonText: '+ espandi',
                                    actionButtonPosition: 'bottom',
                                    displayFormat: 'inline-widget',
                                    containerWidth: 110,
                                    containerHeight: 110,
                                    containerBorderRadius: 13,
                                    borderRadius: 7,
                                    fontSize: 11,
                                    fontWeight: '600',
                                    text: 'Carica Progetto',
                                    borderSideColor:
                                        Theme.of(context).primaryColor,
                                    hoverColor:
                                        Color.fromRGBO(133, 133, 133, 1),
                                    allFiles: widget.allVideoFiles,
                                    pageContext: context,
                                    storageDirectory:
                                        'projects/${widget.project!.id}/videos/',
                                    removeExistingOnChange: true,
                                    progressMessage: progressMessage,
                                    onUploadCompleted:
                                        onPreRennoUploadCompleted,
                                  )
                                ]);
                          }),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
        Column(
          children: [
            SizedBox(
              height: 10,
            ),
            Container(
              width: double.infinity,
              height: 1,
              decoration: BoxDecoration(
                color: Color.fromRGBO(231, 231, 231, 1),
              ),
              child: SizedBox(height: 0),
            ),
            SizedBox(
              height: 10,
            ),
          ],
        ),
      ],
    );
  }
}
