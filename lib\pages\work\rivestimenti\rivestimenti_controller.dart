import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/NAMaterial.dart';
import 'package:intl/intl.dart';
import '../../../classes/NewarcMaterialManufacturer.dart';
import '../../../classes/NewarcMaterialDimension.dart';
import '../../../classes/NewarcMaterialDimensionMeta.dart';
import '../../../classes/NewarcMaterialSubcategory.dart';
import '../../../classes/NewarcMaterialSupplier.dart';
import '../../../classes/NewarcMaterialVariant.dart';
import '../../../classes/NewarcMaterialManufacturerCollection.dart';
import '../../../classes/NewarcMaterialVariantSupplierPrice.dart';



class RivestimentiController extends GetxController {

  TextEditingController collectionFilterController =  TextEditingController();
  TextEditingController manufacturerFilterController =  TextEditingController();
  String manufacturerSelectedFilter = '';
  String collectionSelectedFilter = '';

  TextEditingController searchTextController = new TextEditingController();

  List<Map> filters = [];


  void clearFilter(){
    collectionFilterController.clear();
    manufacturerFilterController.clear();
    manufacturerSelectedFilter = '';
    collectionSelectedFilter = '';
    filters.clear();
  }

  List<NewarcMaterialManufacturer> manufacturerList = [];
  List<NewarcMaterialManufacturerCollection> collectionList = [];

  List manufacturerDropdownFilterList = [];
  List collectionDropdownFilterList = [];

  String formProgressMessage = '';

  int? maxCode = 0;
  bool? maxCodeEvaluated = false;

  NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 2);


  //?-------------------------------------------------------------------------

  List<NewarcMaterialManufacturer> newarcManufacturer = [];
  List<NewarcMaterialDimensionMeta> newarcMaterialDimensionMeta = [];
  List<NewarcMaterialDimension> newarcMaterialDimension = [];
  List<NewarcMaterialVariant> newarcMaterialVariant = [];
  List<NewarcMaterialSupplier> newarcMaterialSupplier = [];
  List<NewarcMaterialManufacturerCollection> newarcProductCollection = [];

  List<NewarcMaterialVariantSupplierPrice> newarcMaterialSupplierPrices = [];
  List<Map<String, List<NewarcMaterialVariantSupplierPrice>>> newarcMaterialSupplierGroupedData = [];

  List<NewarcMaterialSubCategory> newarcMaterialSubCategory = [];

  List<NAMaterial> naMaterial = [];
  bool loadingNAMaterial = true;
  List<DocumentSnapshot> documentList = [];

  TextEditingController manufacturerController = TextEditingController();
  TextEditingController collectionController = TextEditingController();
  TextEditingController productNameController = TextEditingController();
  TextEditingController subCategoryController = TextEditingController();
  TextEditingController thicknessDropdownController = TextEditingController();
  TextEditingController formatDropdownController = TextEditingController();

  //-------Supplier Price Pop up
  TextEditingController supplierNameDropdownController = TextEditingController();
  TextEditingController variantProductDropdownForSupplierPriceController = TextEditingController();
  TextEditingController codeAtSupplierController = TextEditingController();
  TextEditingController supplierPriceController = TextEditingController();
  TextEditingController newarcPriceController = TextEditingController();

  double newArcPrice = 0.0;
  double supplierPrice = 0.0;


  void clearController() {
    subCategoryController.clear();
    newarcProductCollection.clear();
    newArcPrice = 0.0;
    supplierPrice = 0.0;
    manufacturerController.clear();
    collectionController.clear();
    productNameController.clear();
    thicknessDropdownController.clear();
    formatDropdownController.clear();
    supplierNameDropdownController.clear();
    variantProductDropdownForSupplierPriceController.clear();
    codeAtSupplierController.clear();
    supplierPriceController.clear();
    newarcPriceController.clear();
    newarcManufacturer.clear();
    newarcMaterialDimensionMeta.clear();
    newarcMaterialDimension.clear();
    newarcMaterialVariant.clear();
    newarcMaterialSupplier.clear();
    newarcMaterialSupplierPrices.clear();
    newarcMaterialSupplierGroupedData.clear();
    naMaterial.clear();
    documentList.clear();

    loadingNAMaterial = false;
    formProgressMessage = '';
  }



}
