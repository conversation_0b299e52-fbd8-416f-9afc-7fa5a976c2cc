import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/pages/work/gestione/agencies/agencies_controller.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/tab/status_widget.dart';
import 'package:intl/intl.dart';

class AgenciesDataSource extends DataTableSource {
  final List<Agency> agencies;
  final BuildContext context;
  final Function(Agency) onAgencyTap;
  final controller = Get.put<AgenciesController>(AgenciesController());

  NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);

  AgenciesDataSource({
    required this.context,
    required this.agencies,
    required this.onAgencyTap,
  });

  @override
  DataRow? getRow(int index) {
    if (index < agencies.length){
      Agency agency = agencies[index];
      return DataRow2(
        specificRowHeight: 50,
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
                color: AppColor.borderColor,
                width: 1,
            ),
          ),
        ),
        cells: [
          // Agency Name
          DataCell(
            NarLinkWidget(
              text: "${agency.name}",
              fontSize: 12,
              fontWeight: '800',
              textAlign: TextAlign.left,
              textColor: Colors.black,
              onClick: (){
                onAgencyTap(agency);
              },
            )
          ),
          // Operative Province
          DataCell(
            NarFormLabelWidget(
              label: agency.province ?? agency.city,
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
              maxLines: 3,
            )
          ),
          // Operative Address
          DataCell(
            NarFormLabelWidget(
              label: agency.toShortAddress() ?? agency.address,
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            )
          ),
          // Activity Status
          DataCell(
            StatusWidget(
              status: agency.isActive == true ? "Attiva" : "Disattiva",
              statusColor: agency.isActive == true ? AppColor.successGreenColor : AppColor.redColor,
            )
          ),
        ],
      );
    }
    return null;
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => agencies.length;

  @override
  int get selectedRowCount => 0;
}

