import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get_utils/src/extensions/string_extensions.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/classes/agencyPersone.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/utils/inputFormatters.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'dart:html' as html;
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:http/http.dart' show get;
import '../classes/agencyUser.dart';
import '../classes/reportAcquirente.dart';
import 'package:newarc_platform/app_const.dart' as appConst;
import '../functions/various.dart';

getNetworkImage(url) async {
  if (url == '') {
    final Uint8List coverLogoData = await _loadImage('assets/logo_newarc_immagina.png');
    final coverLogo = pw.MemoryImage(coverLogoData);
    return coverLogo;
  }
  var response = await get(Uri.parse(url));
  var data = response.bodyBytes;
  return pw.MemoryImage(data);
}

Future<Uint8List> _loadImage(String path) async {
  final ByteData data = await rootBundle.load(path);
  return data.buffer.asUint8List();
}

String toHexColor(String argbHex) {
  int colorInt = int.parse(argbHex.replaceFirst("0xFF", ""), radix: 16);
  // Extract RGB (ignore alpha)
  int rgb = colorInt & 0x00FFFFFF;
  return '#${rgb.toRadixString(16).padLeft(6, '0').toUpperCase()}';
}

Future<Map<String, pw.MemoryImage>> loadFeatureIcons(List<String> features) async {
  Map<String, pw.MemoryImage> iconMap = {};

  for (final feature in features) {
    final fileName = feature
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'), '') // remove special chars
        .replaceAll(' ', '-');              // spaces → dashes

    final path = 'assets/character/$fileName.png';

    try {
      final bytes = await rootBundle.load(path);
      iconMap[feature] = pw.MemoryImage(bytes.buffer.asUint8List());
    } catch (e) {
      print('Icon not found for $feature at $path, using default');
      final fallback = await rootBundle.load('assets/icons/pdf_icon_character.png');
      iconMap[feature] = pw.MemoryImage(fallback.buffer.asUint8List());
    }
  }

  return iconMap;
}

String getNextBetterEnergyClass(String currentClass) {
  int index = appConst.energyClassList.indexOf(currentClass);
  if (index >= 0 && index > 0) {
    return appConst.energyClassList[index - 1];
  }
  return currentClass;
}

Map<String,dynamic> stimaCalculationResponseFull ={
  'actual':0,
  'range_min':0,
  'range_max':0,
  'material_standard':0,
  'material_standard_min':0,
  'material_standard_max':0,
  'material_permium':0,
  'material_permium_min':0,
  'material_permium_max':0,
  'infissi':0,
  'infissi_min':0,
  'infissi_max':0,
};

Map<String,dynamic> stimaCalculationResponseLight ={
  'actual':0,
  'range_min':0,
  'range_max':0,
  'material_standard':0,
  'material_standard_min':0,
  'material_standard_max':0,
  'material_permium':0,
  'material_permium_min':0,
  'material_permium_max':0,
  'infissi':0,
  'infissi_min':0,
  'infissi_max':0,
};



Future<void> downloadBuyerReportPDF({required ReportAcquirente  reportAcquirente,ValueNotifier<double>? progress}) async {
  progress?.value = 0.05;

  try{

    List standardFeatures = [
      'Allestimento cantiere',
      'Rifacimento completo bagni',
      'Controsoffitto bagni',
      'Ripristino pavimenti',
      'Posa porte interne',
      'Decorazioni e finiture'
    ];

    List premiumFeatures = [
      'Progettazione',
      'Pratiche',
      'Allestimento cantiere',
      'Demolizioni e costruzioni',
      'Massetti',
      'Controsoffitti',
      'Impianto elettrico',
      'Impianto idrico / gas',
      'Rifacimento completo bagni',
      'Posa pavimenti e rivestimenti',
      'Predisposizione climatizzazione',
      'Rasature',
      'Decorazioni e finiture'
    ];


    //------------------------- FONTS --------------------
    var ralewayMedium;
    var ralewayItalic;
    var ralewayBold;
    var ralewayRegular;

    if(reportAcquirente.fontType == "raleway"){
      final ByteData fontMediumData = await rootBundle.load('assets/fonts/Raleway-Medium.ttf');
      ralewayMedium = pw.Font.ttf(fontMediumData.buffer.asByteData()); //------ 500 or 600

      final ByteData fontItalicData =await rootBundle.load('assets/fonts/Raleway-BoldItalic.ttf');
      ralewayItalic = pw.Font.ttf(fontItalicData.buffer.asByteData());

      final ByteData fontBoldData = await rootBundle.load('assets/fonts/Raleway-Bold.ttf');
      ralewayBold = pw.Font.ttf(fontBoldData.buffer.asByteData()); //-------- 700


      final ByteData fontRegularData = await rootBundle.load('assets/fonts/Raleway-Regular.ttf');
      ralewayRegular = pw.Font.ttf(fontRegularData.buffer.asByteData());
    }else if(reportAcquirente.fontType == "tinos"){
      final ByteData fontMediumData = await rootBundle.load('assets/fonts/Tinos-Regular.ttf');
      ralewayMedium = pw.Font.ttf(fontMediumData.buffer.asByteData()); //------ 500 or 600

      final ByteData fontItalicData =await rootBundle.load('assets/fonts/Tinos-BoldItalic.ttf');
      ralewayItalic = pw.Font.ttf(fontItalicData.buffer.asByteData());

      final ByteData fontBoldData = await rootBundle.load('assets/fonts/Tinos-Bold.ttf');
      ralewayBold = pw.Font.ttf(fontBoldData.buffer.asByteData()); //-------- 700

      final ByteData fontRegularData = await rootBundle.load('assets/fonts/Tinos-Regular.ttf');
      ralewayRegular = pw.Font.ttf(fontRegularData.buffer.asByteData());
    }else{
      final ByteData fontMediumData = await rootBundle.load('assets/fonts/Raleway-Medium.ttf');
      ralewayMedium = pw.Font.ttf(fontMediumData.buffer.asByteData()); //------ 500 or 600

      final ByteData fontItalicData =await rootBundle.load('assets/fonts/Raleway-BoldItalic.ttf');
      ralewayItalic = pw.Font.ttf(fontItalicData.buffer.asByteData());

      final ByteData fontBoldData = await rootBundle.load('assets/fonts/Raleway-Bold.ttf');
      ralewayBold = pw.Font.ttf(fontBoldData.buffer.asByteData()); //-------- 700


      final ByteData fontRegularData = await rootBundle.load('assets/fonts/Raleway-Regular.ttf');
      ralewayRegular = pw.Font.ttf(fontRegularData.buffer.asByteData());
    }





    String primaryHex = toHexColor(reportAcquirente.buyerReportColor ?? "0xFFB3B3B3");

    PdfColor pdfPrimaryColor = PdfColor.fromHex(primaryHex);

    String googleApikey = appConfig.isProduction ? appConfig.GOOGLE_PRODUCTION_STATIC_MAP_API_KEY : appConfig.GOOGLE_STAGING_STATIC_MAP_API_KEY;

    final latitude = reportAcquirente.addressInfo!.latitude;
    final longitude = reportAcquirente.addressInfo!.longitude;

    String mapUrl = "https://maps.googleapis.com/maps/api/staticmap?center=$latitude,$longitude&zoom=15&size=451x451&maptype=roadmap&markers=icon:https%3A%2F%2Ffirebasestorage.googleapis.com%2Fv0%2Fb%2Fnewarc-staging.appspot.com%2Fo%2FgoogleMapMarker%252FLivello_3.png%3Falt%3Dmedia%26token%3Da6d3139d-7e76-4cc1-be28-ff68462da4bc%7C$latitude,$longitude&style=feature:all%7Celement:geometry%7Csaturation:-100&style=feature:road%7Celement:labels%7Cvisibility:on&style=feature:poi%7Celement:labels%7Cvisibility:off&style=feature:administrative%7Celement:labels%7Cvisibility:off&style=feature:transit%7Celement:labels%7Cvisibility:off&style=feature:landscape%7Celement:labels%7Cvisibility:off&style=feature:water%7Celement:labels%7Cvisibility:off&key=$googleApikey";
    final mapImage = await getNetworkImage(mapUrl);
    final Map<String, int> nearByPlaces = await fetchNearbyPlaceCountsFromCloud(latitude: latitude!, longitude: longitude!, radius: 400, types: ['supermarket', 'school', 'park', 'bus_stop']);

    progress?.value = 0.10;
    //------------------------- ICONS --------------------


    final Uint8List phoneIconData = await _loadImage('assets/icons/pdf-phone.png');
    final phoneIcon = pw.MemoryImage(phoneIconData);

    final Uint8List newarcLogoData = await _loadImage('assets/pdf_logo.png');
    final newarcLogo = pw.MemoryImage(newarcLogoData);

    final Uint8List newarcWhiteLogoData = await _loadImage('assets/pdf_stima_logo.png');
    final newarcWhiteLogo = pw.MemoryImage(newarcWhiteLogoData);

    final Uint8List buildingIconData = await _loadImage('assets/icons/pdf_icon_building.png');
    final buildingIcon = pw.MemoryImage(buildingIconData);

    final Uint8List skyLineData = await _loadImage('assets/pdf_skyline.png');
    final skyLineImg = pw.MemoryImage(skyLineData);

    final Uint8List arrowHouseData = await _loadImage('assets/icons/pdf-house-arrow.png');
    final arrowHouseIcon = pw.MemoryImage(arrowHouseData);

    final Uint8List arrowData = await _loadImage('assets/icons/c3-arrow.png');
    final arrowIcon = pw.MemoryImage(arrowData);

    final Uint8List heatEnergyIconData = await _loadImage('assets/icons/pdf_icon_heat_energy.png');
    final heatEnergyIcon = pw.MemoryImage(heatEnergyIconData);

    final Uint8List costIconData = await _loadImage('assets/icons/pdf_icon_cost.png');
    final costIcon = pw.MemoryImage(costIconData);

    final Uint8List shoppingIconData = await _loadImage('assets/icons/pdf_icon_shopping.png');
    final shoppingIcon = pw.MemoryImage(shoppingIconData);

    final Uint8List schoolIconData = await _loadImage('assets/icons/pdf_icon_school.png');
    final schoolIcon = pw.MemoryImage(schoolIconData);

    final Uint8List treeIconData = await _loadImage('assets/icons/pdf_icon_tree.png');
    final treeIcon = pw.MemoryImage(treeIconData);

    final Uint8List busIconData = await _loadImage('assets/icons/pdf_icon_bus.png');
    final busIcon = pw.MemoryImage(busIconData);

    final Uint8List locationIconData = await _loadImage('assets/icons/pdf_icon_location.png');
    final locationIcon = pw.MemoryImage(locationIconData);

    final Uint8List stairsIconData = await _loadImage('assets/icons/pdf_icon_stairs.png');
    final stairsIcon = pw.MemoryImage(stairsIconData);

    final Uint8List bathIconData = await _loadImage('assets/icons/pdf_icon_bath.png');
    final bathIcon = pw.MemoryImage(bathIconData);

    final Uint8List roomIconData = await _loadImage('assets/icons/pdf_icon_room.png');
    final roomIcon = pw.MemoryImage(roomIconData);

    final Uint8List mgIconData = await _loadImage('assets/icons/pdf_icon_mg.png');
    final mgIcon = pw.MemoryImage(mgIconData);

    final Uint8List hammerIconData = await _loadImage('assets/icons/pdf-restructure-hammer.png');
    final hammerIcon = pw.MemoryImage(hammerIconData);


    final Uint8List materialStandardData = await _loadImage('assets/icons/pdf-material-standard.png');
    final materialStandardIcon = pw.MemoryImage(materialStandardData);

    final Uint8List infissiData = await _loadImage('assets/icons/pdf-infissi.png');
    final infissiDataIcon = pw.MemoryImage(infissiData);

    final Uint8List workDurationData = await _loadImage('assets/icons/work-duration.png');
    final workDurationIcon = pw.MemoryImage(workDurationData);

    final Uint8List materialPremiumData = await _loadImage('assets/icons/pdf-material-premium.png');
    final materialPremiumIcon = pw.MemoryImage(materialPremiumData);


    progress?.value = 0.15;

    NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 2);
    NumberFormat localCurrencyFormatWithoutDecimal = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);


    final pdf = pw.Document();

    //------------------------- Fetch agency data -------------------------
    var agencyLogo;
    String agencyImageUrl = '';
    Agency agencyData = Agency.empty();
    if (reportAcquirente.agencyId != '') {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot =
      await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_USERS)
          .where('agencyId', isEqualTo: reportAcquirente.agencyId)
          .limit(1)
          .get();

      DocumentSnapshot<Map<String, dynamic>> collectionSnapshotAgency =
      await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_AGENCIES)
          .doc(reportAcquirente.agencyId)
          .get();

      if (collectionSnapshot.docs.length > 0) {
        AgencyUser agency = AgencyUser.fromDocument(
            collectionSnapshot.docs[0].data(), collectionSnapshot.docs[0].id);
        agencyImageUrl =
        await agencyProfileUrl(agency.agencyId, agency.profilePicture);
        agencyLogo = await getNetworkImage(agencyImageUrl);
      }

      if (collectionSnapshotAgency.exists) {
        agencyData = Agency.fromDocument(
            collectionSnapshotAgency.data()!, collectionSnapshotAgency.id);
        agencyData.phone = agencyData.phone!.replaceFirst('+39 ', '');
      }
    }

    progress?.value = 0.20;

    //------------------------- Fetch agency persona -------------------------
    var agencyPersonLogo;
    String agencyPersonImageUrl = '';
    AgencyPersone agencyPersonData = AgencyPersone.empty();
    if (reportAcquirente.buyerReportAgencyPersonId != '') {

      DocumentSnapshot<Map<String, dynamic>> collectionSnapshotAgencyPerson =
      await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_AGENCY_PERSONA)
          .doc(reportAcquirente.buyerReportAgencyPersonId)
          .get();

      if (collectionSnapshotAgencyPerson.exists) {
        agencyPersonData = AgencyPersone.fromDocument(collectionSnapshotAgencyPerson.data()!, collectionSnapshotAgencyPerson.id);
        agencyPersonImageUrl = await printUrl(agencyPersonData.profilePicturePath!['location'], '', agencyPersonData.profilePicturePath!['filename']);
        agencyPersonLogo = await getNetworkImage(agencyPersonImageUrl);
      }
    }

    progress?.value = 0.25;


    PdfColor greyBoarderColor = PdfColor.fromHex("#C8C8C8");

    String selectedCoverImageUrl = await printUrl("${appConfig.COLLECT_REPORT_ACQUIRENTE}/${reportAcquirente.id}/", "", reportAcquirente.buyerReportCoverImage?["file"]);
    var selectedCoverImage = await  getNetworkImage(selectedCoverImageUrl);

    String planImageUrl;
    var planImage;
    if(reportAcquirente.isPlanimetriaCatastaleInBuyerReport ?? false){
      planImageUrl = await printUrl("${appConfig.COLLECT_REPORT_ACQUIRENTE}/${reportAcquirente.id}/", "", reportAcquirente.planimetry[0]);
      planImage = await  getNetworkImage(planImageUrl);
    }

    List<Map<String, dynamic>> internalImages = [];
    List<Map<String, dynamic>> externalImages = [];

    if (reportAcquirente.pictures.isNotEmpty) {
      for (int i = 0; i < reportAcquirente.pictures.length; i++) {
        final picture = reportAcquirente.pictures[i];
        final type = picture["type"];
        final file = picture["file"];
        final room = picture["tag"] ?? "";

        if ((type == "internal" && (reportAcquirente.isFotografieInterneInBuyerReport ?? false)) ||
            (type == "external" && (reportAcquirente.isFotografieEsterneInBuyerReport ?? false))) {
          final imageUrl = await printUrl(
            "${appConfig.COLLECT_REPORT_ACQUIRENTE}/${reportAcquirente.id}",
            '',
            file,
          );
          final image = await getNetworkImage(imageUrl);

          final imageData = {
            "image": image,
            "tag": room,
          };

          if (type == "internal") {
            internalImages.add(imageData);
          } else {
            externalImages.add(imageData);
          }
        }
      }
    }

    progress?.value = 0.35;

    //------------------ COVER PAGE START --------------------------
    List<pw.Widget> pdfCover = [];

    Map<String, bool> characteristicsMap = Map.from(appConst.houseFeatures);

    // Caratteristiche
    characteristicsMap['Ascensore'] = reportAcquirente.elevator ?? false;
    characteristicsMap['Cantina'] = reportAcquirente.hasCantina ?? false;
    characteristicsMap['Terrazzo'] = reportAcquirente.terrace ?? false;
    characteristicsMap['Portineria'] = reportAcquirente.hasConcierge ?? false;
    characteristicsMap['Infissi ad alta efficienza'] = reportAcquirente.highEfficiencyFrames ?? false;
    characteristicsMap['Doppia esposizione'] = reportAcquirente.doubleEsposition ?? false;
    characteristicsMap['Tripla esposizione'] = reportAcquirente.tripleEsposition ?? false;
    characteristicsMap['Quadrupla esposizione'] = reportAcquirente.quadrupleEsposition ?? false;
    characteristicsMap['Risc. centralizzato'] = reportAcquirente.centralizedHeating ?? false;
    characteristicsMap['Risc. autonomo'] = reportAcquirente.autonomousHeating ?? false;
    characteristicsMap['Giardino privato'] = reportAcquirente.privateGarden ?? false;
    characteristicsMap['Giardino condominiale'] = reportAcquirente.sharedGarden ?? false;
    characteristicsMap['Stabile signorile'] = reportAcquirente.nobleBuilding ?? false;
    characteristicsMap['Stabile videosorvegliato'] = reportAcquirente.surveiledBuilding ?? false;
    characteristicsMap['Fibra ottica'] = reportAcquirente.fiber ?? false;
    characteristicsMap['Pred. condizionatore'] = reportAcquirente.airConditioning ?? false;
    characteristicsMap['Porta blindata'] = reportAcquirente.securityDoor ?? false;
    characteristicsMap['Impianto TV'] = reportAcquirente.tvStation ?? false;
    characteristicsMap['Pred. antifurto'] = reportAcquirente.alarm ?? false;
    characteristicsMap['Tapparelle motorizzate'] = reportAcquirente.motorizedSunblind ?? false;
    characteristicsMap['Tapparelle domotizzate'] = reportAcquirente.domotizedSunblind ?? false;
    characteristicsMap['Luci domotizzate'] = reportAcquirente.domotizedLights ?? false;
    characteristicsMap['Piano alto'] = reportAcquirente.highFloor ?? false;
    characteristicsMap['Vicinanza Metro'] = reportAcquirente.metroVicinity ?? false;
    characteristicsMap['Ampi balconi'] = reportAcquirente.bigBalconies ?? false;
    characteristicsMap['Grande zona living'] = reportAcquirente.bigLiving ?? false;
    characteristicsMap['Doppi servizi'] = reportAcquirente.doubleBathroom ?? false;
    characteristicsMap['Piscina'] = reportAcquirente.swimmingPool ?? false;
    characteristicsMap['Box o garage'] = reportAcquirente.hasGarage ?? false;
    characteristicsMap['Cabina armadio'] = reportAcquirente.walkInCloset ?? false;
    characteristicsMap['Fotovoltaico'] = reportAcquirente.solarPanel ?? false;

    final List<String> enabledFeatures = characteristicsMap.entries
        .where((entry) => entry.value == true)
        .map((entry) => entry.key)
        .toList();

    final featureIcons = await loadFeatureIcons(enabledFeatures);

    progress?.value = 0.45;

    pw.Widget pageFooter;

    pageFooter =  pw.Container(
            padding: pw.EdgeInsets.all(25),
            margin: pw.EdgeInsets.only(left: 55, right: 55, top: 20, bottom: 20 ),
            decoration: pw.BoxDecoration(
                borderRadius: pw.BorderRadius.circular(12),
                color: PdfColor.fromHex('#F5F5F5')
            ),
            child: pw.Center(
                child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Row(
                          children: [
                            pw.Text(
                                'IN VENDITA CON',
                                style: pw.TextStyle(
                                    fontSize: 11,
                                    color: PdfColor.fromHex('#5A5A5A'),
                                    font: ralewayRegular
                                )
                            ),
                            pw.SizedBox(width: 15),
                            pw.Text(
                                agencyData.name!,
                                style: pw.TextStyle(
                                    fontSize: 14,
                                    color: PdfColors.black,
                                    font: ralewayBold
                                )
                            ),
                          ]
                      ),
                      pw.Text(
                        // modify below line so to have a phone number formatted XXX XXX XXXX XXXX
                        '${agencyData.toShortAddress()} • ${phoneNumberMaskFormatterItalian.maskText(agencyData.phone ?? "")}',
                        style: pw.TextStyle(
                            fontSize: 14,
                            color: PdfColors.black,
                            font: ralewayMedium
                        )
                      ),
                    ]
                )
            )
    );

    pw.Widget iconTextBox({required pw.ImageProvider icon,required String label,double? width,double? fontSize}) {
      return pw.Container(
        width: width ?? 150,
        child: pw.Row(
          children: [
            pw.Image(icon, height: 30),
            pw.SizedBox(width: 14),
            pw.Expanded(
              child: pw.Text(
                label,
                overflow: pw.TextOverflow.visible,
                softWrap: true,
                style: pw.TextStyle(
                  fontSize: fontSize ?? 20,
                  font: ralewayMedium,
                  color: PdfColor.fromHex("#4F4F4F"),
                ),
              ),
            )
          ],
        ),
      );
    }

    if(reportAcquirente.coverTemplateType == "classic"){
      pdfCover.add(
          pw.Container(
              child: pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  children: [
                    pw.Expanded(
                        child: pw.Container(
                            margin: pw.EdgeInsets.only(top: 59,left: 55,bottom: 59),
                            //height: 723,
                            child: pw.Column(
                                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: pw.CrossAxisAlignment.start,
                                children: [
                                  pw.Image( agencyLogo,height: 159,width: 320),


                                  pw.Column(
                                      mainAxisAlignment: pw.MainAxisAlignment.start,
                                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                                      children: [

                                        pw.Text(
                                            "${reportAcquirente.addressInfo?.streetName} ${reportAcquirente.addressInfo?.streetNumber}",
                                            style: pw.TextStyle(
                                                fontSize: 40,
                                                font: ralewayBold,
                                                color: PdfColors.black
                                            )
                                        ),
                                        pw.SizedBox(height:20),
                                        pw.Text(
                                            "${reportAcquirente.marketZone?.isNotEmpty ?? false ? "${reportAcquirente.marketZone?.capitalizeFirst}, " : ""}${reportAcquirente.addressInfo?.city}",
                                            style: pw.TextStyle(
                                                fontSize: 25,
                                                font: ralewayMedium,
                                                color: PdfColors.black
                                            )
                                        )
                                      ]
                                  ),

                                  pw.Container(
                                      child: pw.Column(
                                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                                          children: [
                                            pw.Container(
                                              child: pw.Column(
                                                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                  mainAxisSize: pw.MainAxisSize.min,
                                                  children: [
                                                    pw.Row(
                                                        mainAxisSize: pw.MainAxisSize.min,
                                                        children: [
                                                          iconTextBox(icon: mgIcon,label: "${reportAcquirente.grossSquareFootage} mq"),
                                                          pw.SizedBox(width: 20),
                                                          iconTextBox(icon:roomIcon,label: "${reportAcquirente.rooms} locali"),
                                                        ]
                                                    ),
                                                    pw.SizedBox(height: 15),
                                                    pw.Row(
                                                        mainAxisSize: pw.MainAxisSize.min,
                                                        children: [
                                                          iconTextBox(icon:bathIcon,label:"${reportAcquirente.numberOfBathrooms} bagni",),
                                                          pw.SizedBox(width: 20),
                                                          iconTextBox(icon:stairsIcon,label:"${reportAcquirente.unitFloor}${reportAcquirente.unitFloor == "Piano terra / rialzato" ? "":"° piano"}",width: 200),
                                                        ]
                                                    ),
                                                  ]
                                              ),
                                            ),
                                          ]
                                      )
                                  ),



                                ]
                            )
                        )

                    ),
                    pw.Container(
                        width: 639,
                        //height: 723,
                        decoration: pw.BoxDecoration(
                            image: pw.DecorationImage( image: selectedCoverImage, fit: pw.BoxFit.cover),
                            borderRadius: pw.BorderRadius.circular(14)
                        ),
                        margin: pw.EdgeInsets.only(top: 59,right: 55,left: 20,bottom: 59),
                        child: pw.Stack(
                            children: [
                              pw.Positioned(
                                  bottom: 20,
                                  right: 20,
                                  child: pw.Opacity(
                                      opacity: 1,
                                      child: pw.Container(
                                        height: 80,
                                        width: 400,
                                        decoration: pw.BoxDecoration(
                                            borderRadius: pw.BorderRadius.circular(10),
                                            color: PdfColors.white
                                        ),
                                      )
                                  )

                              ),
                              pw.Positioned(
                                  bottom: 20,
                                  right: 20,
                                  child: pw.Container(
                                      height: 80,
                                      width: 400,
                                      decoration: pw.BoxDecoration(
                                        borderRadius: pw.BorderRadius.circular(10),
                                      ),
                                      padding: pw.EdgeInsets.symmetric(horizontal: 30, vertical: 25),
                                      child: pw.Center(
                                          child: pw.Row(
                                              mainAxisAlignment: pw.MainAxisAlignment.center,
                                              mainAxisSize: pw.MainAxisSize.max,
                                              children: [
                                                pw.Text(
                                                    'Prezzo immobile: ',
                                                    style: pw.TextStyle(
                                                        fontSize: 23,
                                                        color: PdfColors.black,
                                                        font: ralewayRegular
                                                    )
                                                ),
                                                pw.Text(
                                                    '${localCurrencyFormatMain.format(reportAcquirente.listingPrice ?? 0.0).toString().trim()}€',
                                                    style: pw.TextStyle(
                                                        fontSize: 23,
                                                        color: PdfColors.black,
                                                        font: ralewayBold
                                                    )
                                                )
                                              ]
                                          )
                                      )

                                  )

                              )
                            ]
                        )
                    ),
                  ]
              )
          )
      );
    }
    else if(reportAcquirente.coverTemplateType == "full"){
      pdfCover.add(
          pw.Stack(
              children: [
                pw.Container(
                    padding: pw.EdgeInsets.symmetric(vertical: 46,horizontal: 49),
                    child: pw.Container(
                      decoration: pw.BoxDecoration(
                          image: pw.DecorationImage( image: selectedCoverImage, fit: pw.BoxFit.cover),
                          borderRadius: pw.BorderRadius.circular(14)
                      ),
                    )
                ),
                pw.Align(
                  alignment: pw.Alignment.topCenter,
                  child: pw.Container(
                          width: 281,
                      height: 126,
                      margin: pw.EdgeInsets.only(top: 45),
                    child: pw.Stack(
                      children: [
                        pw.Container(
                          width: 281,
                          height: 126,
                          decoration: pw.BoxDecoration(
                            borderRadius: pw.BorderRadius.only(bottomLeft: pw.Radius.circular(20),bottomRight: pw.Radius.circular(20)),
                            color: PdfColors.white,
                          ),
                        ),
                        pw.Positioned(
                          top: 2,
                          left: (281 - 244)/2,
                          child: pw.Container(
                            width: 244,
                            height: 121,
                            decoration: pw.BoxDecoration(
                              image: pw.DecorationImage(
                                image: agencyLogo,
                                fit: pw.BoxFit.contain,
                              ),
                            ),
                          ),
                        ),
                      ],
                    )
                  ),
                ),
                pw.Align(
                  alignment: pw.Alignment.bottomCenter,
                  child: pw.Container(
                      width: 811,
                      height: 185,
                      margin: pw.EdgeInsets.only(bottom: 45),
                      padding: pw.EdgeInsets.only(top: 23, right: 15, left: 30),
                      decoration: pw.BoxDecoration(
                        borderRadius: pw.BorderRadius.only(topLeft: pw.Radius.circular(20),topRight: pw.Radius.circular(20)),
                        color: PdfColors.white,
                      ),
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              pw.Text(
                                "${reportAcquirente.addressInfo?.streetName} ${reportAcquirente.addressInfo?.streetNumber}",
                                style: pw.TextStyle(
                                    fontSize: 35,
                                    font: ralewayBold,
                                    color: PdfColors.black
                                )
                              ),
                              pw.Text(
                                  "${reportAcquirente.marketZone?.isNotEmpty ?? false ? "${reportAcquirente.marketZone?.capitalizeFirst}, " : ""}${reportAcquirente.addressInfo?.city}",
                                  style: pw.TextStyle(
                                      fontSize: 20,
                                      font: ralewayMedium,
                                      color: PdfColors.black
                                  )
                              )
                            ]
                          ),
                          pw.SizedBox(height: 10),
                          pw.Expanded(
                            child:pw.Row(
                              crossAxisAlignment: pw.CrossAxisAlignment.center,
                              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                              children: [
                                // pw.Expanded(child: pw.Text(
                                //     "${reportAcquirente.marketZone?.isNotEmpty ?? false ? "${reportAcquirente.marketZone?.capitalizeFirst}, " : ""}${reportAcquirente.addressInfo?.city}",
                                //     style: pw.TextStyle(
                                //         fontSize: 20,
                                //         font: ralewayMedium,
                                //         color: PdfColors.black
                                //     )
                                // )),
                                pw.Row(
                                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                                  children: [
                                    pw.Text(
                                      'Prezzo immobile: ',
                                      style: pw.TextStyle(
                                          fontSize: 23,
                                          color: PdfColors.black,
                                          font: ralewayRegular
                                      )
                                    ),
                                    pw.Text(
                                      '${localCurrencyFormatMain.format(reportAcquirente.listingPrice ?? 0.0).toString().trim()}€',
                                      style: pw.TextStyle(
                                          fontSize: 23,
                                          color: PdfColors.black,
                                          font: ralewayBold
                                      )
                                    )
                                  ]
                                ),
                                pw.Column(
                                  mainAxisAlignment: pw.MainAxisAlignment.center,
                                  children: [
                                    pw.Row(
                                      mainAxisSize: pw.MainAxisSize.min,
                                      mainAxisAlignment: pw.MainAxisAlignment.end,
                                      children: [
                                        iconTextBox(icon: mgIcon,label: "${reportAcquirente.grossSquareFootage} mq",fontSize: 18),
                                        pw.SizedBox(width: 20),
                                        iconTextBox(icon:roomIcon,label: "${reportAcquirente.rooms} locali",fontSize: 18),
                                      ]
                                    ),
                                    pw.SizedBox(height: 5),
                                    pw.Row(
                                      mainAxisAlignment: pw.MainAxisAlignment.end,
                                      children: [
                                        iconTextBox(icon:bathIcon,label:"${reportAcquirente.numberOfBathrooms} bagni",fontSize: 18),
                                        pw.SizedBox(width: 20),
                                        iconTextBox(icon:stairsIcon,label:"${reportAcquirente.unitFloor}${reportAcquirente.unitFloor == "Piano terra / rialzato" ? "":"° piano"}",fontSize: 18),
                                      ]
                                    )
                                  ]
                                ),
                                // pw.Expanded(
                                //   child: pw.Row(
                                //       mainAxisSize: pw.MainAxisSize.min,
                                //       mainAxisAlignment: pw.MainAxisAlignment.end,
                                //       children: [
                                //         iconTextBox(icon: mgIcon,label: "${reportAcquirente.grossSquareFootage} mq",fontSize: 18),
                                //         pw.SizedBox(width: 20),
                                //         iconTextBox(icon:roomIcon,label: "${reportAcquirente.rooms} locali",fontSize: 18),
                                //       ]
                                //   ),),
                              ],
                            ),
                          )
                          // pw.SizedBox(height: 25),
                          // pw.Row(
                          //   crossAxisAlignment: pw.CrossAxisAlignment.start,
                          //   children: [
                          //     pw.Expanded(
                          //         child:
                          //         pw.Row(
                          //         mainAxisAlignment: pw.MainAxisAlignment.start,
                          //         children: [
                          //           pw.Text(
                          //               'Prezzo immobile: ',
                          //               style: pw.TextStyle(
                          //                   fontSize: 23,
                          //                   color: PdfColors.black,
                          //                   font: ralewayRegular
                          //               )
                          //           ),
                          //           pw.Text(
                          //               '${localCurrencyFormatMain.format(reportAcquirente.listingPrice ?? 0.0).toString().trim()}€',
                          //               style: pw.TextStyle(
                          //                   fontSize: 23,
                          //                   color: PdfColors.black,
                          //                   font: ralewayBold
                          //               )
                          //           )
                          //         ]
                          //     ),
                          //     ),
                          //     pw.Expanded(child: pw.Row(
                          //             mainAxisAlignment: pw.MainAxisAlignment.end,
                          //             children: [
                          //               iconTextBox(icon:bathIcon,label:"${reportAcquirente.numberOfBathrooms} bagni",fontSize: 18),
                          //               pw.SizedBox(width: 20),
                          //               iconTextBox(icon:stairsIcon,label:"${reportAcquirente.unitFloor}${reportAcquirente.unitFloor == "Piano terra / rialzato" ? "":"° piano"}",fontSize: 18),
                          //             ]
                          //         )),
                          //   ],
                          // ),
                        ],
                      )
                  ),
                )
              ]
          )
      );
    }


    pdf.addPage(
        pw.Page(
          theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
          pageFormat: PdfPageFormat(1300, 940),
          orientation: pw.PageOrientation.landscape,
          margin: const pw.EdgeInsets.all(0),
          build: (pw.Context context) => pdfCover[0],
        )
    );

    progress?.value = 0.50;




    //?------------------ COVER PAGE END --------------------------



    pw.Widget commonHeader(String title){
      return pw.Container(
          width: double.infinity,
          height: 92,
          decoration: pw.BoxDecoration(
              color: pdfPrimaryColor,
          ),
          padding: pw.EdgeInsets.symmetric(horizontal: 55),
          margin: pw.EdgeInsets.only(bottom: 10),
          child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(
                    title,
                    style: pw.TextStyle(
                        fontSize: 30,
                        font: ralewayBold,
                        color: PdfColors.white
                    )
                ),
                pw.Text(
                    "${reportAcquirente.addressInfo?.streetName} ${reportAcquirente.addressInfo?.streetNumber}",
                    style: pw.TextStyle(
                        fontSize: 18,
                        font: ralewayMedium,
                        color: PdfColors.white
                    )
                ),
              ]
          )
      );
    }

    //?------------------ L’immobile PAGE START --------------------------

    List<pw.Widget> pdfimmobilePage = [];

    pdfimmobilePage.add(
        pw.Container(
            padding: pw.EdgeInsets.symmetric(horizontal: 55),
            margin: pw.EdgeInsets.only(top: 48,bottom: 10),
            height: 665,
            child: pw.Column(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  ((reportAcquirente.isDescrizioneImmobileIncludeInBuyerReport ?? false) || (reportAcquirente.isPlanimetriaCatastaleInBuyerReport ?? false)) ?
                  pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                      children: [
                        (reportAcquirente.isPlanimetriaCatastaleInBuyerReport ?? false) ?
                        pw.Container(
                          height: 352,
                          width: 352,
                          decoration: pw.BoxDecoration(
                              color: PdfColors.white,
                              borderRadius: pw.BorderRadius.circular(15),
                              image: pw.DecorationImage(image: planImage,fit: pw.BoxFit.cover)
                          ),
                        ): pw.SizedBox.shrink(),

                        pw.SizedBox(width: 93),

                        (reportAcquirente.isDescrizioneImmobileIncludeInBuyerReport ?? false) ?
                        pw.Expanded(
                            child: pw.Text(
                                reportAcquirente.description ?? "",
                                textAlign: pw.TextAlign.left,
                                overflow: pw.TextOverflow.visible,

                                style: pw.TextStyle(
                                  fontSize: 16,
                                  font: ralewayRegular,
                                  height: 36,
                                  lineSpacing: 8,
                                )
                            )
                        ) : pw.SizedBox.shrink(),
                      ]
                  ): pw.SizedBox.shrink(),


                  //------ Character List
                  reportAcquirente.isCaratteristicheInBuyerReport ?? false ?
                  pw.Container(
                      padding: pw.EdgeInsets.all(15),
                      decoration: pw.BoxDecoration(
                          borderRadius: pw.BorderRadius.circular(13),
                          border: pw.Border.all(color: greyBoarderColor,width: 1),
                          color: PdfColors.white
                      ),
                      width: double.infinity,
                      child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          mainAxisSize: pw.MainAxisSize.min,
                          children: [
                            pw.Text(
                                "Caratteristiche dell’immobile",
                                style: pw.TextStyle(
                                    fontSize: 18,
                                    font: ralewayBold
                                )
                            ),
                            pw.SizedBox(height: 20),
                            pw.Wrap(
                                spacing: 16,
                                runSpacing: 18,
                                children: enabledFeatures.map((feature) {
                                  return pw.Container(
                                    height: 53,
                                    padding: const pw.EdgeInsets.symmetric(horizontal: 27, vertical: 12),
                                    decoration: pw.BoxDecoration(
                                      border: pw.Border.all(color: PdfColor.fromHex("#E8E8E8"),width: 1),
                                      borderRadius: pw.BorderRadius.all(pw.Radius.circular(26.5)),
                                      color: PdfColors.white,
                                    ),
                                    child: pw.Row(
                                      mainAxisSize: pw.MainAxisSize.min,
                                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                                      mainAxisAlignment: pw.MainAxisAlignment.center,
                                      children: [
                                        pw.Image(
                                          featureIcons[feature]!,
                                          width: 27,
                                          height: 27,
                                        ),
                                        pw.SizedBox(width: 14),
                                        pw.Text(
                                          feature,
                                          style: pw.TextStyle(
                                              fontSize: 15,
                                              color: PdfColors.black,
                                              font: ralewayRegular
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                }).toList()
                            ),
                          ]
                      )
                  ) : pw.SizedBox.shrink(),
                ]
            )
        )
    );

    pdf.addPage(
        pw.MultiPage(
          theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
          pageFormat: PdfPageFormat(1300, 940),
          orientation: pw.PageOrientation.landscape,
          margin: const pw.EdgeInsets.all(0),
          build: (pw.Context context) => pdfimmobilePage,
          header: (pw.Context context) => commonHeader("L’immobile"),
          footer: (pw.Context context) => pageFooter,
        )
    );

    progress?.value = 0.55;


    //?------------------ L’immobile PAGE END --------------------------



    //?------------------ Informazioni PAGE START --------------------------

    List<pw.Widget> pdfInfoPage = [];

    pw.Widget iconTextBoxForMap(pw.ImageProvider icon, String label,String count) {
      return pw.Container(
        height: 32,
        child: pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.start,
          children: [
            pw.Image(icon,height: 27),
            pw.SizedBox(width: 8),
            pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    count,
                    style: pw.TextStyle(
                      fontSize: 14,
                      font: ralewayBold,
                      color: PdfColors.black,
                    ),
                  ),
                  pw.SizedBox(width: 5),
                  pw.Text(
                    label,
                    style: pw.TextStyle(
                      fontSize: 12,
                      font: ralewayRegular,
                      color: PdfColor.fromHex("#646464"),
                    ),
                  )
                ]
            )
          ],
        ),
      );
    }

    pw.Widget buildEntry(MapEntry entry, {bool expand = false}) {
      final widget = pw.Container(
        margin: pw.EdgeInsets.only(right: 20, bottom: 33),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              entry.key,
              style: pw.TextStyle(
                fontSize: 14,
                font: ralewayRegular,
                color: PdfColor.fromHex("#6D6D6D"),
              ),
            ),
            pw.SizedBox(height: 9),
            pw.Text(
              entry.value.toString(),
              style: pw.TextStyle(
                fontSize: 14,
                font: ralewayMedium,
                color: PdfColors.black,
              ),
            ),
          ],
        ),
      );

      return expand ? pw.Expanded(child: widget) : widget;
    }


    pw.Widget infoWidget(pw.ImageProvider icon, List<Map> items, String title,double marginTop,{double? fixedHeight}) {
      final entries = items
          .expand((map) => map.entries.where((e) => e.value.toString().trim().isNotEmpty))
          .toList();

      // Group into chunks of 2
      List<List<MapEntry>> rows = [];
      for (int i = 0; i < entries.length; i += 2) {
        rows.add(entries.sublist(i, (i + 2 > entries.length) ? entries.length : i + 2));
      }


      return pw.Container(
        padding: pw.EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        margin: pw.EdgeInsets.only(top: marginTop),
        decoration: pw.BoxDecoration(
          borderRadius: pw.BorderRadius.circular(13),
          border: pw.Border.all(color: greyBoarderColor, width: 1),
          color: PdfColors.white,
        ),
        height: fixedHeight,
        width: double.infinity,
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Row(
              children: [
                pw.Image(icon, height: 25),
                pw.SizedBox(width: 18),
                pw.Text(
                  title,
                  style: pw.TextStyle(
                    fontSize: 18,
                    color: PdfColors.black,
                    font: ralewayBold,
                  ),
                ),
              ],
            ),
            pw.SizedBox(height: 20),

            ...rows.map((row) {
              return pw.Row(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: row.map((entry) {
                  return pw.Expanded(
                    child: pw.Container(
                      margin: pw.EdgeInsets.only(right: 20,bottom: 33),
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            entry.key,
                            style: pw.TextStyle(
                              fontSize: 14,
                              font: ralewayRegular,
                              color: PdfColor.fromHex("#6D6D6D"),
                            ),
                          ),
                          pw.SizedBox(height: 9),
                          pw.Text(
                            entry.value.toString(),
                            style: pw.TextStyle(
                              fontSize: 14,
                              font: ralewayMedium,
                              color: PdfColors.black,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              );
            }).toList(),
          ],
        ),
      );
    }




    pdfInfoPage.add(
        pw.Padding(
            padding: pw.EdgeInsets.symmetric(horizontal: 55),
            child: pw.Row(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                mainAxisSize: pw.MainAxisSize.max,
                children: [
                  //------ Map Container
                  pw.Expanded(
                    child: pw.Container(
                        padding: pw.EdgeInsets.symmetric(horizontal: 20,vertical: 5),
                        margin: pw.EdgeInsets.only(top: 20),
                        decoration: pw.BoxDecoration(
                            borderRadius: pw.BorderRadius.circular(13),
                            border: pw.Border.all(color: greyBoarderColor,width:1),
                            color: PdfColors.white
                        ),
                        height: 705,
                        child: pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            mainAxisSize: pw.MainAxisSize.min,
                            mainAxisAlignment: pw.MainAxisAlignment.start,
                            children: [
                              pw.SizedBox(height: 10),
                              pw.Row(
                                  children: [
                                    pw.Image(locationIcon,height: 25),
                                    pw.SizedBox(width: 8),
                                    pw.Text(
                                        "La zona",
                                        style: pw.TextStyle(
                                            fontSize: 18,
                                            font: ralewayBold
                                        )
                                    ),
                                  ]
                              ),
                              pw.SizedBox(height: 25),
                              pw.Container(
                                height: 451,
                                width: double.infinity,
                                decoration: pw.BoxDecoration(
                                    color: PdfColors.white,
                                    borderRadius: pw.BorderRadius.circular(10),
                                    image: pw.DecorationImage(image: mapImage,fit: pw.BoxFit.cover)
                                ),
                              ),
                              pw.SizedBox(height: 28),
                              pw.Row(
                                  //mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                  children: [
                                    pw.Expanded(child: iconTextBoxForMap(shoppingIcon,"Supermercati",nearByPlaces["supermarket"].toString())),
                                    pw.SizedBox(width: 2),
                                    pw.Expanded(child: iconTextBoxForMap(schoolIcon,"Scuole",nearByPlaces["school"].toString())),
                                    pw.SizedBox(width: 2),
                                    pw.Expanded(child: iconTextBoxForMap(treeIcon,"Aree verdi",nearByPlaces["park"].toString())),
                                    pw.SizedBox(width: 2),
                                    pw.Expanded(child: iconTextBoxForMap(busIcon,"Fermate bus",nearByPlaces["bus_stop"].toString()),),

                                  ]
                              ),
                              (reportAcquirente.isDescrizioneDelQuartiereIncludeInBuyerReport ?? false) ?
                              pw.Padding(
                                  padding: pw.EdgeInsets.only(top: 20),
                                  child: pw.Text(
                                      reportAcquirente.descriptionNeighborhood ?? "",
                                      textAlign: pw.TextAlign.left,
                                      style: pw.TextStyle(
                                          fontSize: 13,
                                          font: ralewayRegular,
                                          height: 26
                                      )
                                  )
                              ) : pw.SizedBox.shrink(),
                              pw.SizedBox(height: 5),
                            ]
                        )
                    )
                  ),

                  pw.SizedBox(width: 35),
                  pw.Expanded(
                    child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          //------ Lo stabile Container
                          reportAcquirente.isInformazioniSulloStabileIncludeInBuyerReport ?? false ?
                          infoWidget(
                              buildingIcon,[
                            {
                              "Piani totali": reportAcquirente.totalPlans,
                              "Tipologia stabile": reportAcquirente.tipologiaStabile,
                              "Condizione stabile": reportAcquirente.stableCondition,
                              "Spese condominiali": "${localCurrencyFormatMain.format(reportAcquirente.condominiumExpenses).toString().trim()}€/mese",
                            }
                          ],"Lo stabile",20
                          ) : pw.SizedBox.shrink(),

                          //------ Riscaldamento ed Energia Container
                          reportAcquirente.isHeatingAndEnergyInBuyerReport ?? false ?
                          infoWidget(
                              heatEnergyIcon,[
                            {
                              "Riscaldamento": reportAcquirente.heating,
                              "Spese di riscaldamento": "${localCurrencyFormatMain.format(reportAcquirente.heatingCosts).toString().trim()}€/anno",
                              "Classe energetica": reportAcquirente.actualEnergyClass,
                              "Stato infissi": reportAcquirente.stateOfFixtures,
                            }
                          ],"Riscaldamento ed Energia",30
                          ) : pw.SizedBox.shrink(),

                          //------ Prospetto costi
                          reportAcquirente.isProspettoCostiInBuyerReport ?? false ?
                          infoWidget(
                              costIcon,[
                            {
                              if(reportAcquirente.listingPrice != null)
                                "La richiesta": "${localCurrencyFormatMain.format(reportAcquirente.listingPrice).toString().trim()}€",
                              if(reportAcquirente.landRegistryIncome != null)
                                "Rendita catastale": "${localCurrencyFormatMain.format(reportAcquirente.landRegistryIncome).toString().trim()}€",
                              if(reportAcquirente.notaryCost != null)
                                "Costi notarili stimati": "${localCurrencyFormatMain.format(reportAcquirente.notaryCost).toString().trim()}€",
                              if(reportAcquirente.registrationTax != null)
                                "Imposta di registro": "${localCurrencyFormatMain.format(reportAcquirente.registrationTax).toString().trim()}€",
                            }
                          ],"Prospetto costi",30,fixedHeight: [
                            if (reportAcquirente.listingPrice != null) "x",
                            if (reportAcquirente.landRegistryIncome != null) "x",
                            if (reportAcquirente.notaryCost != null) "x",
                            if (reportAcquirente.registrationTax != null) "x",
                          ].length <= 2 ? 210 : null
                          ) : pw.SizedBox.shrink(),
                        ]
                    )
                  )
                ]
            )
        )
    );
    pdf.addPage(
        pw.MultiPage(
          theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
          pageFormat: PdfPageFormat(1300, 940),
          orientation: pw.PageOrientation.landscape,
          margin: const pw.EdgeInsets.all(0),
          build: (pw.Context context) => pdfInfoPage,
          header: (pw.Context context) => commonHeader("Informazioni"),
          footer: (pw.Context context) => pageFooter,
        )
    );

    progress?.value = 0.60;


    //?------------------ Informazioni PAGE END --------------------------

    //?------------------ Fotografie PAGE START --------------------------
    
    List<pw.Widget> pdfPhotoPage = [];
    List<pw.Widget> pdfPhotoPageFullInternal = [];
    List<pw.Widget> pdfPhotoPageFullExternal = [];

    List<pw.Widget> _buildFixedImageRows(List<Map<String,dynamic>> images, {int imagesPerRow = 3,double? right,double? left,double? bottom,required bool isCenter}) {
      final List<pw.Widget> rows = [];

      for (int i = 0; i < images.length; i += imagesPerRow) {
        final rowImages = images.skip(i).take(imagesPerRow).toList();

        rows.add(
          pw.Container(
            child: pw.Row(
              mainAxisAlignment: isCenter ? pw.MainAxisAlignment.start : pw.MainAxisAlignment.start,
              children: rowImages.map((img) {
                return pw.Container(
                  margin: pw.EdgeInsets.only(right: right ?? 10, bottom: bottom ?? 10,left: left ?? 0),
                  width: 260,
                  height: 195,
                  decoration: pw.BoxDecoration(
                    borderRadius: pw.BorderRadius.circular(8),
                    image: pw.DecorationImage(
                      image: img["image"],
                      fit: pw.BoxFit.cover,
                    ),
                  ),
                );
              }).toList(),
            )
          ),
        );
      }

      return rows;
    }

    if(((reportAcquirente.isFotografieInterneInBuyerReport ?? false) || (reportAcquirente.isFotografieEsterneInBuyerReport ?? false)) && reportAcquirente.photoTemplateType == "short")
    pdfPhotoPage.add(
        pw.Container(
            padding: pw.EdgeInsets.only(left: 55,right: 55),
            child: pw.Row(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                mainAxisAlignment: pw.MainAxisAlignment.start,
                children: [
                  //------ Photo internal
                  reportAcquirente.isFotografieInterneInBuyerReport ?? false ?
                  pw.Container(
                      width: 864,
                      height: 711,
                      padding: pw.EdgeInsets.symmetric(horizontal: 20,vertical: 5),
                      margin: pw.EdgeInsets.only(top: 10),
                      decoration: pw.BoxDecoration(
                          borderRadius: pw.BorderRadius.circular(13),
                          border: pw.Border.all(color: greyBoarderColor,width: 1),
                          color: PdfColors.white
                      ),
                      child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          mainAxisSize: pw.MainAxisSize.max,
                          mainAxisAlignment: pw.MainAxisAlignment.start,
                          children: [
                            pw.SizedBox(height: 10),
                            pw.Text(
                                "Fotografie interni",
                                style: pw.TextStyle(
                                    fontSize: 18,
                                    font: ralewayBold
                                )
                            ),
                            pw.SizedBox(height: 22),

                            pw.Column(
                              crossAxisAlignment: pw.CrossAxisAlignment.center,
                              children: _buildFixedImageRows(internalImages, imagesPerRow: 3,right: 20,bottom: 20,isCenter: true),
                            ),

                            pw.SizedBox(height: 10),
                          ]
                      )
                  ) : pw.SizedBox.shrink(),

                  pw.SizedBox(width: 25),

                  //------ Photo external
                  reportAcquirente.isFotografieEsterneInBuyerReport ?? false ?
                  pw.Expanded(
                    flex: 2,
                    child: pw.Container(
                        padding: pw.EdgeInsets.symmetric(horizontal: 20,vertical: 5),
                        margin: pw.EdgeInsets.only(top: 10),
                        decoration: pw.BoxDecoration(
                            borderRadius: pw.BorderRadius.circular(13),
                            border: pw.Border.all(color: greyBoarderColor,width: 1),
                            color: PdfColors.white
                        ),
                        height: 711,
                        child: pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            mainAxisSize: pw.MainAxisSize.max,
                            mainAxisAlignment: pw.MainAxisAlignment.start,
                            children: [
                              pw.SizedBox(height: 10),
                              pw.Text(
                                  "Fotografie esterni",
                                  style: pw.TextStyle(
                                      fontSize: 18,
                                      font: ralewayBold
                                  )
                              ),
                              pw.SizedBox(height: 22),
                              pw.Column(
                                crossAxisAlignment: pw.CrossAxisAlignment.start,
                                children: _buildFixedImageRows(externalImages, imagesPerRow: 1,bottom: 20,isCenter: true,left: 0,right: 20),
                              ),
                              pw.SizedBox(height: 10),
                            ]
                        )
                    )
                  ) : pw.SizedBox.shrink(),

                ]
            )
        )
    );
    if(((reportAcquirente.isFotografieInterneInBuyerReport ?? false) || (reportAcquirente.isFotografieEsterneInBuyerReport ?? false)) && reportAcquirente.photoTemplateType == "short")
    pdf.addPage(
        pw.MultiPage(
          theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
          pageFormat: PdfPageFormat(1300, 940),
          orientation: pw.PageOrientation.landscape,
          margin: const pw.EdgeInsets.all(0),
          build: (pw.Context context) => pdfPhotoPage,
          header: (pw.Context context) => commonHeader("Fotografie"),
          footer: (pw.Context context) => pageFooter,
        )
    );

    //---Full image for internal
    if((reportAcquirente.isFotografieInterneInBuyerReport ?? false) && reportAcquirente.photoTemplateType == "wide"){
      internalImages.forEach((val){
        pdfPhotoPageFullInternal.add(pw.Container(
          padding: pw.EdgeInsets.only(left: 55, right: 55, top: 25, bottom: 6),
          child: pw.Stack(
            children: [
              pw.Container(
                width: 1190,
                height: 699,
                decoration: pw.BoxDecoration(
                  borderRadius: pw.BorderRadius.circular(12),
                  image: pw.DecorationImage(
                    image: val["image"],
                    fit: pw.BoxFit.cover,
                  ),
                ),
              ),
              pw.Positioned(
                  top: 10,
                  left: 10,
                  child: pw.Container(
                    height: 43,
                    padding: pw.EdgeInsets.symmetric(horizontal: 10),
                    alignment: pw.Alignment.center,
                    decoration: pw.BoxDecoration(
                      borderRadius: pw.BorderRadius.circular(21.5),
                      color: PdfColors.white
                    ),
                    child: pw.Text(val["tag"],style: pw.TextStyle(color: PdfColors.black,font: ralewayRegular,fontSize: 20))
                  ))
            ]
          )
        ));
      });
      pdf.addPage(
          pw.MultiPage(
            theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
            pageFormat: PdfPageFormat(1300, 940),
            orientation: pw.PageOrientation.landscape,
            margin: const pw.EdgeInsets.all(0),
            build: (pw.Context context) => pdfPhotoPageFullInternal,
            header: (pw.Context context) => commonHeader("Fotografie interni"),
            footer: (pw.Context context) => pageFooter,
          )
      );
    }

    //---Full image for external
    if((reportAcquirente.isFotografieEsterneInBuyerReport ?? false) && reportAcquirente.photoTemplateType == "wide"){
      externalImages.forEach((val){
        pdfPhotoPageFullExternal.add(pw.Container(
            padding: pw.EdgeInsets.only(left: 55, right: 55, top: 25, bottom: 6),
            child: pw.Stack(
                children: [
                  pw.Container(
                    width: 1190,
                    height: 699,
                    decoration: pw.BoxDecoration(
                      borderRadius: pw.BorderRadius.circular(12),
                      image: pw.DecorationImage(
                        image: val["image"],
                        fit: pw.BoxFit.cover,
                      ),
                    ),
                  ),
                  pw.Positioned(
                      top: 10,
                      left: 10,
                      child: pw.Container(
                          height: 43,
                          padding: pw.EdgeInsets.symmetric(horizontal: 10),
                          alignment: pw.Alignment.center,
                          decoration: pw.BoxDecoration(
                              borderRadius: pw.BorderRadius.circular(21.5),
                              color: PdfColors.white
                          ),
                          child: pw.Text(val["tag"],style: pw.TextStyle(color: PdfColors.black,font: ralewayRegular,fontSize: 20))
                      ))
                ]
            )
        ));
      });
      pdf.addPage(
          pw.MultiPage(
            theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
            pageFormat: PdfPageFormat(1300, 940),
            orientation: pw.PageOrientation.landscape,
            margin: const pw.EdgeInsets.all(0),
            build: (pw.Context context) => pdfPhotoPageFullExternal,
            header: (pw.Context context) => commonHeader("Fotografie esterni"),
            footer: (pw.Context context) => pageFooter,
          )
      );
    }

    progress?.value = 0.75;


    //?------------------ Fotografie PAGE END ----------------------------


    //?------------------ Stima Ristrutturazione PAGE SART ----------------------------


    List<pw.Widget> stimaPage = [];

    if((reportAcquirente.isStimaRistrutturazioneInBuyerReport ?? false) && (reportAcquirente.wantToIncludeEstimateForRenovation ?? false)){
      calculateRisFull(reportAcquirente: reportAcquirente);
    }


    if((reportAcquirente.isStimaRistrutturazioneInBuyerReport ?? false) && (reportAcquirente.wantToIncludeEstimateForRenovation ?? false))
    stimaPage.add(
      pw.Padding(padding: pw.EdgeInsets.symmetric(horizontal: 55),
        child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.SizedBox(height: 20),
              pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Expanded(
                        flex: 5,
                        child: pw.Column(
                            mainAxisAlignment: pw.MainAxisAlignment.start,
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              pw.Text(
                                  'Lavori',
                                  style: pw.TextStyle(
                                      fontSize: 18,
                                      color: PdfColor.fromHex('#000000'),
                                      font: ralewayBold
                                  )
                              ),
                              pw.SizedBox(height: 20),
                              pw.Row(
                                  mainAxisAlignment: pw.MainAxisAlignment.start,
                                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                                  children: [
                                    pw.Expanded(
                                        child: pw.Container(
                                            height: 550,
                                            decoration: pw.BoxDecoration(
                                                borderRadius: pw.BorderRadius.circular(10),
                                                color: PdfColor.fromHex('#66B393')
                                            ),
                                            child: pw.Column(
                                                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                children: [
                                                  pw.Padding(
                                                      padding: pw.EdgeInsets.symmetric(vertical: 20, horizontal: 10),
                                                      child: pw.Row(
                                                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                          children: [
                                                            pw.Row(
                                                                crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                                children: [
                                                                  pw.Image(
                                                                      hammerIcon,
                                                                      height: 25,
                                                                      width: 25
                                                                  ),
                                                                  pw.SizedBox(width: 10),
                                                                  pw.Container(
                                                                    width: 345,
                                                                    child: pw.Column(
                                                                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                                        children: [
                                                                          pw.SizedBox(height: 3),
                                                                          pw.Row(
                                                                              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                                              mainAxisSize: pw.MainAxisSize.max,
                                                                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                                              children: [

                                                                                pw.Text(
                                                                                    'Leggera',
                                                                                    style: pw.TextStyle(
                                                                                        fontSize: 16,
                                                                                        color: PdfColors.white,
                                                                                        font: ralewayMedium
                                                                                    )
                                                                                ),

                                                                                pw.Padding(
                                                                                  padding: pw.EdgeInsets.only(right: 10),
                                                                                  child: pw.Column(
                                                                                      crossAxisAlignment: pw.CrossAxisAlignment.end,
                                                                                      children: [
                                                                                        pw.Text(
                                                                                            '${localCurrencyFormatWithoutDecimal.format(stimaCalculationResponseLight["range_min"]).toString().trim()}-${localCurrencyFormatWithoutDecimal.format(stimaCalculationResponseLight["range_max"]).toString().trim()}€',
                                                                                            style: pw.TextStyle(
                                                                                                fontSize: 18,
                                                                                                color: PdfColors.white,
                                                                                                font: ralewayBold
                                                                                            )
                                                                                        ),
                                                                                        pw.Text(
                                                                                            '+iva',
                                                                                            style: pw.TextStyle(
                                                                                                fontSize: 13,
                                                                                                color: PdfColors.white,
                                                                                                font: ralewayBold
                                                                                            )
                                                                                        ),
                                                                                      ]
                                                                                  )
                                                                                ),
                                                                              ]
                                                                          ),
                                                                          pw.SizedBox(height: 15),
                                                                          ...standardFeatures.map((e){
                                                                            return pw.Column(
                                                                                crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                                                children: [
                                                                                  pw.Text(
                                                                                      e,
                                                                                      style: pw.TextStyle(
                                                                                          fontSize: 14,
                                                                                          color: PdfColors.white,
                                                                                          font: ralewayMedium
                                                                                      )
                                                                                  ),
                                                                                  pw.SizedBox(height: 8),
                                                                                ]
                                                                            );
                                                                          }).toList(),

                                                                        ]
                                                                    ),

                                                                  )

                                                                ]
                                                            ),
                                                          ]
                                                      )
                                                  ),
                                                  pw.Row(
                                                      children: [
                                                        pw.Expanded(
                                                            child: pw.Container(
                                                                padding: pw.EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                                                                decoration: pw.BoxDecoration(
                                                                    border: pw.Border(
                                                                      top: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                                      right: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                                    )
                                                                ),
                                                                child: pw.Column(
                                                                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                                                                    children: [
                                                                      pw.Row(
                                                                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                                        children: [
                                                                          pw.Image(
                                                                              materialStandardIcon,
                                                                              height: 25,
                                                                              width: 25
                                                                          ),
                                                                          pw.Text(
                                                                              'Materiali Standard',
                                                                              style: pw.TextStyle(
                                                                                  fontSize: 16,
                                                                                  color: PdfColors.white,
                                                                                  font: ralewayMedium
                                                                              )
                                                                          ),
                                                                        ],

                                                                      ),
                                                                      pw.Row(
                                                                          mainAxisAlignment: pw.MainAxisAlignment.end,
                                                                          children: [
                                                                            pw.Text(
                                                                                '${localCurrencyFormatWithoutDecimal.format(stimaCalculationResponseLight["material_standard_min"]).toString().trim()}-${localCurrencyFormatWithoutDecimal.format(stimaCalculationResponseLight["material_standard_max"]).toString().trim()}€',
                                                                                style: pw.TextStyle(
                                                                                    fontSize: 16,
                                                                                    color: PdfColors.white,
                                                                                    font: ralewayBold
                                                                                )
                                                                            ),
                                                                            pw.Text(
                                                                                '+iva',
                                                                                style: pw.TextStyle(
                                                                                    fontSize: 13,
                                                                                    color: PdfColors.white,
                                                                                    font: ralewayBold
                                                                                )
                                                                            ),
                                                                          ]
                                                                      )
                                                                    ]
                                                                )
                                                            )

                                                        ),
                                                        pw.Expanded(
                                                            child: pw.Container(
                                                                padding: pw.EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                                                                decoration: pw.BoxDecoration(
                                                                    border: pw.Border(
                                                                      top: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                                    )
                                                                ),
                                                                child: pw.Column(
                                                                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                                                                    children: [
                                                                      pw.Row(
                                                                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                                        children: [
                                                                          pw.Image(
                                                                              materialPremiumIcon,
                                                                              height: 25,
                                                                              width: 31
                                                                          ),
                                                                          pw.Text(
                                                                              'Materiali Premium',
                                                                              style: pw.TextStyle(
                                                                                  fontSize: 16,
                                                                                  color: PdfColors.white,
                                                                                  font: ralewayMedium
                                                                              )
                                                                          ),
                                                                        ],

                                                                      ),
                                                                      pw.Row(
                                                                          mainAxisAlignment: pw.MainAxisAlignment.end,
                                                                          children: [
                                                                            pw.Text(
                                                                                '${localCurrencyFormatWithoutDecimal.format(stimaCalculationResponseLight["material_permium_min"]).toString().trim()}-${localCurrencyFormatWithoutDecimal.format(stimaCalculationResponseLight["material_permium_max"]).toString().trim()}€',
                                                                                style: pw.TextStyle(
                                                                                    fontSize: 16,
                                                                                    color: PdfColors.white,
                                                                                    font: ralewayBold
                                                                                )
                                                                            ),
                                                                            pw.Text(
                                                                                '+iva',
                                                                                style: pw.TextStyle(
                                                                                    fontSize: 13,
                                                                                    color: PdfColors.white,
                                                                                    font: ralewayBold
                                                                                )
                                                                            ),
                                                                          ]
                                                                      )
                                                                    ]
                                                                )
                                                            )

                                                        ),
                                                      ]
                                                  )
                                                ]
                                            )
                                        )
                                    ),

                                    pw.SizedBox(width:30),

                                    pw.Expanded(
                                        child: pw.Container(
                                            height: 550,
                                            decoration: pw.BoxDecoration(
                                                borderRadius: pw.BorderRadius.circular(10),
                                                color: PdfColor.fromHex('#449272')
                                            ),
                                            child: pw.Column(
                                                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                children: [
                                                  pw.Padding(
                                                      padding: pw.EdgeInsets.symmetric(vertical: 20, horizontal: 10),
                                                      child: pw.Row(
                                                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                          children: [
                                                            pw.Row(
                                                                crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                                children: [
                                                                  pw.Image(
                                                                      hammerIcon,
                                                                      height: 25,
                                                                      width: 25
                                                                  ),
                                                                  pw.SizedBox(width: 10),
                                                                  pw.Container(
                                                                    width: 345,
                                                                    child: pw.Column(
                                                                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                                        children: [
                                                                          pw.SizedBox(height: 3),
                                                                          pw.Row(
                                                                              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                                              mainAxisSize: pw.MainAxisSize.max,
                                                                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                                              children: [
                                                                                pw.Text(
                                                                                    'Completa',
                                                                                    style: pw.TextStyle(
                                                                                        fontSize: 16,
                                                                                        color: PdfColors.white,
                                                                                        font: ralewayMedium
                                                                                    )
                                                                                ),



                                                                                pw.Padding(
                                                                                    padding: pw.EdgeInsets.only(right: 10),
                                                                                    child : pw.Column(
                                                                                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                                                                                    mainAxisSize: pw.MainAxisSize.min,
                                                                                    children: [
                                                                                      pw.Text(
                                                                                          '${localCurrencyFormatWithoutDecimal.format(stimaCalculationResponseFull["range_min"]).toString().trim()}-${localCurrencyFormatWithoutDecimal.format(stimaCalculationResponseFull["range_max"]).toString().trim()}€',
                                                                                          style: pw.TextStyle(
                                                                                              fontSize: 18,
                                                                                              color: PdfColors.white,
                                                                                              font: ralewayBold
                                                                                          )
                                                                                      ),
                                                                                      pw.Text(
                                                                                          '+iva',
                                                                                          style: pw.TextStyle(
                                                                                              fontSize: 13,
                                                                                              color: PdfColors.white,
                                                                                              font: ralewayBold
                                                                                          )
                                                                                      ),
                                                                                    ]
                                                                                )),
                                                                              ]
                                                                          ),
                                                                          pw.SizedBox(height: 15),
                                                                          ...premiumFeatures.map((e){
                                                                            return pw.Column(
                                                                                crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                                                children: [
                                                                                  pw.Text(
                                                                                      e,
                                                                                      style: pw.TextStyle(
                                                                                          fontSize: 14,
                                                                                          color: PdfColors.white,
                                                                                          font: ralewayMedium
                                                                                      )
                                                                                  ),
                                                                                  pw.SizedBox(height: 8),
                                                                                ]
                                                                            );
                                                                          }).toList(),

                                                                        ]
                                                                    ),

                                                                  )

                                                                ]
                                                            ),
                                                            // pw.SizedBox(width: 10),


                                                          ]
                                                      )
                                                  ),
                                                  pw.Column(
                                                      children: [
                                                        pw.Container(
                                                            padding: pw.EdgeInsets.all(10),
                                                            decoration: pw.BoxDecoration(
                                                                border: pw.Border(
                                                                  top: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                                )
                                                            ),
                                                            child: pw.Row(
                                                                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                                crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                                children: [
                                                                  pw.Row(
                                                                    children: [
                                                                      pw.Image(
                                                                          infissiDataIcon,
                                                                          height: 25,
                                                                          width: 25
                                                                      ),
                                                                      pw.SizedBox(width: 20),
                                                                      pw.Text(
                                                                          'Infissi',
                                                                          style: pw.TextStyle(
                                                                              fontSize: 16,
                                                                              color: PdfColors.white,
                                                                              font: ralewayMedium
                                                                          )
                                                                      ),
                                                                    ],

                                                                  ),

                                                                  pw.Row(
                                                                      mainAxisAlignment: pw.MainAxisAlignment.end,
                                                                      children: [
                                                                        pw.Text(
                                                                            '${localCurrencyFormatWithoutDecimal.format(stimaCalculationResponseFull["infissi_min"]).toString().trim()}-${localCurrencyFormatWithoutDecimal.format(stimaCalculationResponseFull["infissi_max"]).toString().trim()}€',
                                                                            style: pw.TextStyle(
                                                                                fontSize: 16,
                                                                                color: PdfColors.white,
                                                                                font: ralewayBold
                                                                            )
                                                                        ),
                                                                        pw.Text(
                                                                            '+iva',
                                                                            style: pw.TextStyle(
                                                                                fontSize: 13,
                                                                                color: PdfColors.white,
                                                                                font: ralewayBold
                                                                            )
                                                                        ),
                                                                      ]
                                                                  )
                                                                ]
                                                            )

                                                        ),
                                                        pw.Row(
                                                            children: [
                                                              pw.Expanded(
                                                                  child: pw.Container(
                                                                      padding: pw.EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                                                                      decoration: pw.BoxDecoration(
                                                                          border: pw.Border(
                                                                            top: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                                            right: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                                          )
                                                                      ),
                                                                      child: pw.Column(
                                                                          crossAxisAlignment: pw.CrossAxisAlignment.end,
                                                                          children: [
                                                                            pw.Row(
                                                                              crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                                              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                                              children: [
                                                                                pw.Image(
                                                                                    materialStandardIcon,
                                                                                    height: 25,
                                                                                    width: 25
                                                                                ),
                                                                                pw.Text(
                                                                                    'Materiali Standard',
                                                                                    style: pw.TextStyle(
                                                                                        fontSize: 16,
                                                                                        color: PdfColors.white,
                                                                                        font: ralewayMedium
                                                                                    )
                                                                                ),
                                                                              ],

                                                                            ),
                                                                            pw.Row(
                                                                                mainAxisAlignment: pw.MainAxisAlignment.end,
                                                                                children: [
                                                                                  pw.Text(
                                                                                      '${localCurrencyFormatWithoutDecimal.format(stimaCalculationResponseFull["material_standard_min"]).toString().trim()}-${localCurrencyFormatWithoutDecimal.format(stimaCalculationResponseFull["material_standard_max"]).toString().trim()}€',
                                                                                      style: pw.TextStyle(
                                                                                          fontSize: 16,
                                                                                          color: PdfColors.white,
                                                                                          font: ralewayBold
                                                                                      )
                                                                                  ),
                                                                                  pw.Text(
                                                                                      '+iva',
                                                                                      style: pw.TextStyle(
                                                                                          fontSize: 13,
                                                                                          color: PdfColors.white,
                                                                                          font: ralewayBold
                                                                                      )
                                                                                  ),
                                                                                ]
                                                                            )
                                                                          ]
                                                                      )
                                                                  )

                                                              ),
                                                              pw.Expanded(
                                                                  child: pw.Container(
                                                                      padding: pw.EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                                                                      decoration: pw.BoxDecoration(
                                                                          border: pw.Border(
                                                                            top: pw.BorderSide( width: 1, color: PdfColor.fromHex('#AFD8C7') ),
                                                                          )
                                                                      ),
                                                                      child: pw.Column(
                                                                          crossAxisAlignment: pw.CrossAxisAlignment.end,
                                                                          children: [
                                                                            pw.Row(
                                                                              crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                                              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                                              children: [
                                                                                pw.Image(
                                                                                    materialPremiumIcon,
                                                                                    height: 25,
                                                                                    width: 31
                                                                                ),
                                                                                pw.Text(
                                                                                    'Materiali Premium',
                                                                                    style: pw.TextStyle(
                                                                                        fontSize: 16,
                                                                                        color: PdfColors.white,
                                                                                        font: ralewayMedium
                                                                                    )
                                                                                ),
                                                                              ],

                                                                            ),
                                                                            pw.Row(
                                                                                mainAxisAlignment: pw.MainAxisAlignment.end,
                                                                                children: [
                                                                                  pw.Text(
                                                                                      '${localCurrencyFormatWithoutDecimal.format(stimaCalculationResponseFull["material_permium_min"]).toString().trim()}-${localCurrencyFormatWithoutDecimal.format(stimaCalculationResponseFull["material_permium_max"]).toString().trim()}€',
                                                                                      style: pw.TextStyle(
                                                                                          fontSize: 16,
                                                                                          color: PdfColors.white,
                                                                                          font: ralewayBold
                                                                                      )
                                                                                  ),
                                                                                  pw.Text(
                                                                                      '+iva',
                                                                                      style: pw.TextStyle(
                                                                                          fontSize: 13,
                                                                                          color: PdfColors.white,
                                                                                          font: ralewayBold
                                                                                      )
                                                                                  ),
                                                                                ]
                                                                            )
                                                                          ]
                                                                      )
                                                                  )

                                                              ),
                                                            ]
                                                        )
                                                      ]
                                                  ),

                                                ]
                                            )
                                        )
                                    )
                                  ]
                              )
                            ]
                        )

                    ),

                    pw.SizedBox(width: 50),

                    pw.Expanded(
                        flex: 2,
                        child: pw.Column(
                            mainAxisAlignment: pw.MainAxisAlignment.start,
                            mainAxisSize: pw.MainAxisSize.max,
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              pw.Text(
                                  'Classe energetica',
                                  style: pw.TextStyle(
                                      fontSize: 18,
                                      color: PdfColor.fromHex('#000000'),
                                      font: ralewayBold
                                  )
                              ),
                              pw.SizedBox(height: 3),
                              pw.Text(
                                  'Miglioramento stimato sostituendo gli infissi.',
                                  style: pw.TextStyle(
                                      fontSize: 10,
                                      color: PdfColor.fromHex('#8E8E8E'),
                                      fontItalic: ralewayItalic
                                  )
                              ),
                              pw.SizedBox(height: 5),
                              pw.Row(
                                  children: [
                                    pw.Expanded(
                                        child: pw.Container(
                                            height: 550,
                                            width: 300,
                                            decoration: pw.BoxDecoration(
                                              borderRadius: pw.BorderRadius.circular(10),
                                            ),
                                            child: pw.Column(
                                                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                children: [

                                                  pw.Row(
                                                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                      children: [
                                                        pw.Container(
                                                            height: 78,
                                                            width: 125,
                                                            padding: pw.EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                                                            decoration: pw.BoxDecoration(
                                                                borderRadius: pw.BorderRadius.circular(10),
                                                                border: pw.Border.all(
                                                                    width: 1,
                                                                    color: greyBoarderColor
                                                                )
                                                            ),
                                                            child: pw.Row(
                                                                children: [
                                                                  pw.Image(
                                                                    arrowHouseIcon,
                                                                    width: 54,
                                                                    height: 40,
                                                                  ),
                                                                  pw.Container(
                                                                      height: 43,
                                                                      width: 43,
                                                                      decoration: pw.BoxDecoration(
                                                                          color: getEnergyColorCode(reportAcquirente.actualEnergyClass!),
                                                                          borderRadius: pw.BorderRadius.circular(21.5)
                                                                      ),
                                                                      child: pw.Center(
                                                                          child: pw.Text(
                                                                              reportAcquirente.actualEnergyClass!,
                                                                              style: pw.TextStyle(
                                                                                  font: ralewayBold,
                                                                                  fontSize: 30,
                                                                                  color: PdfColors.white
                                                                              )
                                                                          )
                                                                      )

                                                                  )
                                                                ]
                                                            )
                                                        ),
                                                        pw.Container(
                                                            height: 97,
                                                            width: 10,
                                                            child: pw.Stack(
                                                                overflow: pw.Overflow.visible,

                                                                children: [
                                                                  pw.Positioned(
                                                                      child: pw.Image(
                                                                        arrowIcon,
                                                                        width: 37,
                                                                        height: 28,
                                                                      ),
                                                                      top: 33,
                                                                      left: -7
                                                                  )
                                                                ]
                                                            )
                                                        ),
                                                        pw.Container(
                                                            height: 97,
                                                            width: 160,
                                                            padding: pw.EdgeInsets.only(right: 15, left: 30, top: 10, bottom: 10),
                                                            decoration: pw.BoxDecoration(
                                                                borderRadius: pw.BorderRadius.circular(10),
                                                                border: pw.Border.all(
                                                                    width: 1,
                                                                    color: greyBoarderColor
                                                                )

                                                            ),

                                                            child: pw.Row(
                                                                children: [
                                                                  pw.Image(
                                                                    arrowHouseIcon,
                                                                    width: 64,
                                                                    height: 48,
                                                                  ),
                                                                  pw.Container(
                                                                      height: 43,
                                                                      width: 43,
                                                                      decoration: pw.BoxDecoration(
                                                                          color: getEnergyColorCode(getNextBetterEnergyClass(reportAcquirente.actualEnergyClass!)),
                                                                          borderRadius: pw.BorderRadius.circular(21.5)
                                                                      ),
                                                                      child: pw.Center(
                                                                          child: pw.Text(
                                                                              getNextBetterEnergyClass(reportAcquirente.actualEnergyClass!),
                                                                              style: pw.TextStyle(
                                                                                  font: ralewayBold,
                                                                                  fontSize: 30,
                                                                                  color: PdfColors.white
                                                                              )
                                                                          )
                                                                      )

                                                                  )
                                                                ]
                                                            )
                                                        )

                                                      ]
                                                  ),


                                                  pw.Column(
                                                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                      children: [
                                                        pw.Text(
                                                            'Bonus ristrutturazioni 2025',
                                                            style: pw.TextStyle(
                                                                fontSize: 18,
                                                                color: PdfColor.fromHex('#000000'),
                                                                font: ralewayBold
                                                            )
                                                        ),
                                                        pw.SizedBox(height: 10),
                                                        pw.Container(
                                                            decoration: pw.BoxDecoration(
                                                                border: pw.Border.all(
                                                                  width: 1,
                                                                  color: greyBoarderColor,
                                                                ),
                                                                borderRadius: pw.BorderRadius.circular(10)
                                                            ),
                                                            padding: pw.EdgeInsets.symmetric(vertical: 15, horizontal: 10),
                                                            child: pw.Column(
                                                                crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                                children: [
                                                                  pw.Row(
                                                                      mainAxisAlignment: pw.MainAxisAlignment.start,
                                                                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                                      children: [
                                                                        pw.Container(
                                                                            height: 41,
                                                                            width: 41,
                                                                            decoration: pw.BoxDecoration(
                                                                                borderRadius: pw.BorderRadius.circular(20.5),
                                                                                color: PdfColor.fromHex('#489B79')
                                                                            ),
                                                                            margin: pw.EdgeInsets.only(right:10),
                                                                            child: pw.Center(
                                                                              child: pw.Text(
                                                                                  '50%',
                                                                                  style: pw.TextStyle(
                                                                                      fontSize: 17,
                                                                                      color: PdfColor.fromHex('#ffffff'),
                                                                                      font: ralewayBold
                                                                                  )
                                                                              ),
                                                                            )

                                                                        ),
                                                                        pw.Text(
                                                                            'Sulla prima casa',
                                                                            style: pw.TextStyle(
                                                                                fontSize: 16,
                                                                                color: PdfColor.fromHex('#489B79'),
                                                                                font: ralewayBold
                                                                            )
                                                                        ),


                                                                      ]
                                                                  ),
                                                                  pw.Row(
                                                                      mainAxisAlignment: pw.MainAxisAlignment.start,
                                                                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                                      children: [
                                                                        pw.Container(
                                                                            height: 36,
                                                                            width: 36,
                                                                            decoration: pw.BoxDecoration(
                                                                                borderRadius: pw.BorderRadius.circular(18),
                                                                                color: PdfColor.fromHex('#489B79')

                                                                            ),
                                                                            margin: pw.EdgeInsets.only(right:10, left: 40),
                                                                            child: pw.Center(
                                                                              child: pw.Text(
                                                                                  '36%',
                                                                                  style: pw.TextStyle(
                                                                                      fontSize: 14,
                                                                                      color: PdfColor.fromHex('#ffffff'),
                                                                                      font: ralewayBold
                                                                                  )
                                                                              ),
                                                                            )

                                                                        ),
                                                                        pw.Text(
                                                                            'Sulla seconda casa',
                                                                            style: pw.TextStyle(
                                                                                fontSize: 14,
                                                                                color: PdfColor.fromHex('#489B79'),
                                                                                font: ralewayBold
                                                                            )
                                                                        ),


                                                                      ]
                                                                  ),
                                                                  pw.SizedBox(height: 15),
                                                                  pw.Text(
                                                                      'Il bonus fiscale consiste in una detrazione dall’Irpef, da ripartire in 10 quote annuali di pari importo, del 50% delle spese sostenute sulla prima casa fino ad un massimo di 96.000€ e 36% sulla seconda casa fino ad un massimo di 48.000€.',
                                                                      overflow: pw.TextOverflow.visible,
                                                                      textAlign: pw.TextAlign.justify,
                                                                      style: pw.TextStyle(

                                                                          fontSize: 11,
                                                                          height: 16,
                                                                          letterSpacing: 0.01,
                                                                          color: PdfColor.fromHex('#737373'),
                                                                          font: ralewayMedium
                                                                      )
                                                                  ),
                                                                ]

                                                            )
                                                        ),

                                                      ]
                                                  ),

                                                  pw.Column(
                                                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                      children: [
                                                        pw.Text(
                                                            'Tempistiche',
                                                            style: pw.TextStyle(
                                                                fontSize: 18,
                                                                color: PdfColor.fromHex('#000000'),
                                                                font: ralewayBold
                                                            )
                                                        ),
                                                        pw.SizedBox(height: 10),
                                                        pw.Container(
                                                            decoration: pw.BoxDecoration(
                                                                border: pw.Border.all(
                                                                  width: 1,
                                                                  color: greyBoarderColor,
                                                                ),
                                                                borderRadius: pw.BorderRadius.circular(10)
                                                            ),
                                                            padding: pw.EdgeInsets.symmetric(vertical: 15, horizontal: 20),
                                                            child: pw.Column(
                                                                crossAxisAlignment: pw.CrossAxisAlignment.start,
                                                                children: [
                                                                  pw.Row(
                                                                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                                                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                                      children: [
                                                                        pw.Text(
                                                                            'Durata presunta cantiere',
                                                                            style: pw.TextStyle(
                                                                                fontSize: 14,
                                                                                color: PdfColor.fromHex('#737373'),
                                                                                font: ralewayMedium
                                                                            )
                                                                        ),
                                                                        pw.Container(
                                                                          height: 35,
                                                                          width: 35,
                                                                          decoration: pw.BoxDecoration(
                                                                              image: pw.DecorationImage(image: workDurationIcon, fit: pw.BoxFit.cover)
                                                                          ),

                                                                        ),
                                                                      ]
                                                                  ),
                                                                  pw.SizedBox(height: 15),
                                                                  pw.Row(
                                                                      mainAxisAlignment: pw.MainAxisAlignment.start,
                                                                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                                                                      children: [
                                                                        pw.Text(
                                                                            '75 giorni lavorativi',
                                                                            style: pw.TextStyle(
                                                                                fontSize: 22,
                                                                                color: PdfColor.fromHex('#000000'),
                                                                                font: ralewayBold
                                                                            )
                                                                        ),
                                                                      ]
                                                                  ),
                                                                  pw.SizedBox(height: 5),
                                                                  pw.Text(
                                                                      'da inizio lavori',
                                                                      overflow: pw.TextOverflow.visible,
                                                                      textAlign: pw.TextAlign.justify,
                                                                      style: pw.TextStyle(

                                                                          fontSize: 14,
                                                                          color: PdfColor.fromHex('#000000'),
                                                                          font: ralewayMedium
                                                                      )
                                                                  ),
                                                                ]

                                                            )
                                                        )
                                                      ]
                                                  )


                                                ]
                                            )
                                        )
                                    )
                                  ]
                              )
                            ]
                        )


                    )

                  ]
              ),

              pw.SizedBox(height: 20),
              pw.Row(
                children: [
                  pw.Expanded(
                    flex: 5,
                      child: pw.Text(
                      'La presente quotazione è una stima e potrebbe subire variazioni in sede di preventivazione definitiva. Si consiglia di venire in sede per avere un preventivo gratuito personalizzato e personalizzato. Nella ristrutturazione leggera i materiali inclusi sono: porte interne, pavimenti e rivestimenti bagno, rubinetterie, sanitari, kit doccia incasso, piatti doccia, box doccia, mobili bagno e termoarredi. Nella ristrutturazione completa oltre ai materiali precedenti sono inclusi anche i pavimenti di tutta casa, battiscopa, materiale elettrico e illuminotecnico. I materiali Standard sono i materiali da capitolato di buona finitura e dall’ottimo rapporto qualità/prezzo. I materiali Premium sono materiali di prima scelta e dal massimo livello qualitativo.',
                      overflow: pw.TextOverflow.visible,
                      textAlign: pw.TextAlign.justify,
                      style: pw.TextStyle(
                          fontSize: 10,
                          height: 13,
                          letterSpacing: 0.02,
                          color: PdfColor.fromHex('#8E8E8E'),
                          font: ralewayBold
                      )
                  )),
                  pw.SizedBox(width: 50),
                  pw.Expanded(
                      flex: 2,
                      child: pw.Container(
                        height: 72,
                        padding: pw.EdgeInsets.symmetric(horizontal: 15),
                        decoration: pw.BoxDecoration(
                            borderRadius: pw.BorderRadius.circular(10),
                            color: PdfColor.fromHex("#499B79")
                        ),
                        child: pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.start,
                            crossAxisAlignment: pw.CrossAxisAlignment.center,
                            children: [
                              pw.Image(
                                newarcWhiteLogo,
                                height: 30,
                              ),
                              pw.SizedBox(width: 20),
                              pw.Text(
                                  '************',
                                  style: pw.TextStyle(
                                      fontSize: 22,
                                      color: PdfColors.white,
                                      font: ralewayBold
                                  )
                              ),
                            ],
                        )
                      ),
                  ),
                ]
              ),
              pw.SizedBox(height: 5),
            ]
        )
      )
    );


    pdf.addPage(
        pw.MultiPage(
          theme:
          pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
          pageFormat: PdfPageFormat(1300, 940),
          orientation: pw.PageOrientation.landscape,
          margin: const pw.EdgeInsets.all(0),
          build: (pw.Context context) => stimaPage,
          header: (pw.Context context) => pw.Container(
              width: double.infinity,
              height: 92,
              decoration: pw.BoxDecoration(
                color: PdfColors.black,
              ),
              padding: pw.EdgeInsets.symmetric(horizontal: 55),
              margin: pw.EdgeInsets.only(bottom: 10),
              child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text(
                        "Ristrutturazione",
                        style: pw.TextStyle(
                            fontSize: 30,
                            font: ralewayBold,
                            color: PdfColors.white
                        )
                    ),
                    pw.Row(children: [
                      pw.Text(
                          "Stima eseguita da",
                          style: pw.TextStyle(
                              fontSize: 15,
                              font: ralewayRegular,
                              color: PdfColors.white
                          )
                      ),
                      pw.SizedBox(width: 21),
                      pw.Image(newarcLogo, height: 50,width: 134)
                    ]),
                  ]
              )
          ),
          footer: (pw.Context context) => pageFooter,
        )
    );

    progress?.value = 0.85;


    //?------------------ Stima Ristrutturazione PAGE END ----------------------------

    //?------------------ Agency PAGE START --------------------------

    pw.Widget pdfAgencyPage = pw.Container(
        width: 1300,
        height: 940,
        color: pdfPrimaryColor,
        child: pw.Stack(
          children: [
            pw.Positioned(
              bottom: 0,
              child: pw.Opacity(
                opacity: 0.5,
                child: pw.Container(
                  child: pw.Image(
                    skyLineImg,
                    width: 1300,
                  ),
                ),
              ),
            ),
            pw.Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.center,
                mainAxisAlignment: pw.MainAxisAlignment.center,
                mainAxisSize: pw.MainAxisSize.max,
                children: [
                  pw.SizedBox(height: 112),
                  pw.Text(
                      "Contattaci",
                      style: pw.TextStyle(
                          fontSize: 40,
                          font: ralewayBold,
                          color: PdfColors.white
                      )
                  ),
                  pw.SizedBox(height: 50),
                  pw.Container(
                      width: 711,
                      height: 214,
                      padding: pw.EdgeInsets.symmetric(horizontal: 53,vertical: 5),
                      decoration: pw.BoxDecoration(
                        borderRadius: pw.BorderRadius.circular(13),
                        color: PdfColors.white,
                        boxShadow: [
                          pw.BoxShadow(
                            color: pdfPrimaryColor,
                            offset: PdfPoint(0, 0),
                            blurRadius: 25,
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.center,
                          mainAxisSize: pw.MainAxisSize.min,
                          mainAxisAlignment: pw.MainAxisAlignment.start,
                          children: [
                            pw.SizedBox(height: 10),
                            pw.Text(
                                "Report realizzato da",
                                style: pw.TextStyle(
                                    fontSize: 20,
                                    font: ralewayBold
                                )
                            ),
                            pw.SizedBox(height: 40),

                            pw.Row(
                                mainAxisAlignment: pw.MainAxisAlignment.center,
                                crossAxisAlignment: pw.CrossAxisAlignment.center,
                                children: [
                                  pw.Container(
                                      width: 276,
                                      child: pw.Row(
                                          children: [
                                            pw.Container(
                                              height: 75,
                                              width: 75,
                                              decoration: pw.BoxDecoration(
                                                  color: PdfColors.white,
                                                  shape: pw.BoxShape.circle,
                                                  image: pw.DecorationImage(image: agencyPersonLogo,fit: pw.BoxFit.cover)
                                              ),
                                            ),
                                            pw.SizedBox(width: 12),
                                            pw.Text(
                                                "${agencyPersonData.name} ${agencyPersonData.surname}",
                                                style: pw.TextStyle(
                                                    fontSize: 17,
                                                    font: ralewayMedium
                                                )
                                            ),
                                          ]
                                      )
                                  ),
                                  pw.SizedBox(width: 73),
                                  pw.Container(
                                      width: 238,
                                      height: 63,
                                      padding: pw.EdgeInsets.symmetric(horizontal: 26),
                                      decoration: pw.BoxDecoration(
                                          borderRadius: pw.BorderRadius.circular(8),
                                          color: PdfColor.fromHex("#F2F2F2")
                                      ),
                                      alignment: pw.Alignment.center,
                                      child: pw.Row(
                                          mainAxisAlignment: pw.MainAxisAlignment.start,
                                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                                          children: [
                                            pw.Image(phoneIcon,height: 24),
                                            pw.SizedBox(width: 25),
                                            pw.Text(
                                              phoneNumberMaskFormatterItalian.maskText(agencyPersonData.phone ?? ""),
                                              textAlign: pw.TextAlign.center,
                                              style: pw.TextStyle(
                                                fontSize: 18,
                                                font: ralewayMedium,
                                              )
                                            ),
                                          ]
                                      )
                                  )
                                ]
                            ),
                            pw.SizedBox(height: 10),
                          ]
                      )
                  ),
                  pw.SizedBox(height: 19),


                  //------ Agency
                  pw.Container(
                      width: 711,
                      height: 214,
                      padding: pw.EdgeInsets.symmetric(horizontal: 53,vertical: 20),
                      margin: pw.EdgeInsets.only(top: 20),
                      decoration: pw.BoxDecoration(
                        borderRadius: pw.BorderRadius.circular(13),
                        color: PdfColors.white,
                        boxShadow: [
                          pw.BoxShadow(
                            color: pdfPrimaryColor,
                            offset: PdfPoint(0, 0),
                            blurRadius: 25,
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.center,
                          mainAxisSize: pw.MainAxisSize.min,
                          mainAxisAlignment: pw.MainAxisAlignment.start,
                          children: [
                            pw.Text(
                                "L’agenzia",
                                style: pw.TextStyle(
                                    fontSize: 20,
                                    font: ralewayBold
                                )
                            ),
                            pw.SizedBox(height: 10),
                            pw.Row(
                                mainAxisAlignment: pw.MainAxisAlignment.start,
                                crossAxisAlignment: pw.CrossAxisAlignment.end,
                                children: [
                                  pw.Container(
                                    height: 123,
                                    width: 250,
                                    decoration: pw.BoxDecoration(
                                        color: PdfColors.white,
                                        image: pw.DecorationImage(image: agencyLogo,fit: pw.BoxFit.contain)
                                    ),
                                  ),
                                  pw.SizedBox(width: 125),
                                  pw.SizedBox(
                                    height: 123,
                                    child: pw.Column(
                                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                                      mainAxisAlignment: pw.MainAxisAlignment.center,
                                      children: [
                                        pw.Text(
                                          agencyData.name ?? "",
                                          style: pw.TextStyle(
                                              fontSize: 18,
                                              font: ralewayBold
                                          )
                                        ),
                                        pw.SizedBox(height: 10),
                                        pw.Text(
                                          "${agencyData.streetName ?? ""} ${agencyData.streetNumber ?? ""}, ${agencyData.city ?? ""}",
                                          style: pw.TextStyle(
                                              fontSize: 18,
                                              font: ralewayRegular
                                          )
                                        ),
                                        pw.SizedBox(height: 2),
                                        pw.Text(
                                          phoneNumberMaskFormatterItalian.maskText(agencyData.phone ?? ""),
                                          style: pw.TextStyle(
                                              fontSize: 18,
                                              font: ralewayRegular
                                          )
                                        ),
                                        pw.SizedBox(height: 2),
                                        pw.Text(
                                          agencyData.email ?? "",
                                          style: pw.TextStyle(
                                              fontSize: 18,
                                              font: ralewayRegular
                                          )
                                        ),
                                      ]
                                    )
                                  )
                                ]
                            ),
                          ]
                      )
                  ),
                ]
            )),

          ]
        )
    );

    pdf.addPage(
        pw.Page(
          theme:
          pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
          pageFormat: PdfPageFormat(1300, 940),
          orientation: pw.PageOrientation.landscape,
          margin: const pw.EdgeInsets.all(0),
          build: (pw.Context context) => pdfAgencyPage,
        )
    );

    progress?.value = 0.95;

    //?------------------ Agency PAGE END ----------------------------



    //?------------------ SAVE PDF --------------------------

    final fileName = generateFileName(reportAcquirente.addressInfo?.toShortAddress());
    progress?.value = 1.00;
    // Save PDF to bytes
    final pdfBytes = await pdf.save();
    // Create a Blob from PDF bytes
    final blob = html.Blob([pdfBytes], 'application/pdf');
    // Create a link element
    final url = html.Url.createObjectUrlFromBlob(blob);
    final anchor = html.AnchorElement(href: url)
      ..setAttribute('download', fileName + '.pdf')
      ..click();

    // Clean up
    html.Url.revokeObjectUrl(url);



  }catch(e,s){
    log("Error while generating buy report PDF ${e.toString()}");
    log("Stack trace while generating buy report PDF ${s.toString()}");
  }

}

String generateFileName(String? address) {
  if (address == null || address.trim().isEmpty) {
    return "Report_Unknown_Address";
  }
  final cleanAddress = address
      .trim()
      .replaceAll(RegExp(r'\s+'), '_')
      .replaceAll(RegExp(r'[^\w\d_]'), '');

  return "Report_$cleanAddress";
}

PdfColor getEnergyColorCode(String code) {
  code = code.toLowerCase();

  switch (code) {
    case 'g':
      return PdfColor.fromHex('#D30202');
    case 'f':
      return PdfColor.fromHex('#E8422E');
    case 'e':
      return PdfColor.fromHex('#F38F24');
    case 'd':
      return PdfColor.fromHex('#FDDF04');
    case 'c':
      return PdfColor.fromHex('#7DCB2D');
    case 'b':
      return PdfColor.fromHex('#25B106');
    case 'a1':
      return PdfColor.fromHex('#037603');
    case 'a2':
      return PdfColor.fromHex('#037603');
    case 'a3':
      return PdfColor.fromHex('#037603');
    case 'a4':
      return PdfColor.fromHex('#037603');
    default:
      return PdfColor.fromHex('#D30202');
  }
}




calculateRisFull({required ReportAcquirente reportAcquirente}) {
  double x = getXValue(a: 500, b: 1000,reportAcquirente: reportAcquirente);

  stimaCalculationResponseFull['actual'] = int.tryParse(x.toString())??0;
  stimaCalculationResponseFull['range_min'] = roundToNearestThousand(x * 0.9);
  stimaCalculationResponseFull['range_max'] = roundToNearestThousand(x * 1.1);

  int doorsIns =  reportAcquirente.rooms ?? 0;
  int bathsIns = reportAcquirente.numberOfBathrooms ?? 0;
  double walkableIns = (reportAcquirente.grossSquareFootage ?? 0) * 0.95;
  int localsIns = reportAcquirente.rooms ?? 0;
  double fixtureIns = (reportAcquirente.rooms ?? 0) * 4;

  double materialStandardFull =
      (doorsIns * 500) +
          (bathsIns * 3000) +
          (walkableIns * 25) +
          (localsIns * 180);

  double materialPremiumFull =
      (doorsIns * 800) +
          (bathsIns * 5000) +
          (walkableIns * 65) +
          (localsIns * 300);

  stimaCalculationResponseFull['material_standard'] = int.tryParse(materialStandardFull.toString());
  stimaCalculationResponseFull['material_standard_min'] = roundToNearestThousand(materialStandardFull * 0.9);
  stimaCalculationResponseFull['material_standard_max'] = roundToNearestThousand(materialStandardFull * 1.1);
  stimaCalculationResponseFull['material_permium'] = int.tryParse(materialPremiumFull.toString());
  stimaCalculationResponseFull['material_permium_min'] = roundToNearestThousand(materialPremiumFull * 0.9);
  stimaCalculationResponseFull['material_permium_max'] = roundToNearestThousand(materialPremiumFull * 1.1);
  stimaCalculationResponseFull['infissi'] = fixtureIns * 620;
  stimaCalculationResponseFull['infissi_min'] = roundToNearestThousand(stimaCalculationResponseFull['infissi'] * 0.9);
  stimaCalculationResponseFull['infissi_max'] = roundToNearestThousand(stimaCalculationResponseFull['infissi'] * 1.1);


  double x2 = getXValue(a: 50, b: 1000,reportAcquirente: reportAcquirente);

  double materialStandardLight = (doorsIns * 500) + (bathsIns * 3000);
  double materialPremiumLight = (doorsIns * 800) + (bathsIns * 5000);

  stimaCalculationResponseLight['acutal'] = int.tryParse(x2.toString())??0;
  stimaCalculationResponseLight['range_min'] = roundToNearestThousand(x2 * 0.9);
  stimaCalculationResponseLight['range_max'] = roundToNearestThousand(x2 * 1.1);
  stimaCalculationResponseLight['material_standard'] = int.tryParse(materialStandardLight.toString());
  stimaCalculationResponseLight['material_standard_min'] = roundToNearestThousand(materialStandardLight * 0.9);
  stimaCalculationResponseLight['material_standard_max'] = roundToNearestThousand(materialStandardLight * 1.1);
  stimaCalculationResponseLight['material_permium'] = int.tryParse(materialPremiumLight.toString());
  stimaCalculationResponseLight['material_permium_min'] = roundToNearestThousand(materialPremiumLight*0.9);
  stimaCalculationResponseLight['material_permium_max'] = roundToNearestThousand(materialPremiumLight*1.1);
}

getXValue({required double a, required double b,required ReportAcquirente reportAcquirente}) {
  double mqTrample = (reportAcquirente.grossSquareFootage ?? 0) * 0.95;
  int rooms = reportAcquirente.rooms ?? 0;
  int bathsCount = reportAcquirente.numberOfBathrooms ?? 0;
  String qualityOfArea = "zona ottima";

  String floors = reportAcquirente.unitFloor ?? "";

  double x = (mqTrample * a) + (rooms * b);

  // Adding value based on the number of bathrooms
  for (int i = 1; i <= bathsCount; i++) {
    if (i == 1) {
      x += 7000;
    } else if (i == 2) {
      x += 5000;
    } else {
      x += 4000;
    }
  }

  // Adjusting value based on the quality of the area
  double qualityMultiplier = 0;
  if (qualityOfArea == "zona normale") {
    qualityMultiplier = 0; // No adjustment
  } else if (qualityOfArea == "zona ottima") {
    qualityMultiplier = 0.05;
  } else if (qualityOfArea == "zona di pregio") {
    qualityMultiplier = 0.10;
  }

  x += (x * qualityMultiplier);

  // Adjusting value based on the floor
  double floorMultiplier = 0;
  if (floors.toLowerCase().contains("terra") || floors.toLowerCase().contains("rialzato")) {
    floorMultiplier = 0;
  } else if (floors == "1" || floors == "2") {
    floorMultiplier = 0.05;
  } else if (int.tryParse(floors) != null && int.parse(floors) >= 3) {
    floorMultiplier = 0.1;
  }

  x += (x * floorMultiplier);

  return x;
}