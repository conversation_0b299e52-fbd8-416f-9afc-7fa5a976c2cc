import 'package:flutter/material.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

/// *  textInputType - The type of information for which to optimize the text input control.
/// *  hintText - Text that suggests what sort of input the field accepts.
/// *  prefixIcon - Pass Icon if required
/// *  defaultText - If there is predefined value is there for a text field
/// *  focusNode - Currently focus node
/// *  obscureText - Is Password field?
/// *  controller - Text controller
/// *  functionValidate - Validation function(currently I have used to check empty field)
/// *  parametersValidate - Value to validate
/// *  actionKeyboard - Keyboard action eg. next, done, search, etc
/// *  onSubmitField - Done click action
/// *  onFieldTap - On focus on TextField
class NarLinkWidget extends StatefulWidget {
  final VoidCallback? onClick;
  final String? text;
  final Color? textColor;
  final TextStyle? style;
  final TextAlign? textAlign;
  final Widget? leadingIcon;
  final Widget? trailingIcon;
  final TextDecoration? textDecoration;
  double? fontSize = 14;
  String? fontWeight = '800';
  TextOverflow? overflow;

  NarLinkWidget(
      {this.onClick,
      required this.text,
      this.textColor = Colors.black,
      this.style,
      this.leadingIcon,
      this.trailingIcon,
      this.overflow,
      this.fontSize = 12,
      this.fontWeight = '600',
      this.textAlign = TextAlign.left,
      this.textDecoration = TextDecoration.underline});

  @override
  _NarLinkWidgetState createState() => _NarLinkWidgetState();
}

class _NarLinkWidgetState extends State<NarLinkWidget> {
  double bottomPaddingToError = 12;

  @override
  Widget build(BuildContext context) {
    return ButtonTheme(
      child: ElevatedButton(
        
          style: ButtonStyle(
            padding: WidgetStateProperty.all(EdgeInsets.symmetric(vertical: 0, horizontal: 0)),
            textStyle: WidgetStateProperty.all(
              TextStyle(
                fontFamily: 'Raleway-' + widget.fontWeight!,
                fontSize: widget.fontSize,
                letterSpacing: 0,
                // height: 10,
                overflow: widget.overflow,
                decoration: TextDecoration.underline,
              ),
            ),
            foregroundColor: WidgetStateProperty.all<Color>(widget.textColor ?? Colors.black),
            backgroundColor: WidgetStateProperty.all<Color>(Colors.transparent),
            overlayColor: WidgetStateProperty.resolveWith<Color>(
              (Set<WidgetState> states) {
                return Colors.transparent;
              },
            ),
            shadowColor: WidgetStateProperty.all<Color>(Colors.transparent),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: widget.textAlign == TextAlign.left
                ? MainAxisAlignment.start
                : widget.textAlign == TextAlign.right
                    ? MainAxisAlignment.end
                    : MainAxisAlignment.center,
            children: <Widget>[
              widget.leadingIcon == null
                  ? SizedBox.shrink()
                  : Row(
                      children: <Widget>[widget.leadingIcon!, SizedBox(width: 5)],
                    ),
              Flexible(
                child: NarFormLabelWidget(
                  textDecoration: widget.textDecoration,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight!,
                  label: widget.text ?? '',
                  textAlign: widget.textAlign,
                  textColor: widget.textColor,
                  overflow: widget.overflow,
                ),
              ),
              widget.trailingIcon == null
                  ? SizedBox.shrink()
                  : Row(
                      children: <Widget>[widget.trailingIcon!, SizedBox(width: 10)],
                    ),
            ],
          ),
          onPressed: () {
            return widget.onClick!();
          }),
    );
  }
}
