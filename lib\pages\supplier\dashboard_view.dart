import 'package:flutter/material.dart';

import 'package:newarc_platform/classes/supplier.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';


class Dashboard extends StatefulWidget {
  const Dashboard(
      {Key? key,
      required this.supplier,
      required this.supplierUser,
      required this.responsive})
      : super(key: key);

  final bool responsive;
  final Supplier supplier;
  final SupplierUser supplierUser;

  @override
  State<Dashboard> createState() => _DashboardState();
}

class _DashboardState extends State<Dashboard> {
  bool loading = false;
  int timestamp = DateTime.now().millisecondsSinceEpoch;
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ListView(
      primary: true,
      children: [
        Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NarFormLabelWidget(
                label: 'Dashboard',
                fontSize: 22,
                fontWeight: 'bold',
              ),
            ]),
        SizedBox(height: 20),
        Container(
          height: 180,
          child: ListView(
            primary: false,
            scrollDirection: Axis.horizontal,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  NarFormLabelWidget(
                    label: 'D&D - Dashboard Ditte',
                    fontSize: 16,
                    fontWeight: 'bold',
                    textColor: Theme.of(context).primaryColor,
                  ),
                ]
              )
            ],
          ),
        ),
      ],
    );
  }
}