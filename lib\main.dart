import 'dart:developer';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/pages/login_page.dart';
import 'package:newarc_platform/pages/register_page.dart';
import 'package:newarc_platform/route_generator.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:url_strategy/url_strategy.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if ( !appConfig.isProduction) {
  // if (false) {
    FlutterError.onError = (FlutterErrorDetails details) {
      print({'error', details});
      FlutterError.dumpErrorToConsole(details);
    };
  }

  if (appConfig.isProduction) {
    await Firebase.initializeApp(
        options: const FirebaseOptions(
      apiKey: appConfig.FIREBASE_PRODUCTION_API_KEY,
      appId: appConfig.FIREBASE_PRODUCTION_APP_ID,
      messagingSenderId: appConfig.FIREBASE_PRODUCTION_MESSAGING_SENDER_ID,
      storageBucket: appConfig.FIREBASE_PRODUCTION_STORAGE_BUCKET,
      projectId: appConfig.FIREBASE_PRODUCTION_PROJECT_ID,
    ));
  } else {
    await Firebase.initializeApp(
        options: const FirebaseOptions(
      apiKey: appConfig.FIREBASE_STAGING_API_KEY,
      appId: appConfig.FIREBASE_STAGING_APP_ID,
      messagingSenderId: appConfig.FIREBASE_STAGING_MESSAGING_SENDER_ID,
      storageBucket: appConfig.FIREBASE_STAGING_STORAGE_BUCKET,
      projectId: appConfig.FIREBASE_STAGING_PROJECT_ID,
    ));
  }

  setPathUrlStrategy();
  // Redirects to login platform based on url, can be among "work", "professionals", "agency"
  String platformName = Uri.base.toString().contains('work') ? 'work' : Uri.base.toString().contains('pro') ? 'professionals' : 'agency';
  // if( Uri.base.queryParameters.containsKey('work') ) {}
  // platformName = "professionals";
  runApp(MyApp(
    accessType: platformName,
  ));
}

class MyCustomScrollBehavior extends MaterialScrollBehavior {
  // Override behavior methods and getters like dragDevices
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
      };
}

class MyApp extends StatelessWidget {
  final String accessType;
  MyApp({
    this.accessType = 'agency',
  });

  @override
  Widget build(BuildContext context) {
    ThemeData newarcTheme = setTheme(accessType);
    return GetMaterialApp(
      title: 'Newarc Platform',
      scrollBehavior: MyCustomScrollBehavior(),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('it', 'IT') // English, India
      ],
      theme: newarcTheme.copyWith(
        textTheme: GoogleFonts.ralewayTextTheme(newarcTheme.textTheme),
      ),
      initialRoute: LoginPage.route,
      //HomeAgency.route,
      routes: {
        '/': (context) => LoginPage(
              loginType: accessType,
            ),
        LoginPage.route: (context) => LoginPage(
              loginType: accessType,
            ),
        RegisterPage.route: (context) => RegisterPage(
              origin: accessType,
            ),
      },
      locale: Locale('it'),
      onGenerateRoute: RouteGenerator.generateRoute,
      debugShowCheckedModeBanner: false,
    );
  }

  ThemeData setTheme(String accessType) {
    return ThemeData(
      appBarTheme: const AppBarTheme(
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.white,
      ),
      highlightColor: Color(0xff5DB1E3),
      // primaryColor: accessType == 'work' ? Color(0xff499B79) : accessType == 'professionals' ? const Color.fromARGB(255, 56, 56, 56) : Color(0xff13427A),
      primaryColor: accessType == 'work' ? Color(0xff499B79) : accessType == 'professionals' ? const Color.fromARGB(255, 56, 56, 56) : Color(0xff499B79),
      primaryColorDark: accessType == 'work' ? Color(0xff262626) : accessType == 'professionals' ? Colors.black : Color(0xff1c1e21),
      primaryColorLight: Color(0xff6C6C6C),
      unselectedWidgetColor: Colors.white,
      disabledColor: Colors.black,
      switchTheme: SwitchThemeData(
        trackColor:
            WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
          if (states.contains(WidgetState.selected)) {
            return Color(0xffE5E5E5);
          }
          return Colors.white;
        }),
        thumbColor: WidgetStateProperty.all(Color(0xffC0C0C0)),
        trackOutlineWidth: WidgetStateProperty.all(1),
        trackOutlineColor:
            WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
          if (states.contains(WidgetState.selected)) {
            return Colors.transparent;
          }
          return Color(0xffC0C0C0);
        }),
      ),
      textTheme: TextTheme(
        headlineLarge: TextStyle(
          color: Color(0xff262626),
          fontSize: 25,
          fontWeight: FontWeight.w800,
        ),
        headlineMedium: TextStyle(
          color: Color(0xff262626),
          fontSize: 23,
          fontWeight: FontWeight.w800,
        ),
        headlineSmall: TextStyle(
          color: Color(0xff262626),
          fontSize: 21,
          fontWeight: FontWeight.w800,
        ),
      ),
      textSelectionTheme: TextSelectionThemeData(
        selectionColor: Color(0xffE4E4E4),
      ),
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: accessType == 'work' ? Color(0xff489B79) : accessType == 'professionals' ?  Color.fromARGB(255, 56, 56, 56) : Color(0xff13427A)),
      dropdownMenuTheme: DropdownMenuThemeData(
          inputDecorationTheme: InputDecorationTheme(
            filled: true,
            fillColor: Colors.white,
          ),
          menuStyle: MenuStyle(
            backgroundColor: WidgetStateProperty.all(Colors.white),
            surfaceTintColor: WidgetStateProperty.all(Colors.white),
          )),
      checkboxTheme: CheckboxThemeData(
        overlayColor: WidgetStateProperty.all(Colors.white),
        fillColor:
            WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
          if (states.contains(WidgetState.selected)) {
            return accessType == 'work' ? Color(0xff489B79) : accessType == 'professionals' ?  Color.fromARGB(255, 56, 56, 56) : Color(0xff13427A);
          }
          return Colors.white;
        }),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5),
        ),
        side: WidgetStateBorderSide.resolveWith(
          (states) => BorderSide(
            width: 1.0,
            color: Color(0xffdbdbdb),
          ),
        ),
      ),
      datePickerTheme: DatePickerThemeData(
          backgroundColor: Colors.white,
          dayOverlayColor: WidgetStateProperty.all(
            Color(0xffE5E5E5),
          ),
          dayBackgroundColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.selected)) {
              return accessType == 'work' ? Color(0xff489B79) : accessType == 'professionals' ?  Color.fromARGB(255, 56, 56, 56) : Color(0xff13427A);
            }
            return Colors.transparent;
          }),
          todayBackgroundColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.selected)) {
              return accessType == 'work' ? Color(0xff489B79) : accessType == 'professionals' ?  Color.fromARGB(255, 56, 56, 56) : Color(0xff13427A);
            }
            return Colors.white;
          }),
          todayForegroundColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.selected)) {
              return Colors.white;
            }
            return Colors.black;
          }),
          rangeSelectionBackgroundColor:
            accessType == 'work' ? Color(0xff489B79) : accessType == 'professionals' ?  Color.fromARGB(255, 56, 56, 56) : Color(0xff13427A),
          cancelButtonStyle: ButtonStyle(
            foregroundColor: WidgetStateProperty.all(
                Colors.black), // Set text color to black
            overlayColor: WidgetStateProperty.all(Color(0xffE5E5E5)),
          ),
          confirmButtonStyle: ButtonStyle(
            foregroundColor: WidgetStateProperty.all(Colors.black),
            backgroundColor: WidgetStateProperty.all(Color(0xffE5E5E5)),
            overlayColor: WidgetStateProperty.all(Color(0xffE5E5E5)),
          )),
      inputDecorationTheme: InputDecorationTheme(
        fillColor: Colors.white,
        filled: true,
        labelStyle: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 15,
          color: Color(0xff489B79),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0),
          borderSide: BorderSide(
            color: Color(0xff489B79),
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0),
          borderSide: BorderSide(
            color: Colors.red,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0),
          borderSide: BorderSide(
            color: Color(0xff6C6C6C),
            width: 0.3,
          ),
        ),
      ),
    );
  }
}
