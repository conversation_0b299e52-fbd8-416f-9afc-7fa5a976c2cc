class ProposedEstate {
  String? id;
  String? zona;
  String? link;
  String? address;
  bool noLink = false;
  String? margineTrattativa;
  String? possibilityCessionePreliminare;
  int? insertionTimestamp;
  String? agencyId;
  String? floor;
  String? sqm;
  String? price;
  bool elevator = false;
  String? city;
  String? agencyName;

  ProposedEstate();

  ProposedEstate.fromDocument(Map<String, dynamic> data, String id) {
    try {
      this.id = id;
      this.address = data['address'];
      this.city = data['city'];
      this.zona = data['zona'];
      this.link = data['link'];
      this.insertionTimestamp = data['insertion_timestamp'] ?? 0;
      this.agencyId = data['agencyId'];
      this.noLink = data['noLink'] ?? false;
      this.margineTrattativa = data['margineTrattativa'];
      this.possibilityCessionePreliminare =
          data['possibilityCessionePreliminare'];
      this.sqm = data['sqm'];
      this.price = data['price'];
      this.elevator = data['elevator'];
      this.floor = data['floor'];
      this.agencyName = data['agencyName'];
    } catch (e) {
      print(data['id']);
      print(e);
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': this.id,
      'address': this.address,
      'city': this.city,
      'zona': this.zona,
      'link': this.link,
      'noLink': this.noLink,
      'agencyId': this.agencyId,
      'insertion_timestamp': this.insertionTimestamp,
      'margineTrattativa': this.margineTrattativa,
      'possibilityCessionePreliminare': this.possibilityCessionePreliminare,
      'floor': this.floor,
      'price': this.price,
      'elevator': this.elevator,
      'sqm': this.sqm,
      'agencyName': this.agencyName,
    };
  }
}
