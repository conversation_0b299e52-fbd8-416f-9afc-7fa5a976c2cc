import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/economics.dart';

class AgencyServiziView extends StatelessWidget {
  AgencyServiziView({
    super.key,
    this.updateViewCallback,
    required this.agencyUser,
  });

  final Function? updateViewCallback;
  final Agency? agencyUser;

  // List<String> subscription = ["Con Success Fee", "Senza Success Fee"];
  // List<Map<String, dynamic>> subscriptions = [];
  // List<String> tabList = ["Con Success Fee", "Senza Success Fee"];
  // String errorMsg = "Seleziona un abbonamento";
  // List<Map<String, dynamic>> finalSubscriptionsList = [];

  // /// selected plan - please check with selectedTab index for with Success fee or without Success fee
  // Map<String, dynamic> selectedPlan = {};

  // ///selected card index
  // int selectedCardIndex = -1;

  // /// Success fee or without Success fee
  // int selectedTab = 0;

  // bool loading = false;
  // bool _isFrozen = false;
  // bool paymentError = false;

  // final GlobalKey _planKey = GlobalKey();
  // final ScrollController _scrollController = ScrollController();

  void scrollToSection(GlobalKey key) {
    Scrollable.ensureVisible(
      key.currentContext!,
      duration: Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SingleChildScrollView(
        // controller: _scrollController,
        scrollDirection: Axis.vertical,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            immaginaAgencyPrices(),
            Divider(thickness: 1, color: Color(0xffDADADA),),
            SizedBox(height: 20,),
            immaginaProfessionalsPrices(),
          ],
        ),
      ),
    );
  }


  Widget immaginaAgencyPrices(){
    final pricingDataForSingleRequest = ImmaginaProjectEconomics.generatePricingData();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Center(
          child: Image.asset("assets/logo_newarc_immagina.png", height: 50)
        ),
        SizedBox(height: 15,),
        Center(
          child: SizedBox(
            width: 800,
            child: ListView.builder(
              itemCount: ImmaginaProjectEconomics.generatePricingData().length,
              shrinkWrap: true,
              itemBuilder: (context, index) {
                return PricingCard(
                  title: pricingDataForSingleRequest[index]["title"],
                  prices: pricingDataForSingleRequest[index]["prices"],
                  extraPrices: pricingDataForSingleRequest[index]["extraPrices"],
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget immaginaProfessionalsPrices(){
    final pricingDataForSingleRequest = ImmaginaProjectEconomics.generateProfessionalsPricingData();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Center(
          child: Image.asset("assets/logo_newarc_immagina_professionals.png", height: 50)
        ),
        SizedBox(height: 15,),
        Center(
          child: SizedBox(
            width: 800,
            child: ListView.builder(
              itemCount: ImmaginaProjectEconomics.generateProfessionalsPricingData().length,
              shrinkWrap: true,
              itemBuilder: (context, index) {
                return PricingCard(
                  title: pricingDataForSingleRequest[index]["title"],
                  prices: pricingDataForSingleRequest[index]["prices"],
                  extraPrices: pricingDataForSingleRequest[index]["extraPrices"],
                  isForProfessionals: true,
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}

class PricingCard extends StatelessWidget {
  final String title;
  final List<Map<String, String>> prices;
  final List<Map<String, String>> extraPrices;
  final bool isForProfessionals;

  const PricingCard({
    required this.title,
    required this.prices,
    required this.extraPrices,
    this.isForProfessionals = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffCFCFCF)),
        borderRadius: BorderRadius.circular(15)
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            NarFormLabelWidget(
              label: title,
              fontSize: 19,
              fontWeight: '700',
              textColor: Colors.black,
            ),
            SizedBox(height: 12),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(flex: 5, child: _buildPriceList(prices)),
                Expanded(
                  flex: 1,
                  child: Container(
                    height: 70,
                    width: 1,
                    color: Colors.transparent,
                    child: VerticalDivider(
                      color: Color(0xffCFCFCF),
                    ),
                  ),
                ),
                Expanded(flex: 5, child: _buildPriceList(extraPrices)),
              ],
            ),
            SizedBox(height: 10),
            Center(
              child: NarFormLabelWidget(
                label: "Immobili oltre i ${this.isForProfessionals ? professionalsGSFUpperLimit : gSFUpperLimit}mq - preventivo personalizzato",
                fontSize: 13,
                fontWeight: '500',
                textColor: Color(0xFF616161),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceList(List<Map<String, String>> priceList) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: priceList.map((item) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                label: "${item['label'] ?? ''} - ",
                fontSize: 13,
                fontWeight: '700',
                textColor: AppColor.black,
              ),
              NarFormLabelWidget(
                label: item['price'] ?? '',
                fontSize: 13,
                fontWeight: '500',
                textColor: AppColor.black,
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}

