import 'package:flutter/material.dart';

abstract class AppColor {
  static const white = Colors.white;
  static const black = Colors.black;

  static const greyColor = Color(0xFF838383);
  static const drawerButtonColor = Color(0xFF484848);
  static const drawerIconButtonColor = Color(0xFFBCBCBC);
  static const iconGreyColor = Color(0xFF7E7E7E);
  static const lightGreyColor = Color(0xFFF1F1F1);
  static const disabledGreyColor = Color(0xFFEDEDED);
  static const dataTableBorderColor = Color(0xFFEEEEEE);
  static const successGreenColor = Color(0xFF39C14F);
  static const lightGreen100Color = Color(0xFFF0FBF6);
  static const lightGreen200Color = Color(0xFFC4DED3);
  static const lightGreenDataTableBgColor = Color(0xFFF3FBF4);
  static const borderColor = Color(0xFFE3E3E3);
  static const redColor = Color(0xFFDD0000);
  static const buttonBorderColor = Color(0xFFCACACA);
}
