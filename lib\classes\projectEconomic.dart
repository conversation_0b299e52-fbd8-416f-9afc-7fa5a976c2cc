import 'package:newarc_platform/classes/baseAddressInfo.dart';

class ProjectEconomic{

  String? firebaseId;
  @Deprecated('Use ProjectEconomic.addressInfo.fullAddress instead, keep in mind retrocompatibility')
  String? address;
  @Deprecated('Use ProjectEconomic.addressInfo.city instead, keep in mind retrocompatibility')
  String? city;
  BaseAddressInfo? addressInfo;
  int? created;
  String? creatorUserId;
  Map? acquisitionCosts; //costiDiAcquisizione
  Map? agencyCost; //costiDiAgenzia
  Map? restorationAndMaterail; //ristrutturazioneEMateriali
  Map? revenueExpected; //venditaImmobile
  Map? variableCost; //venditaImmobile
  bool? isArchived;
  bool? isAssigned;




  ProjectEconomic(Map<String, dynamic> data) {
    this.address = data['address'];
    this.city = data['city'];
    this.addressInfo = (data.containsKey('addressInfo') && data['addressInfo'] != null) ? BaseAddressInfo.fromMap(data['addressInfo']) : null;
    this.created = data['created'];
    this.creatorUserId = data['creatorUserId'];
    this.acquisitionCosts = data['acquisitionCosts'];
    this.agencyCost = data['agencyCost'];
    this.restorationAndMaterail = data['restorationAndMaterail'];
    this.variableCost = data['variableCost'];
    this.revenueExpected = data['revenueExpected'];
    this.isArchived = data['isArchived'];
    this.isAssigned = data['isAssigned'];
  }

  ProjectEconomic.empty() {
    this.address = '';
    this.city = '';
    this.addressInfo = BaseAddressInfo.empty();
    this.created = DateTime.now().millisecondsSinceEpoch;
    this.creatorUserId = '';
    this.acquisitionCosts = {};
    this.agencyCost = {};
    this.restorationAndMaterail = {};
    this.revenueExpected = {};
    this.variableCost = {};
    this.isArchived = false;
    this.isAssigned = false;
  }

  Map<String, dynamic> toMap() {
    return {
      'address': this.address,
      'city': this.city,
      "addressInfo": this.addressInfo?.toMap(),
      'created': this.created,
      'creatorUserId': this.creatorUserId,
      'acquisitionCosts': this.acquisitionCosts,
      'agencyCost': this.agencyCost,
      'restorationAndMaterail': this.restorationAndMaterail,
      'variableCost': this.variableCost,
      'revenueExpected': this.revenueExpected,
      'isArchived': this.isArchived,
      'isAssigned': this.isAssigned,
    };
  }

  ProjectEconomic.fromDocument(Map<String, dynamic> data, String firbaseId) {
    try {
      this.firebaseId = firbaseId;
      this.address = data['address'];
      this.created = data['created'];
      this.city = data['city'];
      this.addressInfo = (data.containsKey('addressInfo') && data['addressInfo'] != null) ? BaseAddressInfo.fromMap(data['addressInfo']) : null;
      this.creatorUserId = data['creatorUserId'];
      this.acquisitionCosts = data['acquisitionCosts'] == null ? {} : data['acquisitionCosts'];
      this.agencyCost = data['agencyCost'] == null ? {} : data['agencyCost'];
      this.restorationAndMaterail = data['restorationAndMaterail'] == null ? {} : data['restorationAndMaterail'];
      this.revenueExpected = data['revenueExpected'] == null ? {} : data['revenueExpected'];
      this.variableCost = data['variableCost'] == null ? {} : data['variableCost'];
      this.isArchived = data['isArchived'] == null ? false : data['isArchived'];
      this.isAssigned = data['isAssigned'] == null ? false : data['isAssigned'];
    } catch (e, s) {
      print({'projectEconomic.dart', e, s});
    }
  }
}