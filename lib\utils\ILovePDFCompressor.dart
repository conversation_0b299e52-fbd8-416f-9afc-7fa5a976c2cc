import 'dart:convert';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';

import '../app_config.dart';

class ILovePDFCompressor {


  String? token;
  String? server;

  Future<void> authenticate() async {
    final response = await http.post(
      Uri.parse('https://api.ilovepdf.com/v1/auth'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'public_key': ILOVEPDF_PUBLIC_KEY}),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      token = data['token'];
    } else {
      throw Exception('Authentication failed: ${response.body}');
    }
  }

  Future<String> createTask() async {
    final response = await http.get(
      Uri.parse('https://api.ilovepdf.com/v1/start/compress'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
    );
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      server = data['server'];
      return data['task'];
    } else {
      throw Exception('Failed to create task: ${response.body}');
    }
  }

  Future<String> uploadUint8List(
      String taskId,
      Uint8List pdfBytes,
      String filename,
      String server,
      ) async {
    final uri = Uri.parse('https://$server/v1/upload');

    final request = await http.MultipartRequest('POST', uri)
      ..fields['task'] = taskId
      ..files.add(http.MultipartFile.fromBytes(
        'file',
        pdfBytes,
        filename: filename,
        contentType: MediaType('application', 'pdf'),
      ));

    request.headers['Authorization'] = 'Bearer $token';


    try {
      final response = await request.send();

      if (response.statusCode == 200) {
        final body = await response.stream.bytesToString();
        final json = jsonDecode(body);
        return json['server_filename'];
      }else{
        final error = await response.stream.bytesToString();
        throw Exception('Upload failed: $error');
      }
    } catch (e) {
      throw Exception('Upload failed: ${e.toString()}');
    }
  }

  Future<void> processTask({
    required String taskId,
    required String serverFilename,
    String compressionLevel = 'recommended',
  }) async {
    final uri = Uri.parse('https://$server/v1/process');

    final body = jsonEncode({
      'task': taskId,
      'tool': 'compress',
      'compression_level': compressionLevel,
      'files': [
        {
          'server_filename': serverFilename,
          'filename': 'compressed_output.pdf',
        }
      ]
    });

    final response = await http.post(
      uri,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: body,
    );

    if (response.statusCode != 200) {
      throw Exception('Processing failed: ${response.body}');
    }
  }

  Future<Uint8List> downloadResult(String taskId) async {
    final uri = Uri.parse('https://$server/v1/download/$taskId');
    final response = await http.get(
      uri,
      headers: {
        'Authorization': 'Bearer $token'
      },
    );
    if (response.statusCode == 200) {
      return await response.bodyBytes;
    } else {
      throw Exception('Download failed: ${response.body}');
    }
  }

  Future<Uint8List> compressPDF(Uint8List pdfBytes) async {
    await authenticate();
    final taskId = await createTask();
    String serverFileName = await uploadUint8List(taskId, pdfBytes,"document.pdf",server!);
    await processTask(taskId: taskId,serverFilename: serverFileName);
    return await downloadResult(taskId);
  }
}
