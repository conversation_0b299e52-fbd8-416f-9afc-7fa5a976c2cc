import 'package:newarc_platform/classes/property.dart';

class WebLeads {
  
  String? firebaseId;
  String? fullName;
  String? phone;
  String? email;
  String? adId;
  String? projectId;
  String? agencyId;
  int? insertTimestamp;
  String? status;
  Property? adData;
  
  WebLeads.empty() {
    this.firebaseId = '';
    this.fullName = '';
    this.phone = '';
    this.email = '';
    this.adId = '';
    this.projectId = '';
    this.agencyId = '';
    this.status = '';
    this.insertTimestamp = 0;
    this.adData = Property.empty();
  }

  WebLeads.fromDocument(Map<String, dynamic> data, String id) {
    

    try {
      this.firebaseId = id;
      this.fullName = data['name']??'';
      this.phone = data['phone']??'';
      this.email = data['email']??'';
      this.adId = data['newarcHomesId']??'';
      this.projectId = data['projectId']??'';
      this.agencyId = data['agencyId']??'';
      this.insertTimestamp = data['insertTimestamp'];
      this.status = data['status']??'Da contattare';
      
    } catch (e, s) {
      print({ 'web lead error', e, s});
      // print(id);
    }
  }


  Map<String, dynamic> toMap() {
    return {
      'fullName': fullName,
      'phone': phone,
      'email': email,
      'adId': adId,
      'projectId': projectId,
      'agencyId': agencyId,
      'status': status,
      'insertTimestamp': insertTimestamp,
    };
  }
}