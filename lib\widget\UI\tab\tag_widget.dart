import 'package:flutter/material.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/tab/text_style.dart';

class TagWidget extends StatelessWidget {
  const TagWidget({
    super.key,
    this.text,
    this.textStyle,
    this.textColor,
    this.borderRadius,
    this.statusColor,
    this.changingColors,
    this.suffixIcon,
  });

  final String? text;
  final TextStyle? textStyle;
  final Color? textColor, statusColor;
  final double? borderRadius;
  final List<Color>? changingColors;
  final Widget? suffixIcon;

  @override
  Widget build(BuildContext context) {
    BoxDecoration decoration = changingColors!=null
      ? BoxDecoration(
        gradient: LinearGradient(
          colors: changingColors!,
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(
          borderRadius ?? 100,
        ),
      )
      : BoxDecoration(
        color: statusColor ?? AppColor.successGreenColor,
        borderRadius: BorderRadius.circular(
          borderRadius ?? 100,
        ),
      );
    Widget textWidget = NarFormLabelWidget(
      label: text ?? "",
      fontSize: 10,
      fontWeight: '500',
      textColor: textColor ?? AppColor.white,
      letterSpacing: 0.5,
    );
    return Container(
      padding: EdgeInsets.only(
        left: 6,
        right: 6,
        bottom: 3,
        top: 2,
      ),
      decoration: decoration,
      child: suffixIcon != null ? Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          textWidget,
          SizedBox(width: 5),
          suffixIcon!,
        ],
      ) :
      textWidget
    );
  }
}
