import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/basePersonInfo.dart';
import 'package:newarc_platform/utils/various.dart';

class Professional {
  String? id;
  String? companyName;
  bool hasPIva = true;
  String? profession;
  bool consentedToPrivacyPolicy = false;
  bool acceptedTermsConditions = false;
  String? email;
  String? phone;
  late BasePersonInfo contactPersonInfo;
  String? legalEntity;
  String? fiscalCode;
  String? sdi;
  String? vat;
  String? formationType;
  late BaseAddressInfo legalAddressInfo;

  bool? isActive;
  bool? isArchived;

  int? insertTimestamp;
  int? modificationTimestamp;

  String? stripeCustomerId;

  Professional(Map<String, dynamic> userMap, String userId) {
    this.id = userId;
    this.companyName = userMap['companyName'];
    this.hasPIva = userMap['hasPIva'] ?? true;
    this.profession = userMap['profession'];
    this.sdi = userMap['sdi'];
    this.vat = userMap['vat'];
    this.formationType = userMap['formationType'];
    this.phone = userMap['phone'];
    this.email = userMap['email'];

    this.isActive = userMap['isActive'] != null ? userMap['isActive'] : true;
    this.isArchived = userMap['isArchived'] != null ? userMap['isArchived'] : false;

    this.insertTimestamp = userMap['insertTimestamp'];
    this.modificationTimestamp = userMap['modificationTimestamp'];
    this.legalEntity = userMap['legalEntity'];
    this.fiscalCode = userMap['fiscalCode'];
    this.legalAddressInfo = (userMap.containsKey('legalAddressInfo') && userMap['legalAddressInfo'] != null) ? BaseAddressInfo.fromMap(userMap['legalAddressInfo']) : BaseAddressInfo.empty();
    this.contactPersonInfo = (userMap.containsKey('contactPersonInfo') && userMap['contactPersonInfo'] != null) ? BasePersonInfo.fromMap(userMap['contactPersonInfo']) : BasePersonInfo.empty();

    this.stripeCustomerId = userMap['stripeCustomerId'];
  }

  Professional.empty() {
    this.id = '';
    this.companyName = null;
    this.formationType = null;
    this.profession = null;
    this.sdi = null;
    this.vat = null;
    this.phone = null;
    this.email = null;

    this.isActive = true;
    this.isArchived = false;

    this.insertTimestamp = Timestamp.now().millisecondsSinceEpoch;
    this.modificationTimestamp = null;
    this.legalEntity = null;
    this.fiscalCode = null;
    this.legalAddressInfo = BaseAddressInfo.empty();
    this.contactPersonInfo = BasePersonInfo.empty();

    this.stripeCustomerId = null;
  }

  Professional.fromDocument(Map<String, dynamic> data, String id) {
    this.id = id;
    this.companyName = data['companyName'];
    this.hasPIva = data['hasPIva'] ?? true;
    this.profession = data['profession'];
    this.sdi = data['sdi'];
    this.vat = data['vat'];
    this.formationType = data['formationType'];
    this.phone = data['phone'];
    this.email = data['email'];
    this.isActive = data['isActive'] != null ? data['isActive'] : true;
    this.isArchived = data['isArchived'] != null ? data['isArchived'] : false;
    this.insertTimestamp = data['insertTimestamp'];
    this.modificationTimestamp = data['modificationTimestamp'];
    this.legalEntity = data['legalEntity'];
    this.fiscalCode = data['fiscalCode'];
    this.legalAddressInfo = (data.containsKey('legalAddressInfo') && data['legalAddressInfo'] != null) ? BaseAddressInfo.fromMap(data['legalAddressInfo']) : BaseAddressInfo.empty();
    this.contactPersonInfo = (data.containsKey('contactPersonInfo') && data['contactPersonInfo'] != null) ? BasePersonInfo.fromMap(data['contactPersonInfo']) : BasePersonInfo.empty();

    this.stripeCustomerId = data['stripeCustomerId'];
  }

  Map<String, dynamic> toMap() {
    return {
      'companyName' : this.companyName,
      'hasPIva' : this.hasPIva,
      'profession' : this.profession,
      'sdi' : this.sdi,
      'vat': this.vat,
      'formationType' : this.formationType,
      'phone' : this.phone,
      'email' : this.email,
      'isActive' : this.isActive,
      'isArchived': this.isArchived,
      'insertTimestamp': this.insertTimestamp,
      'modificationTimestamp': this.modificationTimestamp,
      'legalEntity': this.legalEntity,
      'fiscalCode': this.fiscalCode,
      'legalAddressInfo': this.legalAddressInfo.toMap(),
      'contactPersonInfo': this.contactPersonInfo.toMap(),
      'stripeCustomerId': this.stripeCustomerId,
    };
  }

  String printName() {
    return this.hasPIva ?"${this.companyName!.toCapitalized()} - ${this.formationType}" : "${this.contactPersonInfo.name!.toCapitalized()} ${this.contactPersonInfo.surname!.toCapitalized()}";
  }
}

class ProfessionalsUser {
  String? id;
  String? professionalId;
  String? phone;
  String? email;
  String? name;
  String? surname;
  Professional? professional;
  User? user;
  String? type;
  String? role = 'professionals';
  String? profilePicture;
  bool? isActive;
  bool? isArchived;

  ProfessionalsUser(Map<String, dynamic> userMap, String userId,
      Map<String, dynamic> professionalMap, String professionalId) {
    this.id = userId;
    this.name = userMap['name'];
    this.surname = userMap['surname'];
    this.phone = userMap['phone'] ?? "-";
    this.email = userMap['email'] ?? "-";
    this.professionalId = userMap['professionalId'];
    professionalMap['email'] = this.email;
    this.professional = Professional(professionalMap, professionalId);
    this.role = userMap['role'];
    this.profilePicture = userMap['profilePicture'];
    this.type = userMap['type'];
    this.isArchived = userMap['isArchived'] ?? false;
    this.isActive = userMap['isActive'] ?? true;
  }

  ProfessionalsUser.fromDocument(Map<String, dynamic> data, String id) {
    this.id = id;
    this.name = data['name'];
    this.surname = data['surname'];
    this.phone = data['phone'] ?? "-";
    this.email = data['email'] ?? "-";
    this.professionalId = data['professionalId'];
    this.role = data['role'];
    this.profilePicture = data['profilePicture'];
    this.type = data['type'];
    this.isArchived = data['isArchived'] ?? false;
    this.isActive = data['isActive'] ?? true;
  }

  ProfessionalsUser.empty();

  Map<String, dynamic> toMap() {
    return {
      'id': this.id,
      'name': this.name,
      'surname': this.surname,
      'phone': this.phone,
      'email': this.email,
      'professionalId': this.professionalId,
      'type': this.type,
      'role': this.role,
      'profilePicture': this.profilePicture,
      'isActive': this.isActive,
      'isArchived': this.isArchived,
    };
  }
}
