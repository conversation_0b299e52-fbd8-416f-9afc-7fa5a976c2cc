import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/renovationContact.dart';

class SuggestedContactsController extends GetxController {
  bool loading = false;
  // suggested contacts to be shown in table
  List<RenovationContact> contacts = [];
  // "nuova segnalazione" popup
  TextEditingController contactNameController = new TextEditingController();
  TextEditingController contactSurnameController = new TextEditingController();
  TextEditingController contactEmailController = new TextEditingController();
  TextEditingController contactPhoneController = new TextEditingController();
  BaseAddressInfo addressInfo = BaseAddressInfo.empty();
  String formProgressMessage = "";
  List<String> formErrorMessage = [""];
  // table filters
  TextEditingController stateFilterController = new TextEditingController();
  String stateSelectedFilter = "";

  clearFilter() {
    stateSelectedFilter = '';
    stateFilterController.clear();
  }

  clearPopupcontrollers() {
    contactNameController.clear();
    contactSurnameController.clear();
    contactEmailController.clear();
    contactPhoneController.clear();
    addressInfo = BaseAddressInfo.empty();
    formProgressMessage = "";
    formErrorMessage = [""];
  }
}
