import 'newarcAdElementConfiguration.dart';

class NewarcAdRoomConfiguration {
  String? id;
  String? roomName;
  Map? coverImage;
  List? newarcAdElementConfigurationIDS;
  List<NewarcAdElementConfiguration>? newarcAdElementConfiguration;

  //Only for UI
  List? coverImageList;



  Map<String, Object?> toMap() {
    return {
      'id': id,
      'roomName': roomName,
      'coverImage': coverImage,
      'newarcAdElementConfigurationIDS': newarcAdElementConfigurationIDS,
      'coverImageList': coverImageList,
    };
  }
  NewarcAdRoomConfiguration.empty() {
    this.id = '';
    this.roomName = '';
    this.newarcAdElementConfigurationIDS = [];
    this.newarcAdElementConfiguration = [];
    this.coverImageList = [];
    this.coverImage = {};
  }
  NewarcAdRoomConfiguration.fromDocument(Map<String, dynamic> data) {
    try {
      this.roomName = data['roomName'];
      this.id = data['id'];
      this.coverImage = data['coverImage'];
      this.coverImageList = (data['coverImage']["fileName"] != null && data['coverImage']["fileName"] != "") ? [data['coverImage']["fileName"]] : [];
      this.newarcAdElementConfigurationIDS = data['newarcAdElementConfigurationIDS'] ?? [];
      this.newarcAdElementConfiguration = [];
      if (data['newarcAdElementConfiguration'] != null) {
        for (var i = 0; i < data['newarcAdElementConfiguration'].length; i++) {
          this.newarcAdElementConfiguration?.add(NewarcAdElementConfiguration.fromDocument(data['newarcAdElementConfiguration'][i]));
        }
      }
    } catch (e, s) {
      print({ 'NewArcAdManagerConfigRoom Class Error ------->', e, s});
    }
  }
}