
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/moodboard.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/tab/common_icon_button.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class MoodBoardDataSource extends DataTableSource {
  BuildContext context;
  List<Moodboard> displayQuotations;
  List<RenovationContact> renovationContacts;
  Function(Moodboard val) onMaterialTap;
  Function(Moodboard val) onDeleteTap;
  Function(Moodboard val) onLinkProject;
  Function(Moodboard val) onCustomerTap;

  MoodBoardDataSource({
    required this.context,
    required this.displayQuotations,
    required this.renovationContacts,
    required this.onMaterialTap,
    required this.onDeleteTap,
    required this.onLinkProject,
    required this.onCustomerTap,
  });

  @override
  DataRow? getRow(int index) {
    if (index < displayQuotations.length) {
      final moodboard = displayQuotations[index];
      RenovationContact _ren = renovationContacts.firstWhere((contact) => contact.id == moodboard.clientId, orElse: () => RenovationContact.empty());
      int millisecondsSinceEpoch = moodboard.created!;
      var date = DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).day.toString() +
          '/' +
          DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).month.toString() +
          '/' +
          DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).year.toString();
      return DataRow(
        cells: [
          DataCell(
            NarFormLabelWidget(
              label: moodboard.name ?? "",
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: _ren.id == '' ? "MISSINGNO" : _ren.name ?? "",
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),

          ),
          DataCell(
            Row(
              children: [
                NarLinkWidget(
                  text: "Materiali",
                  textColor: Colors.black,
                  fontWeight: '700',
                  fontSize: 12,
                  onClick: () {
                    onMaterialTap(moodboard);
                  },
                ),

                SizedBox(width: 8),
                moodboard.newarcProjectId != ""
                    ? Container()
                    : IconButtonWidget(
                  onTap: () {
                    onDeleteTap(moodboard);
                  },
                  iconPadding: EdgeInsets.all(4),
                  isSvgIcon: true,
                  isOnlyBorder: true,
                  backgroundColor: Colors.transparent,
                  borderColor: AppColor.redColor,
                  iconColor: AppColor.redColor,
                  icon: 'assets/icons/trash.svg',
                ),
              ],
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: date,
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),
          DataCell(
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    child: Row(
                      children: [
                        Image.asset(
                          moodboard.newarcProjectId == "" ? "assets/icons/link-project.png" : "assets/icons/unlink.png",
                          color: moodboard.newarcProjectId == "" ? Theme.of(context).primaryColor : Color(0xff999999),
                          height: 10,
                        ),
                        SizedBox(width: 4),
                        NarLinkWidget(
                          text: moodboard.newarcProjectId == "" ? "Collega ad un progetto" : "Scollega da un progetto",
                          fontSize: 12,
                          fontWeight: '700',
                          textColor: moodboard.newarcProjectId == "" ? Theme.of(context).primaryColor : Color(0xff999999),
                        ),
                      ],
                    ),
                    onTap: () {
                      onLinkProject(moodboard);
                    },
                  ),
                ),
                SizedBox(width: 12),
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    child: Row(
                      children: [
                        Image.asset(
                          moodboard.clientId == "" ? "assets/icons/link-project.png" : "assets/icons/unlink.png",
                          color: moodboard.clientId == "" ? Theme.of(context).primaryColor : Color(0xff999999),
                          height: 10,
                        ),
                        SizedBox(width: 4),
                        NarLinkWidget(
                          text: moodboard.clientId == "" ? "Collega cliente" : "Scollega cliente",
                          fontSize: 12,
                          fontWeight: '700',
                          textColor: moodboard.clientId == "" ? Theme.of(context).primaryColor : Color(0xff999999),
                        ),

                      ],
                    ),
                    onTap: () {
                      onCustomerTap(moodboard);
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return null;
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => displayQuotations.length;

  @override
  int get selectedRowCount => 0;
}
