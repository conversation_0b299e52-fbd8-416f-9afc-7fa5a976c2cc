import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/pages/work/acquired_contacts/acquired_contacts_controller.dart';
import 'package:newarc_platform/pages/work/acquired_contacts/acquired_contacts_data_source.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

import '../../../widget/UI/tab/search_text_field.dart';

class AcquiredContactsView extends StatefulWidget {
  //solo robe master si devono vedere
  final responsive;

  const AcquiredContactsView({
    Key? key,
    required this.responsive,
    //this.receivedContactsPageFilters
  }) : super(key: key);

  @override
  State<AcquiredContactsView> createState() => _AcquiredContactsViewState();
}

class _AcquiredContactsViewState extends State<AcquiredContactsView> {
  final controller = Get.put<AcquiredContactsController>(AcquiredContactsController());
  Key? paddingKey;

  @override
  void initState() {
    controller.clearFilter();
    controller.selectedStatus = controller.testList.first;
    initialFetchContacts();
    getAgencies();
    getNewarcUser();

    super.initState();
  }

  // @override
  // void dispose() {
  //   setState(() {
  //     controller.contacts = [];
  //   });
  //   super.dispose();
  // }

  // @protected
  // void didUpdateWidget(AcquiredContactsView oldWidget) {
  //   super.didUpdateWidget(oldWidget);
  //   getMasterFilter();
  // }

  getNewarcUser() async {
    final User user = FirebaseAuth.instance.currentUser!;
    final uid = user.uid;

    DocumentSnapshot<Map<String, dynamic>> collectionSnapshot = await FirebaseFirestore.instance.collection(appConfig.COLLECT_USERS).doc(uid).get();

    if (collectionSnapshot.exists) {
      controller.newarcUser = NewarcUser.fromDocument(collectionSnapshot.data()!, collectionSnapshot.id);
    }
  }

  getAgencies() async {
    QuerySnapshot<Map<String, dynamic>> collectionSnapshot = await FirebaseFirestore.instance.collection('agencies').get();

    controller.documentList = collectionSnapshot.docs;

    for (var element in collectionSnapshot.docs) {
      Agency agency = Agency.fromDocument(element.data(), element.id);

      controller.agencyList.add(agency);
    }

    setState(() {});
  }

  Widget dataTablePagination() {
    return Visibility(
      visible: controller.currentlyShowing.isNotEmpty,
      child: Padding(
        key: paddingKey,
        padding: EdgeInsets.symmetric(vertical: 10),
        child: IconTheme.merge(
          data: const IconThemeData(opacity: 0.54),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "${controller.currentlyShowing} di  ${controller.totalRecords.toString()}",
                style: TextStyle(
                  fontFamily: '',
                  fontSize: 12.0,
                  color: Colors.black.withOpacity(0.54),
                ),
              ),
              SizedBox(width: 32.0),
              IconButton(
                icon: const Icon(Icons.chevron_left),
                onPressed: () {
                  if (controller.disablePreviousButton == true) return;
                  if (controller.loadingContacts.value == true) return;
                  fetchPrevContacts();
                },
                padding: EdgeInsets.zero,
              ),
              SizedBox(width: 24.0),
              IconButton(
                icon: const Icon(Icons.chevron_right),
                padding: EdgeInsets.zero,
                onPressed: () {
                  if (controller.disableNextButton == true) return;
                  if (controller.loadingContacts.value == true) return;

                  fetchNextContacts();
                },
              ),
              SizedBox(width: 14.0),

              // TextButton(
              //   child: Icon(Icons.refresh, size: 20, color: disableNextButton ? Colors.grey : Colors.black),
              //   onPressed: () {
              //     cacheFirestore.clear();
              //     initialFetchContacts();
              //   },
              //   style: ButtonStyle(overlayColor: MaterialStateProperty.all(Colors.transparent)),
              // )
            ],
          ),
        ),
      ),
    );
  }

  Future<void> initialFetchContacts({bool force = false,bool reloadAll = false}) async {
    if (controller.contacts.isNotEmpty && !force && !reloadAll) return;

    //TODO Rimuovere la collectionSnapshotCounterQuery, ha duplicato tutto555
    controller.pageCounter = 1;

    setState(() {
      controller.contacts = [];
      controller.loadingContacts.value = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshotCounter;
      Query<Map<String, dynamic>> collectionSnapshotCounterQuery;

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance.collection('valuatorSubmissions');
      collectionSnapshotCounterQuery = FirebaseFirestore.instance.collection('valuatorSubmissions');

      //print({'master filter', filters.length, filters});
      if (controller.filters.length > 0) {
        for (var i = 0; i < controller.filters.length; i++) {
          // collectionSnapshotQuery = collectionSnapshotQuery
          //     .where(filters[i]['field'], isEqualTo: filters[i]['value']);
          //collectionSnapshotCounterQuery = collectionSnapshotCounterQuery
          //    .where(filters[i]['field'], isEqualTo: filters[i]['value']);
          if (controller.filters[i]['search'] == 'equal') {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
            collectionSnapshotCounterQuery = collectionSnapshotCounterQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          } else if (controller.filters[i]['search'] == 'like') {
            collectionSnapshotQuery = collectionSnapshotQuery
                .where(controller.filters[i]['field'], isGreaterThanOrEqualTo: controller.filters[i]['value'])
                .where(controller.filters[i]['field'], isLessThanOrEqualTo: controller.filters[i]['value'] + "\uf7ff");
            collectionSnapshotCounterQuery
                .where(controller.filters[i]['field'], isGreaterThanOrEqualTo: controller.filters[i]['value'])
                .where(controller.filters[i]['field'], isLessThanOrEqualTo: controller.filters[i]['value'] + "\uf7ff");
          }
        }
      }

      if (reloadAll) {
        collectionSnapshot = await collectionSnapshotQuery.get();
      } else {
        collectionSnapshot = await collectionSnapshotQuery.orderBy('insertion_timestamp', descending: true).limit(controller.recordsPerPage).get();
      }


      //await collectionSnapshotQuery.limit(recordsPerPage).get();

      collectionSnapshotCounter = await collectionSnapshotCounterQuery.get();

      //totalRecords = collectionSnapshot.docs.length;
      controller.totalRecords = collectionSnapshotCounter.docs.length;

      controller.totalPages = (controller.totalRecords / controller.recordsPerPage).ceil();

      if (controller.totalRecords > controller.recordsPerPage) {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      } else {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      }

      controller.documentList = collectionSnapshot.docs;


      await generateContacts(collectionSnapshot);


      // setState(() {
      //   controller.loadingContacts.value = false;
      // });
    } catch (e,s) {
      log(" Error in initialFetchContacts : $e");
      log("Stacktrace in initialFetchContacts: $s");
      setState(() {
        controller.loadingContacts.value = false;
      });
      // print(e.toString());
    }
  }

  fetchNextContacts() async {
    setState(() {
      controller.loadingContacts.value = true;
    });

    controller.pageCounter++;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;
      int indexOfSnapshot = isRecordExists(controller.pageCounter);

      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshotQuery = FirebaseFirestore.instance.collection('valuatorSubmissions');

        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }

        collectionSnapshot = await collectionSnapshotQuery
            .orderBy('insertion_timestamp', descending: true)
            .limit(controller.recordsPerPage)
            .startAfterDocument(controller.documentList[controller.documentList.length - 1])
            .get();
        // collectionSnapshot = await collectionSnapshotQuery.limit(recordsPerPage).startAfterDocument(documentList[documentList.length - 1]).get();
      }

      // List<DocumentSnapshot> newDocumentList = collectionSnapshot.docs;
      controller.documentList = collectionSnapshot.docs;

      // documentList.addAll(newDocumentList);

      generateContacts(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingContacts.value = false;
      });
      // print(e.toString());
      // movieController.sink.addError(e);
    }
  }

  fetchPrevContacts() async {
    setState(() {
      controller.loadingContacts.value = true;
    });

    controller.pageCounter--;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      int indexOfSnapshot = isRecordExists(controller.pageCounter);
      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshotQuery = FirebaseFirestore.instance.collection('valuatorSubmissions');

        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }

        collectionSnapshot = await collectionSnapshotQuery
            .orderBy('insertion_timestamp', descending: true)
            .limit(controller.recordsPerPage)
            .endBeforeDocument(controller.documentList[controller.documentList.length - 1])
            .get();
        // collectionSnapshot = await collectionSnapshotQuery.limit(recordsPerPage).endBeforeDocument(documentList[documentList.length - 1]).get();

        // List<DocumentSnapshot> newDocumentList = collectionSnapshot.docs;
      }

      // updateIndicator(true);

      // documentList.addAll(newDocumentList);
      controller.documentList = collectionSnapshot.docs;
      generateContacts(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingContacts.value = false;
      });
    }
  }

  int isRecordExists(int pageCounter) {
    String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
    return controller.cacheFirestore.indexWhere(
      (record) => record['key'] == pageCounter && record['filtersKey'] == filtersKey,
    );
  }

  generateContacts(collectionSnapshot) async {
    // If a record already doesn't exists then store
    // if (isRecordExists(pageCounter) < 0) {
    //   cacheFirestore.add({'key': pageCounter, 'snapshot': collectionSnapshot});
    // }
    if (isRecordExists(controller.pageCounter) < 0) {
      String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
      controller.cacheFirestore.add({
        'key': controller.pageCounter,
        'snapshot': collectionSnapshot,
        'filtersKey': filtersKey,
      });
    }
    if (controller.pageCounter == 1) {
      controller.disablePreviousButton = true;
    } else {
      controller.disablePreviousButton = false;
    }

    if (controller.pageCounter == controller.totalPages) {
      controller.disableNextButton = true;
    } else {
      controller.disableNextButton = false;
    }

    List<AcquiredContact> _contacts = [];
    for (var element in collectionSnapshot.docs) {
      try {
        var _tmp = AcquiredContact.fromDocument(element.data(), element.id);


        if (_tmp.assignedAgencyId != null && _tmp.assignedAgencyId != '') {
          DocumentSnapshot<Map<String, dynamic>> collectionSnapshot;
          collectionSnapshot = await FirebaseFirestore.instance.collection('agencies').doc(_tmp.assignedAgencyId).get();

          if (collectionSnapshot.exists && collectionSnapshot.data() != null) {
            _tmp.agencyUser = Agency.fromDocument(collectionSnapshot.data()!, collectionSnapshot.id);
          } else {
            log("Agency doc not found for ID: ${_tmp.assignedAgencyId}");
          }
          // contacts[contacts.indexOf(contact)].agencyUser = Agency.fromDocument(collectionSnapshot.data()!
          // , collectionSnapshot.id);

          // print(contact.agencyUser);
        }

        _contacts.add(_tmp);
      }catch (e, stacktrace) {
        log("Error processing doc ${element.id}: $e");
        log("Stacktrace: $stacktrace");
      }
    }

    _contacts.sort((a, b) => b.insertionTimestamp!.compareTo(a.insertionTimestamp!));

    int lastRecordNumber = controller.pageCounter * controller.recordsPerPage;
    if (_contacts.length == controller.recordsPerPage) {
      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + lastRecordNumber.toString();
    } else if (_contacts.length > 0 && _contacts.length < controller.recordsPerPage) {
      int prevLastRecordNumber = (controller.pageCounter - 1) * controller.recordsPerPage;

      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + (prevLastRecordNumber + _contacts.length).toString();
    }
    setState(() {
      controller.contacts = _contacts;
      controller.displayContacts = _contacts;
      controller.loadingContacts.value = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }

    return LayoutBuilder(builder: (context, constraints) {
      return Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                label: 'Valutazioni online',
                fontSize: 19,

                fontWeight: '700',
                textColor: Colors.black,
              ),
            ],
          ),
          _filter(),
          SizedBox(height: 10),
          Container(
            height: constraints.maxHeight / 1.2,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: AppColor.white,
              border: Border.all(width: 1.5, color: AppColor.borderColor),
            ),
            child: Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Opacity(
                        opacity: controller.loadingContacts.value ? 0.5 : 1,
                        child: NewarcDataTable(
                          rowsPerPage: 20,
                          hidePaginator: true,
                          onPageChanged: (val) {
                            print("page : $val");
                          },
                          columns: [
                            DataColumn2(
                              label: Text(
                                'Immobile',
                              ),
                              size: ColumnSize.L
                            ),
                            DataColumn2(
                              label: Text(
                                'Data',
                              ),
                              size: ColumnSize.S
                            ),
                            DataColumn2(
                              label: Text(
                                'Tipo',
                              ),
                                size: ColumnSize.S
                            ),
                            DataColumn2(
                              label: Text(
                                'Professione',
                              ),
                                size: ColumnSize.S
                            ),
                            DataColumn2(
                              label: Text(
                                'Contatto',
                              ),
                                size: ColumnSize.S
                            ),
                            DataColumn2(
                              label: Text(
                                'Agenzia',
                              ),
                                size: ColumnSize.S
                            ),
                            DataColumn2(
                              label: Text(
                                'Assegnazione',
                              ),
                                size: ColumnSize.S
                            ),
                            DataColumn2(
                              label: Text(
                                'Stato',
                              ),
                                size: ColumnSize.S
                            ),
                            DataColumn2(
                              label: Text(
                                'Commenti',
                              ),
                                size: ColumnSize.S
                            ),
                          ],
                          isHasDecoration: false,
                          source: AcquiredContactsDataSource(
                            query: controller.query,
                            displayContacts: controller.displayContacts,
                            context: context,
                            initialFetchContacts: initialFetchContacts,
                            selectedStatus: controller.selectedStatus,
                            newarcUser: controller.newarcUser,
                          ),
                        ),
                      ),
                      if (controller.loadingContacts.value)
                        Positioned.fill(
                          child: Center(
                            child: CircularProgressIndicator(
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: dataTablePagination(),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }

  NarFilter _filter() {
    return NarFilter(
      showSearchInput: true,
      searchTextEditingControllers: controller.searchTextController,
      onChanged: (String? searchQuery)async{
        if(searchQuery?.isNotEmpty ?? false){
          if (searchQuery != null && searchQuery.isNotEmpty) {
            List<AcquiredContact> filtered = controller.contacts.where((contact) {
              final address = contact.address?.toLowerCase() ?? '';
              final city = contact.city.toLowerCase() ?? '';
              final streetNumber = contact.streetNumber.toLowerCase() ?? '';
              return address.contains(searchQuery.toLowerCase()) || city.contains(searchQuery.toLowerCase()) || streetNumber.contains(searchQuery.toLowerCase());
            }).toList();

            setState(() {
              controller.displayContacts = filtered;
            });
          }
        }else{
          await initialFetchContacts(force: true);
          setState(() {
            controller.displayContacts = controller.contacts;
          });
        }
      },
      searchHintText: "Cerca per indirizzo..",
      suffixIconOnTap: ()async{
        log("controller.searchTextController.text.toLowerCase() ----> ${controller.searchTextController.text.toLowerCase()}");
        await initialFetchContacts(force: true,reloadAll: true);
        log("Contacts loaded: ${controller.contacts.length}");
        if(controller.searchTextController.text.trim().isNotEmpty){
          List<AcquiredContact> filtered = controller.contacts.where((contact) {
            final address = contact.address?.toLowerCase() ?? '';
            final city = contact.city.toLowerCase() ?? '';
            final streetNumber = contact.streetNumber.toLowerCase() ?? '';
            log("address ----> $address");
            return address.contains(controller.searchTextController.text.toLowerCase()) || city.contains(controller.searchTextController.text.toLowerCase()) || streetNumber.contains(controller.searchTextController.text.toLowerCase());
          }).toList();
          setState(() {
            controller.displayContacts = filtered;
          });
        }else{
          await initialFetchContacts(force: true);
          setState(() {
            controller.displayContacts = controller.contacts;
          });
        }
      },
      selectedFilters: [controller.agencyFilter, controller.cityFilter, controller.newarcTypeFilter],
      textEditingControllers: [controller.agencyFilterController, controller.cityFilterController, controller.newarcTypeFilterController],
      filterFields: [
        {
          'Tipo': NarSelectBoxWidget(
            options: ["Tutti", "Subito/Insieme", "Agenzia", "Compra", "Curiosità"],
            controller: controller.newarcTypeFilterController,
            onChanged: (value) {
              if (controller.newarcTypeFilterController.text == 'Tutti') {
                controller.filters.removeWhere((element) {
                  return element['field'] == 'additionalInfo.newarcType';
                });
              } else {
                String tipo = controller.newarcTypeFilterController.text;
                if (tipo == 'Agenzia') {
                  tipo = 'agency'; //vendi
                } else if (tipo == 'Subito/Insieme') {
                  tipo = 'subito/insieme'; //vendi
                } else if (tipo == 'Curiosità') {
                  tipo = 'curiosity'; //valuta
                } else if (tipo == 'Compra') {
                  tipo = 'buy'; //valuta
                }

                controller.filters.removeWhere((element) {
                  return element['field'] == 'additionalInfo.newarcType';
                });

                controller.filters.add({'field': 'additionalInfo.newarcType', 'value': tipo, 'search': 'equal'});
              }
              controller.newarcTypeFilter = controller.newarcTypeFilterController.text;

              setState(() {
                controller.cacheFirestore.clear();
              });
            },
          ),
        }
      ],
      onSubmit: () async {
        await initialFetchContacts(force: true);
      },
      onReset: () async {
        controller.clearFilter();
        controller.filters.clear();
        await initialFetchContacts(force: true);
      },
    );
  }

  //Why not a stream?
  Future getCommentCount(String firebaseId) async {
    QuerySnapshot<Map<String, dynamic>>? collectionSnapshot;
    collectionSnapshot = await FirebaseFirestore.instance.collection(appConfig.COLLECT_VALUATOR_SUBMISSION_COMMENTS).where('contactId', isEqualTo: firebaseId).orderBy('date', descending: true).get();

    return collectionSnapshot.docs.length;
  }
}


// List<DataColumn> getColumns(double pageWidth) {
//   List<DataColumn> list = [];
//
//   list.add(DataColumn2(
//       fixedWidth: 0.14 * pageWidth,
//       label: Padding(
//         padding: const EdgeInsets.only(left: 10.0),
//         child: NarFormLabelWidget(
//           label: 'Immobile',
//           fontSize: 13,
//           fontWeight: 'bold',
//           textColor: Color.fromRGBO(131, 131, 131, 1),
//           height: 1.15,
//         ),
//       )));
//
//   list.add(DataColumn2(
//       fixedWidth: 0.07 * pageWidth,
//       label: NarFormLabelWidget(
//         label: 'Data',
//         fontSize: 13,
//         fontWeight: 'bold',
//         textColor: Color.fromRGBO(131, 131, 131, 1),
//         height: 1.15,
//       )));
//
//   list.add(DataColumn2(
//       fixedWidth: 0.07 * pageWidth,
//       label: NarFormLabelWidget(
//         label: 'Tipo',
//         fontSize: 13,
//         fontWeight: 'bold',
//         textColor: Color.fromRGBO(131, 131, 131, 1),
//         height: 1.15,
//       )));
//
//   list.add(DataColumn2(
//       fixedWidth: 0.07 * pageWidth,
//       label: NarFormLabelWidget(
//         label: 'Professione',
//         fontSize: 13,
//         fontWeight: 'bold',
//         textColor: Color.fromRGBO(131, 131, 131, 1),
//         height: 1.15,
//       )));
//
//   list.add(DataColumn2(
//       fixedWidth: 0.07 * pageWidth,
//       label: NarFormLabelWidget(
//         label: 'Contatto',
//         fontSize: 13,
//         fontWeight: 'bold',
//         textColor: Color.fromRGBO(131, 131, 131, 1),
//         height: 1.15,
//       )));
//
//   list.add(
//     DataColumn2(
//       fixedWidth: 0.05 * pageWidth,
//       label: NarFormLabelWidget(
//         label: 'Agenzia',
//         fontSize: 13,
//         fontWeight: 'bold',
//         textColor: Color.fromRGBO(131, 131, 131, 1),
//         height: 1.15,
//       ),
//     ),
//   );
//
//   list.add(DataColumn2(
//       fixedWidth: 0.07 * pageWidth,
//       label: NarFormLabelWidget(
//         label: 'Assegnazione',
//         fontSize: 13,
//         fontWeight: 'bold',
//         textColor: Color.fromRGBO(131, 131, 131, 1),
//         height: 1.15,
//       )));
//
//   list.add(DataColumn2(
//       fixedWidth: 0.1 * pageWidth,
//       label: NarFormLabelWidget(
//         label: 'Stato',
//         fontSize: 13,
//         fontWeight: 'bold',
//         textColor: Color.fromRGBO(131, 131, 131, 1),
//         height: 1.15,
//       )));
//
//   list.add(DataColumn2(
//       fixedWidth: 0.07 * pageWidth,
//       label: NarFormLabelWidget(
//         label: 'Commenti',
//         fontSize: 13,
//         fontWeight: 'bold',
//         textColor: Color.fromRGBO(131, 131, 131, 1),
//         height: 1.15,
//       )));
//   return list;
// }

// List<DataCell> getDataRow(AcquiredContact contact) {
//   List<DataCell> list = [];
//   // if (contact.agencyUser != null) {
//   //   // print(contact.agencyUser!.name);
//   // }
//
//   // 1. Immobile
//   var address = contact.address! + " " + contact.streetNumber + "," + contact.city;
//   list.add(
//     DataCell(
//       Stack(
//         children: [
//           MouseRegion(
//             cursor: SystemMouseCursors.click,
//             child: GestureDetector(
//                 onTap: () {
//                   showHousePopup(contact);
//                 },
//                 child: Padding(
//                   padding: const EdgeInsets.only(left: 10),
//                   child: NarFormLabelWidget(
//                     label: address,
//                     textAlign: TextAlign.start,
//                     textColor: Colors.black,
//                     fontWeight: 'bold',
//                     fontSize: 12,
//                     height: 2,
//                     textDecoration: TextDecoration.underline,
//                     overflow: TextOverflow.ellipsis,
//                   ),
//                 )),
//           )
//         ],
//       ),
//     ),
//   );
//
//   int millisecondsSinceEpoch = contact.insertionTimestamp!;
//
//   // 2. Data
//   var date = DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).day.toString() +
//       '/' +
//       DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).month.toString() +
//       '/' +
//       DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).year.toString();
//   list.add(
//     DataCell(Container(
//       child: NarFormLabelWidget(
//         label: date,
//         fontSize: 12,
//         fontWeight: '800',
//         textColor: Colors.black,
//       ),
//     )),
//   );
//
//   String? tipo;
//   if (contact.newarcType != null) {
//     tipo = contact.newarcType;
//   } else {
//     tipo = contact.valutaType ?? contact.vendiType;
//   }
//
//   if (tipo == 'nok' || tipo == 'agency') {
//     tipo = 'Agenzia'; //vendi
//   } else if (tipo == 'ok' || tipo == 'subito/insieme') {
//     tipo = 'Subito/Insieme'; //vendi
//   } else if (tipo == 'curiosity') {
//     tipo = 'Curiosità'; //valuta
//   } else if (tipo == 'compra' || tipo == 'buy') {
//     tipo = 'Compra'; //valuta
//   }
//
//   list.add(
//     DataCell(Container(
//       child: NarFormLabelWidget(
//         label: tipo,
//         fontSize: 12,
//         fontWeight: '800',
//         textColor: Colors.black,
//       ),
//     )),
//   );
//
//   String professione = contact.sellerProfession ?? 'Privato';
//
//   if (professione == 'Agente immobiliare') {
//     professione = 'Agente';
//   }
//
//   list.add(
//     DataCell(Container(
//       child: NarFormLabelWidget(
//         label: professione,
//         fontSize: 12,
//         fontWeight: '800',
//         textColor: Colors.black,
//       ),
//     )),
//   );
//
//   // 3. contact
//   // list.add(DataCell(
//   //   IconButton(
//   //     icon: Icon(Icons.contact_page_rounded),
//   //     onPressed: () {
//   //       showContactPopup(contact);
//   //     },
//   //   ),
//
//   // ));
//
//   list.add(
//     DataCell(
//       Container(
//         child: TextButton(
//           child: Container(
//             child: Image.asset('assets/icons/user-icon.png', height: 20),
//             padding: const EdgeInsets.all(6),
//             decoration: BoxDecoration(
//               color: Color.fromRGBO(227, 227, 227, 1),
//               borderRadius: BorderRadius.circular(7.0),
//             ),
//           ),
//           onPressed: () {
//             showContactPopup(contact);
//           },
//           style: ButtonStyle(overlayColor: MaterialStateProperty.all(Colors.transparent)),
//         ),
//       ),
//     ),
//   );
//
//   list.add(DataCell(Icon(Icons.circle, color: contact.wantsAgencyValuation == false ? Colors.grey : Colors.green[700], size: 15)));
//
//   Color lockedColor = Color.fromRGBO(146, 146, 146, 1);
//   Color unlockedColor = Color.fromRGBO(72, 155, 121, 1);
//
//   list.add(
//     DataCell(
//       TextButton(
//         child: Row(
//           children: [
//             Container(
//               height: 30,
//               width: 30,
//               child: SvgPicture.asset('assets/icons/plane.svg', height: 20, color: contact.assignedAgencyId == null ? Color(0xff5b5b5b) : Colors.white),
//               padding: const EdgeInsets.all(6),
//               decoration: BoxDecoration(
//                 color: contact.assignedAgencyId == null
//                     ? Colors.transparent
//                     : contact.dateUnlocked != 'Not unlocked'
//                     ? Color.fromRGBO(72, 155, 121, 1)
//                     : Color.fromRGBO(147, 147, 147, 1),
//                 borderRadius: BorderRadius.circular(7.0),
//               ),
//             ),
//           ],
//         ),
//         style: ButtonStyle(overlayColor: MaterialStateProperty.all(Colors.transparent)),
//         onPressed: () {
//           showAgencyStatusPopup(contact);
//         },
//       ),
//     ),
//   );
//
//   list.add(
//     DataCell(contact.assignedAgencyId == null
//         ? Container()
//         : CustomDropdown2(
//       isMaster: true,
//       acquiredContact: contact,
//       updateStage: updateContactStatus,
//     )),
//   );
//
//   list.add(DataCell(
//     Stack(
//       children: [
//         Align(
//           alignment: AlignmentDirectional.centerStart,
//           child: TextButton(
//             child: Container(
//               height: 30,
//               width: 30,
//               padding: EdgeInsets.all(4),
//               child: SvgPicture.asset('assets/icons/comment.svg', height: 12, color: Color(0xff5b5b5b)),
//               decoration: BoxDecoration(
//                 color: Color.fromRGBO(231, 231, 231, 1),
//                 borderRadius: BorderRadius.circular(7.0),
//               ),
//             ),
//             onPressed: () {
//               showCommentPopup(contact);
//             },
//             style: ButtonStyle(overlayColor: MaterialStateProperty.all(Colors.transparent)),
//           ),
//         ),
//         Positioned(
//           bottom: 15,
//           left: 35,
//           child: Container(
//             height: 15,
//             width: 15,
//             alignment: Alignment.center,
//             decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(15.0), border: Border.all(color: Color.fromRGBO(197, 197, 197, 1), width: 1)),
//             child: StreamBuilder(
//               stream: FirebaseFirestore.instance.collection(appConfig.COLLECT_VALUATOR_SUBMISSION_COMMENTS).where('contactId', isEqualTo: contact.firebaseId).snapshots(),
//               builder: (BuildContext context, AsyncSnapshot<QuerySnapshot> snapshot) {
//                 if (!snapshot.hasData) {
//                   return Center(
//                     child: Container(),
//                   );
//                 }
//
//                 return NarFormLabelWidget(
//                   label: snapshot.data!.docs.length.toString(),
//                   fontWeight: 'bold',
//                   fontSize: 10,
//                   height: 1.3,
//                   textColor: Colors.black,
//                   textAlign: TextAlign.center,
//                 );
//               },
//             ),
//           ),
//         ),
//       ],
//     ),
//   ));
//   // list.add(DataCell(
//   //   !contact.wantsAgencyValuation!
//   //       ? Container()
//   //       : ImageFiltered(
//   //           imageFilter: ImageFilter.blur(
//   //               sigmaX: _showBlur(contact, widget.agency) ? 0 : 7,
//   //               sigmaY: _showBlur(contact, widget.agency) ? 0 : 7),
//   //           child: IconButton(
//   //             icon: Icon(Icons.comment),
//   //             onPressed: () {
//   //               !_showBlur(contact, widget.agency)
//   //                   ? () => {}
//   //                   : showCommentPopup(contact);
//   //             },
//   //           ),
//   //         ),
//   // ));
//
//   return list;
// }

/*Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      /* Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 5),
                        child: NarInputWidget(
                          hintText: "Cerca",
                          borderColor: Color.fromRGBO(227,227,227,1),
                          hintFontColor: Color.fromRGBO(227,227,227,1),
                          borderWidth: 2,
                          fontWeight: FontWeight.bold,
                          height: 0.7,
                          fontSize: 12,
                          onChanged: ( String _query ){
                            setState(() {
                              query = _query.toLowerCase();
                            });
                          },
                        ),*/

                      SizedBox(width: 30),
                      Container(
                        width: 180,
                        child: Column(
                          children: [
                            NarFormLabelWidget(
                              label: '',
                              fontSize: 10,
                              fontWeight: 'bold',
                              height: 1.5,
                            ),
                            NarInputWidget(
                              hintText: "Cerca contatto",
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              controller: searchTextController,
                              borderWidth: 1,
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: 10),
                      Column(
                        children: [
                          NarFormLabelWidget(
                            label: '',
                            fontSize: 10,
                            fontWeight: 'bold',
                            height: 1.5,
                          ),
                          Container(
                            height: 35,
                            width: 61,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(7),
                              color: Theme.of(context).primaryColor,
                            ),
                            child: TextButton(
                              onPressed: () {
                                if (searchTextController.text == '') {
                                  filters.removeWhere((element) {
                                    return element['field'] ==
                                        'additionalInfo.submitterName';
                                  });
                                } else {
                                  // var indexoffilter = filters.indexOf({ 'field': 'additionalInfo.submitterName' });

                                  filters.removeWhere((element) {
                                    return element['field'] ==
                                        'additionalInfo.submitterName';
                                  });

                                  filters.add({
                                    'field': 'additionalInfo.submitterName',
                                    'value': searchTextController.text,
                                    'search': 'equal'
                                  });
                                }

                                setState(() {
                                  cacheFirestore.clear();
                                  initialFetchContacts();
                                });
                              },
                              child: Center(
                                child: NarFormLabelWidget(
                                  label: "Cerca",
                                  textColor: Colors.white,
                                  fontSize: 13,
                                  fontWeight: 'bold',
                                ),
                              ),
                              style: ButtonStyle(
                                  overlayColor: MaterialStateProperty.all(
                                      Colors.transparent)),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),*/
/*showHideContacts() {
    if (agencyController.text != '' &&
        agencyController.text != 'Mostra tutti') {
      displayContacts = contacts.where((element) {
        if (element.agencyUser != null &&
            element.agencyUser!.name == agencyController.text) {
          return true;
        }
        return false;
      }).toList();
    } else {
      displayContacts = contacts.where((element) {
        return true;
      }).toList();
    }

    setState(() {});
  }*/

// void showAgencyCommentPopup(
//     AcquiredContact acquiredContact, Agency agency, AgencyUser agencyUser) {
//   showGeneralDialog(
//     context: context,
//     barrierLabel: "Barrier",
//     barrierDismissible: true,
//     barrierColor: Colors.black.withOpacity(0.5),
//     pageBuilder: (_, __, ___) {
//       return Center(
//           child: AgencyCommentPopup(
//               acquiredContact: acquiredContact,
//               agency: agency,
//               agencyUser: agencyUser));
//     },
//   );
// }
// Expanded(
//   child: DataTable2(
//     dataRowHeight: loadingContacts ? 300 : 70,
//     isHorizontalScrollBarVisible: true,
//     minWidth: 1500,
//     columnSpacing: 10,
//     horizontalMargin: 0,
//     headingRowHeight: 25,
//     columns: getColumns(pageWidth),
//     border: TableBorder(
//       bottom: BorderSide.none,
//       top: BorderSide.none,
//       left: BorderSide.none,
//       right: BorderSide.none,
//     ),
//     empty: Text('Nessun record trovato!'),
//     rows: List.generate(displayContacts.where(filterFunction).length, (int index) {
//       return DataRow(
//         key: ValueKey(contacts.where(filterFunction).elementAt(index).firebaseId),
//         color: MaterialStateProperty.resolveWith((states) {
//           // If the button is pressed, return green, otherwise blue
//           if (displayContacts.where(filterFunction).elementAt(index).wantsAgencyValuation!) {
//             return Colors.white;
//           }
//           return Colors.white;
//         }),
//         cells: getDataRow(
//           displayContacts.where(filterFunction).elementAt(index),
//         ),
//       );
//     }),
//   ),
// ),
// dataTablePagination(),
