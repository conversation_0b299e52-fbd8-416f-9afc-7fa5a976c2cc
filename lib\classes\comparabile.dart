import 'dart:html';

import 'package:flutter/foundation.dart';

class Comparabile {
  int? id;
  String? title;
  String? description;
  String? stato;
  String? address;
  double? prezzo;
  int? locali;
  double? superficie;
  double? piano;
  String? url;
  double? sqmPrice;
  List<String>? images;
  String? zonaOmi;
  double? latitude;
  double? longitude;
  //The scostamento of this comparabile related to its own zona omi
  int? scostamento = 0;
  double? predictedPrice;
  List<double>? offerPrice;

  Comparabile.fromAwsJson(Map<String, dynamic> data) {
    try {
      this.id = int.parse(data['listingUrl'].toString().split('/').last);
      this.title = data['address'];
      this.stato = data['maintenanceStatus'];
      //(data['stato'] as String).replaceAll('\n', '').replaceAll(' ', '');
      this.address = data['address'];
      this.description = data['description'];
      this.prezzo = double.parse(data['price']);
      this.locali = int.parse(data['rooms']);
      this.superficie = double.parse(data['grossSquareFootage']);
      this.piano = double.parse(data['unitFloor']);
      this.url = data['listingUrl'];
      this.sqmPrice = double.parse(data['pricePsm']);
      this.images = [data['coverImage']];
      this.zonaOmi = data['marketZone'] ?? 'None';
      this.latitude = data['latitude'];
      this.longitude = data['longitude'];
      this.predictedPrice = 0;
      this.offerPrice = [0, 0];
    } catch (e) {}
  }

  Comparabile.fromJson(Map<String, dynamic> data) {
    try {
      this.id = data['id'];
      this.title = data['title'];
      this.stato =
          (data['stato'] as String).replaceAll('\n', '').replaceAll(' ', '');
      this.address = data['title'];
      this.description = data['description'];
      this.prezzo = data['prezzo'];
      this.locali = data['locali'];
      this.superficie = data['superficie'];
      this.piano = data['piano'];
      this.url = data['url'];
      this.sqmPrice = this.prezzo! / this.superficie!;
      this.images = [
        "https://pwm.im-cdn.it/image/1037570468/xxl.jpg",
        "https://pwm.im-cdn.it/image/1037570472/xxl.jpg"
      ];
      this.zonaOmi = data['zona_omi'];
      this.latitude = data['latitude'];
      this.longitude = data['longitude'];
      this.predictedPrice = 0;
      this.offerPrice = [0, 0];
    } catch (e) {
      print(data['id']);
      print(e);
    }
  }

  Map<String, dynamic> toJsonPredict() {
    return {
      'locali': this.locali,
      'superficie': this.superficie,
      'zona_omi': this.zonaOmi,
      'piano': this.piano,
      'latitude': this.latitude,
      'longitude': this.longitude
    };
  }
}
