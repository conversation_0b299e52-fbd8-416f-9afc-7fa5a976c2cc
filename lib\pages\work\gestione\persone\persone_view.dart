import 'package:collection/collection.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/tab/status_widget.dart';
import 'package:newarc_platform/widget/UI/tab/text_button.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/widget/custom_drawer.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class PersoneViewController extends GetxController {
  RxBool loadingPeople = false.obs;

  List<NewarcUser> people = [];

  String currentlyShowing = '';

  int totalRecords = 0;
  final recordsPerPage = 20;

  int pageCounter = 1;
  int totalPages = 0;
  bool disablePreviousButton = true;
  bool disableNextButton = false;

  List<DocumentSnapshot> documentList = [];

  List<dynamic> cacheFirestore = [];

  List<String> formMessages = [];

  /// Aggiungi Professionista Popup controllers
  TextEditingController firstName = new TextEditingController();
  TextEditingController lastName= new TextEditingController();
  TextEditingController userEmail= new TextEditingController();
  TextEditingController userRole= new TextEditingController();
  TextEditingController isHiredInternally= new TextEditingController();
  TextEditingController newPassword= new TextEditingController();
  TextEditingController confirmPassword= new TextEditingController();
  bool newPasswordObscured = true;
  bool repeatPasswordObscured = true;
  /// Filter Popup controllers 
  final TextEditingController contSearchUserRoleController = new TextEditingController();
  String contSearchUserRole = '';

  String query = "";
  List<Map> filters = [];
  String currentMenu = 'team';
}

class PersoneView extends StatefulWidget {
  final bool isArchived;
  final bool forceDataFetch;

  // final AgencyUser agencyUser;
  final Function? updateViewCallback;
  final Map? projectArguments;

  static const String route = '/team/index';

  const PersoneView(
      {super.key,
      required this.isArchived,
      // required this.agencyUser,
      this.updateViewCallback,
      this.forceDataFetch = false,
      this.projectArguments = const {}
      });

  @override
  State<PersoneView> createState() => _PersoneViewState();
}

class _PersoneViewState extends State<PersoneView> {
  final controller = Get.put<PersoneViewController>(PersoneViewController());

  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();

    if (controller.currentMenu != widget.projectArguments?['current_menu']) {
      controller.people = [];

      if( widget.projectArguments != null ) {
        controller.currentMenu = widget.projectArguments?['current_menu'];
      }

    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      initialFetch( force: widget.forceDataFetch );
    });
  }

  

  Widget dataTablePagination() {
    return Visibility(
      visible: controller.currentlyShowing.isNotEmpty,
      child: Padding(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: IconTheme.merge(
            data: const IconThemeData(opacity: 0.54),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "${controller.currentlyShowing} di  ${controller.totalRecords.toString()}",
                  style: TextStyle(
                    fontFamily: '',
                    fontSize: 12.0,
                    color: Colors.black.withOpacity(0.54),
                  ),
                ),
                SizedBox(width: 32.0),
                IconButton(
                  icon: const Icon(Icons.chevron_left),
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    if (controller.disablePreviousButton == true) return;
                    if (controller.loadingPeople.value == true) return;
                    fetchPrevPeople();
                  },
                ),
                SizedBox(width: 24.0),
                IconButton(
                  icon: const Icon(Icons.chevron_right),
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    if (controller.disableNextButton == true) return;
                    if (controller.loadingPeople.value == true) return;

                    fetchNextPeople();
                  },
                ),
                SizedBox(width: 14.0),
              ],
            ),
          )),
    );
  }

  _initialFetch({bool force = false}) {
    initialFetch(force: force);
  }

  Future<void> initialFetch({bool force = false}) async {
    
    if (controller.people.isNotEmpty && !force) return;

    controller.pageCounter = 1;

    if (!mounted) return;

    setState(() {
      controller.people = [];
      controller.loadingPeople.value = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshotCounter;
      Query<Map<String, dynamic>> counterQuery;

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> dataQuery;

      if (controller.contSearchUserRoleController.text != '') {
        dataQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_USERS)
          .where('type', isEqualTo: 'newarc')
          .where('role', isEqualTo: controller.contSearchUserRole)
          .where('isArchived', isEqualTo: widget.isArchived)
          .orderBy('name', descending: false);

        counterQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_USERS)
          .where('type', isEqualTo: 'newarc')
          .where('role', isEqualTo: controller.contSearchUserRole)
          .where('isArchived', isEqualTo: widget.isArchived)
          .orderBy('name', descending: false);

      } else {
        dataQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_USERS)
          .where('type', isEqualTo: 'newarc')
          .where('isArchived', isEqualTo: widget.isArchived)
          .orderBy('name', descending: false);

        counterQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_USERS)
          .where('type', isEqualTo: 'newarc')
          .where('isArchived', isEqualTo: widget.isArchived)
          .orderBy('name', descending: false);      
      }

      collectionSnapshot = await dataQuery.limit(controller.recordsPerPage).get();
      collectionSnapshotCounter = await counterQuery.get();

      controller.totalRecords = collectionSnapshotCounter.docs.length;
      controller.totalPages = (controller.totalRecords / controller.recordsPerPage).ceil();

      if (controller.totalRecords > controller.recordsPerPage) {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      }

      controller.documentList = collectionSnapshot.docs;

      generateDataRows(collectionSnapshot);
      if (!mounted) return;
      setState(() {});
    } catch (e, s) {
      print({e, s});
      if (!mounted) return;
      setState(() {
        controller.loadingPeople.value = false;
      });
    }
  }

  fetchNextPeople() async {
    if (!mounted) return;
    setState(() {
      controller.loadingPeople.value = true;
    });

    controller.pageCounter++;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> dataQuery;

      int indexOfSnapshot = isRecordExists(controller.pageCounter);

      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        // dataQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_PROJECTS).where('isArchived', isEqualTo: widget.isArchived);
        dataQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_USERS).where('type', isEqualTo: 'newarc').where('isArchived', isEqualTo: widget.isArchived);
        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            dataQuery = dataQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }
        // if (contSearchProjectType != '') {
        //   dataQuery = FirebaseFirestore.instance
        //       .collection(appConfig.COLLECT_NEWARC_PROJECTS)
        //       .where('type', isEqualTo: contSearchProjectType)
        //       // .orderBy('created', descending: true)
        //       .startAfterDocument(documentList[documentList.length - 1])
        //       .limit(recordsPerPage);
        // } else {
        //   dataQuery = FirebaseFirestore.instance
        //       .collection(appConfig.COLLECT_NEWARC_PROJECTS)
        //       // .orderBy('created', descending: true)
        //       .startAfterDocument(documentList[documentList.length - 1])
        //       .limit(recordsPerPage);
        // }

        // collectionSnapshot =
        //     await dataQuery.orderBy('created', descending: true).limit(controller.recordsPerPage).startAfterDocument(controller.documentList[controller.documentList.length - 1]).get();
        collectionSnapshot =
            await dataQuery.orderBy('name', descending: false).limit(controller.recordsPerPage).startAfterDocument(controller.documentList[controller.documentList.length - 1]).get();
      }

      controller.documentList = collectionSnapshot.docs;

      generateDataRows(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingPeople.value = false;
      });
    }
  }

  fetchPrevPeople() async {
    if (!mounted) return;
    setState(() {
      controller.loadingPeople.value = true;
    });

    controller.pageCounter--;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> dataQuery;

      int indexOfSnapshot = isRecordExists(controller.pageCounter);
      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        // dataQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_PROJECTS).where('isArchived', isEqualTo: widget.isArchived);
        dataQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_USERS).where('type', isEqualTo: 'newarc').where('isArchived', isEqualTo: widget.isArchived);

        if (controller.filters.isNotEmpty) {
          for (var i = 0; i < controller.filters.length; i++) {
            dataQuery = dataQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }
        // if (contSearchProjectType != '') {
        //   dataQuery = FirebaseFirestore.instance
        //       .collection(appConfig.COLLECT_NEWARC_PROJECTS)
        //       .where('type', isEqualTo: contSearchProjectType)
        //       // .orderBy('created', descending: true)
        //       .endBeforeDocument(documentList[documentList.length - 1])
        //       .limit(recordsPerPage);
        // } else {
        //   dataQuery = FirebaseFirestore.instance
        //       .collection(appConfig.COLLECT_NEWARC_PROJECTS)
        //       // .orderBy('created', descending: true)
        //       .endBeforeDocument(documentList[documentList.length - 1])
        //       .limit(recordsPerPage);
        // }

        // collectionSnapshot = await dataQuery.orderBy('created', descending: true).limit(controller.recordsPerPage).endBeforeDocument(controller.documentList[controller.documentList.length - 1]).get();
      collectionSnapshot =
            await dataQuery.orderBy('name', descending: false).limit(controller.recordsPerPage).endBeforeDocument(controller.documentList[controller.documentList.length - 1]).get();
      }

      controller.documentList = collectionSnapshot.docs;

      generateDataRows(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingPeople.value = false;
      });
    }
  }

  int isRecordExists(int pageCounter) {
    String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
    return controller.cacheFirestore.indexWhere(
      (record) => record['key'] == pageCounter && record['filtersKey'] == filtersKey,
    );
  }

  generateDataRows(QuerySnapshot<Map<String, dynamic>> collectionSnapshot) async {
    if (!mounted) return;
    if (isRecordExists(controller.pageCounter) < 0) {
      String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
      controller.cacheFirestore.add({
        'key': controller.pageCounter,
        'snapshot': collectionSnapshot,
        'filtersKey': filtersKey,
      });
    }
    if (controller.pageCounter == 1) {
      controller.disablePreviousButton = true;
    } else {
      controller.disablePreviousButton = false;
    }

    if (controller.pageCounter == controller.totalPages) {
      controller.disableNextButton = true;
    } else {
      controller.disableNextButton = false;
    }

    List<NewarcUser> _people = [];
    for (var element in collectionSnapshot.docs) {
      NewarcUser _tmp = NewarcUser.fromDocument(element.data(), element.id);
      _people.add(_tmp);
    }

    int lastRecordNumber = controller.pageCounter * controller.recordsPerPage;
    if (_people.length == controller.recordsPerPage) {
      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + lastRecordNumber.toString();
    } else if (_people.length > 0 && _people.length < controller.recordsPerPage) {
      int prevLastRecordNumber = (controller.pageCounter - 1) * controller.recordsPerPage;

      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + (prevLastRecordNumber + _people.length).toString();
    }

    if (!mounted) return;

    setState(() {
      controller.people = _people;
      controller.loadingPeople.value = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      key: scaffoldKey,
      drawer: CustomDrawer(),
      body: LayoutBuilder(builder: (BuildContext context, BoxConstraints constraints) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                NarFormLabelWidget(
                  label: widget.isArchived ? 'Persone archiviate' : 'Gestisci persone',
                  fontSize: 19,
                  fontWeight: '700',
                ),
                _create(context),
              ],
            ),

            // Filter goes here
            _filter(),

            SizedBox(height: 10),
            Container(
              height: constraints.maxHeight / 1.2,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: AppColor.white,
                border: Border.all(width: 1.5, color: AppColor.borderColor),
              ),
              child: Column(
                children: [
                  Expanded(
                    child: Stack(
                      children: [
                        Opacity(
                          opacity: controller.loadingPeople.value ? 0.5 : 1,
                          child: NewarcDataTable(
                            rowsPerPage: 20,
                            isHasDecoration: false,
                            hidePaginator: true,
                            onPageChanged: (val) {
                              print("page : $val");
                            },
                            source: PeopleRowSource(
                              people: controller.people,
                              rolesDict: appConst.userRolesDict,
                              context: context,
                              redirectToSinglePage: (String userId) {
                                widget.projectArguments!.clear();
                                widget.projectArguments!.addAll({
                                  'userId': userId,
                                  'updateViewCallback': widget.updateViewCallback,
                                  'initialFetchPeople': _initialFetch
                                });
                                widget.updateViewCallback!('team-single', projectArguments: widget.projectArguments);
                              },
                            ),
                            columns: getColumns1(constraints),
                          ),
                        ),
                        if (controller.loadingPeople.value)
                          Positioned.fill(
                            child: Center(
                              child: CircularProgressIndicator(
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerRight,
                    child: dataTablePagination(),
                  ),
                ],
              ),
            )

            // dataTablePagination(),
          ],
        );
      }),
    );
  }

  NarFilter _filter() {
    return NarFilter(
      showSearchInput: false,
      selectedFilters: [controller.contSearchUserRole],
      textEditingControllers: [controller.contSearchUserRoleController],
      filterFields: [
        {
          'Ruolo': NarSelectBoxWidget(
            options: appConst.userRolesDict.values.sorted().toList(),
            onChanged: (value) {
              controller.contSearchUserRole = appConst.userRolesDict.entries.firstWhere((entry) => entry.value == controller.contSearchUserRoleController.text).key;
              setState(() {});
            },
            controller: controller.contSearchUserRoleController,
          ),
        },
      ],
      onSubmit: () async {
        controller.filters = [
          {
            'field': 'role',
            'value': controller.contSearchUserRole,
          }
        ];
        print("search with ----> ${controller.contSearchUserRole}");
        await initialFetch(force: true);
      },
      onReset: () async {
        controller.filters.clear();
        controller.contSearchUserRole = '';
        await initialFetch(force: true);
      },
    );
  }

  MouseRegion _create(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () async {
          controller.formMessages.clear();

          return await showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) {
                return StatefulBuilder(builder: (BuildContext context, StateSetter _setState) {
                  return Center(
                    child: BaseNewarcPopup(
                      formErrorMessage: controller.formMessages,
                      buttonText: 'Aggiungi professionista',
                      onPressed: () async {
                        if (!controller.userEmail.text.isValidEmail()) {
                          setState(() {
                            controller.formMessages.clear();
                            controller.formMessages.add('Invalid email!');
                          });

                          return false;
                        }
                        if (controller.newPassword.text != '' &&
                            controller.newPassword.text ==
                                controller.confirmPassword.text) {
                          setState(() {
                            setState(() {
                              controller.formMessages.clear();
                              controller.formMessages.add(
                                  'Processing information!');
                            });
                          });

                          try {
                            final result =
                                await FirebaseFunctions.instance
                                    .httpsCallable('createUser')
                                    .call({
                              'email': controller.userEmail.text,
                              'password': controller.newPassword.text,
                            });

                            NewarcUser userData =
                                new NewarcUser({});

                            userData.firstName = controller.firstName.text;
                            userData.lastName = controller.lastName.text;
                            userData.role = appConst.userRolesDict.entries.firstWhere((entry) => entry.value == controller.userRole.text).key;
                            userData.type = 'newarc';
                            userData.email = controller.userEmail.text;
                            userData.isActive = true;
                            userData.isArchived = false;
                            userData.isHiredInternally = controller.isHiredInternally.text == 'Interno' ? true : false;

                            await FirebaseFirestore.instance
                                .collection(
                                    appConfig.COLLECT_USERS)
                                .doc(result.data['uid'])
                                .set(userData.toMap());
                            Navigator.of(context).pop();
                            await initialFetch(force: true);
                            // return true;
                          } on FirebaseAuthException catch (e) {
                            if (e.code == 'weak-password') {
                              setState(() {
                                controller.formMessages.clear();
                                controller.formMessages.add(
                                    'The password provided is too weak.');
                              });
                            } else if (e.code ==
                                'email-already-in-use') {
                              setState(() {
                                controller.formMessages.clear();
                                controller.formMessages.add(
                                    'The account already exists for that email.');
                              });
                            } else {

                              setState(() {
                                controller.formMessages.clear();
                                controller.formMessages.add(e.message!);
                              });
                            }

                            return false;
                          } catch (e, s) {

                            setState(() {
                              controller.formMessages.clear();
                              controller.formMessages
                                  .add('Something went wrong!');
                            });
                            return false;
                          }
                        } else {
                          setState(() {
                            controller.formMessages.clear();
                            controller.formMessages
                                .add("Password doesn't match!");
                          });

                          return false;
                        }
                      },
                      title: "Aggiungi un professionista",
                      column: Container(
                        width: 600,
                        height: 400,
                        child: ListView(
                          children: [
                            Row(
                              mainAxisAlignment:
                                  MainAxisAlignment.center,
                              children: [
                                CustomTextFormField(
                                  label: "Nome",
                                  controller: controller.firstName,
                                  // validationMessage: 'Required!',
                                  validator: (value) {
                                    if (value == '') {
                                      return 'Required!';
                                    }

                                    return null;
                                  },
                                ),
                              ],
                            ),
                            SizedBox(height: 6),
                            Row(
                              mainAxisAlignment:
                                  MainAxisAlignment.center,
                              children: [
                                CustomTextFormField(
                                  label: "Cognome",
                                  controller: controller.lastName,
                                  // validationMessage: 'Required!',
                                  validator: (value) {
                                    if (value == '') {
                                      return 'Required!';
                                    }

                                    return null;
                                  },
                                ),
                              ],
                            ),
                            SizedBox(height: 6),
                            Row(
                              mainAxisAlignment:
                                  MainAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisSize:
                                        MainAxisSize.max,
                                    crossAxisAlignment:
                                        CrossAxisAlignment
                                            .start,
                                    mainAxisAlignment:
                                        MainAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: "Professione",
                                        textColor:
                                            Color(0xff696969),
                                        fontSize: 14,
                                        fontWeight: '600',
                                      ),
                                      SizedBox(height: 4),
                                      NarSelectBoxWidget(
                                          options: appConst.userRolesDict.values.sorted().toList(),
                                          controller: controller.userRole,
                                          validationType:
                                              'required',
                                          parametersValidate:
                                              'Required!'),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 6),
                            Row(
                              mainAxisAlignment:
                                  MainAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: Column(
                                    mainAxisSize:
                                        MainAxisSize.max,
                                    crossAxisAlignment:
                                        CrossAxisAlignment
                                            .start,
                                    mainAxisAlignment:
                                        MainAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: "Contratto",
                                        textColor:
                                            Color(0xff696969),
                                        fontSize: 14,
                                        fontWeight: '600',
                                      ),
                                      SizedBox(height: 4),
                                      NarSelectBoxWidget(
                                        options: ["Interno", "Esterno"],
                                        controller: controller.isHiredInternally,
                                        validationType: 'required',
                                        parametersValidate: 'Required!'
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 6),
                            Row(
                              mainAxisAlignment:
                                  MainAxisAlignment.center,
                              children: [
                                CustomTextFormField(
                                  label: "E-mail",
                                  controller: controller.userEmail,
                                  validator: (value) {
                                    if (value == '' ||
                                        !value
                                            .toString()
                                            .isValidEmail()) {
                                      return 'Invalid email';
                                    }

                                    return null;
                                  },
                                ),
                              ],
                            ),
                            SizedBox(height: 6),
                            Row(
                              mainAxisAlignment:
                                  MainAxisAlignment.center,
                              children: [
                                CustomTextFormField(

                                  isObscureText: controller.newPasswordObscured,
                                  label: "Scegli una password",
                                  controller: controller.newPassword,
                                  // validationMessage: 'Required!',
                                  validator: (value) {
                                    if (value == '') {
                                      return 'Required!';
                                    }

                                    return null;
                                  },
                                  suffixIcon: IconButton(
                                    icon: controller.newPasswordObscured
                                        ? Icon(
                                            Icons.visibility_rounded,
                                            color: Theme.of(context)
                                                .primaryColor,
                                          )
                                        : Icon(
                                            Icons
                                                .visibility_off_rounded,
                                            color: Colors.grey),
                                    onPressed: () {
                                      // print(newPasswordVisible);
                                      _setState(() {
                                        controller.newPasswordObscured = !controller.newPasswordObscured;
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 6),
                            Row(
                              mainAxisAlignment:
                                  MainAxisAlignment.center,
                              children: [
                                CustomTextFormField(
                                  isObscureText: controller.repeatPasswordObscured,
                                  label: "Ripeti la password ",
                                  controller: controller.confirmPassword,
                                  validator: (value) {
                                    if (value == '') {
                                      return 'Required!';
                                    }
                                    if (controller.newPassword.text !=
                                        controller.confirmPassword.text) {
                                      return "Confirm password doesn't match!";
                                    }

                                    return null;
                                  },
                                  suffixIcon: IconButton(
                                    icon: controller.repeatPasswordObscured
                                        ? Icon(
                                            Icons.visibility_rounded,
                                            color: Theme.of(context)
                                                .primaryColor,
                                          )
                                        : Icon(
                                            Icons
                                                .visibility_off_rounded,
                                            color: Colors.grey),
                                    onPressed: () {
                                      _setState(() {
                                        controller.repeatPasswordObscured = !controller.repeatPasswordObscured;
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                });
              });
        },
        child: Container(
          height: 32,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: Text(
              "Aggiungi persone",
              style: TextStyle(
                color: Colors.white,
                fontSize: 13,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  List<DataColumn> getColumns1(BoxConstraints constraints) {
    List<DataColumn> list = [];
    list.add(
      DataColumn2(
        label: Text(
          'Nome e Cognome',
        ),
        size: ColumnSize.L
      ),
    );
    list.add(
      DataColumn2(
        label: Text(
          'Email',
        ),
        size: ColumnSize.L
      ),
    );
    list.add(
      DataColumn2(
        // fixedWidth: 250,
        label: Text(
          'Professione',
        ),
        size: ColumnSize.M
      ),
    );
    list.add(
      DataColumn2(
        label: Text(
          'Prog. assegnati',
        ),
        size: ColumnSize.S
      ),
    );
    list.add(
      DataColumn2(
        label: Text(
          'Contratto',
        ),
        size: ColumnSize.S
      ),
    );
    list.add(
      DataColumn2(
        label: Text(
          'Attivo',
        ),
        size: ColumnSize.S
      ),
    );

    return list;
  }
}

Future<String> getImageUrl(String userId) async {
  final extensions = ['.jpeg', '.png', '.jpg'];
  for (final extension in extensions) {
    final ref = FirebaseStorage.instance.ref().child('users/$userId/profile$extension');
    try {
      return await ref.getDownloadURL();
    } catch (error) {
      continue;
    }
  }
  throw Exception('Profile image not found for user $userId');
}

class PeopleRowSource extends DataTableSource {
  PeopleRowSource({
    required this.people, 
    required this.rolesDict,
    required this.redirectToSinglePage, 
    required this.context, 
  });

  List<NewarcUser> people = [];
  BuildContext context;
  Function(String) redirectToSinglePage;
  Map<String, String> rolesDict = {};

  Future<ImageProvider> getImage(String userId) async {
    NewarcUser _user =
        people.where((element) => element.id == userId).first;
    // final extensions = ['.jpeg', '.png', '.jpg'];
    // for (final extension in extensions) {

    // }
    final ref = FirebaseStorage.instance
        .ref()
        .child('users/$userId/' + _user.profilePicture!);
    // print({'pro', 'users/$userId/'+ _user.profilePicture!});
    try {
      final url = await ref.getDownloadURL();
      return NetworkImage(url);
    } catch (error) {
      return AssetImage('assets/icons/user-icon.png');
    }
    
  }

  @override
  DataRow? getRow(int index) {
    if (index < people.length) {
      final row = people[index];
      return DataRow2(
        specificRowHeight: 50,
        cells: [
          DataCell(
            NarLinkWidget(
              text: "${row.firstName ?? 'NoFirstName'} ${row.lastName ?? 'NoLastName'}",
              textColor: Colors.black,
              fontWeight: '700',
              fontSize: 12,
              leadingIcon: row.profilePicture == null 
                ? Icon(Icons.person, size: 25)
                : FutureBuilder<ImageProvider>(
                future:
                  getImage(row.id!),
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    NewarcUser _user = row;
                    if (_user.id == '' || snapshot.hasError) {
                      return Icon(Icons.person, size: 25);
                    } else if (_user.profilePicture == '') {
                      return Icon(Icons.person, size: 25);
                    }
                    return Row(
                      children: [
                        ClipOval(
                          child: Image(
                            image: snapshot.data!,
                            width: 25,
                            height: 25,
                            fit: BoxFit.cover, // Adjust as needed
                          ),
                        ),
                      ],
                    );
                  } else if (snapshot.hasError) {
                    return Icon(Icons.person, size: 25);
                  }
                  return Icon(Icons.person, size: 25);
                },
              ),
              onClick: () {
                redirectToSinglePage(row.id ?? "0");
              },
            )
          ),
          DataCell(
            NarFormLabelWidget(
              label: row.email ?? 'NoEmail',
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: row.role == null ? 'NoRole' : rolesDict[row.role],
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: "0",
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),
          DataCell(
            TextButtonWidget(
              status: row.isHiredInternally == null ? 'null' : row.isHiredInternally! ? 'Interno' : 'Esterno',
              textColor: AppColor.black,
              isOnlyBorder: true,
              borderColor: row.isHiredInternally == null ? AppColor.redColor : row.isHiredInternally! ? AppColor.lightGreen200Color : null,
              backgroundColor: row.isHiredInternally == null ? AppColor.redColor : row.isHiredInternally! ? AppColor.lightGreen100Color : null,
            ),
          ),
          DataCell(
            StatusWidget(
              statusColor: row.isActive==null ? AppColor.redColor : row.isActive! ? AppColor.successGreenColor : AppColor.redColor,
            ),
          ),
        ],
      );
    }
    return null;
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => people.length;

  @override
  int get selectedRowCount => 0;
}
