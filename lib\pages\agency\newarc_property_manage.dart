import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/property.dart';
import 'package:newarc_platform/widget/work/project/add_property_form.dart';

import 'package:newarc_platform/widget/custom_drawer.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class NewarcPropertyManage extends StatefulWidget {
  static const String route = '/property/index';
  final Property property;

  const NewarcPropertyManage({super.key, required this.property});

  @override
  State<NewarcPropertyManage> createState() => _NewarcPropertyManageState();
}

class _NewarcPropertyManageState extends State<NewarcPropertyManage> {
  bool loadingProperties = true;
  List<Property> properties = [];

  String query = "";

  @override
  void initState() {
    super.initState();
  }

  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      key: scaffold<PERSON><PERSON>,
      drawer: CustomDrawer(),
      body: LayoutBuilder(
          builder: (BuildContext context, BoxConstraints constraints) {
        return SingleChildScrollView(
          // scrollDirection: Axis.vertical,

          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      child: Icon(
                        Icons.arrow_back_ios_new_rounded,
                        color: Colors.black,
                        size: 21,
                      ),
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                    ),
                  ),
                  SizedBox(width: 20),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      NarFormLabelWidget(
                        label: 'Gestisci progetto',
                        fontSize: 13,
                        fontWeight: '500',
                        textColor: Color(0xff737373),
                      ),
                      NarFormLabelWidget(
                        label: widget.property.propertyName,
                        fontSize: 22,
                        fontWeight: 'bold',
                        textColor: Colors.black,
                      ),
                    ],
                  ),
                ],
              ),
              SizedBox(height: 20),
              // Container(
              //     decoration: BoxDecoration(
              //         color: Colors.white,
              //         borderRadius: BorderRadius.circular(13),
              //         border: Border.all(
              //             width: 1, color: Color.fromRGBO(219, 219, 219, 1))),
              //     height: constraints.maxHeight / 1.2,
              //     // width: 300,
              //     child: AddPropertyForm(
              //       firebaseId: widget.property.firebaseId,
              //       property: widget.property,
              //       initialFetchProperties: () {},
              //     )
              //   ),
            ],
          ),
        );
      }),
    );
  }
}
