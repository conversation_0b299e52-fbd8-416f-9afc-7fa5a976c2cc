import 'package:flutter/material.dart';
import 'package:newarc_platform/widget/UI/input.dart';

/// *  textInputType - The type of information for which to optimize the text input control.
/// *  hintText - Text that suggests what sort of input the field accepts.
/// *  prefixIcon - Pass Icon if required
/// *  defaultText - If there is predefined value is there for a text field
/// *  focusNode - Currently focus node
/// *  obscureText - Is Password field?
/// *  controller - Text controller
/// *  functionValidate - Validation function(currently I have used to check empty field)
/// *  parametersValidate - Value to validate
/// *  actionKeyboard - Keyboard action eg. next, done, search, etc
/// *  onSubmitField - Done click action
/// *  onFieldTap - On focus on TextField
class NarCheckboxWidget extends StatefulWidget {
  final String? label;
  final Map<String, dynamic>? values;
  final int? columns;
  final Widget? appendText;
  final Map<String, TextEditingController>? additionalValue;
  final double? childAspectRatio;
  final String? appendPosition;
  final IconData? suffixIcon;
  final double? fontSize;
  final double? listTileBoxWidthHeight;
  final Function? onChanged;
  final bool? singleSelected;
  


  const NarCheckboxWidget(
      {required this.label,
      required this.values,
      this.columns,
      this.appendText,
      this.additionalValue,
      this.childAspectRatio = 8,
      this.appendPosition = 'bottom',
      this.suffixIcon,
      this.fontSize = 14,
      this.listTileBoxWidthHeight, 
      this.onChanged,
      this.singleSelected = false,
      });

  @override
  _NarCheckboxWidgetState createState() => _NarCheckboxWidgetState();
}

class _NarCheckboxWidgetState extends State<NarCheckboxWidget> {
  double? childAspectRatio = 8;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    // childAspectRatio =
    //     widget!.childAspectRatio == null ? 8 : widget!.childAspectRatio;
  }

  setAspectRatio() {
    // if( widget.appendText != null ) {
    // childAspectRatio = 8;
    // widget.values!.forEach((key, value) {
    //   if (value == true) {
    //     childAspectRatio = 2.4;
    //   }
    // });
    // }
  }

  @override
  Widget build(BuildContext context) {
    // print({ widget.appendText, widget.label });
    return Theme(
      data: Theme.of(context).copyWith(
        primaryColor: Theme.of(context).primaryColor,
        unselectedWidgetColor: Color.fromRGBO(219, 219, 219, 1),
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        hoverColor: Colors.transparent,
        focusColor: Colors.transparent,
        /*checkboxTheme: CheckboxThemeData(
          fillColor: WidgetStateProperty.resolveWith<Color>(
              (Set<MaterialState> states) {
            if (states.contains(MaterialState.disabled)) {
              return Color.fromRGBO(219, 219, 219, 1);
            } else if (states.contains(MaterialState.selected)) {
              return Color(0xff489B79);
            }

            return Color.fromRGBO(219, 219, 219, 1);
          }),
          overlayColor: WidgetStateProperty.resolveWith<Color>(
              (Set<MaterialState> states) {
            if (states.contains(MaterialState.disabled)) {
              return Colors.transparent;
            } else if (states.contains(MaterialState.selected)) {
              return Colors.transparent;
            }

            return Colors.transparent;
          }),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5),
          ),
        ),*/
      ),
      child: new GridView.count(
        crossAxisCount: widget.columns ?? 3,
        shrinkWrap: true,
        childAspectRatio: widget.childAspectRatio!,
        mainAxisSpacing: 1,
        children: widget.values!.keys.map((String key) {
          return SizedBox(
            height: 24,
            width: 24,
            child: ListTileTheme(
              horizontalTitleGap: 0,
              child: CheckboxListTile(
                isThreeLine: widget.appendText != null &&
                        widget.values![key] == true &&
                        widget.appendPosition == 'bottom'
                    ? true
                    : false,
                dense: true,
                contentPadding: EdgeInsets.zero,
                controlAffinity: ListTileControlAffinity.leading,
                splashRadius: 15,
                title: widget.appendPosition == 'bottom'
                    ? Container(
                        child: new Text(key,
                            style: TextStyle(
                                // color: colorRed,
                                fontSize: widget.fontSize,
                                fontWeight: FontWeight.w800,
                                fontStyle: FontStyle.normal,
                                overflow: TextOverflow.ellipsis
                                //fontFamily: 'Visby800'
                                )),
                      )
                    : Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          new Text(key,
                              style: TextStyle(
                                // color: colorRed,
                                fontSize: 14.0,
                                fontWeight: FontWeight.w800,
                                fontStyle: FontStyle.normal,
                                //fontFamily: 'Visby800'
                              )),
                          //widget.appendText != null && widget.values![key] == true
                          //?
                          AbsorbPointer(
                            absorbing: !widget.values![key],
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Opacity(
                                    opacity: widget.values![key] ? 1 : 0.5,
                                    child: widget.appendText!),
                                SizedBox(
                                  width: 10,
                                ),
                                Container(
                                  width: 130,
                                  child: NarInputWidget(
                                      borderRadius: 8,
                                      suffixIcon: Icon(
                                        widget.suffixIcon!,
                                        size: 13,
                                        weight: 1,
                                        color: widget.values![key]
                                            ? Color.fromRGBO(105, 105, 105, 1)
                                            : Color.fromRGBO(
                                                105, 105, 105, 0.5),
                                      ),
                                      enabled: widget.values![key],
                                      hintText: '',
                                      controller: widget.additionalValue![key]),
                                ),
                              ],
                            ),
                          )
                          //: SizedBox(height: 0,)

                          // NarInputWidget(
                          //     hintText: '',
                          //     controller: widget.additionalValue![key])
                        ],
                      ),
                subtitle: widget.appendText != null &&
                        widget.values![key] == true &&
                        widget.appendPosition == 'bottom'
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Expanded(
                              flex: 50,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    height: 5,
                                  ),
                                  widget.appendText!,
                                  NarInputWidget(
                                      hintText: '',
                                      controller: widget.additionalValue![key])
                                ],
                              )),
                          Expanded(flex: 50, child: SizedBox(height: 0))
                        ],
                      )
                    : null,
                value: widget!.values![key],
                // onChanged: (bool? value) {
                //   setState(() {
                //     widget.values![key] = value!;
                //     if (widget.appendText != null) {
                //       setAspectRatio();
                //       // childAspectRatio = 2.2;
                //     }
                //   });
                // },
                onChanged: (bool? value) {
                  setState(() {
                    if (widget.singleSelected ?? false) {
                      if (value == true) {
                        // Select only this one, unselect others
                        widget.values!.updateAll((k, v) => k == key);
                      } else {
                        // Optional: allow deselecting all (or prevent if at least one must be selected)
                        widget.values![key] = false;
                      }
                    } else {
                      // Multi-select logic
                      widget.values![key] = value!;
                    }

                    if (widget.appendText != null) {
                      setAspectRatio();
                    }
                  });
                  if (widget.onChanged != null) {
                    widget.onChanged!(value);
                  }
                },
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
