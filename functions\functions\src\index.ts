// @ts-nocheck
const {Client} = require("pg");
import * as functions from "firebase-functions";
require('dotenv').config();
import * as cors from "cors";

const axios = require('axios');
const admin = require("firebase-admin");
const {getAuth} = require("firebase-admin/auth");
const serviceAccount = require("../serviceAccountKey.js");

const {initializeApp: fb_initializeApp, getApp} = require("firebase/app");
const {getAuth: fb_getAuth, initializeAuth,applyActionCode, verifyPasswordResetCode, confirmPasswordReset} = require("firebase/auth");


admin.initializeApp({
	credential: admin.credential.cert(serviceAccount)
});

const credentials = {
	user: "postgres_newarc",
	host: "database-1.c3em0kigj93o.eu-south-1.rds.amazonaws.com",
	database: "postgres",
	password: "NewarcRDS1!",
	port: 5432,
};

export const queryDatabase = functions.https.onCall(async (data, context) => {
	try {
		const query: String = data.query

		const client = new Client(credentials);
		await client.connect();
		const result = await client.query(query);
		await client.end();
		return result;
	} catch (e) {
		return { error: String(e) };
	}


	//functions.logger.info("Hello logs!", { structuredData: true });

});

//usermgmt?mode=verifyEmail&oobCode=p531ZacpZtEHpihTaL5LCkmo0D-U89OJVp2dF5CBoJ8AAAGE4aBt9Q&apiKey=AIzaSyBq5h5fMtUIE5i0LKSY8B5y8s6SdG_g1dQ&continueUrl=https%3A%2F%2Fnewarc.it&lang=en
//usermgmt?mode=verifyEmail&oobCode=p123ZacpZtEHpihTaL5LCkmo0D-U89OJVp2dF5CBoJ8AAAGE4aBt9Q&apiKey=AIzaSyBq5h5fMtUIE5i0LKSY8B5y8s6SdG_g1dQ&continueUrl=https%3A%2F%2Fnewarc.it&lang=en

async function handleVerifyEmail(auth, actionCode, continueUrl, lang) {
	
	applyActionCode(auth, actionCode).then((resp) => {
		console.log('Email verified with custom link.', auth, actionCode, continueUrl, lang);
		return true;
	}).catch((error) => {
		console.log('Email verification issue: ',error);
		return error;
		
	});
};

async function handleResetPassword(auth, actionCode, continueUrl, lang, newPassword) {
	try{
		await verifyPasswordResetCode(auth, actionCode)
		 await confirmPasswordReset(auth, actionCode, newPassword)
		 return true
	}catch(error){
		console.log(error)
		return error;
	}
	
	/*
	verifyPasswordResetCode(auth, actionCode).then((email) => {
	  //const accountEmail = email;
		console.log("in verify")
	  // Save the new password.
		confirmPasswordReset(auth, actionCode, newPassword).then((resp) => {
		console.log("in confirm")
		return true;
	  }).catch((error) => {
		console.log('Password reset issue: ',error);
		return error;
	  });
	}).catch((error) => {
		console.log('Verification code for reset issue: ',error);
		return error;
	});*/
  }
//TODO To be renamed as to be the generic endpoint for auth actions
export const verifyUserEmail = functions.https.onCall(async (data, context) => {

	console.log("CALLED");
	
	return false;
	
});

export const testFunction = functions.https.onCall((data, context) => {
	console.log(data);

	const mode = data['mode'];
	// Get the one-time code from the query parameter.
	const actionCode = data['oobCode'];
	// (Optional) Get the continue URL from the query parameter if available.
	const continueUrl = data['continueUrl'];
	// (Optional) Get the language code if available.
	const lang = data['lang'] || 'en';

	// Configure the Firebase SDK.
	// This is the minimum configuration required for the API to be used.
	const config = {
		'apiKey': process.env.WEB_API_KEY // Copy this key from the web initialization
								// snippet found in the Firebase console.
	};

	console.log(process.env.WEB_API_KEY);

	let app;
	let auth;
	try {
		app = getApp();
	} catch {
		app =  fb_initializeApp({
			'apiKey': 'AIzaSyCQonHGTeEdhNQV1XJufEyu-B6rP1msc6Y'
		});
		
	} finally {
		auth = initializeAuth(app)
	}

	// = fb_initializeApp(config);

	console.log(auth)

	switch (mode) {
		case 'resetPassword':
			const newPassword = data['newPassword'];
			console.log("Handle reset", resp)
			var resp = handleResetPassword(auth, actionCode, continueUrl, lang, newPassword)
			return resp
		case 'verifyEmail':
			console.log("Handle verification email")
		  return handleVerifyEmail(auth, actionCode, continueUrl, lang).then((resp) => {
			return resp;
		  }).catch((error) => {
			console.log(error);
			return false;
		  });
		default:
			console.log("Unsupported method");
			return false;
	  }
	
	/*return handleVerifyEmail(auth, actionCode, continueUrl, lang).then((resp) => {
		return resp;
	  }).catch((error) => {
		console.log(error);
		return false;
	  });*/
})


export const verifyUserEmailOld = functions.https.onCall(async (data, context) => {

	const actionCodeSettings = {
		url: process.env.REDIRECT_URL // URL you want to be redirected to after email verification
	}

	console.log(data);

	const mode = data['mode'];
	// Get the one-time code from the query parameter.
	const actionCode = data['oobCode'];
	// (Optional) Get the continue URL from the query parameter if available.
	const continueUrl = data['continueUrl'];
	// (Optional) Get the language code if available.
	const lang = data['lang'] || 'en';

	// Configure the Firebase SDK.
	// This is the minimum configuration required for the API to be used.
	const config = {
		'apiKey': "AIzaSyCQonHGTeEdhNQV1XJufEyu-B6rP1msc6Y" //process.env.WEB_API_KEY // Copy this key from the web initialization
								// snippet found in the Firebase console.
	};

	console.log(process.env.WEB_API_KEY);

	//const app = fb_initializeApp(config);
	//const auth = fb_getAuth(app);

	switch (mode) {
		case 'resetPassword':
			const newPassword = data['newPassword'];

			var resp = handleResetPassword(auth, actionCode, continueUrl, lang, newPassword)
			console.log("after handle handle ", resp)
			return resp
		case 'recoverEmail':
		  // Display email recovery handler and UI.
		//   handleRecoverEmail(auth, actionCode, lang);
		  break;
		case 'verifyEmail':
		  // Display email verification handler and UI.
		  return handleVerifyEmail(auth, actionCode, continueUrl, lang).then((resp) => {
			return resp;
		  }).catch((error) => {
			console.log(error);
			return false;
		  });
		default:
	  }

	  return false;
	
});


export const sendVerificationLinkEmail = functions.https.onCall(async (data, context) => {

	const userEmail = data.email;
	const actionCodeSettings = {
		url: process.env.REDIRECT_URL // URL you want to be redirected to after email verification
	}

	try{
		
		let actionLink = getAuth()
		.generateEmailVerificationLink(userEmail, actionCodeSettings).then(async (link) => {
			
			const mailjet = require("node-mailjet").apiConnect(
				"********************************",
				"a63bc5bb6f0d7865afb08f7ca1bd4a85"
			);

			// Fare interfacce
			const mailData = {
				Subject: "Verifica Email Account Newarc",
				name: data.email,
				email: data.email
			};

			console.log('Link Generated', link);

			//let templateId = data.templateId;

			let mailjet_response = await mailjet.post("send", { version: "v3.1" }).request({
				Messages: [
					{
						From: {
							Email: process.env.EMAIL_FROM_ADDRESS,
							Name: process.env.EMAIL_FROM_TITLE
						},
						To: [
							{
								Email: mailData.email,
								Name: mailData.name,
							},
						],
						TemplateID: parseInt(process.env.VERIFICATION_EMAIL_TEMPLATE_ID),
						TemplateLanguage: true,
						Subject: mailData.Subject,
						Variables: {
							name: mailData.name,
							authUrl: link

						},

					},
				],
			}).then((message)=>{ 
				console.log('mailjet message', message);
				return "true";
			})
			.catch((error)=>{
				console.log('mailjet error', error);
				return "false";
			});
			
			return mailjet_response;

		}).catch((error) => {
			console.log('firebase error', error);
			return "false";
		});

		return actionLink;
	
	} catch(error) {
		console.log('firebase general error', error);
		return "false";
	// handle errors
	}
	
});
 
export const sendNotificationEmail = functions.https.onCall(async (data, context) => {


	try {

		const mailjet = require("node-mailjet").apiConnect(
			"********************************",
			"a63bc5bb6f0d7865afb08f7ca1bd4a85"
		);

		if (context.auth != null && context.auth.uid == undefined) {
			return { code: 401, result: "unauthorized" };
		}
		// Fare interfacce
		const mailData = data;
		console.log(mailData)

		let templateId = data.templateId;

		await mailjet.post("send", { version: "v3.1" }).request({
			Messages: [
				{
					From: {
						Email: "<EMAIL>",
						Name: "Newarc s.r.l.",
					},
					To: [
						{
							Email: mailData.email,
							Name: mailData.name,
						},
					],
					TemplateID: templateId,
					TemplateLanguage: true,
					Subject: mailData.Subject,
					Variables: {
						name: mailData.name,

					},

				},
			],
		});
		return { code: 200 };
	} catch (e) {
		//console.log(e);
		functions.logger.error(e);
		return { code: 500, result: e };
	}
});

exports.getDataFromUrl = functions.https.onCall(async (data, context) => {
	const url = data.url;
	try {
	  const info = await axios.get(url);
	  return info.data;
	} catch (error) {
	  return (error);
	}
  });

  exports.contactFormEmailSend = functions.https.onCall(async (data, context) => {
	try {
	
		const mailjet = require("node-mailjet").apiConnect(
			"********************************",
			"a63bc5bb6f0d7865afb08f7ca1bd4a85"
		);
		
	  const mailjetTemplateId = 5607191; // Replace with your Mailjet template ID
  
	  // Validate the data received from the frontend
	  console.log(data)
	  const { nomecognome, email, telefono, orario, requestId } = data;
	  /*if (!nomecognome || !email || !telefono || !orario || !requestId || requestId !== mailjetTemplateId) {
		throw new functions.https.HttpsError('invalid-argument', 'Invalid data received from the frontend.');
	  }*/
  
	  // Send email using Mailjet
	  const result = await mailjet.post('send', { version: 'v3.1' }).request({
		Messages: [
		  {
			From: {
			  Email: '<EMAIL>',
			},
			To: [
			  {
				Email: '<EMAIL>',
			  },
			  {
				Email: '<EMAIL>',
			  },
			],
			TemplateID: mailjetTemplateId,
			TemplateLanguage: true,
			Subject: 'Richiesta post-valutazione per accesso programma insieme',
			Variables: {
				nomecognome: nomecognome, 
				email: email,
				telefono: telefono,
				orario: orario
			},
		  },
		],
	  });
  
	  // Check Mailjet API response
	  if (result.body.Messages[0].Status === 'success') {
		return { success: true, message: 'Email sent successfully' };
	  } else {
		throw new functions.https.HttpsError('internal', 'Failed to send email');
	  }
	} catch (error) {
	  console.error('Error:', error);
	  throw new functions.https.HttpsError('internal', 'Internal server error');
	}
  });
  

  const authorizedUserIds = ['adminUserId1', 'adminUserId2'];

  export const dummyMethod = functions.https.onCall(async (data, context) => {
	
  });

  
  export const createUser = functions.https.onCall(async (data, context) => {
	// Check if the request is authenticated
	if (!context.auth) {
	  throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
	}
  
	// Check if the authenticated user is authorized to perform this action
	//if (!authorizedUserIds.includes(context.auth.uid)) {
	//  throw new functions.https.HttpsError('permission-denied', 'You are not authorized to perform this action');
	//}
  
	// Extract email and password from the data payload
	const { email, password } = data;
  
	// Check if email and password are provided
	if (!email || !password) {
	  throw new functions.https.HttpsError('invalid-argument', 'Email and password are required');
	}
  
	try {
	  // Create user with email and password
	  const userRecord = await admin.auth().createUser({
		email: email,
		password: password
	  });

	  console.log(userRecord);
	  
	  // Return the UID of the newly created user
	  return { uid: userRecord.uid, message: 'User created successfully' };
	} catch (error) {
	  // Throw an HttpsError
	  throw new functions.https.HttpsError('internal', 'Error creating user: ' + error.message);
	}
  });