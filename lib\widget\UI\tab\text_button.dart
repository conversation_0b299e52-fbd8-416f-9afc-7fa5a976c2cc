import 'package:flutter/material.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class TextButtonWidget extends StatelessWidget {
  const TextButtonWidget({
    super.key,
    this.backgroundColor,
    this.borderRadius,
    this.onTap,
    this.isOnlyBorder,
    this.borderColor,
    this.status,
    this.textColor,
    this.textWeight,
    this.textSize,
    this.containerAlignment,
  });

  final String? status;
  final String? textWeight;
  final double? textSize;

  final bool? isOnlyBorder;

  final Color? backgroundColor, borderColor, textColor;
  final double? borderRadius;
  final VoidCallback? onTap;
  final AlignmentGeometry? containerAlignment;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(
          borderRadius ?? 8,
        ),
        child: Container(
          alignment: containerAlignment ?? null,
          decoration: BoxDecoration(
            color: backgroundColor ?? Colors.transparent,
            border: isOnlyBorder == true
                ? Border.all(
              width: 1,
              color: borderColor ?? AppColor.lightGreyColor,
            )
                : null,
            borderRadius: BorderRadius.circular(
              borderRadius ?? 8,
            ),
          ),
          padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
          // child: Text(
          //   status ?? "",
          //   style: TextStyle().text12w600.textColor(
          //     textColor ?? AppColor.black,
          //   ),
          // ),

          child: NarFormLabelWidget(
            label: status ?? "",
            fontSize: textSize ?? 12,
            fontWeight: textWeight ?? '600',
            textColor: textColor ?? AppColor.black,
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}
