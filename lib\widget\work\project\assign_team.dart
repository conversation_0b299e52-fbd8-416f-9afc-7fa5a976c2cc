import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/image-select-box.dart';

class ProjectAssignTeam extends StatefulWidget {
  final NewarcProject? project;
  final List<Map>? userList;
  final List<bool>? isInputChangeDetected;

  const ProjectAssignTeam({Key? key, this.project, this.userList, this.isInputChangeDetected})
      : super(key: key);

  @override
  State<ProjectAssignTeam> createState() => _ProjectAssignTeamState();
}

class _ProjectAssignTeamState extends State<ProjectAssignTeam> {
  bool loading = false;
  // TextEditingController? contScouter = new TextEditingController();
  // List<Map> dataList = []; 
  // Map userRoleIndex = {
  //   'scouter': 0,
  //   'renovator': 1,
  //   'renderist': 2,
  //   'geometra': 3,
  //   'media_creator': 4
  // };
  String progressMessage = '';

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    setInitialValues();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      for (var index = 0; index < widget.userList!.length; index++) {
        widget.userList![index]['controller'].addListener(_checkForChanges);  
      }
    });
    
  }

  void _checkForChanges() {

    if (mounted) { 
      Future.microtask(() {
        setState(() {
          widget.isInputChangeDetected![0] = true;  
        });
      });
    }
  }

  @protected
  void didUpdateWidget(ProjectAssignTeam oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    for (var index = 0; index < widget.userList!.length; index++) {
      widget.userList![index]['controller'].removeListener(_checkForChanges);
    }
    super.dispose();
  }

  setInitialValues() {


    // contScouter!.text = '';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      child: loading == true
          ? NarFormLabelWidget(label: 'Loading')
          : Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: ListView(
                    children: [
                      SizedBox(height: 20),
                      NarFormLabelWidget(
                        label: 'Componi il team',
                        fontSize: 20,
                        fontWeight: 'bold',
                      ),
                      SizedBox(height: 30),
                      Container(
                        // color: Colors.grey,
                        // width: 200,
                        child: ListView.builder(
                            shrinkWrap: true,
                            itemCount: widget.userList!.length,
                            itemBuilder: (context, index) {

                              if( widget.project!.type == 'Ristrutturazione' && widget.userList![index]['name'].toString().toLowerCase() == 'commerciale' ) {
                                return SizedBox(height: 0);
                              }

                              if (widget.project!.assignedTeam.length > 0) {
                                int teamindex = widget.project!.assignedTeam
                                    .indexWhere((element) =>
                                        element['type'] ==
                                        widget.userList![index]['name']
                                            .toString()
                                            .toLowerCase());

                                if (widget.project!.assignedTeam[teamindex]
                                        ['userId'] !=
                                    '') {
                                  widget.userList![index]['controller'].text =
                                      widget.project!.assignedTeam[teamindex]
                                          ['userId'];
                                }
                              }
                              return Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        
                                        children: [
                                          NarFormLabelWidget(
                                            label: widget.userList![index]['name']
                                                .toString()
                                                .toCapitalized(),
                                            textColor: Color(0xff696969),
                                            fontSize: 14,
                                            fontWeight: '600',
                                          ),
                                          SizedBox(height: 4),
                                          widget.userList![index]['subMenu'].length >
                                                  0
                                              ? NarImageSelectBoxWidget(
                                                  iconSize: 35,
                                                  buttonPadding: EdgeInsets.only(
                                                      top: 3,
                                                      bottom: 3,
                                                      left: 5,
                                                      right: 5),
                                                  options: widget.userList![index]
                                                      ['subMenu'],
                                                  parametersValidate: "Required!",
                                                  validationType: 'required',
                                                  controller: widget.userList![index]
                                                      ['controller'],
                                                )
                                              : NarFormLabelWidget(
                                                  label: 'No team member found.'),
                                          SizedBox(height: 20)
                                        ]),
                                  ),
                                  Expanded(child: SizedBox(height:0))
                                ],
                              );
                            }),
                      )
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    NarFormLabelWidget(label: progressMessage),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        BaseNewarcButton(
                          buttonText: "Salva",
                          onPressed: () async {
                            setState(() {
                              progressMessage = 'Salvataggio in corso...';
                            });

                            List<Map> teamData = [];

                            for (int i = 0; i < widget.userList!.length; i++) {
                              teamData.add({
                                'type': widget.userList![i]['name']
                                    .toString()
                                    .toLowerCase(),
                                'userId':
                                    widget.userList![i]['controller'] != null
                                        ? widget.userList![i]['controller'].text
                                        : ''
                              });
                            }

                            widget.project!.assignedTeam = teamData;

                            final FirebaseFirestore _db =
                                FirebaseFirestore.instance;

                            try {
                              await _db
                                  .collection(appConfig.COLLECT_NEWARC_PROJECTS)
                                  .doc(widget.project!.id)
                                  .update(widget.project!.toMap());

                              setState(() {
                                widget.isInputChangeDetected![0] = false;
                                progressMessage = 'Saved!';
                              });
                            } catch (e) {
                              setState(() {
                                progressMessage = 'Error';
                              });
                            }
                          },
                        )
                      ],
                    ),
                  ],
                ),
              ],
            ),
    );
  }
}
