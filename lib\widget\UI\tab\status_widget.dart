import 'package:flutter/material.dart';
import 'package:newarc_platform/pages/work/common_widget.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/tab/text_style.dart';

class StatusWidget extends StatelessWidget {
  const StatusWidget({super.key, this.status, this.statusColor, this.textStyle});

  final String? status;
  final Color? statusColor;
  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: 12,
          width: 12,
          decoration: BoxDecoration(
            color: statusColor,
            shape: BoxShape.circle,
          ),
        ),
        Flexible(
          child: Visibility(
            visible: (status ?? "").isNotEmpty,
            child: Padding(
              padding: const EdgeInsets.only(left: 8.0),
              // child: Text(
              //   status ?? "",
              //   style: TextStyle().text12w600.textColor(
              //         AppColor.black,
              //       ),
              // ),
              child: NarFormLabelWidget(
                label: status ?? "",
                fontSize: 12,
                fontWeight: '600',
                textColor: AppColor.black,
              ),
            ),
          ),
        )
      ],
    );
  }
}
