// import 'dart:ffi';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/pages/agency/home_agency.dart';
import 'package:newarc_platform/pages/agency/models/home_work.model.dart';
import 'package:newarc_platform/pages/home_scouter.dart';
import 'package:newarc_platform/pages/register_page.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/functions/various.dart';
import 'package:newarc_platform/widget/custom_icon_button.dart';
import 'package:firebase_auth/firebase_auth.dart';

class Page404 extends StatefulWidget {
  Page404({Key? key}) : super(key: key);

  static const String route = '/404';

  @override
  _Page404State createState() => _Page404State();
}

class _Page404State extends State<Page404> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: AppBar(),
      backgroundColor: Color.fromARGB(255, 0, 0, 0),
      body: Stack(
        children: [
          Positioned(
            top: 24,
            bottom: 200,
            left: 24,
            right: 24,
            child: Container(
              child: Image.asset('assets/logo.png'),
            ),
          ),
          Positioned(
            top: 150,
            bottom: 0,
            left: 24,
            right: 24,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children:  [
                Text(
                  '404',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontSize: 50,
                      letterSpacing: 2,
                      color: Theme.of(context).primaryColor,
                      // fontFamily: 'Anton',
                      fontWeight: FontWeight.bold),
                ),
                 Text(
                  'Ops!\nNon abbiamo trovato\nla pagina richiesta\n😞',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 30,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
