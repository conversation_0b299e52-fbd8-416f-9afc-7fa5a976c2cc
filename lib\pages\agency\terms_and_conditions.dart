import 'dart:html';

import 'package:flutter/material.dart';

class AgencyTermsAndConditions extends StatefulWidget {
  AgencyTermsAndConditions({Key? key}) : super(key: key);

  static const String route = '/terms-condition';

  @override
  _AgencyTermsAndConditionsState createState() =>
      _AgencyTermsAndConditionsState();
}

class _AgencyTermsAndConditionsState extends State<AgencyTermsAndConditions> {
  late List<Map<String, dynamic>> pageElements;
  bool loading = true;

  @override
  void initState() {
    // align: possible values [left, center, right]
    // type: possible values [text, heading, sub-heading, ordered-list, unordered-list]
    pageElements = [
      {
        'type': 'heading',
        'text': 'Regolamento "Punti Newarc"\nper le Agenzie',
        'align': "center"
      },
      {"type": "sub-heading", "text": "Panoramica", 'align': "left"},
      {
        "type": "paragraph",
        "text":
            "Il ”Sistema a punti” consente all’AGENZIA di ottenere da NEWARC particolari vantaggi al raggiungimento di un certo punteggio rappresentato dai c.d. NEWARC Points che vengono assegnati a seguito di determinate attività svolte dalle parti."
      },
      {
        "type": "sub-heading",
        "text": "Guadagnare punti Newarc",
        'align': "left"
      },
      {
        "type": "paragraph",
        'align': "left",
        "text":
            " Le attività che comportano l’assegnazione di NEWARC Points all’AGENZIA sono:"
      },
      {
        "type": "ordered-list",
        "list-items": [
          "La segnalazione e l’acquisto da parte di NEWARC dell’immobile in vendita tramite l’AGENZIA (40 punti).",
          "La rivendita di un immobile NEWARC, qualora questa abbia conferito l’incarico di mediazione all’AGENZIA, nei tempi e ai prezzi inizialmente concordati (40 punti);",
          "Il caricamento dei dati relativi agli immobili richiesti della Sezione Prezzi del Venduto (1 punto per ogni immobile caricato fino ad un massimo di 10 punti al mese)."
        ]
      },
      {"type": "sub-heading", "text": "Vantaggi", 'align': "left"},
      {
        "type": "paragraph",
        "align": "left",
        "text":
            "Al raggiungimento di un nuovo livello, NEWARC riconoscerà all’AGENZIA la preferenza rispetto ad altre agenzie nell’invio dei Contatti. Al raggiungimento di un nuovo livello, inoltre, verrà riconosciuto da NEWARC all’AGENZIA un bonus di € 500,00 che andrà a sommarsi alla provvigione che NEWARC paga all’AGENZIA in caso di vendita di un immobile NEWARC. Ogni 5 livelli il bonus che NEWARC paga all’AGENZIA in caso di vendita di un immobile NEWARC sale ad € 1.000,00. I bonus sono cumulabili fino ad un massimo di € 1.500,00. I bonus vengono pagati esclusivamente insieme alla provvigione di rivendita degli immobili NEWARC."
      },
      {"type": "sub-heading", "text": "Termini e condizioni", 'align': "left"},
      {
        "type": "paragraph",
        "align": "left",
        "text":
            "NEWARC si riserva la possibilità di modificare il sistema a punti, aggiungere e sottrarre punti a propria discrezione anche senza alcun preavviso."
      },
      /*{
        "type": "unordered-list",
        "icon": Icon(
          Icons.check,
          size: 15,
        ),
        "list-items": [
          "Lorem Ipsum has been the industry's standard dummy text",
          "Lorem Ipsum has been the industry's standard dummy text",
          "Lorem Ipsum has been the industry's standard dummy text"
        ]
      },
      {
        "type": "sub-heading",
        "text": "Wait and read this section",
        'align': "left"
      },
      {
        "type": "ordered-list",
        "list-items": [
          "Lorem Ipsum has been the industry's standard dummy text",
          "Lorem Ipsum has been the industry's standard dummy text",
          "Lorem Ipsum has been the industry's standard dummy text"
        ]
      },*/
    ];
    buildHtml();
    // TODO: implement initState
    super.initState();
  }

  List<Widget> htmlElements = [];
  buildHtml() {
    TextStyle heading = TextStyle(
      fontSize: 30,
      fontWeight: FontWeight.w900,
      color: Color(0xff489B79),
    );
    TextStyle sub_heading = TextStyle(
      fontSize: 20,
      fontWeight: FontWeight.bold,
    );
    TextStyle plainText = TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.bold,
    );

    for (var element in pageElements) {
      MainAxisAlignment textAlignment = MainAxisAlignment.start;
      if (element["align"] != null) {
        if (element["align"] == "center") {
          textAlignment = MainAxisAlignment.center;
        } else if (element["align"] == "right") {
          textAlignment = MainAxisAlignment.end;
        }
      }

      if (element["type"] == "paragraph") {
        var textAlign = TextAlign.left;
        if (element['align'] != null) {
          if (element['align'] == "center") {
            textAlign = TextAlign.center;
          } else if (element['align'] == "right") {
            textAlign = TextAlign.right;
          }
        }
        htmlElements.add(Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Text(
            element["text"],
            textAlign: textAlign,
            // softWrap: true,

            // textAlign: element["align"] ?? TextAlign.left,
            style: plainText,
          ),
        ));
      } else if (element["type"] == "heading") {
        htmlElements.add(Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: textAlignment,
            children: [
              Expanded(
                child: Text(
                  element["text"],
                  // textAlign: element["align"] ?? TextAlign.left,
                  style: heading,
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ));
      } else if (element["type"] == "sub-heading") {
        htmlElements.add(Padding(
          padding: const EdgeInsets.only(top: 20, bottom: 5),
          child: Row(
            mainAxisAlignment: textAlignment,
            children: [
              Text(
                element["text"],
                // textAlign: element["align"] ?? TextAlign.left,
                style: sub_heading,
              ),
            ],
          ),
        ));
      } else if (element["type"] == "ordered-list") {
        var ol = Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: ListView.builder(
              itemCount: element["list-items"].length,
              scrollDirection: Axis.vertical,
              shrinkWrap: true,
              itemBuilder: (BuildContext context, int index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 5),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        (index + 1).toString() + '.',
                        style: plainText,
                      ),
                      SizedBox(
                        width: 10,
                      ),
                      Container(
                          // leading: const Icon(Icons.list),
                          child: Expanded(
                        child: Text(
                          element["list-items"][index],
                          style: plainText,
                        ),
                      )),
                    ],
                  ),
                );
              }),
        );

        htmlElements.add(ol);
      } else if (element["type"] == "unordered-list") {
        var ul = Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: ListView.builder(
              itemCount: element["list-items"].length,
              scrollDirection: Axis.vertical,
              shrinkWrap: true,
              itemBuilder: (BuildContext context, int index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 5),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      element["icon"],
                      SizedBox(
                        width: 10,
                      ),
                      Container(
                          // leading: const Icon(Icons.list),
                          child: Text(
                        element["list-items"][index],
                        style: plainText,
                      )),
                    ],
                  ),
                );
              }),
        );

        htmlElements.add(ul);
      }
    }

    // htmlElements = pageElements.map((key, element) {
    //   print(element);
    //   if (element["type"] == "paragraph") {
    //     // paragraph = Text(element.text);
    //     return Text("Hello");
    //   }
    //   return Text("");
    // });

    // for (var i = 0; i < pageElements.length; i++) {
    //   // var element = pageElements[i];
    //   // print(element);
    //   // if (element.type == "paragraph") {
    //   //   Text paragraph = Text(element["text"]);
    //   //   htmlElements.add(paragraph);
    //   // }
    // }

    // pageElements.map((key, element) {
    //   if (element.type == "paragraph") {
    //     Text paragraph = Text("Test");
    //     htmlElements.add(paragraph);
    //   }
    // });

    setState(() {
      loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return loading
        ? Text('Loading')
        : SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: htmlElements,
            ),
          );
  }
}
