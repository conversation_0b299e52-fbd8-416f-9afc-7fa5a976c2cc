import 'package:flutter/material.dart';

/// *  textInputType - The type of information for which to optimize the text input control.
/// *  hintText - Text that suggests what sort of input the field accepts.
/// *  prefixIcon - Pass Icon if required
/// *  defaultText - If there is predefined value is there for a text field
/// *  focusNode - Currently focus node
/// *  obscureText - Is Password field?
/// *  controller - Text controller
/// *  functionValidate - Validation function(currently I have used to check empty field)
/// *  parametersValidate - Value to validate
/// *  actionKeyboard - Keyboard action eg. next, done, search, etc
/// *  onSubmitField - Done click action
/// *  onFieldTap - On focus on TextField
class NarH1Widget extends StatefulWidget {
  final String? label;

  const NarH1Widget({required this.label});

  @override
  _NarH1WidgetState createState() => _NarH1WidgetState();
}

class _NarH1WidgetState extends State<NarH1Widget> {
  double bottomPaddingToError = 12;

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        primaryColor: Theme.of(context).colorScheme.primary,
      ),
      child: Text(
        widget.label!,
        textAlign: TextAlign.start,
        style: TextStyle(
            color: Color.fromRGBO(0, 0, 0, 1),
            //fontFamily: 'Visby900',
            fontSize: 22,
            letterSpacing: 1,
            height: 2),
      ),
    );
  }
}
