import 'package:flutter/material.dart';
import 'package:newarc_platform/pages/models/home_scouter.model.dart';
import 'package:newarc_platform/widget/custom_icon_button.dart';
import 'package:provider/provider.dart';

class SearchPopup extends StatefulWidget {
  SearchPopup({Key? key}) : super(key: key);

  @override
  _SearchPopupState createState() => _SearchPopupState();
}

class _SearchPopupState extends State<SearchPopup> {
  double borderRadius = 10;
  TextStyle menuItemStyle = TextStyle(color: Colors.white, fontSize: 18);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 425,
      height: 133,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
              color: Colors.black.withOpacity(0.4),
              blurRadius: 20,
              spreadRadius: 10),
        ],
      ),
      padding: EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(width: 20),
              Text(
                "Ricerca un immobile",
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              IconButton(
                  onPressed: () {
                    Provider.of<HomeScouterModel>(context, listen: false)
                        .searchPopupShow();
                  },
                  icon: Icon(Icons.close))
            ],
          ),
          Row(
            children: [
              Flexible(
                child: TextFormField(
                  decoration: InputDecoration(
                    fillColor: Colors.white,
                    filled: true,
                    labelText: "Inserisci un indirizzo",
                    labelStyle: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 15,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 10),
              CustomIconButton(
                label: "Cerca",
                icon: Container(),
                width: 149,
                height: 56,
                textStyle: TextStyle(color: Colors.white),
                color: Theme.of(context).primaryColor,
                function: () => {},
              )
            ],
          ),
        ],
      ),
    );
  }
}
