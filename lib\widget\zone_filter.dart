import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/comparabile.dart';
import 'package:newarc_platform/pages/models/home_scouter.model.dart';
import 'package:newarc_platform/widget/custom_icon_button.dart';
import 'package:provider/provider.dart';

class ZoneFilter extends StatefulWidget {
  List<Comparabile> comparabili;

  ZoneFilter({Key? key, required this.comparabili}) : super(key: key);

  @override
  _ZoneFilterState createState() => _ZoneFilterState();
}

class _ZoneFilterState extends State<ZoneFilter> {
  double borderRadius = 10;
  TextStyle menuItemStyle = TextStyle(color: Colors.white, fontSize: 18);
  int? _selectedZoneIndex;
  TextStyle selectedStyle = TextStyle(fontWeight: FontWeight.w600);
  TextStyle normalStyle = TextStyle(fontWeight: FontWeight.w400);

  List<String> zones = [];

  @override
  void initState() {
    Map<String, List<Comparabile>> groupedZoneMap =
        groupBy(widget.comparabili, (c) => c.zonaOmi!);
    zones = groupedZoneMap.keys.toList();
    int index = zones.indexOf(
        Provider.of<HomeScouterModel>(context, listen: false).selectedZone);
    if (index != -1) {
      _selectedZoneIndex = index;
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 425,
      height: 445,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.4),
            blurRadius: 20,
            spreadRadius: 10,
          ),
        ],
      ),
      padding: EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(width: 30),
              Text(
                "Seleziona una zona",
                style: Theme.of(context)
                    .textTheme
                    .headlineSmall!
                    .copyWith(color: Theme.of(context).primaryColor),
              ),
              IconButton(
                onPressed: () {
                  Provider.of<HomeScouterModel>(context, listen: false)
                      .showZoneFilter();
                },
                icon: Icon(Icons.close),
              )
            ],
          ),
          SizedBox(width: 10),
          Expanded(
            child: ListView.separated(
              itemCount: zones.length,
              separatorBuilder: (context, index) {
                return Container(
                  height: 1,
                  margin: EdgeInsets.symmetric(vertical: 10, horizontal: 50),
                  color: Colors.grey.shade300,
                );
              },
              itemBuilder: (context, index) {
                return TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedZoneIndex = index;
                    });
                  },
                  child: Text(
                    zones.elementAt(index),
                    textAlign: TextAlign.center,
                    style: _isSelected(_selectedZoneIndex, index)
                        ? selectedStyle
                        : normalStyle,
                  ),
                );
              },
            ),
          ),
          SizedBox(height: 20),
          CustomIconButton(
              label: "Conferma",
              icon: Container(),
              width: 149,
              height: 56,
              textStyle: TextStyle(color: Colors.white),
              color: Theme.of(context).primaryColor,
              function: _setSelectedZone),
          SizedBox(height: 20)
        ],
      ),
    );
  }

  bool _isSelected(int? selectedIndex, int currentIndex) {
    return selectedIndex != null && selectedIndex == currentIndex;
  }

  void _setSelectedZone(BuildContext ctx) {
    if (_selectedZoneIndex != null) {
      Provider.of<HomeScouterModel>(context, listen: false)
          .updateSelectedZone(zones.elementAt(_selectedZoneIndex!));
      Provider.of<HomeScouterModel>(context, listen: false).showZoneFilter();
    }
  }
}
