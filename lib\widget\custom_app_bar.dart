import 'package:flutter/material.dart';
import 'package:newarc_platform/pages/models/home_scouter.model.dart';
import 'package:provider/provider.dart';

class CustomAppBar extends StatefulWidget {
  CustomAppBar({Key? key, required this.responsive}) : super(key: key);

  bool responsive;

  @override
  _CustomAppBarState createState() => _CustomAppBarState();
}

class _CustomAppBarState extends State<CustomAppBar> {
  double borderRadius = 10;
  TextStyle menuItemStyle = TextStyle(color: Colors.white, fontSize: 18);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 57,
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 15),
      child: Row(
        children: [
          widget.responsive
              ? IconButton(
                  onPressed: () {
                    Provider.of<HomeScouterModel>(context, listen: false)
                        .drawerStateChange();
                  },
                  icon: Icon(Icons.menu),
                )
              : Container(),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  onPressed: () {},
                  icon: Icon(
                    Icons.settings,
                    color: Color(0xff6C6C6C),
                  ),
                ),
                SizedBox(width: 10),
                IconButton(
                  onPressed: () {},
                  icon: Icon(
                    Icons.notifications,
                    color: Color(0xff6C6C6C),
                  ),
                ),
                SizedBox(width: 15),
                CircleAvatar(
                  foregroundImage: NetworkImage(
                      'https://fs.xcity.jp/imgsrc/image/person/thumb_1439432239.jpg?width=200&height=200'),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
