import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/work/project/manage_jobs.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'dart:html' as html;
import 'dart:typed_data';
import 'package:newarc_platform/classes/newarcProject.dart';


class NewarcGantt extends StatefulWidget {
  final String? name;
  final dynamic rows;
  final bool showCurrentDate;
  final Map<int, String> monthNames;
  final double columnWidth;
  final double labelColumnWidth;
  final double barHeight;
  final bool showCompleteTimeline;

  const NewarcGantt({
    Key? key,
    this.name,
    required this.rows,
    this.showCurrentDate = false,
    required this.monthNames,
    this.columnWidth = 375,
    this.labelColumnWidth = 500,
    this.barHeight = 25,
    this.showCompleteTimeline = true,
  }) : super(key: key);

  @override
  _NewarcGanttState createState() => _NewarcGanttState();
}

class _NewarcGanttState extends State<NewarcGantt> {
  late DateTime _startDate;
  late DateTime _endDate;
  late List<DateTime> _months;
  late List<DateTime> _weeks;
  final ScrollController _horizontalController = ScrollController();

  late double _rowHeight;
  late double fullLabelColWidth;
  late double labelContainersPadding;
  late double firstLabelContainerWidth;
  late double secondLabelContainerWidth;
  late double thirdLabelContainerWidth;
  late double fourthLabelContainerWidth;

  late bool groupedRows;

  _initVariables(){
    _rowHeight = widget.barHeight + 20;
    labelContainersPadding = 5;
    fullLabelColWidth = widget.labelColumnWidth - 4*labelContainersPadding - 5;
    firstLabelContainerWidth = fullLabelColWidth * 0.45;
    secondLabelContainerWidth = fullLabelColWidth * 0.2;
    thirdLabelContainerWidth = fullLabelColWidth * 0.2;
    fourthLabelContainerWidth = fullLabelColWidth * 0.15;
    groupedRows = widget.rows is Map;
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _removeTooltip(); // Rimuovi il tooltip quando il widget viene distrutto
    super.dispose();
  }

  @override
  void initState() {
    _initVariables();
    super.initState();
    _calculateDateRange();
  }

  void _calculateDateRange() {
    // Trova la data di inizio e fine del progetto
    DateTime earliest = DateTime.now();
    DateTime latest = DateTime.now();
    List rows = [];
    if (groupedRows) {
      widget.rows.forEach((key, value) {
        rows.add(value);
      });
    } else {
      rows = widget.rows;
    }

    if (!groupedRows) {
      if (rows.isNotEmpty) {
        for (var row in rows) {
          int startInt = int.tryParse(row.start) ?? 0;
          int endInt = int.tryParse(row.end) ?? 0;

          if (startInt > 0) {
            DateTime startDate = DateTime.fromMillisecondsSinceEpoch(startInt);
            if (startDate.isBefore(earliest)) {
              earliest = startDate;
            }
          }

          if (endInt > 0) {
            DateTime endDate = DateTime.fromMillisecondsSinceEpoch(endInt);
            if (endDate.isAfter(latest)) {
              latest = endDate;
            }
          }
        }
      }
    } else {
      earliest = DateTime.now();
      latest = DateTime(earliest.year, earliest.month + 12, 0);
    }

    // Arrotonda al primo giorno del mese per la data di inizio
    _startDate = DateTime(earliest.year, earliest.month, 1);

    // Arrotonda all'ultimo giorno del mese per la data di fine
    _endDate = DateTime(latest.year, latest.month + 1, 0);

    // Genera la lista dei mesi da visualizzare
    _months = [];
    DateTime current = _startDate;
    while (current.isBefore(_endDate) || current.month == _endDate.month && current.year == _endDate.year) {
      _months.add(DateTime(current.year, current.month, 1));
      current = DateTime(current.year, current.month + 1, 1);
    }

    // Genera la lista delle settimane
    _weeks = [];
    DateTime weekStart = _startDate;
    // Trova il primo lunedì dalla data di inizio
    while (weekStart.weekday != DateTime.monday) {
      weekStart = weekStart.add(Duration(days: 1));
    }

    DateTime currentWeek = weekStart;
    while (currentWeek.isBefore(_endDate)) {
      _weeks.add(currentWeek);
      currentWeek = currentWeek.add(Duration(days: 7));
    }
  }

  String _getFormattedDate(int timestamp) {
    DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return "${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}";
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              child: Center(
                child: NarFormLabelWidget(
                  label: groupedRows 
                    ? 'Disponibilità Ditte ${widget.name != null ? "- ${widget.name}" : ""}' 
                    : '${widget.name != null ? widget.name! + " - " : ""}Cronoprogramma',
                  fontSize: 20,
                  fontWeight: '700',
                  textColor: Colors.black,
                ),
              ),
            ),
            if (!groupedRows)
            Container(
              height: 25,
              width: 25,
              padding: EdgeInsets.all(5),
              decoration: ShapeDecoration(
                shape: CircleBorder(
                  // side: BorderSide(
                  //   width: 1,
                  //   color: Colors.grey,
                  // ),
                ),
                color: Color(0xffE2E2E2),
                // color: Colors.black,
              ),
              child: MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                    child: Image.asset(
                      'assets/icons/download.png',
                      width: 20,
                      color: Color(0xff757575),
                    ),
                    onTap: () {
                      pdfDownloadGantt();
                    }),
              ),
            ),
            if (!groupedRows)
            SizedBox(width: 15,),
            Container(
              height: 15,
              width: 15,
              child: MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                    child: SvgPicture.asset(
                      'assets/icons/close-popup.svg',
                      width: 15,
                    ),
                    onTap: () {
                      Navigator.pop(context);
                    }),
              ),
            )
          ],
        ),
        SizedBox(height: 15,),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: Color(0xffE2E2E2),
                  width: 1.5,
                  )
            ),
            child: (!groupedRows &&widget.rows.isNotEmpty) || (groupedRows && widget.rows.entries.isNotEmpty)
            ? Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    controller: _horizontalController,
                    child: Column(
                      children: [
                        // Header con i mesi
                        _buildMonthsHeader(),
                        // Contenuto del Gantt
                        Expanded(
                          child: SingleChildScrollView(
                            scrollDirection: Axis.vertical,
                            child: !groupedRows
                            ? Column(
                              children: [
                                // Riga completa del cantiere (mostrata solo se showCompleteTimeline è true)
                                if (widget.showCompleteTimeline && widget.rows.isNotEmpty)
                                  _buildCompleteCantiereLine(),
                                // Righe normali del Gantt
                                ...widget.rows.map((row) => _buildGanttRow(row)).toList(),
                              ],
                            )
                            : Column(
                              children: [
                                ...widget.rows.entries.map((entry) => _buildNestedGanttRow(entry.key, entry.value)).toList(),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            )
            : Center(
              child: NarFormLabelWidget(
                label: "Nessuna lavorazione selezionata",
                fontSize: 15,
                fontWeight: '600',
                textColor: Color(0xff696969),
              )
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMonthsHeader() {
    return Container(
      height: _rowHeight + 10,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Colonna delle etichette
          Container(
            width: widget.labelColumnWidth,
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(color: Color(0xffE2E2E2), width:2.5),
                bottom: BorderSide(color: Color(0xffE2E2E2), width:2.5),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: !groupedRows ? firstLabelContainerWidth : fullLabelColWidth,
                  padding: EdgeInsets.all(labelContainersPadding),
                  alignment: Alignment.centerLeft,
                  child: NarFormLabelWidget(
                    label: groupedRows ? "Ditta" : "Lavorazione",
                    fontSize: 12,
                    fontWeight: "700",
                    textColor: Colors.black,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.left,
                  ),
                ),
                if(!groupedRows)
                Container(
                  width: secondLabelContainerWidth,
                  padding: EdgeInsets.all(labelContainersPadding),
                  alignment: Alignment.centerLeft,
                  child: NarFormLabelWidget(
                    label: "Inizio ip.",
                    fontSize: 12,
                    fontWeight: "700",
                    textColor: Colors.black,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.left,
                  ),
                ),
                if(!groupedRows)
                Container(
                  width: thirdLabelContainerWidth,
                  padding: EdgeInsets.all(labelContainersPadding),
                  alignment: Alignment.centerLeft,
                  child: NarFormLabelWidget(
                    label: "Fine ip.",
                    fontSize: 12,
                    fontWeight: "700",
                    textColor: Colors.black,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.left,
                  ),
                ),
                if(!groupedRows)
                Container(
                  width: fourthLabelContainerWidth,
                  padding: EdgeInsets.all(labelContainersPadding),
                  alignment: Alignment.centerLeft,
                  child: NarFormLabelWidget(
                    label: "Durata ip.",
                    fontSize: 12,
                    fontWeight: "700",
                    textColor: Colors.black,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.left,
                  ),
                ),
              ]
            ),
          ),
          // Colonne dei mesi
          Row(
            mainAxisSize: MainAxisSize.min,
            children: _months.map((month) {
              String monthName = (widget.monthNames[month.month]?.substring(0, 1).toUpperCase() ?? "") +
                                (widget.monthNames[month.month]?.substring(1) ?? "");
              return Container(
                width: widget.columnWidth,
                // padding: EdgeInsets.symmetric(vertical: 15),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    right: BorderSide(color: Color(0xffECECEC), width:1),
                    bottom: BorderSide(color: Color(0xffE2E2E2), width:2.5),
                  ),
                ),
                alignment: Alignment.center,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    NarFormLabelWidget(
                      label: month.year.toString(),
                      fontSize: 10,
                      fontWeight: "700",
                      textColor: Colors.blueGrey,
                    ),
                    NarFormLabelWidget(
                      label: monthName,
                      fontSize: 14,
                      fontWeight: "700",
                      textColor: Colors.black,
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildGanttRow(JobEventItem item) {
    int startInt = int.tryParse(item.start) ?? 0;
    int endInt = int.tryParse(item.end) ?? 0;

    String startLabel = (item.start != "Inizio ip." && item.start.isNotEmpty && item.start != "first date")
        ? _getFormattedDate(startInt == 0 ? DateTime.now().millisecondsSinceEpoch : startInt)
        : item.start;

    String endLabel = (item.end != "Fine ip." && item.end.isNotEmpty && item.end != "last date")
        ? _getFormattedDate(endInt == 0 ? DateTime.now().millisecondsSinceEpoch : endInt)
        : (int.tryParse(item.start) != null ? _getFormattedDate(int.parse(item.start)) : item.end);

    return Container(
      height: _rowHeight, // Altezza della barra + padding
      child: Row(
        mainAxisSize: MainAxisSize.min, // Set to min to avoid unbounded width issues
        children: [
          // Colonna delle etichette
          Container(
            width: widget.labelColumnWidth,
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(color: Color(0xffE2E2E2), width:2.5),
                bottom: BorderSide(color: Color(0xffECECEC), width:1),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: firstLabelContainerWidth,
                  padding: EdgeInsets.all(labelContainersPadding),
                  alignment: Alignment.centerLeft,
                  child: NarFormLabelWidget(
                    label: item.title,
                    fontSize: 11,
                    fontWeight: '500',
                    textColor: Colors.black,
                    textAlign: TextAlign.left,
                  ),
                ),
                Container(
                  width: secondLabelContainerWidth,
                  padding: EdgeInsets.all(labelContainersPadding),
                  alignment: Alignment.centerLeft,
                  child: NarFormLabelWidget(
                    label: startLabel,
                    fontSize: 11,
                    fontWeight: '500',
                    textColor: Colors.black,
                    textAlign: TextAlign.left,
                  ),
                ),
                Container(
                  width: thirdLabelContainerWidth,
                  padding: EdgeInsets.all(labelContainersPadding),
                  alignment: Alignment.centerLeft,
                  child: NarFormLabelWidget(
                    label: endLabel,
                    fontSize: 11,
                    fontWeight: '500',
                    textColor: Colors.black,
                    textAlign: TextAlign.left,
                  ),
                ),
                Container(
                  width: fourthLabelContainerWidth,
                  padding: EdgeInsets.all(labelContainersPadding),
                  alignment: Alignment.centerLeft,
                  child: NarFormLabelWidget(
                    label: "${item.duration.isEmpty ? "No Duration" : "${item.duration} giorni"}",
                    fontSize: 11,
                    fontWeight: '500',
                    textColor: Colors.black,
                    textAlign: TextAlign.left,
                  ),
                ),
              ],
            ),
          ),
          // Barra del Gantt - Replace Expanded with Container of fixed width
          Container(
            width: _months.length * widget.columnWidth,
            child: Stack(
              children: [
                // Griglia di sfondo
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: _months.map((month) {
                    return Container(
                      width: widget.columnWidth,
                      decoration: BoxDecoration(
                        border: Border(
                        right: BorderSide(color: Color(0xffECECEC), width:1),
                        bottom: BorderSide(color: Color(0xffECECEC), width:1),
                      ),
                      ),
                    );
                  }).toList(),
                ),

                // Linee verticali per le settimane
                ..._weeks.map((week) {
                  return Positioned(
                    left: _calculateWeekPosition(week, widget.columnWidth),
                    top: 0,
                    bottom: 0,
                    child: Container(
                      width: 1,
                      color: Color(0xffF1F1F1),
                    ),
                  );
                }).toList(),

                // Linea verticale per la data corrente
                if (widget.showCurrentDate)
                  Positioned(
                    left: _calculateCurrentDatePosition(widget.columnWidth),
                    top: 0,
                    bottom: 0,
                    child: Container(
                      width: 2,
                      color: Colors.red,
                    ),
                  ),

                // Barra dell'attività
                if (startInt > 0 && endInt > 0)
                  Positioned(
                    left: _calculateBarPosition(startInt, widget.columnWidth),
                    top: 10,
                    child: GestureDetector(
                      onTapUp: (TapUpDetails details) => _showTaskDetails(context, item, startLabel, endLabel, details.globalPosition),
                      child: Container(
                        width: _calculateBarWidth(startInt, endInt, widget.columnWidth),
                        height: widget.barHeight,
                        decoration: BoxDecoration(
                          color: Color(0xffAAD1C1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

   Widget _buildNestedGanttRow(rowKey, itemList) {
    if (itemList.isEmpty) {
      itemList.add(JobEventItem(
        project: null,
        title: "Nessuna lavorazione selezionata",
        duration: "0",
        start: "0",
        end: "0",
      ));
    } else {
      itemList.sort((a, b) => int.parse(a.start).compareTo(int.parse(b.start)));
      // drop rows that are not in viewed timespan
      itemList.removeWhere((item) 
        => int.parse(item.start) > _endDate.millisecondsSinceEpoch || int.parse(item.end) < _startDate.millisecondsSinceEpoch
      );
      if (itemList.isEmpty) {
        itemList.add(JobEventItem(
        project: null,
        title: "Nessuna lavorazione selezionata",
        duration: "0",
        start: "0",
        end: "0",
      ));
      }
    }
    return Container(
      height: _rowHeight*(itemList.length) + 1,
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xffECECEC), width: 1),
        ),
      ),
      child: Column(
        children: [
          ... itemList.map((item) {
          int startInt = int.tryParse(item.start) ?? 0;
          int endInt = int.tryParse(item.end) ?? 0;
      
          String startLabel = (item.start != "Inizio ip." && item.start.isNotEmpty && item.start != "first date")
              ? _getFormattedDate(startInt == 0 ? DateTime.now().millisecondsSinceEpoch : startInt)
              : item.start;
      
          String endLabel = (item.end != "Fine ip." && item.end.isNotEmpty && item.end != "last date")
              ? _getFormattedDate(endInt == 0 ? DateTime.now().millisecondsSinceEpoch : endInt)
              : (int.tryParse(item.start) != null ? _getFormattedDate(int.parse(item.start)) : item.end);
      
          return Container(
            height: _rowHeight, // Altezza della barra + padding
              child: Row(
                mainAxisSize: MainAxisSize.min, // Set to min to avoid unbounded width issues
                children: [
                  // Colonna delle etichette
                  Container(
                    width: widget.labelColumnWidth,
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      border: Border(
                        right: BorderSide(color: Color(0xffE2E2E2), width:2.5),
                      ),
                    ),
                    child: itemList.indexOf(item) == 0 ? Container(
                      width: widget.labelColumnWidth,
                      padding: EdgeInsets.all(labelContainersPadding),
                      alignment: Alignment.centerLeft,
                      child: NarFormLabelWidget(
                        label: rowKey,
                        fontSize: 11,
                        fontWeight: '500',
                        textColor: Colors.black,
                        textAlign: TextAlign.left,
                      ),
                    )
                    : Container(),
                  ),
                  // Barra del Gantt - Replace Expanded with Container of fixed width
                  Container(
                    width: _months.length * widget.columnWidth,
                    child: Stack(
                      children: [
                        // Griglia di sfondo
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: _months.map((month) {
                            return Container(
                              width: widget.columnWidth,
                              decoration: BoxDecoration(
                                border: Border(
                                right: BorderSide(color: Color(0xffECECEC), width:1),
                                // bottom: BorderSide(color: Color(0xffECECEC), width:1),
                              ),
                              ),
                            );
                          }).toList(),
                        ),
      
                        // Linee verticali per le settimane
                        ..._weeks.map((week) {
                          return Positioned(
                            left: _calculateWeekPosition(week, widget.columnWidth),
                            top: 0,
                            bottom: 0,
                            child: Container(
                              width: 1,
                              color: Color(0xffF1F1F1),
                            ),
                          );
                        }).toList(),
      
                        // Linea verticale per la data corrente
                        if (widget.showCurrentDate)
                          Positioned(
                            left: _calculateCurrentDatePosition(widget.columnWidth),
                            top: 0,
                            bottom: 0,
                            child: Container(
                              width: 2,
                              color: Colors.red,
                            ),
                          ),
      
                        // Barra dell'attività
                        if (startInt > 0 && endInt > 0)
                          Positioned(
                            left: _calculateBarPosition(startInt, widget.columnWidth),
                            top: 10,
                            child: GestureDetector(
                              onTapUp: (TapUpDetails details) => _showTaskDetails(context, item, startLabel, endLabel, details.globalPosition),
                              child: Container(
                                width: _calculateBarWidth(startInt, endInt, widget.columnWidth),
                                height: widget.barHeight,
                                decoration: BoxDecoration(
                                  color: Color(0xffF39999),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                alignment: Alignment.center,
                                child: NarFormLabelWidget(
                                  label: item.project ?? "",
                                  fontSize: 10,
                                  fontWeight: '500',
                                  textColor: Colors.white,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            );
        }).toList()
        ],
      ),
    );
  }

  double _calculateBarPosition(int startTimestamp, columnWidth) {
    DateTime startDate = DateTime.fromMillisecondsSinceEpoch(startTimestamp);

    // Calcola quanti mesi ci sono tra la data di inizio del progetto e la data di inizio dell'attività
    int monthsDiff = (startDate.year - _startDate.year) * 12 + startDate.month - _startDate.month;

    // Calcola la posizione proporzionale all'interno del mese
    double dayRatio = (startDate.day - 1) / DateTime(startDate.year, startDate.month + 1, 0).day;

    return monthsDiff * columnWidth + dayRatio * columnWidth;
  }

  double _calculateBarWidth(int startTimestamp, int endTimestamp, double columnWidth) {
    DateTime startDate = DateTime.fromMillisecondsSinceEpoch(startTimestamp);
    DateTime endDate = DateTime.fromMillisecondsSinceEpoch(endTimestamp);

    // Calcola la differenza in giorni
    int daysDiff = endDate.difference(startDate).inDays + 1;

    // Calcola la larghezza in base al numero di giorni
    double totalMonthsWidth = _months.length * columnWidth;
    int totalDays = _endDate.difference(_startDate).inDays;

    return (daysDiff / totalDays) * totalMonthsWidth;
  }

  double _calculateWeekPosition(DateTime week, double columnWidth) {
    // Calcola quanti mesi ci sono tra la data di inizio del progetto e la settimana
    int monthsDiff = (week.year - _startDate.year) * 12 + week.month - _startDate.month;

    // Calcola la posizione proporzionale all'interno del mese
    double dayRatio = (week.day - 1) / DateTime(week.year, week.month + 1, 0).day;

    return monthsDiff * columnWidth + dayRatio * columnWidth;
  }

  double _calculateCurrentDatePosition(double columnWidth) {
    DateTime now = DateTime.now();

    // Se la data corrente è fuori dall'intervallo del grafico, non mostrare la linea
    if (now.isBefore(_startDate) || now.isAfter(_endDate)) {
      return -100; // Posizione fuori dal grafico
    }

    // Calcola quanti mesi ci sono tra la data di inizio del progetto e oggi
    int monthsDiff = (now.year - _startDate.year) * 12 + now.month - _startDate.month;

    // Calcola la posizione proporzionale all'interno del mese
    double dayRatio = (now.day - 1) / DateTime(now.year, now.month + 1, 0).day;

    return monthsDiff * columnWidth + dayRatio * columnWidth;
  }

  // Overlay entry per il tooltip
  OverlayEntry? _overlayEntry;

  // Mostra i dettagli della lavorazione quando si clicca sulla barra
  void _showTaskDetails(BuildContext context, JobEventItem item, String startLabel, String endLabel, Offset tapPosition) {
    // Rimuovi eventuali tooltip già visibili
    _removeTooltip();

    // Crea un overlay entry per il tooltip
    _overlayEntry = OverlayEntry(
      builder: (BuildContext context) {
        return Stack(
          children: [
            // Layer invisibile per catturare i tap e chiudere il tooltip
            Positioned.fill(
              child: GestureDetector(
                onTap: _removeTooltip,
                behavior: HitTestBehavior.translucent,
                child: Container(color: Colors.transparent),
              ),
            ),
            // Tooltip
            Positioned(
              // Posiziona il tooltip vicino alla posizione del tap
              left: tapPosition.dx + 20,
              top: tapPosition.dy - 60,
              child: Material(
                elevation: 4.0,
                borderRadius: BorderRadius.circular(6),
                child: Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  constraints: BoxConstraints(maxWidth: 200),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(item.title + (item.project!=null ? " - " + item.project! : ""), style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                      SizedBox(height: 4),
                      Text('Inizio: $startLabel', style: TextStyle(fontSize: 11)),
                      Text('Fine: $endLabel', style: TextStyle(fontSize: 11)),
                      Text('Durata: ${item.duration} giorni', style: TextStyle(fontSize: 11)),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );

    // Inserisci il tooltip nell'overlay
    Overlay.of(context).insert(_overlayEntry!);

    // // Rimuovi il tooltip dopo 3 secondi
    // Future.delayed(Duration(seconds: 3), () {
    //   _removeTooltip();
    // });
  }

  // Rimuovi il tooltip
  void _removeTooltip() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  Widget _buildCompleteCantiereLine() {
    // Trova la data di inizio e fine del progetto
    int? earliestStart;
    int? latestEnd;

    for (var row in widget.rows) {
      int startInt = int.tryParse(row.start) ?? 0;
      int endInt = int.tryParse(row.end) ?? 0;

      if (startInt > 0) {
        earliestStart = (earliestStart == null || startInt < earliestStart) ? startInt : earliestStart;
      }

      if (endInt > 0) {
        latestEnd = (latestEnd == null || endInt > latestEnd) ? endInt : latestEnd;
      }
    }

    // Se non ci sono date valide, non mostrare la riga
    if (earliestStart == null || latestEnd == null) {
      return SizedBox.shrink();
    }

    String startLabel = _getFormattedDate(earliestStart);
    String endLabel = _getFormattedDate(latestEnd);

    // Calcola la durata in giorni
    int durationDays = DateTime.fromMillisecondsSinceEpoch(latestEnd)
        .difference(DateTime.fromMillisecondsSinceEpoch(earliestStart))
        .inDays + 1;

    return Container(
      height: _rowHeight, // Altezza della barra + padding
      child: Row(
        mainAxisSize: MainAxisSize.min, // Set to min to avoid unbounded width issues
        children: [
          // Colonna delle etichette
          Container(
            width: widget.labelColumnWidth,
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(color: Color(0xffE2E2E2), width:2.5),
                bottom: BorderSide(color: Color(0xffECECEC), width:1),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: firstLabelContainerWidth,
                  padding: EdgeInsets.all(labelContainersPadding),
                  alignment: Alignment.centerLeft,
                  child: NarFormLabelWidget(
                    label: "Cantiere",
                    fontSize: 12,
                    fontWeight: "500",
                    textColor: Colors.black,
                    textAlign: TextAlign.left,
                  ),
                ),
                Container(
                  width: secondLabelContainerWidth,
                  padding: EdgeInsets.all(labelContainersPadding),
                  alignment:Alignment.centerLeft,
                  child: NarFormLabelWidget(
                    label: startLabel,
                    fontSize: 11,
                    fontWeight: "500",
                    textColor: Colors.black,
                    textAlign: TextAlign.left,
                  ),
                ),
                Container(
                  width: thirdLabelContainerWidth,
                  padding: EdgeInsets.all(labelContainersPadding),
                  alignment: Alignment.centerLeft,
                  child: NarFormLabelWidget(
                    label: endLabel,
                    fontSize: 11,
                    fontWeight: "500",
                    textColor: Colors.black,
                    textAlign: TextAlign.left,
                  ),
                ),
                Container(
                  width: fourthLabelContainerWidth,
                  padding: EdgeInsets.all(labelContainersPadding),
                  alignment: Alignment.centerLeft,
                  child: NarFormLabelWidget(
                    label: "$durationDays giorni",
                    fontSize: 11,
                    fontWeight: "500",
                    textColor: Colors.black,
                    textAlign: TextAlign.left,
                  ),
                ),
              ],
            ),
          ),
          // Barra del Gantt - Replace Expanded with Container of fixed width
          Container(
            width: _months.length * widget.columnWidth,
            child: Stack(
              children: [
                // Griglia di sfondo
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: _months.map((month) {
                    return Container(
                      width: widget.columnWidth,
                      decoration: BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Color(0xffECECEC), width:1),
                          bottom: BorderSide(color: Color(0xffECECEC), width:1),
                        ),
                      ),
                    );
                  }).toList(),
                ),

                // Linee verticali per le settimane
                ..._weeks.map((week) {
                  return Positioned(
                    left: _calculateWeekPosition(week, widget.columnWidth),
                    top: 0,
                    bottom: 0,
                    child: Container(
                      width: 1,
                      color: Color(0xffF1F1F1),
                    ),
                  );
                }).toList(),

                // Linea verticale per la data corrente
                if (widget.showCurrentDate)
                  Positioned(
                    left: _calculateCurrentDatePosition(widget.columnWidth),
                    top: 0,
                    bottom: 0,
                    child: Container(
                      width: 2,
                      color: Colors.red,
                    ),
                  ),

                // Barra dell'attività completa del cantiere
                Positioned(
                  left: _calculateBarPosition(earliestStart, widget.columnWidth),
                  top: 10,
                  child: GestureDetector(
                    onTapUp: (TapUpDetails details) => _showTaskDetails(
                      context,
                      JobEventItem(
                        title: "Cantiere",
                        start: earliestStart.toString(),
                        end: latestEnd.toString(),
                        duration: durationDays.toString()
                      ),
                      startLabel,
                      endLabel,
                      details.globalPosition
                    ),
                    child: Container(
                      width: _calculateBarWidth(earliestStart, latestEnd, widget.columnWidth),
                      height: widget.barHeight,
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

    // Funzione per generare e scaricare il PDF del Gantt
  Future<void> pdfDownloadGantt([NewarcProject? project]) async {
    try {
      // Crea un nuovo documento PDF
      final pdf = pw.Document();

      // Carica i font (opzionale, usa i font di default se non disponibili)
      final fontData = await rootBundle.load("assets/fonts/Raleway-Regular.ttf");
      final ttf = pw.Font.ttf(fontData);
      final fontDataBold = await rootBundle.load("assets/fonts/Raleway-Bold.ttf");
      final ttfBold = pw.Font.ttf(fontDataBold);

      // Imposta le dimensioni per il PDF
      // Aumenta la larghezza della colonna delle etichette
      final labelColumnWidth = widget.labelColumnWidth; // Aumenta del 20%

      // Imposta la larghezza della colonna dei mesi come se ci fossero solo 2 mesi
      // Indipendentemente dal numero effettivo di mesi
      final double ganttColumnWidth = 300.0; // Larghezza fissa per ogni mese

      // Calcola la larghezza totale del documento
      final totalWidth = labelColumnWidth + (_months.length * ganttColumnWidth);

      // Altezza della riga nel PDF
      final rowHeight = _rowHeight;

      // Calcola l'altezza totale del documento
      final totalHeight = 60 + // Titolo e spazio
                         rowHeight + // Intestazione
                         (widget.showCompleteTimeline ? rowHeight : 0) + // Riga del cantiere
                         (widget.rows.length * rowHeight); // Righe normali

      // Crea un formato di pagina personalizzato con margini minimi
      final pageFormat = PdfPageFormat(
        totalWidth + 20, // Aggiungi un piccolo margine
        totalHeight + 20, // Aggiungi un piccolo margine
        marginAll: 10, // Margini minimi
      );

      // Crea la pagina (una sola pagina con dimensioni personalizzate)
      pdf.addPage(
        pw.Page(
          pageFormat: pageFormat,
          build: (pw.Context context) {
            // Lista di widget per la pagina
            List<pw.Widget> pageWidgets = [];

            // Aggiungi il titolo
            pageWidgets.add(
              pw.Center(
                child: pw.Text(
                  '${widget.name != null ? widget.name! + " - " : ""}Cronoprogramma',
                  style: pw.TextStyle(font: ttfBold, fontSize: 16),
                ),
              ),
            );

            pageWidgets.add(pw.SizedBox(height: 10));

            // Crea l'intestazione con i mesi
            pageWidgets.add(_buildPdfMonthsHeader(ttf, ttfBold, rowHeight, ganttColumnWidth, labelColumnWidth));

            // Aggiungi la riga del cantiere se richiesto
            if (widget.showCompleteTimeline && widget.rows.isNotEmpty) {
              pageWidgets.add(_buildPdfCantiereLine(ttf, ttfBold, rowHeight, ganttColumnWidth, labelColumnWidth));
            }

            // Aggiungi tutte le righe normali del Gantt
            for (int i = 0; i < widget.rows.length; i++) {
              pageWidgets.add(_buildPdfGanttRow(widget.rows[i], ttf, rowHeight, ganttColumnWidth, labelColumnWidth));
            }

            // Restituisci una colonna con tutti i widget della pagina
            return pw.Column(children: pageWidgets);
          },
        ),
      );

      // Genera il PDF
      final pdfBytes = await pdf.save();

      // Crea un Blob dal PDF
      final blob = html.Blob([pdfBytes], 'application/pdf');

      // Crea un link per il download
      final url = html.Url.createObjectUrlFromBlob(blob);
      html.AnchorElement(href: url)
        ..setAttribute('download', '${widget.name != null ? widget.name!.toLowerCase().trim().replaceAll(" ", "_") + "_" : ""}cronoprogramma_gantt.pdf')
        ..click();

      // Pulisci
      html.Url.revokeObjectUrl(url);

    } catch (e, s) {
      print('Errore nella generazione del PDF: $e');
      print(s);
    }
  }

  // Costruisce l'intestazione dei mesi per il PDF
  pw.Widget _buildPdfMonthsHeader(pw.Font font, pw.Font fontBold, double rowHeight,double ganttColumnWidth, double labelColumnWidth) {
    return pw.Container(
      height: rowHeight*1.2,
      decoration: pw.BoxDecoration(
        borderRadius: pw.BorderRadius.only(topLeft: pw.Radius.circular(10), topRight: pw.Radius.circular(10)),
        border: pw.Border(
          top: pw.BorderSide(
            color: PdfColor.fromHex("#E2E2E2"),
            width:1.5),
          left: pw.BorderSide(
            color: PdfColor.fromHex("#E2E2E2"),
            width:1.5),
          right: pw.BorderSide(
            color: PdfColor.fromHex("#E2E2E2"),
            width:1.5),
          bottom: pw.BorderSide(
            color: PdfColor.fromHex("#E2E2E2"),
            width:1.5),
        ),
      ),
      child: pw.Row(
        children: [
          // Colonna delle etichette
          pw.Container(
            width: labelColumnWidth,
            padding: pw.EdgeInsets.all(10),
            // decoration: pw.BoxDecoration(
            //   // border: pw.Border(
            //   //   // top: pw.BorderSide(color: PdfColors.blueGrey),
            //   //   // left: pw.BorderSide(color: PdfColors.blueGrey),
            //   //   // right: pw.BorderSide(color: PdfColors.blueGrey),
            //   //   // bottom: pw.BorderSide(color: PdfColors.blueGrey),
            //   // ),
            // ),
            child: pw.Row(
              children: [
                pw.Container(
                  width: labelColumnWidth*.45,
                  padding: pw.EdgeInsets.all(5),
                  alignment: pw.Alignment.centerLeft,
                  child: pw.Text(
                    "Lavorazione",
                    style: pw.TextStyle(font: fontBold, fontSize: 12),
                  ),
                ),
                pw.Container(
                  width: labelColumnWidth*.2,
                  padding: pw.EdgeInsets.all(5),
                  alignment: pw.Alignment.center,
                  child: pw.Text(
                    "Inizio ip.",
                    style: pw.TextStyle(font: fontBold, fontSize: 12),
                  ),
                ),
                pw.Container(
                  width: labelColumnWidth*.2,
                  padding: pw.EdgeInsets.all(5),
                  alignment: pw.Alignment.center,
                  child: pw.Text(
                    "Fine ip.",
                    style: pw.TextStyle(font: fontBold, fontSize: 12),
                  ),
                ),
                pw.Container(
                  width: labelColumnWidth*.15,
                  padding: pw.EdgeInsets.all(5),
                  alignment: pw.Alignment.center,
                  child: pw.Text(
                    "Durata",
                    style: pw.TextStyle(font: fontBold, fontSize: 12),
                  ),
                ),
              ],
            ),
          ),

          // Colonne dei mesi
          pw.Row(
            children: _months.map((month) {
              String monthName = (widget.monthNames[month.month]?.substring(0, 1).toUpperCase() ?? "") +
                              (widget.monthNames[month.month]?.substring(1) ?? "");
              return pw.Container(
                width: ganttColumnWidth,
                // padding: pw.EdgeInsets.symmetric(vertical: 15),
                decoration: pw.BoxDecoration(
                  border: pw.Border(
                    // top: pw.BorderSide(color: PdfColors.blueGrey),
                    left: pw.BorderSide(
                      color: PdfColor.fromHex("#E2E2E2"),
                      width: 1.5),
                    // right: pw.BorderSide(color: PdfColors.blueGrey),
                    // bottom: pw.BorderSide(color: PdfColors.blueGrey),
                  ),
                ),
                alignment: pw.Alignment.center,
                child: pw.Column(
                  mainAxisAlignment: pw.MainAxisAlignment.center,
                  children: [
                    pw.Text(
                      month.year.toString(),
                      style: pw.TextStyle(font: font, fontSize: 10, color: PdfColors.blueGrey),
                    ),
                    pw.Text(
                      monthName,
                      style: pw.TextStyle(font: fontBold, fontSize: 14),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      )
    );
  }

  // Costruisce una riga del Gantt per il PDF
  pw.Widget _buildPdfGanttRow(JobEventItem item, pw.Font font, double rowHeight, double ganttColumnWidth, double labelColumnWidth) {
    int startInt = int.tryParse(item.start) ?? 0;
    int endInt = int.tryParse(item.end) ?? 0;

    String startLabel = (item.start != "Inizio ip." && item.start.isNotEmpty && item.start != "first date")
        ? _getFormattedDate(startInt == 0 ? DateTime.now().millisecondsSinceEpoch : startInt)
        : item.start;

    String endLabel = (item.end != "Fine ip." && item.end.isNotEmpty && item.end != "last date")
        ? _getFormattedDate(endInt == 0 ? DateTime.now().millisecondsSinceEpoch : endInt)
        : (int.tryParse(item.start) != null ? _getFormattedDate(int.parse(item.start)) : item.end);

    return pw.Container(
      height: rowHeight,
      child: pw.Row(
        children: [
          // Colonna delle etichette
          pw.Container(
            width: labelColumnWidth,
            padding: pw.EdgeInsets.all(10),
            decoration: pw.BoxDecoration(
              border: pw.Border(
                // top: pw.BorderSide(color: PdfColor.fromHex("#E2E2E2")),
                left: pw.BorderSide(color: PdfColor.fromHex("#E2E2E2"), width:1.5),
                right: pw.BorderSide(color: PdfColor.fromHex("#E2E2E2"), width:2.5),
                bottom: pw.BorderSide(color: PdfColor.fromHex("#E2E2E2")),
              ),
            ),
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Container(
                  width: labelColumnWidth*.45,
                  padding: pw.EdgeInsets.all(5),
                  alignment: pw.Alignment.centerLeft,
                  child: pw.Text(
                    item.title,
                    style: pw.TextStyle(font: font, fontSize: 11),
                  ),
                ),
                pw.Container(
                  width: labelColumnWidth*.2,
                  padding: pw.EdgeInsets.all(5),
                  alignment: pw.Alignment.center,
                  child: pw.Text(
                    startLabel,
                    style: pw.TextStyle(font: font, fontSize: 11),
                  ),
                ),
                pw.Container(
                  width: labelColumnWidth*.2,
                  padding: pw.EdgeInsets.all(5),
                  alignment: pw.Alignment.center,
                  child: pw.Text(
                    endLabel,
                    style: pw.TextStyle(font: font, fontSize: 11),
                  ),
                ),
                pw.Container(
                  width: labelColumnWidth*.15,
                  padding: pw.EdgeInsets.all(5),
                  alignment: pw.Alignment.center,
                  child: pw.Text(
                    "${item.duration.isEmpty ? "No Duration" : "${item.duration} giorni"}",
                    style: pw.TextStyle(font: font, fontSize: 11),
                  ),
                ),
              ],
            ),
          ),

          // Barra del Gantt
          pw.Container(
            width: _months.length * ganttColumnWidth,
            child: pw.Stack(
              children: [
                // Griglia di sfondo
                pw.Row(
                  children: _months.map((month) {
                    return pw.Container(
                      width: ganttColumnWidth,
                      height: rowHeight,
                      decoration: pw.BoxDecoration(
                        border: pw.Border(
                          right: pw.BorderSide(color: PdfColor.fromHex("#E2E2E2"), width:1.5),
                          bottom: pw.BorderSide(color: PdfColor.fromHex("#E2E2E2")),
                        ),
                      ),
                    );
                  }).toList(),
                ),

                // Linee verticali per le settimane
                ..._weeks.map((week) {
                  return pw.Positioned(
                    left: _calculateWeekPosition(week, ganttColumnWidth),
                    top: 0,
                    bottom: 0,
                    child: pw.Container(
                      width: 1,
                      color: PdfColor.fromHex("#F1F1F1"),
                    ),
                  );
                }).toList(),

                // Barra dell'attività
                if (startInt > 0 && endInt > 0)
                  pw.Positioned(
                    left: _calculateBarPosition(startInt, ganttColumnWidth),
                    top: 8,
                    bottom: 8,
                    child: pw.Container(
                      width: _calculateBarWidth(startInt, endInt, ganttColumnWidth),
                      height: widget.barHeight,
                      decoration: pw.BoxDecoration(
                        color: PdfColor.fromHex("#AAD1C1"),
                        borderRadius: pw.BorderRadius.circular(4),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Costruisce la riga del cantiere per il PDF
  pw.Widget _buildPdfCantiereLine(pw.Font font, pw.Font fontBold, double rowHeight, double ganttColumnWidth, double labelColumnWidth) {
    // Trova la data di inizio e fine del progetto
    int? earliestStart;
    int? latestEnd;

    for (var row in widget.rows) {
      int startInt = int.tryParse(row.start) ?? 0;
      int endInt = int.tryParse(row.end) ?? 0;

      if (startInt > 0) {
        earliestStart = (earliestStart == null || startInt < earliestStart) ? startInt : earliestStart;
      }

      if (endInt > 0) {
        latestEnd = (latestEnd == null || endInt > latestEnd) ? endInt : latestEnd;
      }
    }

    // Se non ci sono date valide, non mostrare la riga
    if (earliestStart == null || latestEnd == null) {
      return pw.Container(height: 0);
    }

    String startLabel = _getFormattedDate(earliestStart);
    String endLabel = _getFormattedDate(latestEnd);

    // Calcola la durata in giorni
    int durationDays = DateTime.fromMillisecondsSinceEpoch(latestEnd)
        .difference(DateTime.fromMillisecondsSinceEpoch(earliestStart))
        .inDays + 1;

    return pw.Container(
      height: rowHeight,
      child: pw.Row(
        children: [
          // Colonna delle etichette
          pw.Container(
            width: labelColumnWidth,
            padding: pw.EdgeInsets.all(10),
            decoration: pw.BoxDecoration(
              border: pw.Border(
                top: pw.BorderSide(color: PdfColor.fromHex("#E2E2E2"), width:2.5),
                left: pw.BorderSide(color: PdfColor.fromHex("#E2E2E2"), width:1.5),
                right: pw.BorderSide(color: PdfColor.fromHex("#E2E2E2"), width:2.5),
                bottom: pw.BorderSide(color: PdfColor.fromHex("#E2E2E2")),
              ),
            ),
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Container(
                  width: labelColumnWidth*.45,
                  padding: pw.EdgeInsets.all(5),
                  alignment: pw.Alignment.centerLeft,
                  child: pw.Text(
                    "Cantiere",
                    style: pw.TextStyle(font: font, fontSize: 12),
                  ),
                ),
                pw.Container(
                  width: labelColumnWidth*.2,
                  padding: pw.EdgeInsets.all(5),
                  alignment: pw.Alignment.center,
                  child: pw.Text(
                    startLabel,
                    style: pw.TextStyle(font: font, fontSize: 11),
                  ),
                ),
                pw.Container(
                  width: labelColumnWidth*.2,
                  padding: pw.EdgeInsets.all(5),
                  alignment: pw.Alignment.center,
                  child: pw.Text(
                    endLabel,
                    style: pw.TextStyle(font: font, fontSize: 11),
                  ),
                ),
                pw.Container(
                  width: labelColumnWidth*.15,
                  padding: pw.EdgeInsets.all(5),
                  alignment: pw.Alignment.center,
                  child: pw.Text(
                    "${durationDays} giorni",
                    style: pw.TextStyle(font: font, fontSize: 11),
                  ),
                ),
              ],
            ),
          ),

          // Barra del Gantt
          pw.Container(
            width: _months.length * ganttColumnWidth,
            child: pw.Stack(
              children: [
                // Griglia di sfondo
                pw.Row(
                  children: _months.map((month) {
                    return pw.Container(
                      width: ganttColumnWidth,
                      height: rowHeight,
                      decoration: pw.BoxDecoration(
                        border: pw.Border(
                          top: pw.BorderSide(color: PdfColor.fromHex("#E2E2E2"), width:2.5),
                          right: pw.BorderSide(color: PdfColor.fromHex("#E2E2E2"), width:1.5),
                          bottom: pw.BorderSide(color: PdfColor.fromHex("#E2E2E2")),
                        ),
                      ),
                    );
                  }).toList(),
                ),

                // Linee verticali per le settimane
                ..._weeks.map((week) {
                  return pw.Positioned(
                    left: _calculateWeekPosition(week, ganttColumnWidth),
                    top: 0,
                    bottom: 0,
                    child: pw.Container(
                      width: 1,
                      color: PdfColor.fromHex("#F1F1F1"),
                    ),
                  );
                }).toList(),

                // Barra dell'attività completa del cantiere
                pw.Positioned(
                  left: _calculateBarPosition(earliestStart, ganttColumnWidth),
                  top: 8,
                  bottom: 8,
                  // bottom: (rowHeight - widget.barHeight)/2,
                  child: pw.Container(
                    width: _calculateBarWidth(earliestStart, latestEnd, ganttColumnWidth),
                    height: widget.barHeight,
                    decoration: pw.BoxDecoration(
                      color: PdfColor.fromHex("#499B79"),
                      borderRadius: pw.BorderRadius.circular(4),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}








