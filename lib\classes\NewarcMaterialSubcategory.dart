class NewarcMaterialSubCategory{
  String? firebaseId;
  String? name; // SubCategory Name
  int? insertTimestamp;
  String? uid; //the Firebase Id of the logged in user
  int? codeCounter;

  Map<String, Object?> toMap() {
    return {
      'name': name,
      'insertTimestamp': insertTimestamp,
      'uid': uid,
      'codeCounter': codeCounter,
    };
  }

  NewarcMaterialSubCategory.empty() {
    this.firebaseId = '';
    this.name = '';
    this.insertTimestamp = null;
    this.uid = '';
    this.codeCounter = 0;
  }

  NewarcMaterialSubCategory.fromDocument(Map<String, dynamic> data, String id) {
    this.firebaseId = id;
    try {
      this.name = data['name'];
      this.insertTimestamp = data['insertTimestamp'];
      this.uid = data['uid'];
      this.codeCounter = data['codeCounter'];
    } catch (e, s) {
      print({ 'NewarcMaterialSubCategory Class Error ------->', e, s});
    }
  }
}