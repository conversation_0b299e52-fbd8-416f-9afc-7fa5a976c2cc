/// Line chart example
/*
import 'package:charts_flutter/flutter.dart' as charts;
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';

class CustomChart extends StatelessWidget {
  final List<charts.Series<dynamic, num>> seriesList;
  final bool? animate;

  CustomChart(this.seriesList, {this.animate});

  /// Creates a [LineChart] with sample data and no transition.
  factory CustomChart.withSampleData() {
    return new CustomChart(
      _createSampleData(),
      // Disable animations for image tests.
      animate: false,
    );
  }

  factory CustomChart.withWeeklyData(List<AcquiredContact> contacts) {
    return new CustomChart(
      _createWeeklyChart(contacts),
      // Disable animations for image tests.
      animate: false,
    );
  }

  @override
  Widget build(BuildContext context) {
    return new charts.LineChart(
      seriesList,
      animate: animate,
      defaultRenderer: new charts.LineRendererConfig(
          includePoints: false, includeArea: true),
    );
  }

  /// Create one series with sample hard coded data.
  static List<charts.Series<LinearSales, int>> _createSampleData() {
    final data = [
      new LinearSales(0, 5),
      new LinearSales(1, 25),
      new LinearSales(2, 100),
      new LinearSales(3, 75),
    ];

    return [
      new charts.Series<LinearSales, int>(
        id: 'Sales',
        colorFn: (_, __) => charts.ColorUtil.fromDartColor(Color(0xff489B79)),
        domainFn: (LinearSales sales, _) => sales.year,
        measureFn: (LinearSales sales, _) => sales.sales,
        data: data,
      )
    ];
  }

  static List<charts.Series<LinearSales, int>> _createWeeklyChart(
      List<AcquiredContact> contacts) {
    int now = DateTime.now().millisecondsSinceEpoch;
    int oneDay = 86400000;
    int oneWeek = 604800000;

    Map<int, List<AcquiredContact>> groupedDailyContacts = groupBy(
        contacts
            .where((element) => element.insertionTimestamp != null)
            .where((element) => now - element.insertionTimestamp! <= oneWeek),
        (AcquiredContact contact) {
      return ((now - contact.insertionTimestamp!) / oneDay).ceil();
    });

    final data = List.generate(
      7,
      (index) => LinearSales(
          index,
          groupedDailyContacts[index] != null
              ? groupedDailyContacts[index]!.length
              : 0),
    );

    return [
      new charts.Series<LinearSales, int>(
        id: 'Sales',
        colorFn: (_, __) => charts.ColorUtil.fromDartColor(Color(0xff489B79)),
        domainFn: (LinearSales sales, _) => sales.year,
        measureFn: (LinearSales sales, _) => sales.sales,
        data: data,
      )
    ];
  }
}

/// Sample linear data type.
class LinearSales {
  final int year;
  final int sales;

  LinearSales(this.year, this.sales);
}
*/