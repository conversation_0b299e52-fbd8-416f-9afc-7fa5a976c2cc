import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/immaginaProject.dart';


class ImmaginaProfessionalsController extends GetxController {
  bool loading = false;
  List<ImmaginaProject> projects = [];
  TextEditingController stateFilterController = TextEditingController();
  String stateSelectedFilter = '';

  clearFilter() {
    stateSelectedFilter = '';
    stateFilterController.clear();
  }
}