import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/classes/newarcMaterialLibrary.dart';

class Moodboard {
  String? id;
  String? name;
  String? city;
  int? created;
  String? clientId;
  String? newarcProjectId;
  List<NewarcMaterialLibrary> newarcMaterial = [];

  // String? id;
  // String? name;
  // String? city;
  // String? type;
  // String? renovationContactId;
  // int? created;
  // List<dynamic> assignedTeam = [];
  // Map? assignedVendor;
  // AgencyCommissions? newAssignedAgency;
  // Map? assignedAgency;
  // List? designFiles;
  // List? plantEngineeringFiles;
  // List? floorPlanFiles;
  // List? renderFiles;
  // List? constructionPracticesFiles;
  // List? brochureFiles;
  // List? preRennovationFiles;
  // List? postRennovationFiles;
  // List? deedAndContractsFiles;
  // List? videoFiles;
  // String? propertyId;
  // bool? isArchived;
  // FixedProperty? fixedProperty;
  // List<NewarcMaterial>? newarcMaterial = [];
  // List<Process>? vendorAndProfessionals = [];
  // List<ProjectJob>? projectJobs = [];
  // int? jobStartDate;
  // int? jobEndDate;
  // int? hypotheticalJobEndDate;
  // String? provisionalAccountId;
  // String? virtualTourLink;
  // Map? economicAccount = {};

  NewarcMateriotechSelection(
      Map<String, dynamic> newarcMateriotechSelectionMap) {
    this.id = newarcMateriotechSelectionMap['id'];
    this.name = newarcMateriotechSelectionMap['name'];
    this.city = newarcMateriotechSelectionMap['city'];
    // this.type = newarcMateriotechProjectMap['type'];
    this.created = newarcMateriotechSelectionMap['created'];
    // this.assignedTeam = newarcMateriotechProjectMap['assignedTeam'];
    // this.assignedVendor = newarcMateriotechProjectMap['assignedVendor'];
    // this.assignedAgency = newarcMateriotechProjectMap['assignedAgency'];
    // this.propertyId = newarcMateriotechProjectMap['propertyId'];
    // this.designFiles = newarcMateriotechProjectMap['designFiles'];
    // this.plantEngineeringFiles = newarcMateriotechProjectMap['plantEngineeringFiles'];
    // this.floorPlanFiles = newarcMateriotechProjectMap['floorPlanFiles'];
    // this.renderFiles = newarcMateriotechProjectMap['renderFiles'];
    // this.constructionPracticesFiles =
    //     newarcMateriotechProjectMap['constructionPracticesFiles'];
    // this.brochureFiles = newarcMateriotechProjectMap['brochureFiles'];
    // this.preRennovationFiles = newarcMateriotechProjectMap['preRennovationFiles'];
    // this.postRennovationFiles = newarcMateriotechProjectMap['postRennovationFiles'];
    // this.deedAndContractsFiles = newarcMateriotechProjectMap['deedAndContractsFiles'];
    // this.videoFiles = newarcMateriotechProjectMap['videoFiles'];
    // this.isArchived = newarcMateriotechProjectMap['isArchived'];
    // this.fixedProperty = newarcMateriotechProjectMap['fixedProperty'];
    this.clientId = newarcMateriotechSelectionMap['clientId'] ?? "";
    this.newarcProjectId =
        newarcMateriotechSelectionMap['newarcProjectId'] ?? "";
    this.newarcMaterial = newarcMateriotechSelectionMap['newarcMaterial'] ?? [];
    // this.vendorAndProfessionals = newarcMateriotechProjectMap['vendorAndProfessionals'];
    // this.projectJobs = newarcMateriotechProjectMap['projectJobs'];
    // this.jobStartDate = newarcMateriotechProjectMap['jobStartDate'];
    // this.jobEndDate = newarcMateriotechProjectMap['jobEndDate'];
    // this.hypotheticalJobEndDate = newarcMateriotechProjectMap['hypotheticalJobEndDate'];
    // this.virtualTourLink = newarcMateriotechProjectMap['virtualTourLink'];
    // this.provisionalAccountId = newarcMateriotechProjectMap['provisionalAccountId'];
    // this.economicAccount = newarcMateriotechProjectMap['economicAccount'];
  }

  Moodboard.empty() {
    this.id = '';
    this.name = '';
    this.city = '';
    // this.type = '';
    this.created = Timestamp.now().millisecondsSinceEpoch;
    // this.assignedTeam = [];
    // this.assignedVendor = {};
    // this.assignedAgency = {};
    // this.designFiles = [];
    // this.plantEngineeringFiles = [];
    // this.floorPlanFiles = [];
    // this.renderFiles = [];
    // this.constructionPracticesFiles = [];
    // this.brochureFiles = [];
    // this.preRennovationFiles = [];
    // this.postRennovationFiles = [];
    // this.deedAndContractsFiles = [];
    // this.videoFiles = [];
    // this.propertyId = null;
    // this.renovationContactId = '';
    // this.isArchived = false;
    // this.fixedProperty = new FixedProperty.empty();
    // this.vendorAndProfessionals = [];
    this.clientId = '';
    this.newarcProjectId = '';
    this.newarcMaterial = [];
    // this.projectJobs = [];
    // this.jobStartDate = 0;
    // this.jobEndDate = 0;
    // this.hypotheticalJobEndDate = 0;
    // this.virtualTourLink = '';
    // this.provisionalAccountId = null;
    // this.economicAccount = {};
  }

  Moodboard.fromDocument(Map<String, dynamic> data, String id) {
    this.id = id;
    this.name = data['name'] == null ? '' : data['name'];
    this.city = data['city'] == null ? '' : data['city'];
    // this.type = data['type'] == null ? '' : data['type'];
    this.created = data['created'] == null ? '' : data['created'];
    // this.assignedTeam =
    //     data['assignedTeam'] == null ? [] : data['assignedTeam'];
    // this.assignedVendor =
    //     data['assignedVendor'] == null ? {} : data['assignedVendor'];
    // this.assignedAgency =
    //     data['assignedAgency'] == null ? {} : data['assignedAgency'];
    // this.designFiles = data['designFiles'] == null ? [] : data['designFiles'];
    // this.plantEngineeringFiles = data['plantEngineeringFiles'] == null
    //     ? []
    //     : data['plantEngineeringFiles'];
    // this.floorPlanFiles =
    //     data['floorPlanFiles'] == null ? [] : data['floorPlanFiles'];
    // this.renderFiles = data['renderFiles'] == null ? [] : data['renderFiles'];
    // this.constructionPracticesFiles = data['constructionPracticesFiles'] == null
    //     ? []
    //     : data['constructionPracticesFiles'];
    // this.brochureFiles =
    //     data['brochureFiles'] == null ? [] : data['brochureFiles'];
    // this.preRennovationFiles =
    //     data['preRennovationFiles'] == null ? [] : data['preRennovationFiles'];
    // this.postRennovationFiles = data['postRennovationFiles'] == null
    //     ? []
    //     : data['postRennovationFiles'];
    // this.deedAndContractsFiles = data['deedAndContractsFiles'] == null
    //     ? []
    //     : data['deedAndContractsFiles'];

    // this.videoFiles = data['videoFiles'] ?? [];

    // this.propertyId = data['propertyId'] == null ? null : data['propertyId'];
    // this.renovationContactId = data['renovationContactId'] == null
    //     ? null
    //     : data['renovationContactId'];
    // this.isArchived = data['isArchived'] == null ? false : data['isArchived'];

    // this.fixedProperty = data['fixedProperty'] != null
    //     ? FixedProperty.fromDocument(data['fixedProperty'])
    //     : FixedProperty.empty();

    this.clientId = data['clientId'];

    this.newarcProjectId = data['newarcProjectId'];

    if (data['newarcMaterial'] != null) {
      for (var i = 0; i < data['newarcMaterial'].length; i++) {
        this.newarcMaterial.add(
            NewarcMaterialLibrary.fromDocument(data['newarcMaterial'][i], id));
      }
    } else {
      this.newarcMaterial = [];
    }

    // if (data['vendorAndProfessionals'] != null) {
    //   for (var i = 0; i < data['vendorAndProfessionals'].length; i++) {
    //     this
    //         .vendorAndProfessionals!
    //         .add(Process.fromDocument(data['vendorAndProfessionals'][i], i));
    //   }
    // }

    // if (data['projectJobs'] != null) {
    //   for (var i = 0; i < data['projectJobs'].length; i++) {
    //     this
    //         .projectJobs!
    //         .add(ProjectJob.fromDocument(data['projectJobs'][i], i));
    //   }
    // }

    // this.jobStartDate = data['jobStartDate'] == null ? 0 : data['jobStartDate'];
    // this.jobEndDate = data['jobEndDate'] == null ? 0 : data['jobEndDate'];
    // this.hypotheticalJobEndDate = data['hypotheticalJobEndDate'] == null
    //     ? 0
    //     : data['hypotheticalJobEndDate'];
    // this.virtualTourLink = data['virtualTourLink'] ?? data['virtualTourLink'];
    // this.provisionalAccountId = data['provisionalAccountId'] == null
    //     ? ''
    //     : data['provisionalAccountId'];
    // this.economicAccount =
    //     data['economicAccount'] == null ? {} : data['economicAccount'];
  }

  copy(Moodboard newarcMateriotechSelection) {
    this.id = newarcMateriotechSelection.id;
    this.name = newarcMateriotechSelection.name;
    this.city = newarcMateriotechSelection.city;
    // this.type = newarcMateriotechProject.type;
    this.created = newarcMateriotechSelection.created;
    // this.assignedTeam = newarcMateriotechProject.assignedTeam;
    // this.assignedVendor = newarcMateriotechProject.assignedVendor;
    // this.assignedAgency = newarcMateriotechProject.assignedAgency;
    // this.designFiles = newarcMateriotechProject.designFiles;
    // this.plantEngineeringFiles = newarcMateriotechProject.plantEngineeringFiles;
    // this.floorPlanFiles = newarcMateriotechProject.floorPlanFiles;
    // this.renderFiles = newarcMateriotechProject.renderFiles;
    // this.constructionPracticesFiles = newarcMateriotechProject.constructionPracticesFiles;
    // this.brochureFiles = newarcMateriotechProject.brochureFiles;
    // this.preRennovationFiles = newarcMateriotechProject.preRennovationFiles;
    // this.postRennovationFiles = newarcMateriotechProject.postRennovationFiles;
    // this.deedAndContractsFiles = newarcMateriotechProject.deedAndContractsFiles;
    // this.videoFiles = newarcMateriotechProject.videoFiles;
    // this.propertyId = newarcMateriotechProject.propertyId;
    // this.renovationContactId = newarcMateriotechProject.renovationContactId;
    // this.isArchived = newarcMateriotechProject.isArchived;
    // this.fixedProperty = newarcMateriotechProject.fixedProperty;
    this.clientId = newarcMateriotechSelection.clientId;
    this.newarcProjectId = newarcMateriotechSelection.newarcProjectId;
    this.newarcMaterial = newarcMateriotechSelection.newarcMaterial;
    // this.vendorAndProfessionals = newarcMateriotechProject.vendorAndProfessionals;
    // this.projectJobs = newarcMateriotechProject.projectJobs;
    // this.jobStartDate = newarcMateriotechProject.jobStartDate;
    // this.jobEndDate = newarcMateriotechProject.jobEndDate;
    // this.hypotheticalJobEndDate = newarcMateriotechProject.hypotheticalJobEndDate;
    // this.virtualTourLink = newarcMateriotechProject.virtualTourLink;
    // this.provisionalAccountId = newarcMateriotechProject.provisionalAccountId;
    // this.economicAccount = newarcMateriotechProject.economicAccount;
  }

  Map<String, dynamic> toMap() {
    return {
      'name': this.name,
      'city': this.city,
      // 'type': this.type,
      'created': this.created,
      // 'assignedTeam': this.assignedTeam,
      // 'assignedVendor': this.assignedVendor,
      // 'assignedAgency': this.assignedAgency,
      // 'propertyId': this.propertyId,
      // 'designFiles': this.designFiles,
      // 'plantEngineeringFiles': this.plantEngineeringFiles,
      // 'floorPlanFiles': this.floorPlanFiles,
      // 'renderFiles': this.renderFiles,
      // 'constructionPracticesFiles': this.constructionPracticesFiles,
      // 'brochureFiles': this.brochureFiles,
      // 'preRennovationFiles': this.preRennovationFiles,
      // 'postRennovationFiles': this.postRennovationFiles,
      // 'isArchived': this.isArchived,
      // 'deedAndContractsFiles': this.deedAndContractsFiles,
      // 'videoFiles': this.videoFiles,
      // 'fixedProperty': this.fixedProperty!.toMap(),
      'clientId': this.clientId,
      'newarcProjectId': this.newarcProjectId,
      'newarcMaterial': this.newarcMaterial.map((e) => e.toMap()).toList(),
      // 'vendorAndProfessionals':
      //     this.vendorAndProfessionals!.map((e) => e.toMap()).toList(),
      // 'projectJobs': this.projectJobs!.map((e) => e.toMap()).toList(),
      // 'jobStartDate': this.jobStartDate,
      // 'jobEndDate': this.jobEndDate,
      // 'hypotheticalJobEndDate': this.hypotheticalJobEndDate,
      // 'provisionalAccountId': this.provisionalAccountId,
      // 'economicAccount': this.economicAccount,
      // 'virtualTourLink': this.virtualTourLink
    };
  }
}
