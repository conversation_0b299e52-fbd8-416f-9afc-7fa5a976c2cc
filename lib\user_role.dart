// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:localstorage/localstorage.dart';

// final authStorage = new LocalStorage('auth');

// setUserRole(userId) async {
//   return;
//   QuerySnapshot<Map<String, dynamic>> collectionSnapshot =
//       await FirebaseFirestore.instance
//           .collection('users')
//           .where('id', isEqualTo: userId)
//           .get();

//   if (collectionSnapshot.docs.length > 0) {
//     print(collectionSnapshot.docs[0].data());
//     authStorage.setItem('userData', collectionSnapshot.docs[0].data());
//   }
// }

// getUserRole() {
//   return 'master';
//   late Map<String, dynamic> userData = authStorage.getItem('userData');
//   late String userRole = userData['role'];
//   return userRole;
// }

// deleteUserRole() {
//   try {
//     authStorage.deleteItem('userData');
//   } catch (e) {
//     print('Error while deleting user role');
//   }
// }
