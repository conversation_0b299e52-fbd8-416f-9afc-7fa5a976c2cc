import 'package:archive/archive.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/property.dart';
import 'package:newarc_platform/classes/renderImage.dart';
import 'package:newarc_platform/classes/renovationQuotation.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'dart:html' as html;
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/utils/various.dart';
import 'package:http/http.dart' show get;
import 'package:image/image.dart' as img;

import 'dart:js' as js;

getNetworkImage(url) async {

  // print({'loading image:', url});

  if(url == ''){
    final Uint8List coverLogoData = await _loadImage('assets/logo_newarc_immagina.png');
    final coverLogo = pw.MemoryImage(coverLogoData);
    return coverLogo;
  } 

  // print({'loading image:', url});

  var response = await get(Uri.parse(url));
  var data = response.bodyBytes;

  // final img.Image? original = img.decodeImage(data);
  // final img.Image resized = img.copyResize(original!, width: 300); // Resize to 300px width

  // return pw.MemoryImage(Uint8List.fromList(img.encodeJpg(resized)));

  return pw.MemoryImage(data);
}

Future<Uint8List> _loadImage(String path) async {
  final ByteData data = await rootBundle.load(path);
  return data.buffer.asUint8List();
}


imageAdsBrochure( Property adData, Map projectDetails,{ValueNotifier<double>? progress}) async {
  progress?.value = 0.0;

  NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);
  
  try {
    final ByteData fontMediumData = await rootBundle.load('assets/fonts/Raleway-Medium.ttf');
    final ralewayMedium = pw.Font.ttf(fontMediumData.buffer.asByteData());

    final ByteData fontBoldData =await rootBundle.load('assets/fonts/Raleway-Bold.ttf');
    final ralewayBold = pw.Font.ttf(fontBoldData.buffer.asByteData());

    String coverImageUrl = '';
    var coverImage;
    if( adData.photoBrochureCoverPaths!.length > 0 ) {
      coverImageUrl = await printUrl(adData.photoBrochureCoverPaths![0]['location'], '', adData.photoBrochureCoverPaths![0]['filename']);
      coverImage = await getNetworkImage( coverImageUrl );
    }
    progress?.value = .10;
    
    final pdf = pw.Document();

    
    // Before After images
    List photoBeforeAfterPaths = adData.photoBeforeAfterPaths!;
    List<Map> photoBeforeAfterPathsImages = []; 
    if( photoBeforeAfterPaths.isNotEmpty ) {
      for( int rd = 0; rd < photoBeforeAfterPaths.length; rd++ ) {
        
        photoBeforeAfterPaths[rd]['after']['isBrochure'] = photoBeforeAfterPaths[rd]['after']['isBrochure']??false;
        
        if( !photoBeforeAfterPaths[rd]['after']['isBrochure'] ) continue;

        String _beforeImageUrl = await printUrl(photoBeforeAfterPaths[rd]['before']['location'], '', photoBeforeAfterPaths[rd]['before']['filename']);
        
        String _afterImageUrl = await printUrl(photoBeforeAfterPaths[rd]['after']['location'], '', photoBeforeAfterPaths[rd]['after']['filename']);

        photoBeforeAfterPathsImages.add({
          'before': await getNetworkImage( _beforeImageUrl ),
          'after': await getNetworkImage( _afterImageUrl ),
        });
      }
    }
    progress?.value = 0.25;
    
    debugPrint('Ready');

    List photoDayTimePaths = adData.photoDayTimePaths!;
    List photoDayTimePathsImages = []; 
    if( photoDayTimePaths.isNotEmpty ) {
      for( int rd = 0; rd < photoDayTimePaths.length; rd++ ) {

        photoDayTimePaths[rd]['isBrochure'] = photoDayTimePaths[rd]['isBrochure'] ?? false;
        if( !photoDayTimePaths[rd]['isBrochure'] ) continue;

        String _imageUrl = await printUrl(photoDayTimePaths[rd]['location'], '', photoDayTimePaths[rd]['filename'] );
        
        photoDayTimePathsImages.add( await getNetworkImage( _imageUrl ) );
        
      }
    }
    progress?.value = 0.30;

    
    final Uint8List backgroundImageData = await _loadImage('assets/background_last_page.jpg');
    final backgroundImage = pw.MemoryImage(backgroundImageData);

    final Uint8List risLogoData = await _loadImage('assets/rq-pdf-cover-logo.png');
    final risLogoImage = pw.MemoryImage(risLogoData);

    final Uint8List immaginaLogoData = await _loadImage('assets/immagina_logo_white.png');
    final immaginaLogoImage = pw.MemoryImage(immaginaLogoData);

    final Uint8List locationIconData = await _loadImage('assets/icons/pdf-location.png');
    final locationIcon = pw.MemoryImage(locationIconData);
    progress?.value = 0.35;

    final Uint8List phoneIconData = await _loadImage('assets/icons/pdf-phone.png');
    final phoneIcon = pw.MemoryImage(phoneIconData);

    final Uint8List immaginaLogoGreemData = await _loadImage('assets/immagina-logo-esteso.png');
    final immaginaLogoGreem = pw.MemoryImage(immaginaLogoGreemData);


    // Fetch agency data
    var agencyLogo;
    String agencyImageUrl = '';
    Agency agencyData = Agency.empty();
    if( projectDetails['agencyId'] != '' ) {
      
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot = await FirebaseFirestore.instance
      .collection(appConfig.COLLECT_USERS)
      .where('agencyId', isEqualTo: projectDetails['agencyId'])
      .limit(1)
      .get();

      progress?.value = 0.40;

      DocumentSnapshot<Map<String, dynamic>> collectionSnapshotAgency = await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_AGENCIES)
        .doc(projectDetails['agencyId'])
        .get();
      progress?.value = 0.45;
      if( collectionSnapshot.docs.length > 0 ) {

        AgencyUser agency = AgencyUser.fromDocument(collectionSnapshot.docs[0].data() , collectionSnapshot.docs[0].id);
        agencyImageUrl = await agencyProfileUrl(agency.agencyId, agency.profilePicture);
        agencyLogo = await getNetworkImage( agencyImageUrl );

      }

      if( collectionSnapshotAgency.exists ) {
        agencyData = Agency.fromDocument(collectionSnapshotAgency.data()!, collectionSnapshotAgency.id);
      }
      progress?.value = 0.50;

    }

    // Cover page starts

    List<pw.Widget> pdfPage1 = [];
    
    pdfPage1.add(
      pw.Container(
        width: 1920,
        height: 1280,
        child: pw.Stack(
          children: [
            pw.Container(
              width: 1920,
              height: 1280,
              child: pw.Container(
                width: 1920,
                height: 1280,
                decoration: pw.BoxDecoration(
                  color: PdfColors.amber,
                  // borderRadius: pw.BorderRadius.circular(10),
                  image: pw.DecorationImage(image: coverImage, fit: pw.BoxFit.cover )
                ),
                // child: pw.Center(
                //   child: pw.Image(
                //     immaginaLogoImage,
                //     height: 215
                //   )
                // )
                
              ),
            )
            
          ]
        )
      )
    );
    progress?.value = 0.55;
    
    
    int counterAI = 0;
    
    // Before After Image
    counterAI = 0;
    for (var i = 0; i < photoBeforeAfterPathsImages.length; i++) {
      
      if( photoBeforeAfterPathsImages[i]['after'] != '' && photoBeforeAfterPathsImages[i]['before'] != '' ) pdfPage1.add(
        pw.Container(
          width: 1920,
          height: 1280,
          decoration: pw.BoxDecoration(
             color: PdfColors.white,
          ),
          child: pw.Stack(
            children: [
              pw.Container(
                width: 1920,
                height: 1280,
                child: pw.Stack(
                  children: [
                    pw.Positioned(
                     left: 25,
                     top: 25,
                     child: pw.Container(
                        width: 1665,
                        height: 1070,
                        decoration: pw.BoxDecoration(
                            color: PdfColors.amber,
                            borderRadius: pw.BorderRadius.circular(15),
                            image: pw.DecorationImage(image: photoBeforeAfterPathsImages[i]['before'], fit: pw.BoxFit.cover )
                        ),
                     )
                    ),
                    pw.Positioned(
                      left: 25,
                      top: 25,
                      child: pw.Container(
                        height: 45,
                        padding: pw.EdgeInsets.symmetric( horizontal: 15 ),
                        margin: pw.EdgeInsets.symmetric( horizontal: 15,vertical: 15 ),
                        decoration: pw.BoxDecoration(
                          color: PdfColors.white,
                          borderRadius: pw.BorderRadius.circular(22.5),
                        ),
                        child: pw.Center(
                          child: pw.Text(
                            'Progetto',
                            overflow: pw.TextOverflow.visible,
                            style: pw.TextStyle(
                              // height: 14,  
                              fontSize: 25,
                              color: PdfColor.fromHex('#000000'),
                              font: ralewayMedium
                            )
                          )
                        ) 
                      )
                    ),
                    pw.Positioned(
                        bottom: 25,
                        right: 25,
                        left: 25,
                        child: pw.Row(
                            crossAxisAlignment: pw.CrossAxisAlignment.end,
                            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                            children: [
                              pw.Expanded(
                                flex: 1,
                                child: pw.Container()
                              ),
                              // pw.Expanded(
                              //   flex: 1,
                              //   child: pw.Row(
                              //       mainAxisAlignment: pw.MainAxisAlignment.start,
                              //       crossAxisAlignment: pw.CrossAxisAlignment.center,
                              //       children: [
                              //         pw.Text(
                              //             'UN PROGETTO',
                              //             overflow: pw.TextOverflow.visible,
                              //             style: pw.TextStyle(
                              //
                              //                 fontSize: 11,
                              //                 color: PdfColor.fromHex('#5A5A5A'),
                              //                 font: ralewayMedium
                              //             )
                              //         ),
                              //         pw.SizedBox(
                              //             width: 10
                              //         ),
                              //         pw.Image(
                              //             immaginaLogoGreem,
                              //             height: 59,
                              //             width: 160,
                              //             fit: pw.BoxFit.contain
                              //         ),
                              //         pw.SizedBox(
                              //             width: 30
                              //         ),
                              //         pw.Text(
                              //             'PER',
                              //             overflow: pw.TextOverflow.visible,
                              //             style: pw.TextStyle(
                              //               // height: 14,
                              //                 fontSize: 11,
                              //                 color: PdfColor.fromHex('#5A5A5A'),
                              //                 font: ralewayMedium
                              //             )
                              //         ),
                              //         pw.SizedBox(
                              //             width: 30
                              //         ),
                              //         pw.Container(
                              //           width: 162,
                              //           height: 59,
                              //           color: PdfColor.fromHex('#D9D9D9'),
                              //         )
                              //
                              //       ]
                              //   )
                              // ),


                              pw.Stack(
                                  children: [
                                    pw.Container(
                                      width: 885,
                                      height: 577,
                                      decoration: pw.BoxDecoration(
                                          color: PdfColors.amber,
                                          borderRadius: pw.BorderRadius.circular(15),
                                          image: pw.DecorationImage(image: photoBeforeAfterPathsImages[i]['after'], fit: pw.BoxFit.cover )
                                      ),
                                    ),
                                    pw.Positioned(
                                        top: 15,
                                        left: 15,
                                        child: pw.Container(
                                            height: 45,
                                            padding: pw.EdgeInsets.symmetric( horizontal: 15 ),
                                            decoration: pw.BoxDecoration(
                                              color: PdfColors.white,
                                              borderRadius: pw.BorderRadius.circular(22.5),
                                            ),
                                            child: pw.Center(
                                                child: pw.Text(
                                                    'Attuale',
                                                    overflow: pw.TextOverflow.visible,
                                                    style: pw.TextStyle(
                                                      // height: 14,
                                                        fontSize: 25,
                                                        color: PdfColor.fromHex('#000000'),
                                                        font: ralewayMedium
                                                    )
                                                )
                                            )
                                        )
                                    ),
                                  ]
                              ),

                            ]
                        )
                    )

                    
                    
                  ]
                )
              )
              
            ]
          )
        )
      );
    }

    progress?.value = 0.60;
    
    
    // Project Images
    counterAI = 0;
    for (var i = 0; i < photoDayTimePathsImages.length; i++) {
      
      if( photoDayTimePathsImages[i] != '' ) pdfPage1.add(
        pw.Container(
          width: 1920,
          height: 1280,
          child: pw.Stack(
            children: [
              pw.Container(
                width: 1920,
                height: 1280,
                child: pw.Stack(
                  children: [
                    pw.Container(
                      width: 1920,
                      height: 1280,
                      decoration: pw.BoxDecoration(
                        color: PdfColors.amber,
                        // borderRadius: pw.BorderRadius.circular(10),
                        image: pw.DecorationImage(image: photoDayTimePathsImages[i], fit: pw.BoxFit.cover )
                      ),
                      
                    ), 
                    pw.Positioned(
                      left: 15,
                      top: 15,
                      child: pw.Container(
                        height: 45,
                        padding: pw.EdgeInsets.symmetric( horizontal: 15 ),
                        decoration: pw.BoxDecoration(
                          color: PdfColors.white,
                          borderRadius: pw.BorderRadius.circular(22.5),
                        ),
                        child: pw.Center(
                          child: pw.Text(
                            'Stato di progetto',
                            overflow: pw.TextOverflow.visible,
                            style: pw.TextStyle(
                              fontSize: 25,
                              color: PdfColor.fromHex('#000000'),
                              font: ralewayMedium
                            )
                          )
                        ) 
                      )
                    ),
                    pw.Positioned(
                      right: 15,
                      bottom: 15,
                      child: pw.Row(
                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                        mainAxisAlignment: pw.MainAxisAlignment.center,
                        children: [
                          pw.Container(
                            height: 80,
                            width: 300,
                            padding: pw.EdgeInsets.symmetric( horizontal: 15 ),
                            decoration: pw.BoxDecoration(
                              color: PdfColors.white,
                              borderRadius: pw.BorderRadius.circular(12),
                            ),
                            child: pw.Center(
                              child: pw.Row(
                                crossAxisAlignment: pw.CrossAxisAlignment.center,
                                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                children: [
                                  pw.Text(
                                  'UN PROGETTO',
                                    overflow: pw.TextOverflow.visible,
                                    style: pw.TextStyle(
                                      // height: 14,  
                                      fontSize: 11,
                                      color: PdfColor.fromHex('#5A5A5A'),
                                      font: ralewayMedium
                                    )
                                  ),
                                  pw.Image( 
                                    immaginaLogoGreem,
                                    height: 59,
                                    width: 160,
                                    fit: pw.BoxFit.contain
                                  ),  
                                  
                                ]
                              )
                              
                            ) 
                          ),
                          pw.Container(
                            height: 80,
                            width: 300,
                            margin: pw.EdgeInsets.only( left: 15 ),
                            padding: pw.EdgeInsets.symmetric( horizontal: 15 ),
                            decoration: pw.BoxDecoration(
                              color: PdfColors.white,
                              borderRadius: pw.BorderRadius.circular(12),
                            ),
                            child: pw.Center(
                              child: pw.Row(
                                crossAxisAlignment: pw.CrossAxisAlignment.center,
                                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                children: [
                                  pw.Text(
                                    'PER',
                                    overflow: pw.TextOverflow.visible,
                                    style: pw.TextStyle(
                                      // height: 14,  
                                      fontSize: 11,
                                      color: PdfColor.fromHex('#5A5A5A'),
                                      font: ralewayMedium
                                    )
                                  ),
                                  pw.Image( 
                                    agencyLogo,
                                    height: 59,
                                    width: 160,
                                    fit: pw.BoxFit.contain
                                  ),  
                                  
                                ]
                              )
                              
                            ) 
                          )
                        ]
                      )
                      
                      
                    ),
                  ]
                )
              )
              
            ]
          )
        )
      );
    }

    progress?.value = 0.70;
    
    pdfPage1.add(
      pw.Container(
        width: 1920,
        height: 1280,
        child: pw.Stack(
          children: [
            pw.Positioned(
              top: 0,
              left: 0,
              child: pw.Container(
                width: 1920,
                height: 1280,
                decoration: pw.BoxDecoration(
                  image: pw.DecorationImage(image: backgroundImage, fit: pw.BoxFit.cover)
                )
              )
            ),
            pw.Positioned(
              top: 0,
              left: 0,
              child: pw.Container(
                padding: pw.EdgeInsets.symmetric(vertical: 40, horizontal: 45),
                width: 1920,
                height: 1280,
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  mainAxisAlignment: pw.MainAxisAlignment.center,
                  mainAxisSize: pw.MainAxisSize.max,
                  children: [
                    pw.Image( 
                      immaginaLogoImage,
                      height: 97,
                    ),
                    pw.SizedBox(height: 50),

                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.center,
                      children: [
                        pw.Container(
                          decoration: pw.BoxDecoration(
                            borderRadius: pw.BorderRadius.circular(20),
                            color: PdfColors.white
                          ),
                          width: 565,
                          height: 550,
                          child: pw.Center(
                            child: pw.Column(
                              mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
                              children: [
                                pw.Column(
                                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                                  children: [
                                    pw.Image( agencyLogo , height: 70 ),
                                    pw.SizedBox(height:5),
                                    pw.Text(
                                      agencyData.name!,
                                      style: pw.TextStyle(
                                        fontSize: 22,
                                        color: PdfColors.black,
                                        font: ralewayBold
                                      )
                                    ),
                                  ]
                                ),
                                
                                pw.Column(
                                  children: [
                                    pw.Text(
                                      'Ti interessa questo immobile?',
                                      style: pw.TextStyle(
                                        fontSize: 30,
                                        color: PdfColors.black,
                                        font: ralewayBold
                                      )
                                    ),
                                    pw.SizedBox(height: 10),
                                    pw.Text(
                                      'Contatta l’agenzia',
                                      style: pw.TextStyle(
                                        fontSize: 28,
                                        color: PdfColors.black,
                                        font: ralewayMedium 
                                      )
                                    ),
                                  ]
                                ),
                                pw.Row(
                                  children: [
                                    pw.Expanded(
                                      child: pw.Column(
                                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                                        children: [
                                          pw.Image( phoneIcon, height: 40 ),
                                          pw.SizedBox(height: 10),
                                          pw.Text(
                                            agencyData.phone!,
                                            style: pw.TextStyle(
                                              fontSize: 17,
                                              color: PdfColors.black,
                                              font: ralewayMedium 
                                            )
                                          ),
                                        ]
                                      )
                                    ),
                                    pw.Expanded(
                                      child: pw.Column(
                                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                                        children: [
                                          pw.Image( locationIcon, height: 40 ),
                                          pw.SizedBox(height: 10),
                                          pw.Text(
                                            agencyData.address! +', '+ agencyData.city!,
                                            textAlign: pw.TextAlign.center,
                                            style: pw.TextStyle(
                                              fontSize: 17,
                                              color: PdfColors.black,
                                              font: ralewayMedium,
                                            )
                                          ),
                                        ]
                                      )
                                    ),
                                  ]
                                )
                              ]
                            )
                          )
                        ),
                        pw.SizedBox(width: 50),
                        pw.Container(
                          decoration: pw.BoxDecoration(
                            borderRadius: pw.BorderRadius.circular(20),
                            color: PdfColors.white
                          ),
                          width: 565,
                          height: 550,
                          child: pw.Center(
                            child: pw.Column(
                              mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
                              children: [
                                pw.Image( risLogoImage , height: 70 ),
                                pw.Column(
                                  children: [
                                    pw.Text(
                                      'Ti piace questa ristrutturazione?',
                                      style: pw.TextStyle(
                                        fontSize: 30,
                                        color: PdfColor.fromHex('#489B79'),
                                        font: ralewayBold
                                      )
                                    ),
                                    pw.SizedBox(height: 10),
                                    pw.Text(
                                      'Contatta Newarc',
                                      style: pw.TextStyle(
                                        fontSize: 28,
                                        color: PdfColor.fromHex('#489B79'),
                                        font: ralewayMedium 
                                      )
                                    ),
                                  ]
                                ),
                                pw.Row(
                                  children: [
                                    pw.Expanded(
                                      child: pw.Column(
                                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                                        children: [
                                          pw.Image( phoneIcon, height: 40 ),
                                          pw.SizedBox(height: 10),
                                          pw.Text(
                                            '************',
                                            style: pw.TextStyle(
                                              fontSize: 17,
                                              color: PdfColors.black,
                                              font: ralewayMedium 
                                            )
                                          ),
                                        ]
                                      )
                                    ),
                                    pw.Expanded(
                                      child: pw.Column(
                                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                                        children: [
                                          pw.Image( locationIcon, height: 40 ),
                                          pw.SizedBox(height: 10),
                                          pw.Text(
                                            'Corso Ferrucci 36, Torino',
                                            style: pw.TextStyle(
                                              fontSize: 17,
                                              color: PdfColors.black,
                                              font: ralewayMedium 
                                            )
                                          ),
                                        ]
                                      )
                                    ),
                                  ]
                                )
                              ]
                            )
                          )
                        ),
                      ]
                    ),

                    pw.SizedBox(height: 50),
                    
                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.center,
                      children: [
                        pw.Text(
                          'Newarc Srl',
                          style: pw.TextStyle(
                            fontSize: 16,
                            color: PdfColor.fromHex('#ffffff'),
                            font: ralewayMedium
                          )
                        ),
                        pw.SizedBox(height: 15),

                        pw.Text(
                          'Sede Legale: Via Vittorio Emanuele II 29, Chieri • Sede Operativa: Corso Ferrucci 36, Torino • P.Iva 12533550013',
                          style: pw.TextStyle(
                            fontSize: 14,
                            color: PdfColor.fromHex('#ffffff'),
                            font: ralewayMedium
                          )
                        ),
                        pw.SizedBox(height: 3),
                        pw.Text(
                          '<EMAIL> • 011 02 63 850',
                          style: pw.TextStyle(
                            fontSize: 14,
                            color: PdfColor.fromHex('#ffffff'),
                            font: ralewayMedium
                          )
                        ),

                      ]
                    )




                  ]

                )
              )
            )
            
          ]
        )
        
      )
    );
    progress?.value = 0.75;

    pdf.addPage(
      pw.MultiPage(
        theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),

        pageFormat: PdfPageFormat(1920, 1280),
        orientation: pw.PageOrientation.landscape,

        margin: const pw.EdgeInsets.all(0),
        build: (pw.Context context) => pdfPage1,
      )
    );
    progress?.value = 0.85;

    String image_filename = projectDetails['title']!.replaceAll(" ", "_")+'_'+adData.city!;
    String zip_filename = projectDetails['title']!.replaceAll(" ", "_");
    progress?.value = 0.95;
    debugPrint('Downloading...');
    final pdfBytes = await pdf.save();
    await js.context.callMethod('convertPdfToJpeg', [pdfBytes, zip_filename,image_filename]);
    progress?.value = 1.0;
  } catch (e,s) {
    print({e,s});
  }
}

