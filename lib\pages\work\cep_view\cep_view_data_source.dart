
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/projectEconomic.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/tab/common_icon_button.dart';
import 'package:newarc_platform/widget/UI/tab/tag_widget.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';


class CEPViewDataSource extends DataTableSource {
  List<ProjectEconomic> projects;
  BuildContext context;
  Function(ProjectEconomic p) onEditTap;
  Function(ProjectEconomic p) onDownloadTap;
  Function(ProjectEconomic p) onTrashTap;
  Function(ProjectEconomic p) onAddressTap;

  CEPViewDataSource({
    required this.projects,
    required this.context,
    required this.onEditTap,
    required this.onDownloadTap,
    required this.onTrashTap,
    required this.onAddressTap,
  });

  @override
  DataRow? getRow(int index) {
    if (index < projects.length) {
      final economic = projects[index];

      String createdDate = '';
      if (economic.created! > 0) {
        DateTime commentedOn = DateTime.fromMillisecondsSinceEpoch(economic.created!);
        createdDate = (commentedOn.day > 9 ? commentedOn.day.toString() : '0' + commentedOn.day.toString()) +
            '/' +
            (commentedOn.month > 9 ? commentedOn.month.toString() : '0' + commentedOn.month.toString()) +
            '/' +
            commentedOn.year.toString();
      }
      return DataRow(
        cells: [
          DataCell(
            Stack(
              clipBehavior: Clip.none,
              children: [
                if (economic.isAssigned == true)
                  Positioned(
                    top: -18,
                    child: TagWidget(
                      statusColor: Theme.of(context).primaryColor,
                      text: "Progetto",
                    ),
                  ),
                NarLinkWidget(
                  text: economic.address ?? "",
                  textColor: Colors.black,
                  fontWeight: '700',
                  fontSize: 12,
                  onClick: () {
                    onAddressTap(economic);
                  },
                ),
              ],
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: createdDate,
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),

          ),
          DataCell(
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButtonWidget(
                  onTap: () {
                    onEditTap(economic);
                  },
                  iconPadding: EdgeInsets.all(8),
                  isSvgIcon: false,
                  icon: 'assets/icons/edit.png',
                  iconColor: AppColor.greyColor,
                ),
                SizedBox(width: 4),
                IconButtonWidget(
                  iconPadding: EdgeInsets.all(8),
                  onTap: () {
                    onDownloadTap(economic);
                  },
                  isSvgIcon: false,
                  icon: 'assets/icons/download.png',
                  iconColor: AppColor.greyColor,
                ),
                SizedBox(width: 4),
                IconButtonWidget(
                  iconPadding: EdgeInsets.all(6),
                  onTap: () {
                    onTrashTap(economic);
                  },
                  isSvgIcon: false,
                  icon: 'assets/icons/trash-process.png',
                  iconColor: AppColor.redColor,
                ),
              ],
            ),
          ),
        ],
      );
    }

    return null;
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => projects.length;

  @override
  int get selectedRowCount => 0;
}
