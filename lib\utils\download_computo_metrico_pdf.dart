
import 'dart:developer';

import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'dart:html' as html;

import '../classes/renovationQuotation.dart';

NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 2);

Future<Uint8List> _loadImage(String path) async {
  final ByteData data = await rootBundle.load(path);
  return data.buffer.asUint8List();
}



Future<void> computoMatrixPdfDesign({
  required RenovationQuotation  quotation,
  required Map<String, Map<String, List<Map<dynamic, dynamic>>>> renovationData,
  required Map subCategoryTotal,
  required List<String> selectedLavoriSubCategory,
  required List<String> selectedFornitureSubCategory
})async{
  try{

    double total = 0;

    //? ------------------------------ Fonts and Images-----------------------------
    final ByteData fontMediumData = await rootBundle.load('assets/fonts/Raleway-Medium.ttf');
    final ralewayMedium = pw.Font.ttf(fontMediumData.buffer.asByteData());

    final ByteData fontBoldData = await rootBundle.load('assets/fonts/Raleway-Bold.ttf');
    final ralewayBold = pw.Font.ttf(fontBoldData.buffer.asByteData());

    final Uint8List coverbgImageData =
        await _loadImage('assets/renovation-quotation-pdf-bg.jpg');
    final coverbg = pw.MemoryImage(coverbgImageData);

    final Uint8List coverLogoImageData = await _loadImage('assets/rq-pdf-cover-logo.png');
    final coverLogo = pw.MemoryImage(coverLogoImageData);





    //?------------------------------------------------------------------------

    final pdf = pw.Document();

    //? -----------------------------------------------------------
    //? ------------------------------ Cover Page -----------------------------
    //? -----------------------------------------------------------

    pdf.addPage(
      pw.Page(
        theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(0),
        build: (pw.Context context) => pw.Center(
            child: pw.Stack(children: [
              pw.Positioned(
                  child: pw.Image(
                    coverbg,
                    fit: pw.BoxFit.cover,
                  ),
                  left: 0,
                  top: 0,
                  right: 0,
                  bottom: 0),
              pw.Container(
                  width: double.infinity,
                  height: double.infinity,
                  padding: pw.EdgeInsets.only(top: 100, left: 50, right: 25, bottom: 30),
                  child: pw.Column(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Container(
                          child: pw.Column(
                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                              children: [
                                pw.Text('COMPUTO METRICO',
                                    style: pw.TextStyle(
                                        fontSize: 20,
                                        letterSpacing: 1,
                                        font: ralewayMedium,
                                        color: PdfColor.fromHex('ffffff'))),
                                pw.SizedBox(height: 10),
                                //---------- Street Address----------
                                pw.Text("${quotation.renovationContact!.addressInfo!.streetName!} ${quotation.renovationContact!.addressInfo!.streetNumber!}",
                                    style: pw.TextStyle(
                                        font: ralewayBold,
                                        fontSize: 40,
                                        color: PdfColor.fromHex('ffffff'))),
                              ])),
                      pw.Row(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            pw.Expanded(child: pw.Image(coverLogo, height: 56)),
                            pw.SizedBox(width: 45),
                            pw.Expanded(
                                child: pw.Column(
                                    crossAxisAlignment:
                                    pw.CrossAxisAlignment.start,
                                    children: [
                                      pw.Row(children: [
                                        pw.Expanded(
                                            child: pw.Column(
                                                mainAxisAlignment:
                                                pw.MainAxisAlignment.start,
                                                crossAxisAlignment:
                                                pw.CrossAxisAlignment.start,
                                                children: [
                                                  pw.Text('DATA',
                                                      style: pw.TextStyle(
                                                          fontSize: 8,
                                                          letterSpacing: 1,
                                                          color:
                                                          PdfColor.fromHex('000'))),
                                                  pw.SizedBox(height: 5),

                                                  //-----created date
                                                  pw.Text(
                                                      "${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}",
                                                      style: pw.TextStyle(
                                                          font: ralewayBold,
                                                          fontSize: 11,
                                                          color:
                                                          PdfColor.fromHex('000'))),
                                                ])),
                                        pw.SizedBox(width: 1),
                                        pw.Expanded(
                                            child: pw.Column(
                                                mainAxisAlignment:
                                                pw.MainAxisAlignment.start,
                                                crossAxisAlignment:
                                                pw.CrossAxisAlignment.start,
                                                children: [
                                                  pw.Text('CODICE',
                                                      style: pw.TextStyle(
                                                          fontSize: 8,
                                                          letterSpacing: 1,
                                                          color:
                                                          PdfColor.fromHex('000000'))),
                                                  pw.SizedBox(height: 5),

                                                  //---------- quotation code
                                                  pw.Text(
                                                      "${quotation.code!}_V${quotation.version!}_R${quotation.revision!}",
                                                      style: pw.TextStyle(
                                                          font: ralewayBold,
                                                          fontSize: 11,
                                                          color:
                                                          PdfColor.fromHex('000000'))),
                                                ]))
                                      ]),
                                    ]))
                          ])
                    ],
                  )),
            ])),
      ),
    );

    //? -----------------------------------------------------------
    //? ------------------------------ computo matrico page -----------------------------
    //? -----------------------------------------------------------

    List<pw.Widget> pdfDataWidgetsForComputoMatrico = [];

    //---------- page title ------------
    pdfDataWidgetsForComputoMatrico.add(pw.Text('Computo Metrico',
        style: pw.TextStyle(
            font: ralewayBold,
            fontSize: 18,
            color: PdfColor.fromHex('000'))));
    pdfDataWidgetsForComputoMatrico.add(
      pw.SizedBox(height: 20),
    );


    //------sub category Wise Data

    renovationData.keys.map((category) {

      if(category == "C - Lavori interni" || category == "M - Forniture"){

        List<String> filteredSubcategories = [];

        if (category == "C - Lavori interni") {
          filteredSubcategories = renovationData[category]!.keys
              .where((subcategory) => selectedLavoriSubCategory.contains(subcategory))
              .toList();
        } else if (category == "M - Forniture") {
          filteredSubcategories = renovationData[category]!.keys
              .where((subcategory) => selectedFornitureSubCategory.contains(subcategory))
              .toList();
        }

        if(filteredSubcategories.isNotEmpty){
          pdfDataWidgetsForComputoMatrico.add(
            pw.Container(
                width: double.infinity,
                padding: pw.EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex('F2FAF7'),
                  borderRadius: pw.BorderRadius.circular(7),
                ),
                child: pw.Text(
                    category,
                    style: pw.TextStyle(
                        font: ralewayBold,
                        fontSize: 13,
                        color: PdfColor.fromHex('489B79')))
            ),
          );
        }


        pdfDataWidgetsForComputoMatrico.add(
          pw.SizedBox(height: 10),
        );

        filteredSubcategories.forEach((subcategory) {
          pdfDataWidgetsForComputoMatrico.add(pw.Container(
            padding: pw.EdgeInsets.only(left: 15, right: 15, bottom: 5, top: 3),
            child: pw.Text(
              subcategory,
              style: pw.TextStyle(
                font: ralewayBold,
                fontSize: 11,
                color: PdfColor.fromHex('489B79'),
              ),
            ),
          ));

          pdfDataWidgetsForComputoMatrico.add(headRow(ralewayMedium, ralewayBold));

          renovationData[category]![subcategory]!.forEach((entry) {
            pdfDataWidgetsForComputoMatrico.add(dataRow(category, entry, ralewayMedium, ralewayBold));
          });
        });

        pdfDataWidgetsForComputoMatrico.add(pw.SizedBox(
          height: 10,
        ));
      }
    }).toList();



    pdf.addPage(pw.MultiPage(
      theme: pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),

      pageFormat: PdfPageFormat.a4,
      footer: (context) => pw.Container(
        // color: PdfColors.amber,
          height: 110,
          child: pw.Column(
            mainAxisAlignment: pw.MainAxisAlignment.start,
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.SizedBox(height: 10),
              pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  children: [
                    pw.Text("Firma per accettazione",
                        overflow: pw.TextOverflow.visible,
                        style: pw.TextStyle(
                            font: ralewayBold,
                            fontSize: 11,
                            color: PdfColors.black)),
                    pw.Container(
                        height: 1,
                        width: 250,
                        margin: pw.EdgeInsets.only(top: 30),
                        decoration: pw.BoxDecoration(
                          color: PdfColors.black,
                        ))
                  ]),
              pw.SizedBox(height: 30),
              pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    pw.Expanded(
                      flex: 1,
                      child: pw.Image(coverLogo, height: 25),
                    ),
                    pw.Expanded(
                      flex: 5,
                      child: pw.Padding(
                          padding: pw.EdgeInsets.symmetric(horizontal: 20),
                          child: pw.Text(
                              "NEWARC SRL - Sede Legale Via Vittorio Emanuele II 29, Chieri - Sede Operativa Corso Ferrucci 36, Torino - P.Iva 12533550013 - <EMAIL> - 011.02.63.850",
                              overflow: pw.TextOverflow.visible,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 7,
                                  color: PdfColor.fromHex('6E6E6E')))),
                    ),
                    pw.Expanded(
                        flex: 1,
                        child: pw.Text('${context.pageNumber}',
                            textAlign: pw.TextAlign.right,
                            style: pw.TextStyle(
                                font: ralewayBold,
                                fontSize: 13,
                                color: PdfColor.fromHex('000'))))
                  ]),
            ],
          )),
      margin: const pw.EdgeInsets.all(25),
      build: (pw.Context context) => pdfDataWidgetsForComputoMatrico,
    ));

    //? -----------------------------------------------------------
    //? ------------------------------ Prezzi -----------------------------
    //? -----------------------------------------------------------

    List<pw.Widget> pdfDataWidgetsForPrezzi = [];

    //---------- page title ------------
    pdfDataWidgetsForPrezzi.add(pw.Text('Prezzi',
        style: pw.TextStyle(
            font: ralewayBold,
            fontSize: 18,
            color: PdfColor.fromHex('000'))));
    pdfDataWidgetsForPrezzi.add(
      pw.SizedBox(height: 20),
    );

    pdfDataWidgetsForPrezzi.add(
      pw.Container(
        padding: pw.EdgeInsets.all(10),
        decoration: pw.BoxDecoration(
          border: pw.Border.all(
            color: PdfColor.fromHex('D3D3D3'), // Green border color
            width: 1, // Border thickness
          ),
          borderRadius: pw.BorderRadius.circular(7), // Rounded corners
        ),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            ...renovationData.keys
                .where((category) => category == "C - Lavori interni" || category == "M - Forniture")
                .expand((category) {
              final selectedSubList = category == "C - Lavori interni"
                  ? selectedLavoriSubCategory
                  : selectedFornitureSubCategory;

              return renovationData[category]!.keys
                  .where((subcategory) => selectedSubList.contains(subcategory))
                  .map((subcategory) {
                return pw.Container(
                  width: double.infinity,
                  margin: pw.EdgeInsets.symmetric(vertical: 5),
                  padding: pw.EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex('F2FAF7'),
                    borderRadius: pw.BorderRadius.circular(7),
                  ),
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                    children: [
                      pw.Text(
                        subcategory,
                        style: pw.TextStyle(
                          font: ralewayMedium,
                          fontSize: 10,
                          color: PdfColor.fromHex('489B79'),
                        ),
                      ),
                      pw.Text(
                        "...........................€",
                        style: pw.TextStyle(
                          font: ralewayMedium,
                          fontSize: 10,
                          color: PdfColor.fromHex('489B79'),
                        ),
                      ),
                    ],
                  ),
                );
              });
            }),
          ],
        )
      ),
    );

    pdf.addPage(pw.MultiPage(
      theme:
      pw.ThemeData(defaultTextStyle: pw.TextStyle(font: ralewayMedium)),
      pageFormat: PdfPageFormat.a4,
      footer: (context) => pw.Container(
          height: 110,
          child: pw.Column(
            mainAxisAlignment: pw.MainAxisAlignment.start,
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.SizedBox(height: 10),
              pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  children: [
                    pw.Text("Firma per accettazione",
                        overflow: pw.TextOverflow.visible,
                        style: pw.TextStyle(
                            font: ralewayBold,
                            fontSize: 11,
                            color: PdfColors.black)),
                    pw.Container(
                        height: 1,
                        width: 250,
                        margin: pw.EdgeInsets.only(top: 30),
                        decoration: pw.BoxDecoration(
                          color: PdfColors.black,
                        ))
                  ]),
              pw.SizedBox(height: 30),
              pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    pw.Expanded(
                      flex: 1,
                      child: pw.Image(coverLogo, height: 25),
                    ),
                    pw.Expanded(
                      flex: 5,
                      child: pw.Padding(
                          padding: pw.EdgeInsets.symmetric(horizontal: 20),
                          child: pw.Text(
                              "NEWARC SRL - Sede Legale Via Vittorio Emanuele II 29, Chieri - Sede Operativa Corso Ferrucci 36, Torino - P.Iva 12533550013 - <EMAIL> - 011.02.63.850",
                              overflow: pw.TextOverflow.visible,
                              style: pw.TextStyle(
                                  font: ralewayMedium,
                                  fontSize: 7,
                                  color: PdfColor.fromHex('6E6E6E')))),
                    ),
                    pw.Expanded(
                        flex: 1,
                        child: pw.Text('${context.pageNumber}',
                            textAlign: pw.TextAlign.right,
                            style: pw.TextStyle(
                                font: ralewayBold,
                                fontSize: 13,
                                color: PdfColor.fromHex('000'))))
                  ]),
            ],
          )),
      margin: const pw.EdgeInsets.all(25),
      build: (pw.Context context) => pdfDataWidgetsForPrezzi,
    ));


    //? -----------------------------------------------------------
    //? ------------------------------ Save and Download PDF -----------------------------
    //? -----------------------------------------------------------

    // Save PDF to bytes
    final pdfBytes = await pdf.save();
    // Create a Blob from PDF bytes
    final blob = html.Blob([pdfBytes], 'application/pdf');
    // Create a link element
    final url = html.Url.createObjectUrlFromBlob(blob);
    final anchor = html.AnchorElement(href: url)
    //------- quotation code
      ..setAttribute('download', "${quotation.code!}_V${quotation.version!}_R${quotation.revision!}" + '.pdf')
      ..click();

    // Clean up
    html.Url.revokeObjectUrl(url);

  }catch(error,stackTrace){
    log("Error While Crating Computo Matrix Pdf ${error.toString()}");
    log("StackTrace While Crating Computo Matrix Pdf ${stackTrace.toString()}");
  }
}

pw.Widget headRow(ralewayMedium, ralewayBold) {
  return pw.Container(
    margin: pw.EdgeInsets.only(bottom: 5),
    padding: const pw.EdgeInsets.only(left: 15, right: 15, bottom: 0, top: 5),
    child: pw.Row(
      mainAxisSize: pw.MainAxisSize.max,
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 6,
          child: pw.Container(
          ),
        ),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
          flex: 1,
          child: pw.Text('Unità',
              textAlign: pw.TextAlign.right,

              style: pw.TextStyle(fontSize: 8, color: PdfColor.fromHex('6E6E6E'), letterSpacing: 0.3 )),
        ),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
            flex: 1,
            child: pw.Text('Quantità',
                textAlign: pw.TextAlign.right,
                style: pw.TextStyle(
                    fontSize: 8, color: PdfColor.fromHex('6E6E6E'), letterSpacing: 0.3))),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
            flex: 1,
            child: pw.Text('Prezzo',
                textAlign: pw.TextAlign.right,
                style: pw.TextStyle(
                    fontSize: 8, color: PdfColor.fromHex('6E6E6E'), letterSpacing: 0.3))),
      ],
    ),
  );
}

pw.Widget dataRow(String category, Map rowData, ralewayMedium, ralewayBold) {
  return pw.Container(
    margin: pw.EdgeInsets.only(bottom: 5),
    padding: const pw.EdgeInsets.only(left: 15, right: 15, bottom: 0, top: 0),
    child: pw.Row(
      mainAxisSize: pw.MainAxisSize.max,
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 6,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            mainAxisAlignment: pw.MainAxisAlignment.start,
            children: [
              pw.Text(
                  rowData['code'] +
                      (rowData['code'] != '' ? ' - ' : '') +
                      rowData['title'],
                  style: pw.TextStyle(fontSize: 8, color: PdfColor.fromHex('000'))),
              pw.SizedBox(
                height: 5,
              ),
              pw.Padding(
                padding: const pw.EdgeInsets.only(right: 15),
                child: pw.Text(rowData['comment'],
                    style: pw.TextStyle(
                        fontSize: 8,
                        color: PdfColor.fromHex('6e6e6e'),
                        letterSpacing: 0.3)),
              ),
            ],
          ),
        ),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
          flex: 1,
          child: pw.Text(rowData['measurementUnit'],
              textAlign: pw.TextAlign.right,
              style: pw.TextStyle(fontSize: 9, color: PdfColor.fromHex('000'))),
        ),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
            flex: 1,
            child: pw.Text(rowData['quantity'].toString(),
                textAlign: pw.TextAlign.right,
                style: pw.TextStyle(
                    fontSize: 9, color: PdfColor.fromHex('000'))),
        ),
        pw.SizedBox(
          width: 5,
        ),
        pw.Expanded(
          flex: 1,
          child: pw.Text("..............",
              textAlign: pw.TextAlign.right,
              style: pw.TextStyle(
                  fontSize: 9, color: PdfColor.fromHex('000'))),
        ),
      ],
    ),
  );
}