import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart'; // Ensure you import the flutter_svg package
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class MultiSelectDropdownWidget extends StatefulWidget {
  final List<dynamic> options;
  final Function(List<dynamic>)? onChanged;
  final Function? onFieldTap;
  final String? hint;
  final String? disabledHint;
  final bool? autoFocus;
  final List<dynamic>? initialValue;
  final String? validationType;
  final String? parametersValidate;
  final String? label;
  final Color? labelColor;
  final int? flex;
  final double? iconSize;
  final EdgeInsetsGeometry? buttonPadding;
  final bool? enabled;
  final String? dialogTitle;

  MultiSelectDropdownWidget({
    Key? key,
    this.flex = 1,
    this.label = '',
    this.labelColor = const Color(0xff696969),
    required this.options,
    this.hint,
    this.disabledHint,
    this.initialValue,
    this.autoFocus,
    this.onChanged,
    this.onFieldTap,
    this.validationType,
    this.parametersValidate,
    this.iconSize = 24,
    this.buttonPadding =
    const EdgeInsets.only(top: 17, bottom: 17, left: 21.0, right: 8.0),
    this.enabled = true,
    this.dialogTitle
  }) : super(key: key);

  @override
  _MultiSelectDropdownWidgetState createState() =>
      _MultiSelectDropdownWidgetState();
}

class _MultiSelectDropdownWidgetState extends State<MultiSelectDropdownWidget> {
  List<dynamic> selectedOptions = [];

  @override
  void initState() {
    super.initState();
    selectedOptions = widget.initialValue ?? [];
  }

  void _showCustomDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Material(
          color: Colors.transparent,
          child: Center(
            child: Container(
              width: 800,
              height: 450,
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 10,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: Stack(
                // Use Stack to position the close button
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      NarFormLabelWidget(
                        label: widget.dialogTitle ??'Seleziona lavorazioni',
                        textColor: Colors.black,
                        fontSize: 18,
                        fontWeight: '800',
                      ),
                      SizedBox(height: 10),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            children: widget.options.map((option) {
                              return StatefulBuilder(
                                builder: (BuildContext context,
                                    StateSetter setState) {

                                  return CheckboxListTile(
                                    visualDensity: VisualDensity(vertical: -4),
                                    contentPadding: EdgeInsets.symmetric(
                                        vertical: 0, horizontal: 0),
                                    controlAffinity:
                                        ListTileControlAffinity.leading,
                                    title: NarFormLabelWidget(
                                      label: option['label'],
                                      textColor: Colors.black,
                                      fontSize: 14,
                                      fontWeight: '600',
                                      overflow: TextOverflow
                                          .visible, // Prevent ellipsis
                                    ),
                                    // value: selectedOptions.contains(option),
                                    value: selectedOptions.any((item) => item['value'] == option['value']),
                                    onChanged: (bool? value) {
                                      setState(() {
                                        if (value!) {
                                          selectedOptions.add(option);
                                        } else {
                                          selectedOptions.remove(option);
                                        }
                                      });
                                      if (widget.onChanged != null) {
                                        widget.onChanged!(selectedOptions);
                                      }
                                    },
                                  );
                                },
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          BaseNewarcButton(
                              buttonText: "Conferma",
                              onPressed: () {
                                Navigator.of(context).pop();
                              }),
                        ],
                      )
                    ],
                  ),
                  Positioned(
                    top: 10,
                    right: 15,
                    child: Container(
                      height: 15,
                      width: 15,
                      child: MouseRegion(
                        cursor: SystemMouseCursors.click,
                        child: GestureDetector(
                          child: SvgPicture.asset(
                            'assets/icons/close-popup.svg',
                            width: 15,
                          ),
                          onTap: () {
                            Navigator.pop(context);
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: !widget.enabled! ? null : () => _showCustomDialog(context),
      child: InputDecorator(
        decoration: InputDecoration(
          border: OutlineInputBorder(
            borderSide: BorderSide(color: Color(0xffdbdbdb)),
            borderRadius: BorderRadius.circular(8),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Color(0xffdbdbdb)),
            borderRadius: BorderRadius.circular(8),
          ),
          contentPadding: const EdgeInsets.all(3),
          hintText: selectedOptions.isEmpty ? widget.hint : null,
          labelText: widget.label,
          labelStyle: TextStyle(
            color: widget.labelColor,
          ),
          suffixIcon: Icon(Icons.arrow_drop_down),
        ),
        child: selectedOptions.isEmpty
            ? null
            : Wrap(
                children: selectedOptions.map((option) {
                  return Chip(
                    side: BorderSide(color: Color(0xffdbdbdb)),
                    labelStyle: TextStyle(fontFamily: 'Raleway-600'),
                    backgroundColor: Colors.white,
                    label: Text(option['label']),
                    onDeleted: () {
                      setState(() {
                        selectedOptions.remove(option);
                      });
                      if (widget.onChanged != null) {
                        widget.onChanged!(selectedOptions);
                      }
                    },
                  );
                }).toList(),
              ),
      ),
    );
  }
}
