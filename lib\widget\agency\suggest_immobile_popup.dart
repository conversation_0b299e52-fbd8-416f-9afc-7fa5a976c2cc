import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/proposedEstate.dart';
import 'package:newarc_platform/pages/agency/acquired_contacts_view.dart';
import 'package:newarc_platform/utils/firestore.dart';

class SuggestImmobilePopup extends StatefulWidget {
  final Agency? agency;
  final AgencyUser? agencyUser;
  final bool isEdit;
  final ProposedEstate? originalProposedEstate;

  const SuggestImmobilePopup({
    required this.agency,
    required this.agencyUser,
    required this.isEdit,
    this.originalProposedEstate,
    Key? key,
  }) : super(key: key);

  @override
  State<SuggestImmobilePopup> createState() => _SuggestImmobilePopupState();
}

class _SuggestImmobilePopupState extends State<SuggestImmobilePopup> {
  TextStyle sectionTitleStyle =
      TextStyle(fontSize: 16, fontWeight: FontWeight.bold);
  ProposedEstate proposedEstate = ProposedEstate();
  final _formKey = GlobalKey<FormState>();
  bool loading = false;
  bool clicked = false;

  final List<String> list = <String>['Sì', 'No'];

  bool showOptionMissing(String? option) {
    try {
      return (option == null || option.isEmpty) && clicked;
    } catch (e,s) {
      // print({e,s});
      return false;
    }
  }

  @override
  void initState() {
    if (widget.originalProposedEstate != null) {
      proposedEstate = widget.originalProposedEstate!;
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 600,
      width: 850, //MediaQuery.of(context).size.width * 0.6,
      margin: EdgeInsets.symmetric(horizontal: 20),
      padding: EdgeInsets.symmetric(horizontal: 30, vertical: 20),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        child: loading
            ? Center(
                child: CircularProgressIndicator(
                    color: Theme.of(context).primaryColor),
              )
            : ListView(
                children: [
                  Form(
                    key: _formKey,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 50.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          SizedBox(height: 20),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                widget.isEdit
                                    ? "Immobile proposto"
                                    : "Proponi un immobile",
                                style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          SizedBox(height: 25),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Indirizzo",
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold),
                                    ),
                                    SizedBox(height: 8),
                                    TextFormField(
                                      enabled:
                                          widget.agencyUser!.role != 'master',
                                      initialValue: proposedEstate.address,
                                      decoration: InputDecoration(
                                          hintText: "Inserisci l'indirizzo"),
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Inserisci un indirizzo valido';
                                        }
                                        return null;
                                      },
                                      onChanged: (address) {
                                        proposedEstate.address = address;
                                      },
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(width: 18),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text("Zona",
                                        style: TextStyle(
                                            fontWeight: FontWeight.bold)),
                                    SizedBox(height: 8),
                                    TextFormField(
                                      enabled:
                                          widget.agencyUser!.role != 'master',
                                      initialValue: proposedEstate.zona,
                                      decoration: InputDecoration(
                                        hintText: "Inserisci la zona",
                                      ),
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Inserisci una zona valida';
                                        }
                                        return null;
                                      },
                                      onChanged: (zona) {
                                        proposedEstate.zona = zona;
                                      },
                                    )
                                  ],
                                ),
                              )
                            ],
                          ),
                          SizedBox(height: 18),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Città",
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold),
                                    ),
                                    SizedBox(height: 8),
                                    TextFormField(
                                      enabled:
                                          widget.agencyUser!.role != 'master',
                                      initialValue: proposedEstate.city,
                                      decoration: InputDecoration(
                                          hintText: "Inserisci la città"),
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Inserisci una città valida';
                                        }
                                        return null;
                                      },
                                      onChanged: (city) {
                                        proposedEstate.city = city;
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 18),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text("Link (facoltativo)",
                                        style: TextStyle(
                                            fontWeight: FontWeight.bold)),
                                    SizedBox(height: 8),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: TextFormField(
                                            enabled: widget.agencyUser!.role !=
                                                'master',
                                            initialValue: proposedEstate.link,
                                            decoration: InputDecoration(
                                                hintText: "Link all'annuncio"),
                                            onChanged: (link) {
                                              proposedEstate.link = link;
                                            },
                                          ),
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 25),
                          Text(
                            "Informazioni aggiuntive",
                            style: TextStyle(
                                fontWeight: FontWeight.bold, fontSize: 18),
                          ),
                          SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Piano",
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold),
                                    ),
                                    SizedBox(height: 8),
                                    TextFormField(
                                      enabled:
                                          widget.agencyUser!.role != 'master',
                                      initialValue: proposedEstate.floor,
                                      decoration: InputDecoration(
                                          hintText: "Inserisci il piano"),
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Inserisci un piano valido';
                                        }
                                        return null;
                                      },
                                      onChanged: (floor) {
                                        proposedEstate.floor = floor;
                                      },
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(width: 8),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text("Prezzo",
                                        style: TextStyle(
                                            fontWeight: FontWeight.bold)),
                                    SizedBox(height: 8),
                                    TextFormField(
                                      enabled:
                                          widget.agencyUser!.role != 'master',
                                      initialValue: proposedEstate.price,
                                      decoration: InputDecoration(
                                        hintText: "Inserisci il prezzo",
                                      ),
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Inserisci il prezzo valido';
                                        }
                                        return null;
                                      },
                                      onChanged: (price) {
                                        proposedEstate.price = price;
                                      },
                                    )
                                  ],
                                ),
                              ),
                              SizedBox(width: 8),
                            ],
                          ),
                          SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Expanded(
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text("Dimensione",
                                              style: TextStyle(
                                                  fontWeight: FontWeight.bold)),
                                          SizedBox(height: 8),
                                          TextFormField(
                                            enabled: widget.agencyUser!.role !=
                                                'master',
                                            initialValue: proposedEstate.sqm,
                                            decoration: InputDecoration(
                                              hintText:
                                                  "Inserisci la dimensione in metri quadrati",
                                            ),
                                            validator: (value) {
                                              if (value == null ||
                                                  value.isEmpty) {
                                                return 'Inserisci una dimensione valida';
                                              }
                                              return null;
                                            },
                                            onChanged: (sqm) {
                                              proposedEstate.sqm = sqm;
                                            },
                                          )
                                        ],
                                      ),
                                    ),
                                    SizedBox(width: 8),
                                    Expanded(
                                      child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text("Ascensore presente?",
                                                style: TextStyle(
                                                    fontWeight:
                                                        FontWeight.bold)),
                                            SizedBox(height: 8),
                                            DropdownButton<String>(
                                              value: proposedEstate.elevator
                                                  ? 'Sì'
                                                  : 'No',
                                              // icon: const Icon(
                                              //     Icons.arrow_downward),
                                              elevation: 16,
                                              // style: const TextStyle(
                                              //     color: Colors.deepPurple),
                                              underline: Container(
                                                padding: EdgeInsets.symmetric(
                                                    vertical: 10,
                                                    horizontal: 10),
                                                height: 2,
                                                color: Color.fromARGB(
                                                    255, 62, 62, 62),
                                              ),
                                              onChanged:
                                                  widget.agencyUser!.role ==
                                                          'master'
                                                      ? null
                                                      : (String? value) {
                                                          // This is called when the user selects an item.
                                                          setState(() {
                                                            proposedEstate
                                                                    .elevator =
                                                                value == 'Sì'
                                                                    ? true
                                                                    : false;
                                                          });
                                                        },
                                              items: list.map<
                                                      DropdownMenuItem<String>>(
                                                  (String value) {
                                                return DropdownMenuItem<String>(
                                                  value: value,
                                                  child: Text(value),
                                                );
                                              }).toList(),
                                            ),
                                          ]),
                                      // child: CheckboxListTile(
                                      //   value: proposedEstate.elevator,
                                      //   onChanged: (value) {
                                      //     setState(() {
                                      //       proposedEstate.elevator = value!;
                                      //     });
                                      //   },
                                      //   checkboxShape: CircleBorder(),
                                      //   checkColor:
                                      //       Theme.of(context).primaryColor,
                                      //   title: Text("Ascensore presente?"),
                                      // ),
                                    )
                                  ],
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 18),
                          Row(
                            children: [
                              Text(
                                "Margine di trattativa",
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          SizedBox(height: 8),
                          showOptionMissing(proposedEstate.margineTrattativa)
                              ? Row(
                                  children: [
                                    Text(
                                      "Seleziona un'opzione",
                                      style: TextStyle(color: Colors.red),
                                    ),
                                  ],
                                )
                              : Container(),
                          SizedBox(height: 8),
                          MediaQuery.of(context).size.width < 800
                              ? Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        GestureDetector(
                                          onTap: () {
                                            if (widget.agencyUser!.role ==
                                                'master') {
                                              return;
                                            }
                                            setState(() {
                                              proposedEstate.margineTrattativa =
                                                  "basso";
                                            });
                                          },
                                          child: Container(
                                            width: 150,
                                            height: 50,
                                            child: Text(
                                              "Basso",
                                              style: TextStyle(
                                                  color: proposedEstate
                                                              .margineTrattativa ==
                                                          "basso"
                                                      ? Colors.white
                                                      : Colors.black),
                                            ),
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                                color: proposedEstate
                                                            .margineTrattativa ==
                                                        "basso"
                                                    ? Theme.of(context)
                                                        .primaryColor
                                                    : Color(0xffF4F4F4),
                                                borderRadius:
                                                    BorderRadius.circular(25)),
                                          ),
                                        ),
                                        GestureDetector(
                                          onTap: () {
                                            if (widget.agencyUser!.role ==
                                                'master') {
                                              return;
                                            }
                                            setState(() {
                                              proposedEstate.margineTrattativa =
                                                  "medio";
                                            });
                                          },
                                          child: Container(
                                            width: 150,
                                            height: 50,
                                            child: Text(
                                              "Medio",
                                              style: TextStyle(
                                                  color: proposedEstate
                                                              .margineTrattativa ==
                                                          "medio"
                                                      ? Colors.white
                                                      : Colors.black),
                                            ),
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                                color: proposedEstate
                                                            .margineTrattativa ==
                                                        "medio"
                                                    ? Theme.of(context)
                                                        .primaryColor
                                                    : Color(0xffF4F4F4),
                                                borderRadius:
                                                    BorderRadius.circular(25)),
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 8),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        GestureDetector(
                                          onTap: () {
                                            if (widget.agencyUser!.role ==
                                                'master') {
                                              return;
                                            }
                                            setState(() {
                                              proposedEstate.margineTrattativa =
                                                  "alto";
                                            });
                                          },
                                          child: Container(
                                            width: 150,
                                            height: 50,
                                            child: Text(
                                              "Alto",
                                              style: TextStyle(
                                                  color: proposedEstate
                                                              .margineTrattativa ==
                                                          "alto"
                                                      ? Colors.white
                                                      : Colors.black),
                                            ),
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                                color: proposedEstate
                                                            .margineTrattativa ==
                                                        "alto"
                                                    ? Theme.of(context)
                                                        .primaryColor
                                                    : Color(0xffF4F4F4),
                                                borderRadius:
                                                    BorderRadius.circular(25)),
                                          ),
                                        ),
                                        GestureDetector(
                                          onTap: () {
                                            if (widget.agencyUser!.role ==
                                                'master') {
                                              return;
                                            }
                                            setState(() {
                                              proposedEstate.margineTrattativa =
                                                  "unknown";
                                            });
                                          },
                                          child: Container(
                                            width: 150,
                                            height: 50,
                                            child: Text(
                                              "Non so",
                                              style: TextStyle(
                                                  color: proposedEstate
                                                              .margineTrattativa ==
                                                          "unknown"
                                                      ? Colors.white
                                                      : Colors.black),
                                            ),
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                              color: proposedEstate
                                                          .margineTrattativa ==
                                                      "unknown"
                                                  ? Theme.of(context)
                                                      .primaryColor
                                                  : Color(0xffF4F4F4),
                                              borderRadius:
                                                  BorderRadius.circular(25),
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                  ],
                                )
                              : Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    GestureDetector(
                                      onTap: () {
                                        if (widget.agencyUser!.role ==
                                            'master') {
                                          return;
                                        }
                                        setState(() {
                                          proposedEstate.margineTrattativa =
                                              "basso";
                                        });
                                      },
                                      child: Container(
                                        width: 150,
                                        height: 50,
                                        child: Text(
                                          "Basso",
                                          style: TextStyle(
                                              color: proposedEstate
                                                          .margineTrattativa ==
                                                      "basso"
                                                  ? Colors.white
                                                  : Colors.black),
                                        ),
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                            color: proposedEstate
                                                        .margineTrattativa ==
                                                    "basso"
                                                ? Theme.of(context).primaryColor
                                                : Color(0xffF4F4F4),
                                            borderRadius:
                                                BorderRadius.circular(25)),
                                      ),
                                    ),
                                    GestureDetector(
                                      onTap: () {
                                        if (widget.agencyUser!.role ==
                                            'master') {
                                          return;
                                        }
                                        setState(() {
                                          proposedEstate.margineTrattativa =
                                              "medio";
                                        });
                                      },
                                      child: Container(
                                        width: 150,
                                        height: 50,
                                        child: Text(
                                          "Medio",
                                          style: TextStyle(
                                              color: proposedEstate
                                                          .margineTrattativa ==
                                                      "medio"
                                                  ? Colors.white
                                                  : Colors.black),
                                        ),
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                            color: proposedEstate
                                                        .margineTrattativa ==
                                                    "medio"
                                                ? Theme.of(context).primaryColor
                                                : Color(0xffF4F4F4),
                                            borderRadius:
                                                BorderRadius.circular(25)),
                                      ),
                                    ),
                                    GestureDetector(
                                      onTap: () {
                                        if (widget.agencyUser!.role ==
                                            'master') {
                                          return;
                                        }
                                        setState(() {
                                          proposedEstate.margineTrattativa =
                                              "alto";
                                        });
                                      },
                                      child: Container(
                                        width: 150,
                                        height: 50,
                                        child: Text(
                                          "Alto",
                                          style: TextStyle(
                                              color: proposedEstate
                                                          .margineTrattativa ==
                                                      "alto"
                                                  ? Colors.white
                                                  : Colors.black),
                                        ),
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                            color: proposedEstate
                                                        .margineTrattativa ==
                                                    "alto"
                                                ? Theme.of(context).primaryColor
                                                : Color(0xffF4F4F4),
                                            borderRadius:
                                                BorderRadius.circular(25)),
                                      ),
                                    ),
                                    GestureDetector(
                                      onTap: () {
                                        if (widget.agencyUser!.role ==
                                            'master') {
                                          return;
                                        }
                                        setState(() {
                                          proposedEstate.margineTrattativa =
                                              "unknown";
                                        });
                                      },
                                      child: Container(
                                        width: 150,
                                        height: 50,
                                        child: Text(
                                          "Non so",
                                          style: TextStyle(
                                              color: proposedEstate
                                                          .margineTrattativa ==
                                                      "unknown"
                                                  ? Colors.white
                                                  : Colors.black),
                                        ),
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                          color: proposedEstate
                                                      .margineTrattativa ==
                                                  "unknown"
                                              ? Theme.of(context).primaryColor
                                              : Color(0xffF4F4F4),
                                          borderRadius:
                                              BorderRadius.circular(25),
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                          SizedBox(height: 18),
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  "Sarebbe possibile effettuare una cessione di preliminare?",
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 8),
                          showOptionMissing(
                                  proposedEstate.possibilityCessionePreliminare)
                              ? Row(
                                  children: [
                                    Text(
                                      "Seleziona un'opzione",
                                      style: TextStyle(color: Colors.red),
                                    ),
                                  ],
                                )
                              : Container(),
                          SizedBox(height: 8),
                          MediaQuery.of(context).size.width < 600
                              ? Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    GestureDetector(
                                      onTap: () {
                                        if (widget.agencyUser!.role ==
                                            'master') {
                                          return;
                                        }
                                        setState(() {
                                          proposedEstate
                                                  .possibilityCessionePreliminare =
                                              "yes";
                                        });
                                      },
                                      child: Container(
                                        width: 100,
                                        height: 50,
                                        child: Text(
                                          "Sì",
                                          style: TextStyle(
                                            color: proposedEstate
                                                        .possibilityCessionePreliminare ==
                                                    "yes"
                                                ? Colors.white
                                                : Colors.black,
                                          ),
                                        ),
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                            color: proposedEstate
                                                        .possibilityCessionePreliminare ==
                                                    "yes"
                                                ? Theme.of(context).primaryColor
                                                : Color(0xffF4F4F4),
                                            borderRadius:
                                                BorderRadius.circular(25)),
                                      ),
                                    ),
                                    SizedBox(height: 12),
                                    GestureDetector(
                                      onTap: () {
                                        if (widget.agencyUser!.role ==
                                            'master') {
                                          return;
                                        }
                                        setState(() {
                                          proposedEstate
                                                  .possibilityCessionePreliminare =
                                              "no";
                                        });
                                      },
                                      child: Container(
                                        width: 100,
                                        height: 50,
                                        child: Text(
                                          "No",
                                          style: TextStyle(
                                            color: proposedEstate
                                                        .possibilityCessionePreliminare ==
                                                    "no"
                                                ? Colors.white
                                                : Colors.black,
                                          ),
                                        ),
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                            color: proposedEstate
                                                        .possibilityCessionePreliminare ==
                                                    "no"
                                                ? Theme.of(context).primaryColor
                                                : Color(0xffF4F4F4),
                                            borderRadius:
                                                BorderRadius.circular(25)),
                                      ),
                                    ),
                                    SizedBox(height: 12),
                                    GestureDetector(
                                      onTap: () {
                                        if (widget.agencyUser!.role ==
                                            'master') {
                                          return;
                                        }
                                        setState(() {
                                          proposedEstate
                                                  .possibilityCessionePreliminare =
                                              "unknown";
                                        });
                                      },
                                      child: Container(
                                        width: 100,
                                        height: 50,
                                        child: Text(
                                          "Non so",
                                          style: TextStyle(
                                            color: proposedEstate
                                                        .possibilityCessionePreliminare ==
                                                    "unknown"
                                                ? Colors.white
                                                : Colors.black,
                                          ),
                                        ),
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                            color: proposedEstate
                                                        .possibilityCessionePreliminare ==
                                                    "unknown"
                                                ? Theme.of(context).primaryColor
                                                : Color(0xffF4F4F4),
                                            borderRadius:
                                                BorderRadius.circular(25)),
                                      ),
                                    ),
                                  ],
                                )
                              : Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          proposedEstate
                                                  .possibilityCessionePreliminare =
                                              "yes";
                                        });
                                      },
                                      child: Container(
                                        width: 100,
                                        height: 50,
                                        child: Text(
                                          "Sì",
                                          style: TextStyle(
                                            color: proposedEstate
                                                        .possibilityCessionePreliminare ==
                                                    "yes"
                                                ? Colors.white
                                                : Colors.black,
                                          ),
                                        ),
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                            color: proposedEstate
                                                        .possibilityCessionePreliminare ==
                                                    "yes"
                                                ? Theme.of(context).primaryColor
                                                : Color(0xffF4F4F4),
                                            borderRadius:
                                                BorderRadius.circular(25)),
                                      ),
                                    ),
                                    SizedBox(width: 12),
                                    GestureDetector(
                                      onTap: () {
                                        if (widget.agencyUser!.role ==
                                            'master') {
                                          return;
                                        }
                                        setState(() {
                                          proposedEstate
                                                  .possibilityCessionePreliminare =
                                              "no";
                                        });
                                      },
                                      child: Container(
                                        width: 100,
                                        height: 50,
                                        child: Text(
                                          "No",
                                          style: TextStyle(
                                            color: proposedEstate
                                                        .possibilityCessionePreliminare ==
                                                    "no"
                                                ? Colors.white
                                                : Colors.black,
                                          ),
                                        ),
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                            color: proposedEstate
                                                        .possibilityCessionePreliminare ==
                                                    "no"
                                                ? Theme.of(context).primaryColor
                                                : Color(0xffF4F4F4),
                                            borderRadius:
                                                BorderRadius.circular(25)),
                                      ),
                                    ),
                                    SizedBox(width: 12),
                                    GestureDetector(
                                      onTap: () {
                                        if (widget.agencyUser!.role ==
                                            'master') {
                                          return;
                                        }
                                        setState(() {
                                          proposedEstate
                                                  .possibilityCessionePreliminare =
                                              "unknown";
                                        });
                                      },
                                      child: Container(
                                        width: 100,
                                        height: 50,
                                        child: Text(
                                          "Non so",
                                          style: TextStyle(
                                            color: proposedEstate
                                                        .possibilityCessionePreliminare ==
                                                    "unknown"
                                                ? Colors.white
                                                : Colors.black,
                                          ),
                                        ),
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                            color: proposedEstate
                                                        .possibilityCessionePreliminare ==
                                                    "unknown"
                                                ? Theme.of(context).primaryColor
                                                : Color(0xffF4F4F4),
                                            borderRadius:
                                                BorderRadius.circular(25)),
                                      ),
                                    ),
                                  ],
                                ),
                          SizedBox(height: 15),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              MouseRegion(
                                cursor: SystemMouseCursors.click,
                                child: GestureDetector(
                                  onTap: () async {
                                    setState(() {
                                      clicked = true;
                                    });
                                    if (showOptionMissing(proposedEstate
                                            .margineTrattativa!) ||
                                        showOptionMissing(proposedEstate
                                            .possibilityCessionePreliminare!)) {
                                      return;
                                    }
                                    if (!_formKey.currentState!.validate()) {
                                      return;
                                    }

                                    setState(() {
                                      loading = true;
                                    });

                                    proposedEstate.insertionTimestamp =
                                        DateTime.now().millisecondsSinceEpoch;
                                    proposedEstate.agencyId = widget.agency!.id;
                                    proposedEstate.agencyName =
                                        widget.agency!.name;
                                    if (widget.isEdit) {
                                      await updateDocument(
                                          'proposedEstates/',
                                          proposedEstate.id!,
                                          proposedEstate.toJson());
                                    } else {
                                      await writeDocument('proposedEstates/',
                                          proposedEstate.toJson());
                                    }

                                    setState(() {
                                      loading = false;
                                    });
                                    Navigator.of(context).pop(true);
                                  },
                                  child: Container(
                                    height: 45,
                                    width: 240,
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).primaryColor,
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8.0),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            "Invia proposta a Newarc",
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 14,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          SizedBox(width: 10),
                                          Icon(
                                            Icons.arrow_right_alt,
                                            color: Colors.white,
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 15),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
