import 'dart:developer';
import 'dart:typed_data';

import 'package:image_picker/image_picker.dart';
import 'package:image/image.dart' as img;
import 'package:path/path.dart' as path;

Future<XFile> resizeXFile(XFile originalFile, {int width = 335, int height = 195, int quality = 70,String? customFileName}) async {
  final bytes = await originalFile.readAsBytes();
  final image = img.decodeImage(bytes);

  if (image == null) throw Exception("Could not decode image.");

  img.Image resized;


  log("image.width > width ===> ${image.width > width}");
  log("image.height > height ===> ${image.height > height}");
  log("image.width > image.height ===> ${image.width > image.height}");

  if (image.width > width || image.height > height) {
    if (image.width > image.height) {
      log("((image.height / image.width) * width) ===> ${((image.height / image.width) * width)}");
      height = ((image.height / image.width) * width).floor();
    } else {
      width = (image.width / image.height * height).floor();
    }

    log("width ---> $width");
    log("height ---> $height");
    resized = img.copyResize(image, width: width, height: height, maintainAspect: true,interpolation: img.Interpolation.average);
  } else {
    resized = image;
  }

  final ext = path.extension(originalFile.name).toLowerCase();
  final baseName = path.basenameWithoutExtension(originalFile.name);
  Uint8List compressedBytes;


  if (ext == '.png') {
    compressedBytes = Uint8List.fromList(img.encodePng(resized, level: 0));
  } else {
    compressedBytes = Uint8List.fromList(img.encodeJpg(resized, quality: quality));
  }

  return XFile.fromData(
    compressedBytes,
    name: "${customFileName?.isNotEmpty ?? false ? customFileName : "${baseName}_thumbnail"}${ext}",
    mimeType: originalFile.mimeType,
  );
}