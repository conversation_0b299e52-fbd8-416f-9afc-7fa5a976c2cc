class NewarcAdOptionalCategoryConfiguration {
  String? name;
  String? id;
  int? insertTimestamp;



  Map<String, Object?> toMap() {
    return {
      'name': name,
      'id': id,
      'insertTimestamp': insertTimestamp,
    };
  }
  NewarcAdOptionalCategoryConfiguration.empty() {
    this.name = '';
    this.id = '';
    this.insertTimestamp = null;
  }
  NewarcAdOptionalCategoryConfiguration.fromDocument(Map<String, dynamic> data) {
    try {
      this.name = data['name'];
      this.id = data['id'];
      this.insertTimestamp = data['insertTimestamp'];
    } catch (e, s) {
      print({ 'NewarcAdOptionalCategoryConfiguration Class Error ------->', e, s});
    }
  }
}