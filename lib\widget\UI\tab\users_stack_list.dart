import 'package:flutter/material.dart';
import 'package:flutter_image_stack/flutter_image_stack.dart';

class UsersStackWidget extends StatelessWidget {
  const UsersStackWidget({
    super.key,
    required this.imageList,
    this.radius,
    this.itemCount,
  });

  final List<String> imageList;
  final double? radius;
  final int? itemCount;

  @override
  Widget build(BuildContext context) {
    return FlutterImageStack(
      imageList: imageList,
      showTotalCount: false,
      totalCount: imageList.length,
      itemRadius: radius ?? 30,
      itemCount: itemCount ?? 3,
      itemBorderWidth: 0,
    );
  }
}
