import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:newarc_platform/classes/professionals.dart';
import 'package:newarc_platform/pages/work/gestione/professionals/professionals_controller.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/widget/UI/tab/status_widget.dart';

class ProfessionalsDataSource extends DataTableSource {
  final List<Professional> professionals;
  final BuildContext context;
  final Function(Professional) onProfessionalTap;
  final controller = Get.put<ProfessionalsController>(ProfessionalsController());

  NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);

  ProfessionalsDataSource({
    required this.context,
    required this.professionals,
    required this.onProfessionalTap,
  });

  @override
  DataRow? getRow(int index) {
    if (index < professionals.length){
      Professional cont = professionals[index];
      String name = cont.hasPIva ?"${cont.companyName} ${cont.formationType}" : "${cont.contactPersonInfo.name} ${cont.contactPersonInfo.surname}";
      name = name.toCapitalized().trim();
      return DataRow2(
        specificRowHeight: 50,
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
                color: AppColor.borderColor,
                width: 1,
            ),
          ),
        ),
        cells: [
          // P.Iva
          DataCell(
            StatusWidget(
              statusColor: cont.hasPIva == true ? AppColor.successGreenColor : AppColor.redColor,
            )
          ),
          // Nome azienda/persona
          DataCell(
            NarLinkWidget(
              text: name,
              fontSize: 12,
              fontWeight: '800',
              textAlign: TextAlign.left,
              textColor: Colors.black,
              onClick: (){
                onProfessionalTap(cont);
              },
            )
          ),
          // Professione
          DataCell(
            NarFormLabelWidget(
              label: cont.profession,
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
              maxLines: 3,
            )
          ),
          // Provincia
          DataCell(
            NarFormLabelWidget(
              label: cont.legalAddressInfo.province,
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            )
          ),
          // Indirizzo
          DataCell(
            NarFormLabelWidget(
              label: cont.legalAddressInfo.toShortAddress(),
              fontSize: 12,
              fontWeight: '600',
              textAlign: TextAlign.start,
              textColor: Colors.black,
            )
          ),
        ],
      );
    }
    return null;
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => professionals.length;

  @override
  int get selectedRowCount => 0;
}

