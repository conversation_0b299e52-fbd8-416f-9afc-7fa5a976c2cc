class NewarcProjectFixedAssetsPropertyPercentage{
  String? firebaseId;
  int? percentage;
  int? insertTimestamp;
  String? uid;


  Map<String, Object?> toMap() {
    return {
      'percentage': percentage,
      'insertTimestamp': insertTimestamp,
      'uid': uid,
    };
  }

  NewarcProjectFixedAssetsPropertyPercentage.empty() {
    this.firebaseId = '';
    this.percentage = 0;
    this.insertTimestamp = null;
    this.uid = '';
  }

  NewarcProjectFixedAssetsPropertyPercentage.fromDocument(Map<String, dynamic> data, String id) {
    this.firebaseId = id;
    try {
      this.percentage = data['percentage'];
      this.insertTimestamp = data['insertTimestamp'];
      this.uid = data['uid'];
    } catch (e, s) {
      print({ 'NewarcProjectFixedAssetsPropertyPercentage Class Error ------->', e, s});
    }
  }
}