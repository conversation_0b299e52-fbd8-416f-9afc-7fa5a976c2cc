import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';

class NarFilter extends StatefulWidget {
  final List<Map<String, Widget>> filterFields;
  final List<TextEditingController> textEditingControllers;
  final List<String>? selectedFilters;
  final TextEditingController? searchTextEditingControllers;
  final Function onSubmit;
  final Function onReset;
  final bool showSearchInput;
  final bool? isFilterHide;
  final String? searchHintText;
  final void Function(String)? onFieldSubmitted;
  final void Function(String)? onChanged;
  final Function()? suffixIconOnTap;

  NarFilter({
    this.searchTextEditingControllers,
    required this.showSearchInput,
    required this.textEditingControllers,
    required this.filterFields,
    required this.onSubmit,
    required this.onReset,
    this.isFilterHide,
    this.selectedFilters,
    this.searchHintText,
    this.onFieldSubmitted,
    this.onChanged,
    this.suffixIconOnTap,
    Key? key,
  }) : super(key: key);

  @override
  State<NarFilter> createState() => _NarFilterState();
}

class _NarFilterState extends State<NarFilter> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void didUpdateWidget(NarFilter oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  filterDialog() {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (BuildContext context, StateSetter _setState) {
            return Center(
              child: Container(
                width: 350,
                height: 450,
                constraints: BoxConstraints(minHeight: 450),
                decoration: BoxDecoration(
                  color: Color(0xffffffff),
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                child: Material(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              NarFormLabelWidget(
                                label: 'Filtra',
                                textColor: Colors.black,
                                fontSize: 18,
                                fontWeight: '800',
                              ),
                              GestureDetector(
                                onTap: () {
                                  Navigator.pop(context);
                                },
                                child: MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: Container(
                                    height: 13,
                                    width: 13,
                                    child: SvgPicture.asset(
                                      'assets/icons/close-popup.svg',
                                      width: 13,
                                    ),
                                  ),
                                ),
                              )
                            ],
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Container(
                              decoration: BoxDecoration(
                                color: Color(0xffE2E2E2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              width: double.infinity,
                              height: 1),
                          ListView(
                            shrinkWrap: true,
                            children: [
                              SizedBox(
                                height: 15,
                              ),
                              ...widget.filterFields.map((value) {
                                // return Text() );
                                return Column(
                                  children: [
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Column(
                                            mainAxisSize: MainAxisSize.max,
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            mainAxisAlignment: MainAxisAlignment.start,
                                            children: [
                                              NarFormLabelWidget(
                                                label: value.keys.first,
                                                textColor: Color(0xff000000),
                                                fontSize: 14,
                                                fontWeight: '600',
                                              ),
                                              SizedBox(height: 4),
                                              value.values.first,
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(
                                      height: 10,
                                    ),
                                  ],
                                );
                              }).toList(),
                            ],
                          ),
                        ],
                      ),
                      Column(
                        children: [
                          SizedBox(
                            height: 10,
                          ),
                          Container(
                              decoration: BoxDecoration(
                                color: Color(0xffE2E2E2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              width: double.infinity,
                              height: 1),
                          SizedBox(
                            height: 10,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              BaseNewarcButton(
                                  buttonText: 'Reset',
                                  onPressed: () async {
                                    for (var i = 0; i < widget.textEditingControllers.length; i++) {
                                      widget.textEditingControllers[i].text = "";
                                    }

                                    widget.selectedFilters?.clear();

                                    _setState(() {});

                                    widget.onReset();
                                    Navigator.pop(context);
                                  },
                                  color: Colors.white,
                                  textColor: Colors.black,
                                  borderColor: Color(0xffD6D6D6)),
                              SizedBox(
                                width: 10,
                              ),
                              BaseNewarcButton(
                                  buttonText: 'Applica',
                                  onPressed: () async {
                                    setState(() {});
                                    widget.onSubmit();
                                    Navigator.pop(context);
                                  }),
                            ],
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              ),
            );
          });
        });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 5),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          widget.showSearchInput
              ? Row(
                  children: [
                    Container(
                      width: 345,
                      height: 35,
                      decoration: BoxDecoration(
                        color: Color(0xffF6F6F6),
                        borderRadius: BorderRadius.circular(35),
                      ),
                      child: Center(
                        child: TextFormField(
                          cursorColor: Colors.black,
                          controller: widget.searchTextEditingControllers,
                          onFieldSubmitted: widget.onFieldSubmitted,
                          onChanged: widget.onChanged,
                          decoration: InputDecoration(
                            suffixIconConstraints: BoxConstraints(
                              maxHeight: 60,
                            ),
                            suffixIcon: MouseRegion(
                              cursor: SystemMouseCursors.click,
                              child: GestureDetector(
                                onTap: widget.suffixIconOnTap,
                                child: Padding(
                                  padding: const EdgeInsets.only(right: 15.0),
                                  child: SvgPicture.asset(
                                    // height: 10,
                                    "assets/icons/search.svg",
                                  ),
                                ),
                              ),
                            ),
                            hintText: widget.searchHintText ?? 'Cerca per indirizzo o per nome...',
                            hintStyle: TextStyle(fontFamily: 'Raleway-600', fontSize: 11, letterSpacing: 0.2, color: Color(0xff737373)),
                            fillColor: Color(0xffF6F6F6),
                            contentPadding: EdgeInsets.only(top: 17, bottom: 12, right: 2, left: 14),
                            isCollapsed: true,
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(35),
                              borderSide: BorderSide.none,
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(35),
                              borderSide: BorderSide.none,
                            ),
                            disabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(35),
                              borderSide: BorderSide.none,
                            ),
                          ),
                          style: TextStyle(fontFamily: 'Raleway-700', fontSize: 13, letterSpacing: 0.2, color: AppColor.black),
                        ),
                      ),
                    ),
                    // CustomTextFormField()
                    SizedBox(width: 50),
                  ],
                )
              : Container(),

          (widget.isFilterHide ?? false) ? Container() :
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                filterDialog();
              },
              child: Container(
                  height: 35,
                  width: 107,
                  padding: EdgeInsets.symmetric(horizontal: 10),
                  decoration: BoxDecoration(
                      color: Color(0xffF6F6F6),
                      borderRadius: BorderRadius.circular(35),
                      border: Border.all(width: 1, color: Color(0xffF6F6F6))),
                  child: Row(
                    // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        'assets/icons/filter.svg',
                        color: Color(0xffBCBCBC),
                        width: 14,
                      ),
                      SizedBox(
                        width: 5,
                      ),
                      Expanded(
                        child: NarFormLabelWidget(
                          label: 'Filtra',
                          fontSize: 11,
                          fontWeight: '600',
                          textColor: Color(0xff505050),
                        ),
                      ),
                      SvgPicture.asset(
                        'assets/icons/arrow_down.svg',
                        color: Color(0xffBCBCBC),
                        width: 9,
                      ),
                    ],
                  )),
            ),
          ),
          SizedBox(width: 10),
          for (int i = 0; i < (widget.selectedFilters ?? []).length; i++)
            if (widget.selectedFilters?[i] != '')
              Row(
                children: [
                  Container(
                      height: 35,
                      // width: double.infinity,
                      width: double.tryParse(((((widget.selectedFilters?[i] ?? "").length * 6) + 25)).toString()),
                      // constraints: BoxConstraints(
                      //   minWidth: 100,
                      // ),
                      padding: EdgeInsets.symmetric(horizontal: 10),
                      decoration: BoxDecoration(
                          color: Color(0xffffffff),
                          borderRadius: BorderRadius.circular(35),
                          border: Border.all(width: 1, color: Color(0xffEEEEEE)
                              // color: Colors.transparent
                              )),
                      child: Row(
                        // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            child: NarFormLabelWidget(
                              label: widget.selectedFilters?[i] ?? "",
                              fontSize: 11,
                              fontWeight: '600',
                              textAlign: TextAlign.center,
                              textColor: Color(0xff848484),
                            ),
                          ),
                        ],
                      )),
                  SizedBox(width: 10),
                ],
              ),
        ],
      ),
    );
  }
}

// Expanded(
//   child: Column(
//     mainAxisSize: MainAxisSize.max,
//     crossAxisAlignment: CrossAxisAlignment.start,
//     mainAxisAlignment: MainAxisAlignment.start,
//     children: [
//       NarFormLabelWidget(
//         label: "Interesse",
//         textColor: Color(0xff000000),
//         fontSize: 14,
//         fontWeight: '600',
//       ),
//       SizedBox(height: 4),
//       NarSelectBoxWidget (
//         options: [],
//         controller: contFitler1,
//         onChanged: () {

//         },
//         contentPadding: EdgeInsets.only(top: 17, bottom: 12, right: 2, left: 14),
//         borderRadius: 35,
//       ),
//     ],
//   ),
// ),
// SizedBox(height: 10,),
// Expanded(
//   child: Column(
//     mainAxisSize: MainAxisSize.max,
//     crossAxisAlignment: CrossAxisAlignment.start,
//     mainAxisAlignment: MainAxisAlignment.start,
//     children: [
//       NarFormLabelWidget(
//         label: "Assegnazione",
//         textColor: Color(0xff000000),
//         fontSize: 14,
//         fontWeight: '600',
//       ),
//       SizedBox(height: 4),
//       NarSelectBoxWidget (
//         options: [],
//         controller: contFitler1,
//         onChanged: () {

//         },
//         contentPadding: EdgeInsets.only(top: 17, bottom: 12, right: 2, left: 14),
//         borderRadius: 35,
//       ),
//     ],
//   ),
// ),
// SizedBox(height: 10,),

// Container(
//   height: 35,
//   width:  double.tryParse((('Filter due'.length * 6) + 20 ).toString()) ,
//   padding: EdgeInsets.symmetric(horizontal: 10),
//   decoration: BoxDecoration(
//     color: Color(0xffffffff),
//     borderRadius: BorderRadius.circular(35),
//     border: Border.all(
//       width: 1,
//       color: Color(0xffEEEEEE)
//       // color: Colors.transparent
//     )
//   ),
//   child: Row(
//     // mainAxisAlignment: MainAxisAlignment.spaceBetween,
//     mainAxisSize: MainAxisSize.max,
//     crossAxisAlignment: CrossAxisAlignment.center,
//     children: [
//       Expanded(
//         child: NarFormLabelWidget(
//           label: 'Filter due',
//           fontSize: 11,
//           fontWeight: '600',
//           textAlign: TextAlign.center,
//           textColor: Color(0xff848484),
//         ),
//       ),

//     ],
//   )
// ),
