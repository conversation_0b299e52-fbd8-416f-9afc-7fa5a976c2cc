import 'package:flutter/material.dart';
import 'dart:html' as html;
import 'dart:async';
import 'dart:typed_data';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';

import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/utils/heicToJpegConverter.dart' as heicToJpeg;
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:path/path.dart' as p;


List<String> allowedPlanimetryExtensions = ['dwg', 'pdf', 'jpg', 'jpeg', 'png'];
List<String> allowedPicturesExtensions = ['jpg', 'jpeg', 'heic'];

getFirestorePicturesImages(
  List<Map<String, dynamic>> picturesImages, 
  ImmaginaProject selectedProject, 
  StateSetter setState) {
  if (selectedProject != null) {
    if (selectedProject!.pictures.isNotEmpty) {
      selectedProject!.pictures.forEach((pic) async {
        Reference ref = FirebaseStorage.instance.ref().child(appConfig.COLLECT_IMMAGINA_PROJECTS + "/" + selectedProject!.id + "/" + pic['file']);
        Uint8List? data = await ref.getData();
        setState((){
          picturesImages.add({'tag': pic['tag'], 'file': XFile.fromData(data!, name: pic['file'])});
        });
      });
    }
  }
}

Future savePicturesImages(
  List<Map<String, dynamic>> picturesImages, 
  ImmaginaProject selectedProject, 
  StateSetter setState
) async {
  Reference ref = FirebaseStorage.instance.ref('${appConfig.COLLECT_IMMAGINA_PROJECTS}/${selectedProject!.id}/fotografie');
  final listResult = await ref.listAll();
  for (var element in listResult.items) {
    await FirebaseStorage.instance.ref(element.fullPath).delete();
  }
  selectedProject!.pictures.clear();
  for (int i = 0; i < picturesImages.length; i++) {
    String pictureFilename = 'fotografie/${picturesImages[i]['tag'] ?? 'notag'}_${i}' + p.extension(picturesImages[i]['file']!.name);
    await uploadImageToStorage(appConfig.COLLECT_IMMAGINA_PROJECTS, selectedProject!.id, pictureFilename, picturesImages[i]['file']);
    selectedProject!.pictures.add({'tag': picturesImages[i]['tag'], 'file': pictureFilename});
  }
  await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(selectedProject!.id).set(selectedProject!.toMap());
}

Future<UploadTask?> uploadImageToStorage(String directory, String docId, String filename, XFile? file) async {
    if (file == null) {
      return null;
    }
    UploadTask uploadTask;
    Reference ref = FirebaseStorage.instance.ref().child(directory).child(docId).child(filename);
    final metadata = SettableMetadata(
      contentType: file.mimeType,
      customMetadata: {'picked-file-path': file.path},
    );
    uploadTask = ref.putData(await file.readAsBytes(), metadata);
    uploadTask.snapshotEvents.listen((TaskSnapshot taskSnapshot) {
      switch (taskSnapshot.state) {
        case TaskState.running:
          final progress = 100.0 * (taskSnapshot.bytesTransferred / taskSnapshot.totalBytes);
          // print("Upload is $progress% complete.");
          break;
        case TaskState.paused:
        // print("Upload is paused.");
          break;
        case TaskState.canceled:
        // print("Upload was canceled");
          break;
        case TaskState.error:
        // Handle unsuccessful uploads
          break;
        case TaskState.success:
        // Handle successful uploads on complete
        // ...
          break;
      }
    });
    return Future.value(uploadTask);
  }

showEnlargedDownloadableImage(context, Map<String, dynamic> imageFile) {
  showDialog(
    context: context,
    barrierDismissible: true,
    builder: (_) {
      // builder: (context, setState) {
      return FutureBuilder<Size>(
        future: _calculateImageDimension(context, imageFile['file'].path),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    CircularProgressIndicator(
                      color: Theme.of(context).primaryColor,
                    ),
                    SizedBox(height: 5),
                    NarFormLabelWidget(
                      label: 'Loading...',
                      textColor: Colors.white,
                    )
                  ],
                ));
          } else if (snapshot.hasError) {
            return AlertDialog(
              title: NarFormLabelWidget(label: 'Error'),
              content: NarFormLabelWidget(label: 'Could not load image'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: NarFormLabelWidget(label: 'Close'),
                ),
              ],
            );
          } else {
            final size = snapshot.data!;

            double imageWidth = 0;
            double imageHeight = 0;
            double displayWidth = 0;
            double displayHeight = 0;

            double maxImageWidth = MediaQuery.of(context).size.width;
            double maxImageHeight = MediaQuery.of(context).size.height;

            imageWidth = size.width.toDouble();
            imageHeight = size.height.toDouble();
            double aspectRatio = imageWidth / imageHeight;

            displayWidth = imageWidth;
            displayHeight = imageHeight;

            if (displayWidth > maxImageWidth) {
              displayWidth = maxImageWidth;
              displayHeight = displayWidth / aspectRatio;
            }

            if (displayHeight > maxImageHeight) {
              displayHeight = maxImageHeight;
              displayWidth = displayHeight * aspectRatio;
            }

            return Center(
              child: Wrap(
                children: [
                  Material(
                    color: Colors.transparent,
                    child: Center(
                      child: Stack(
                        children: [
                          Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                  width: displayWidth,
                                  height: displayHeight,
                                  padding: const EdgeInsets.all(0),
                                  // decoration: BoxDecoration(
                                  //   borderRadius: BorderRadius.circular(15.0),
                                  // ),

                                  child: ListView(
                                    padding: EdgeInsets.all(0),
                                    shrinkWrap: true,
                                    children: [
                                      Card(
                                        color: const Color.fromRGBO(255, 255, 255, 1),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(15.0),
                                        ),
                                        clipBehavior: Clip.hardEdge,
                                        child: Column(
                                          children: [
                                            Container(
                                              color: const Color.fromARGB(255, 228, 228, 228),
                                              width: displayWidth,
                                              height: displayHeight,
                                              child: Image.network(
                                                imageFile['file'].path,
                                                fit: BoxFit.cover,
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                    ],
                                  )),
                            ],
                          ),
                          // Positioned(
                          //   child: Container(
                          //     color: Color.fromRGBO(255, 255, 255, 0.8),
                          //     child: Padding(
                          //       padding: const EdgeInsets.all(5),
                          //       child: NarFormLabelWidget(label: imageFile['file'].name),
                          //     ),
                          //   ),
                          //   top: 10,
                          //   left: 10,
                          // ),
                          Positioned(
                            top: 10,
                            right: 70,
                            child: Container(
                              height: 50,
                              width: 50,
                              margin: EdgeInsets.only(left: 10),
                              decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(25)),
                              child: Center(
                                child: IconButton(
                                    onPressed: () {
                                      _downloadPictureFile(imageFile['file'].path, imageFile['file'].name);
                                    },
                                    splashRadius: 20,
                                    icon: Icon(
                                      Icons.download,
                                      color: Colors.black,
                                    )),
                              ),
                            ),
                          ),
                          Positioned(
                            top: 10,
                            right: 10,
                            child: Container(
                              height: 50,
                              width: 50,
                              margin: EdgeInsets.only(left: 10),
                              decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(25)),
                              child: Center(
                                child: IconButton(
                                    onPressed: () {
                                      Navigator.pop(context);
                                    },
                                    splashRadius: 20,
                                    icon: Icon(
                                      Icons.close,
                                      color: Colors.black,
                                    )),
                              ),
                            ),
                          ),
                          // Positioned(
                          //   left: 0,
                          //   top: displayHeight / 2,
                          //   child: widget.allFiles!.length > 1
                          //       ? previousButton(context, filename)
                          //       : SizedBox(height: 0),
                          // ),
                          // Positioned(
                          //   right: 0,
                          //   top: displayHeight / 2,
                          //   child: widget.allFiles!.length > 1
                          //       ? nextButton(context, filename)
                          //       : SizedBox(height: 0),
                          // )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          }
        },
      );
    },
  );
}

_downloadPictureFile(String url, String filename) {
  html.AnchorElement anchorElement = new html.AnchorElement(href: url);
  String nome_file = filename.replaceAll("fotografie/", "");
  anchorElement.download = nome_file;
  anchorElement.target = '_blank';
  anchorElement.click();
}

Future<Size> _calculateImageDimension(BuildContext context, String url) {
  Completer<Size> completer = Completer();
  Image image = Image.network(url);
  image.image.resolve(ImageConfiguration()).addListener(
    ImageStreamListener((ImageInfo info, bool _) {
      completer.complete(Size(
        info.image.width.toDouble(),
        info.image.height.toDouble(),
      ));
    }),
  );
  return completer.future;
}

showUploadPicturesDialog(BuildContext context, StateSetter setState, List<Map<String, dynamic>> picturesImages, ImmaginaProject selectedProject) {
  return showDialog(
    context: context,
    barrierDismissible: false,
    builder: (_) {
      return Center(
        child: BaseNewarcPopup(
            title: 'Carica immagini',
            noButton: true,
            column: StatefulBuilder(builder: (BuildContext context, StateSetter _setState) {
              return SingleChildScrollView(
                child: Container(
                  height: 300,
                  width: 500,
                  child: Column(
                    children: [
                      Expanded(
                        flex: 40,
                        child: GestureDetector(
                          onTap: () async {
                            await _getPictureFromGallery(context, setState, picturesImages);
                            await savePicturesImages(picturesImages, selectedProject, setState);
                            setState(() {});
                            Navigator.of(context).pop(true);
                          },
                          child: MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: Container(
                              decoration: BoxDecoration(
                                  color: Color.fromRGBO(240, 240, 240, 1),
                                  //shape: BoxShape.circle,
                                  borderRadius: BorderRadius.all(Radius.circular(10))),
                              width: 400,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.upload_file,
                                    size: 60,
                                    color: Color.fromRGBO(128, 128, 128, 1),
                                  ),
                                  SizedBox(
                                    height: 30,
                                  ),
                                  NarFormLabelWidget(
                                    label: "Clicca per caricare",
                                    fontWeight: '700',
                                    fontSize: 18,
                                    textColor: Color.fromRGBO(128, 128, 128, 1),
                                    textAlign: TextAlign.center,
                                  )
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 30,
                      ),
                    ],
                  ),
                ),
              );
            })),
      );
    },
  );
}

Future<void> _getPictureFromGallery(BuildContext context, StateSetter setState, List<Map<String, dynamic>> picturesImages) async {
  bool wrongExtension = false;
  final filesList = await FilePicker.platform.pickFiles(
    allowMultiple: true,
    type: FileType.custom,
    allowedExtensions: allowedPicturesExtensions,
  );
  if (filesList != null) {
    for (final file in filesList.files) {
      if (allowedPicturesExtensions.contains(file.extension?.toLowerCase())) {
        final bytes = await file.xFile.readAsBytes();
        Uint8List convertedBlob = bytes;

        if (file.extension?.toLowerCase().contains("heic") ?? false) {
          convertedBlob = await heicToJpeg.HeicToJpegService().convertHeicToJpeg(bytes);
        }
        picturesImages.add({
          'tag': null,
          'file': XFile.fromData(
            convertedBlob,
            name: file.name,
            mimeType: 'image/jpeg',
          )
        });
      } else {
        wrongExtension = true;
      }
    }
    setState(() {});
    if (wrongExtension) {
      await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Center(
            child: BaseNewarcPopup(
              title: 'Errore: Estensione file non valida',
              column: Column(
                children: [
                  Text('Inserisci file di fotografie con estensione: ${allowedPicturesExtensions.join(', ')}'),
                ],
              ),
            ),
          );
        },
      );
    }
  }
}