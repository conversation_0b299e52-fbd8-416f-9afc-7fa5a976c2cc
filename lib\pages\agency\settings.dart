import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/widget/UI/image-picker.dart';
import 'package:path/path.dart' as p;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:url_launcher/url_launcher.dart';
import '../../utils/resizeXFile.dart';
import '../../widget/UI/link.dart';

class AgencySetting extends StatefulWidget {
  final Agency agency;
  final AgencyUser agencyUser;
  final Function? getProfilePicture;

  const AgencySetting(
      {Key? key,
      required this.agency,
      required this.agencyUser,
      this.getProfilePicture})
      : super(key: key);

  @override
  State<AgencySetting> createState() => AgencySettingState();
}

class AgencySettingState extends State<AgencySetting> {
  AgencyUser? agencyUser;
  Agency? agency;

  TextEditingController agencyName = new TextEditingController();
  TextEditingController agencyCity = new TextEditingController();
  TextEditingController agencyAddress = new TextEditingController();
  TextEditingController agencyStreetNumber = new TextEditingController();
  TextEditingController agencyPhone = new TextEditingController();
  TextEditingController agencyAccountEmail = new TextEditingController();

  TextEditingController agencyUserName = new TextEditingController();
  TextEditingController agencyUserSurname = new TextEditingController();
  TextEditingController agencyUserPhone = new TextEditingController();
  TextEditingController agencyUserEmail = new TextEditingController();

  TextEditingController agencylegalEntity = new TextEditingController();
  TextEditingController agencyVat = new TextEditingController();
  TextEditingController agencySedeLegal = new TextEditingController();
  TextEditingController agencySdi = new TextEditingController();
  TextEditingController agencyIban = new TextEditingController();
  TextEditingController confirmPassword = new TextEditingController();
  TextEditingController newPassword = new TextEditingController();

  bool isNewPasswordVisible = false;
  bool isConfirmPasswordVisible = false;

  final FirebaseFirestore _db = FirebaseFirestore.instance;
  final profilePicture = [];
  String? agencyProfilePicture;
  String? profilePictureFilename;
  String? validationMessage;

  String? progressMessage;

  double containerWidth = 0;
  @override
  void initState() {
    agencyUser = widget.agencyUser;
    agency = widget.agency;
    log("widget.agency ---> ${widget.agency.id}");

    agencyName.text = agency!.name!;
    agencyCity.text = agency!.city!;
    agencyAddress.text = agency!.address!;
    agencyStreetNumber.text = agency!.streetNumber!;
    agencyPhone.text = agency!.phone!;
    agencyAccountEmail.text = agencyUser!.email!;
    // agencyProfilePicture = profilePictureFilename;

    agencyUserName.text = agency?.referencePerson.name?? agencyUser!.name!;
    agencyUserSurname.text = agency?.referencePerson.surname??  agencyUser!.surname!;
    agencyUserPhone.text =  agency?.referencePerson.phone?? agencyUser!.phone!;
    agencyUserEmail.text = agency?.referencePerson.email?? agencyUser!.email!;

    agencylegalEntity.text = agency!.legalEntity!;
    agencyVat.text = agency!.vat!;
    agencySedeLegal.text =  agency!.sedeLegale?? agency!.sedeLegaleFull?.toFullAddress();
    agencySdi.text = agency!.sdi!;
    agencyIban.text = agency!.iban!;
    confirmPassword.text = '';
    newPassword.text = '';

    getProfileImageUrl();

    super.initState();
  }

  getProfileImageUrl() async {
    setState(() {
      profilePictureFilename = agencyUser!.profilePicture;
    });
  }

  Future<bool> updateData() async {
    setState(() {
      validationMessage = null;
    });
    if (confirmPassword.text != '' && newPassword.text != '') {
      if (newPassword.text == confirmPassword.text) {
        setState(() {
          validationMessage = null;
        });
        await updatePassword();
      } else {
        setState(() {
          validationMessage = 'Password mismatch!';
        });

        return false;
      }
    }

    if (profilePicture.length > 0) {
      String __profilePictureFilename = 'agency-profile' + p.extension(profilePicture[0].name);
      await uploadProfilePicture( '${appConfig.COLLECT_AGENCIES}/', agency!.id!, __profilePictureFilename, profilePicture[0]);
      final resizedFile = await resizeXFile(profilePicture[0],width: 240,height: 240,quality: 60,customFileName: "agency-profile_thumbnail");
      await uploadProfilePicture('${appConfig.COLLECT_AGENCIES}/',agency!.id!, resizedFile.name, resizedFile);
      agencyUser!.profilePicture = __profilePictureFilename;
      profilePicture.clear();
      profilePictureFilename = __profilePictureFilename;
    } else {
      profilePictureFilename = agencyUser!.profilePicture;
    }
    
    await _db.collection(appConfig.COLLECT_AGENCIES).doc(agency!.id).update(agency!.toMap());

    await _db
        .collection(appConfig.COLLECT_USERS)
        .doc(agencyUser!.id)
        .update(agencyUser!.toMap());

    return true;
  }

  updatePassword() async {
    User? user = await FirebaseAuth.instance.currentUser;

    user!.updatePassword(newPassword.text).then((_) {
      print('password changed');
    }).catchError((error) {
      print({'password no changed', error});
      //Error, show something
    });
  }

  @override
  Widget build(BuildContext context) {
    containerWidth = MediaQuery.of(context).size.width * .75;
    print(profilePictureFilename);

    return SingleChildScrollView(
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              NarFormLabelWidget(
                label: 'Impostazioni',
                fontSize: 20,
                fontWeight: 'bold',
              ),
              SizedBox(height: 15),
              Row(
                children: [
                  Container(
                    width: containerWidth,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(13),
                      border: Border.all(
                        color: Color(0xFFE7E7E7),
                        width: 1.0,
                      ),
                    ),
                    padding: EdgeInsets.all(15),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        NarFormLabelWidget(
                          label: 'Dati Agenzia',
                          fontSize: 16,
                          fontWeight: 'bold',
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: 'Nome Agenzia',
                              hintText: widget.agency.name,
                              // initialValue: widget.agency.name,
                              // controller: agencyName,
                              readOnly: true,
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: 'Indirizzo sede operativa',
                              hintText:  widget.agency.toFullAddress() ?? widget.agency.address ?? "",
                              readOnly: true,
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: 'Telefono sede',
                              hintText: widget.agency.phone,
                              readOnly: true,
                            ),
                            SizedBox(
                              width: 15,
                            ),
                            CustomTextFormField(
                              label: 'E-mail accesso',
                              hintText: widget.agency.email,
                              readOnly: true,
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 15,
                        ),

                        Container(
                          width: containerWidth * .80,
                          height: 1,
                          decoration: BoxDecoration(
                            color: Color(0xFFDCDCDC),
                          ),
                          child: SizedBox(height: 0),
                        ),
                        SizedBox(
                          height: 15,
                        ),

                        // Logo
                        NarFormLabelWidget(
                          label: 'Logo Agenzia',
                          fontSize: 16,
                          fontWeight: 'bold',
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            NarImagePickerWidget(
                                allowMultiple: false,
                                imagesToDisplayInList: 0,
                                removeButton: false,
                                removeButtonText: 'rimuovi',
                                uploadButtonPosition: 'back',
                                showMoreButtonText: '+ espandi',
                                removeButtonPosition: 'bottom',
                                displayFormat: 'row',
                                imageDimension: 100,
                                imageBorderRadius: 50,
                                borderRadius: 7,
                                fontSize: 14,
                                fontWeight: '600',
                                text: 'Carica logo',
                                borderSideColor: Theme.of(context).primaryColor,
                                hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                images: profilePicture,
                                pageContext: context,
                                storageDirectory: 'agencies/',
                                preloadedImages:
                                    profilePictureFilename == null ||
                                            profilePictureFilename == ''
                                        ? []
                                        : [profilePictureFilename],
                                firebaseId: agency!.id,
                                removeExistingOnChange: true)
                          ],
                        ),

                        SizedBox(
                          height: 15,
                        ),

                        Container(
                          width: containerWidth * .80,
                          height: 1,
                          decoration: BoxDecoration(
                            color: Color(0xFFDCDCDC),
                          ),
                          child: SizedBox(height: 0),
                        ),

                        SizedBox(
                          height: 15,
                        ),

                        // Persona di riferimento
                        NarFormLabelWidget(
                          label: 'Persona di riferimento',
                          fontSize: 16,
                          fontWeight: 'bold',
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: 'Nome persona riferimento',
                              hintText: widget.agency.referencePerson.name ?? widget.agencyUser.name,
                              readOnly: true,
                            ),
                            SizedBox(
                              width: 15,
                            ),
                            CustomTextFormField(
                              label: 'Cognome persona riferimento',
                              hintText: widget.agency.referencePerson.surname ?? widget.agencyUser.surname,
                              readOnly: true,
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: 'Telefono persona riferimento',
                              hintText: widget.agency.referencePerson.phone ?? widget.agencyUser.phone,
                              readOnly: true,
                            ),
                            SizedBox(
                              width: 15,
                            ),
                            CustomTextFormField(
                              label: 'E-mail persona riferimento',
                              hintText: widget.agency.referencePerson.email ?? widget.agencyUser.email,
                              readOnly: true,
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Container(
                          width: containerWidth * .80,
                          height: 1,
                          decoration: BoxDecoration(
                            color: Color(0xFFDCDCDC),
                          ),
                          child: SizedBox(height: 0),
                        ),
                        SizedBox(
                          height: 15,
                        ),

                        //Fatturazione e pagamenti
                        NarFormLabelWidget(
                          label: 'Fatturazione e pagamenti',
                          fontSize: 16,
                          fontWeight: 'bold',
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: 'Denominazione',
                              hintText: widget.agency.legalEntity,
                              readOnly: true,
                            ),
                            SizedBox(width:15),
                            CustomTextFormField(
                              label: "Forma Societaria",
                              hintText: widget.agency.formationType,
                              readOnly: true,
                            )
                          ],
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: 'Iban',
                              hintText: widget.agency.iban,
                              readOnly: true,
                            ),
                            SizedBox(
                              width: 15,
                            ),
                            CustomTextFormField(
                              label: 'Codice Fatturazione Elettronico',
                              hintText: widget.agency.sdi,
                              readOnly: true,
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                                label: 'P.Iva',
                                hintText: widget.agency.vat,
                                readOnly: true,
                                ),
                            SizedBox(
                              width: 15,
                            ),
                            CustomTextFormField(
                                label: 'Codice Fiscale',
                                hintText: widget.agency.fiscalCode ?? "",
                                readOnly: true,                         
                            ),
                          ],
                        ),
                        SizedBox(height: 15,),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                                label: 'Indirizzo sede legale',
                                hintText: widget.agency.sedeLegaleFull?.toFullAddress() ?? widget.agency.sedeLegale ?? "",
                                readOnly: true,                          
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Container(
                          width: containerWidth * .80,
                          height: 1,
                          decoration: BoxDecoration(
                            color: Color(0xFFDCDCDC),
                          ),
                          child: SizedBox(height: 0),
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        NarFormLabelWidget(
                          label: 'Modifica password',
                          fontSize: 16,
                          fontWeight: 'bold',
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                                label: 'Forza nuova password',
                                hintText: '',
                                controller: confirmPassword,
                                isObscureText: !isNewPasswordVisible,
                                suffixIcon: IconButton(
                                  icon: isNewPasswordVisible
                                      ? Icon(
                                          Icons.visibility_rounded,
                                          color: Theme.of(context).primaryColor,
                                        )
                                      : Icon(Icons.visibility_off_rounded,
                                          color: Colors.grey),
                                  onPressed: () {
                                    setState(() {
                                      isNewPasswordVisible =
                                          !isNewPasswordVisible;
                                    });
                                  },
                                )),
                            SizedBox(
                              width: 15,
                            ),
                            CustomTextFormField(
                                label: 'Ripeti nuova password',
                                hintText: '',
                                controller: newPassword,
                                isObscureText: !isConfirmPasswordVisible,
                                suffixIcon: IconButton(
                                  icon: isConfirmPasswordVisible
                                      ? Icon(
                                          Icons.visibility_rounded,
                                          color: Theme.of(context).primaryColor,
                                        )
                                      : Icon(Icons.visibility_off_rounded,
                                          color: Colors.grey),
                                  onPressed: () {
                                    setState(() {
                                      isConfirmPasswordVisible =
                                          !isConfirmPasswordVisible;
                                    });
                                  },
                                )),
                            Expanded(
                                flex: 1,
                                child: SizedBox(
                                  height: 0,
                                ))
                          ],
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Container(
                          width: containerWidth * .80,
                          height: 1,
                          decoration: BoxDecoration(
                            color: Color(0xFFDCDCDC),
                          ),
                          child: SizedBox(height: 0),
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        NarFormLabelWidget(
                          label: 'Privacy Policy e Termini e Condizioni',
                          fontSize: 16,
                          fontWeight: 'bold',
                        ),
                        Row(
                          children: [
                            SvgPicture.asset(
                              'assets/icons/arrow.svg',
                              width: 10,
                            ),
                            SizedBox(
                              width: 5,
                            ),
                            NarLinkWidget(
                              fontSize: 12,
                              fontWeight: '600',
                              textColor: AppColor.black,
                              text: 'Privacy Policy',
                              textDecoration: TextDecoration.underline,
                              onClick: () async{
                                await launchUrl(
                                  Uri.parse("https://www.newarc.it/privacy-policy-piattaforma-agenzie/"),
                                );
                              },
                            ),
                            SizedBox(
                              width: 25,
                            ),
                            SvgPicture.asset(
                              'assets/icons/arrow.svg',
                              width: 10,
                            ),
                            SizedBox(
                              width: 5,
                            ),
                            NarLinkWidget(
                              fontSize: 12,
                              fontWeight: '600',
                              textColor: AppColor.black,
                              text: 'Termini e Condizioni',
                              textDecoration: TextDecoration.underline,
                              onClick: () async{
                                await launchUrl(
                                Uri.parse("https://www.newarc.it/termini-e-condizioni-piattaforma-agenzie/"),
                                );
                              },
                            )
                          ],
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        NarFormLabelWidget(
                          label:
                              validationMessage != '' ? validationMessage : '',
                          fontSize: 12,
                          textColor: Colors.red,
                        ),

                        SizedBox(
                          height: 50,
                        ),

                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            NarFormLabelWidget(
                              label:
                                  progressMessage != '' ? progressMessage : '',
                              fontSize: 12,
                            )
                          ],
                        ),
                        SizedBox(height: 10),

                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            BaseNewarcButton(
                                buttonText: "Salva",
                                onPressed: () async {
                                  setState(() {
                                    profilePictureFilename = '';
                                    progressMessage = 'Salvataggio in corso...';
                                  });
                                  bool response = await updateData();
                                  
                                  if (response == true) {
                                    
                                    setState(() {
                                      progressMessage = '';
                                      widget.getProfilePicture!();
                                      profilePicture.clear();
                                    });
                                    await showAlertDialog(
                                        context,
                                        "Salvataggio",
                                        "Informazioni agenzia salvate con successo");
                                  } else {
                                    setState(() {
                                      progressMessage =
                                          'Si è verificato un errore. Contatta l\'assistenza.';
                                    });
                                  }
                                })
                          ],
                        )
                      ],
                    ),
                  ),
                ],
              )
            ],
          )
        ],
      ),
    );
  }
}
