import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
//import 'package:newarc_platform/widget/agency/custom_chart.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/pages/work/agency_settings_widget.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/work/inner_side_menu_entry.dart';

class AgenzieNewarc extends StatefulWidget {
  const AgenzieNewarc(
      {Key? key,
      // required this.agency,
      // required this.agencyUser,
      required this.responsive,
      this.getProfilePicture})
      : super(key: key);

  final bool responsive;
  // final Agency agency;
  final Function? getProfilePicture;

  // final AgencyUser agencyUser;

  @override
  State<AgenzieNewarc> createState() => _AgenzieNewarcState();
}

class _AgenzieNewarcState extends State<AgenzieNewarc> {
  bool loading = false;
  List<AcquiredContact> contacts = [];
  int acquiredContacts = 0;
  int acquiredContactsLastMonth = 0;
  int timestamp = DateTime.now().millisecondsSinceEpoch;
  int? indexSelected = 0;
  Agency? selectedAgency;
  List<Agency> agencies = [];
  List<AgencyUser> agencyUsers = [];

  @override
  void initState() {
    loading = true;
    if (mounted) {
      fetchAgencies();
    }

    super.initState();
  }

  Future<void> fetchAgencies() async {
    QuerySnapshot<Map<String, dynamic>> collectionSnapshot =
        await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_AGENCIES)
            .get();

    List<Agency> _agencies = [];
    for (var element in collectionSnapshot.docs) {
      try {
        var _tmp = Agency.fromDocument(element.data(), element.id);
        _agencies.add(_tmp);

        //TODO remove .where to speed up query
        QuerySnapshot<Map<String, dynamic>> _agencyUser =
            await FirebaseFirestore.instance
                .collection(appConfig.COLLECT_USERS)
                .where('agencyId', isEqualTo: _tmp.id)
                .get();
        agencyUsers.add(AgencyUser.fromDocument(
            _agencyUser.docs.first.data(), _agencyUser.docs.first.id));
      } catch (e) {
        // print("error in document ${element.id}");
        // print(e);
      }
    }
    setState(() {
      agencies = _agencies;
      loading = false;
      // print(loading);
    });
  }

  void changeSelectedAgency(int index) {
    setState(() {
      indexSelected = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return loading
        ? Center(
            child: CircularProgressIndicator(
            color: Theme.of(context).primaryColor,
          ))
        : Column(
            children: [
              Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    NarFormLabelWidget(
                      label: 'Gestisci agenzie',
                      fontSize: 22,
                      fontWeight: 'bold',
                    ),
                    //BaseNewarcButton(buttonText: "Aggiungi agenzia", onPressed: () {})
                  ]),
              SizedBox(height: 20),
              Expanded(
                child: Row(
                  children: [
                    Container(
                      width: 330,
                      padding:
                          EdgeInsets.symmetric(horizontal: 10, vertical: 20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(13),
                        border: Border.all(color: Color(0xffe7e7e7)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label: "TORINO",
                            textColor: Color(0xff757575),
                            fontWeight: '700',
                            fontSize: 15,
                          ),
                          SizedBox(height: 8),
                          Expanded(
                              child: ListView(
                            children: List.generate(agencies.length, (index) {
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 8.0),
                                child: InnerSideMenuEntry(
                                    index: index,
                                    label: agencies.elementAt(index).name,
                                    isSelected: indexSelected == index,
                                    changeSelectedAgency: changeSelectedAgency),
                              );
                            }),
                          )),
                        ],
                      ),
                    ),
                    SizedBox(width: 10),
                    Expanded(
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 30, vertical: 10),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(13),
                          border: Border.all(color: Color(0xffe7e7e7)),
                        ),
                        child: AgencySettingWidget(
                            agency: agencies.elementAt(indexSelected!),
                            agencyUser: agencyUsers.elementAt(indexSelected!),
                            getProfilePicture: widget.getProfilePicture),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
  }

  Widget getIndicatorWidget(String title, String value) {
    return Container(
      height: 138,
      width: 205,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 8.0, left: 8),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                )
              ],
            ),
          ),
          SizedBox(height: 18),
          loading
              ? CircularProgressIndicator(color: Theme.of(context).primaryColor)
              : Text(
                  value,
                  style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                    fontSize: 50,
                  ),
                )
        ],
      ),
    );
  }
}
