class NewarcMaterial{
  int? indexPlace;
  String? productName;
  String? supplierName;
  String? group;
  String? transportType; //delivery/collection
  String? measureUnit;
  double? quantity;
  double? price;
  double? cost;
  bool? isAccount;
  double? account;
  
  String? status;
  String? quality;
  String? deposit;

  bool? isPaid;
  int? paidOn;

  bool? isAccountPaid;
  int? accountPaidOn;

  String? uniqueId;
  double? paidAmount;
  Map? invoicePath;
  bool? isInvoiceUploaded;
  List? invoiceImages;


  NewarcMaterial(Map<String, dynamic> fixedProperty) {
    this.indexPlace = fixedProperty['indexPlace'];
    this.productName = fixedProperty['productName'];
    this.supplierName = fixedProperty['supplierName'];
    this.group = fixedProperty['group'];
    this.transportType = fixedProperty['transportType'];
    this.measureUnit = fixedProperty['measureUnit'];
    this.quantity = fixedProperty['quantity'];
    this.price = fixedProperty['price'];
    this.cost = fixedProperty['cost'];
    this.isAccount = fixedProperty['isAccount'];
    this.account = fixedProperty['account'];
    this.status = fixedProperty['status'];
    this.quality = fixedProperty['quality'];
    this.deposit = fixedProperty['deposit'];
    this.paidOn = fixedProperty['paidOn'];
    this.isPaid = fixedProperty['isPaid'];
    this.isAccountPaid = fixedProperty['isAccountPaid'];
    this.accountPaidOn = fixedProperty['accountPaidOn'];
    this.uniqueId = fixedProperty['uniqueId'];
    this.paidAmount = fixedProperty['paidAmount'];
    this.invoicePath = fixedProperty['invoicePath'];
    invoiceImages = (fixedProperty['invoicePath'] != null && (fixedProperty['invoicePath'] as Map).isNotEmpty) ? [fixedProperty['invoicePath']["filename"]] : [];
    isInvoiceUploaded = (fixedProperty['invoicePath'] != null && (fixedProperty['invoicePath'] as Map).isNotEmpty) ? true : false;

  }

  NewarcMaterial.empty(){
    this.indexPlace = -1;
    this.productName = '';
    this.supplierName = '';
    this.group = '';
    this.transportType = 'Consegna';
    this.measureUnit = 'mq';
    this.quantity = 0;
    this.price = 0;
    this.cost = 0;
    this.isAccount = false;
    this.account = 0;
    this.status = '';
    this.quality = '';
    this.deposit = '';
    this.paidOn = 0;
    this.isPaid = false;
    this.isAccountPaid = false;
    this.accountPaidOn = 0;
    this.uniqueId = "";
    this.paidAmount = 0;
    this.invoicePath = {};
    this.isInvoiceUploaded = false;
    this.invoiceImages = [];

  }

  Map<String, dynamic> toMap() {
    return {
      'indexPlace': this.indexPlace,
      'productName': this.productName,
      'supplierName': this.supplierName,
      'group': this.group,
      'transportType': this.transportType,
      'measureUnit': this.measureUnit,
      'quantity': this.quantity,
      'price': this.price,
      'cost': this.cost,
      'isAccount': this.isAccount,
      'account': this.account,
      'status': this.status,
      'quality': this.quality,
      'deposit': this.deposit,
      'paidOn': this.paidOn,
      'isPaid': this.isPaid,
      'isAccountPaid': this.isAccountPaid,
      'accountPaidOn': this.accountPaidOn,
      'uniqueId': this.uniqueId,
      'paidAmount': this.paidAmount,
      'invoicePath': this.invoicePath,


    };
  }

  NewarcMaterial.fromDocument(Map<String, dynamic> data, int index) {

    try {
      this.indexPlace = index;
      this.productName = data['productName'] == null ? '' : data['productName'];
      this.supplierName = data['supplierName'] == null ? '' : data['supplierName'];
      this.group = data['group'] == null ? '' : data['group'];
      this.transportType = data['transportType'] == null ? '' : data['transportType'];
      this.measureUnit = data['measureUnit'] == null ? '' : data['measureUnit'];
      this.quantity = data['quantity'] == null ? 0 : data['quantity'];
      this.price = data['price'] == null ? 0 : data['price'];
      this.cost = data['cost'] == null ? 0 : data['cost'];
      this.isAccount = data['isAccount'] == null ? false : data['isAccount'];
      this.account = data['account'] == null ? 0 : data['account'];
      this.status = data['status'] == '' ? '' : data['status'];
      this.quality = data['quality'] == '' ? '' : data['quality'];
      this.deposit = data['deposit'] == '' ? '' : data['deposit'];
      this.paidOn = data['paidOn'] == null ? 0 : data['paidOn'];
      this.isPaid = data['isPaid'] == null ? false : data['isPaid'];

      this.isAccountPaid = data['isAccountPaid'] == null ? false : data['isAccountPaid'];
      this.accountPaidOn = data['accountPaidOn'] == null ? 0 : data['accountPaidOn'];
      this.uniqueId = data['uniqueId'] == null ? '' : data['uniqueId'];
      this.paidAmount = data['paidAmount'] == null ? 0 : data['paidAmount'];
      this.invoicePath = data['invoicePath'] == null ? {} : data['invoicePath'];
      invoiceImages = (data['invoicePath'] != null && (data['invoicePath'] as Map).isNotEmpty) ? [data['invoicePath']["filename"]] : [];
      isInvoiceUploaded = (data['invoicePath'] != null && (data['invoicePath'] as Map).isNotEmpty) ? true : false;
      // print({'data-->!',this.toMap()}); 
    } catch (e, s) {
      // print({'newarcMateria.dart', e, s});
    }
    
  }

}