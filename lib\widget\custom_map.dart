import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import 'package:flutter/services.dart' show rootBundle;
import 'package:newarc_platform/classes/comparabile.dart';
import 'dart:ui' as ui;

import 'package:newarc_platform/pages/models/home_scouter.model.dart';
import 'package:provider/provider.dart';

class CustomMap extends StatefulWidget {
  CustomMap({Key? key, this.comparabili}) : super(key: key);

  List<Comparabile>? comparabili;

  @override
  _CustomMapState createState() => _CustomMapState();
}

class _CustomMapState extends State<CustomMap> {
  GoogleMapController? _controller;
  String? style;
  Set<Marker> markers = Set();

  @override
  void initState() {
    super.initState();
    _setMap();
  }

  _setMap() async {
    String _style = await rootBundle.loadString('assets/map_style.json');
    Set<Marker> m = await _generateMarkers();
    setState(() {
      markers = m;
      style = _style;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(right: 18, bottom: 15),
      child: style == null
          ? Center(
              child: CircularProgressIndicator(),
            )
          : GoogleMap(
              mapType: MapType.none,
              initialCameraPosition: CameraPosition(
                target: LatLng(45.05, 7.666667),
                zoom: 12,
              ),
              markers: markers,
              onMapCreated: (GoogleMapController controller) async {
                _controller = controller;

                _controller!.setMapStyle(style);
              },
            ),
    );
  }

  _generateMarkers() async {
    List<Marker> _markers = [];
    if (widget.comparabili == null) {
      return Set();
    }

    //final Uint8List markerIcon =
    //  await getBytesFromAsset('assets/icons/map_icons/orange.png', 1);

    for (Comparabile c in widget.comparabili!) {
      _markers.add(
        Marker(
          markerId: MarkerId(c.id!.toString()),
          position: LatLng(c.latitude!, c.longitude!),
          //icon: BitmapDescriptor.fromBytes(markerIcon),
          onTap: () {
            print(c.id.toString());
          },
        ),
      );
    }
    return _markers.toSet();
  }

  Future<Uint8List> getBytesFromAsset(String path, int width) async {
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: 1, targetHeight: 1);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }
}
