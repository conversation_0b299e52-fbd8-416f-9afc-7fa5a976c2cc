import 'package:async/async.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/comparabile.dart';
import 'package:newarc_platform/functions/ai.dart';
import 'package:newarc_platform/functions/algorithms.dart';
import 'package:newarc_platform/functions/db.dart';
import 'package:newarc_platform/pages/models/home_scouter.model.dart';
import 'package:newarc_platform/widget/comparabile_popup.dart';
import 'package:newarc_platform/widget/custom_app_bar.dart';
import 'package:newarc_platform/widget/custom_drawer.dart';
import 'package:newarc_platform/widget/custom_icon_button.dart';
import 'package:newarc_platform/widget/custom_map.dart';
import 'package:newarc_platform/widget/scouter_home_entry.dart';
import 'package:newarc_platform/widget/search_popup.dart';
import 'package:newarc_platform/widget/zone_filter.dart';
import 'package:provider/provider.dart';

class HomeScouter extends StatefulWidget {
  HomeScouter({Key? key}) : super(key: key);

  static const String route = '/home';

  @override
  _HomeScouterState createState() => _HomeScouterState();
}

class _HomeScouterState extends State<HomeScouter> {
  bool loading = false;
  AsyncMemoizer? _memoizer;
  List<Comparabile>? comparabili;

  @override
  void initState() {
    super.initState();
    _queryAws();
  }

  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: scaffoldKey,
      drawer: CustomDrawer(),
      drawerEnableOpenDragGesture: false,
      body: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          bool responsive = constraints.maxWidth < 600;
          bool tabletSize = constraints.maxWidth < 1050;
          return ChangeNotifierProvider(
            create: (context) => HomeScouterModel(scaffoldKey),
            child: Row(
              children: [
                tabletSize ? Container() : CustomDrawer(),
                Expanded(
                  child: Column(
                    children: [
                      CustomAppBar(
                        responsive: responsive || tabletSize,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                          left: 18.0,
                          right: 18,
                          top: 30,
                          bottom: 15,
                        ),
                        child: !responsive
                            ? Row(
                                children: [
                                  Row(
                                    children: _getLocationHeader(),
                                  ),
                                  Expanded(
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      mainAxisSize: MainAxisSize.max,
                                      children: _getSearchActions(),
                                    ),
                                  )
                                ],
                              )
                            : Column(
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: _getLocationHeader(),
                                  ),
                                  SizedBox(height: 10),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    mainAxisSize: MainAxisSize.max,
                                    children: _getSearchActions(),
                                  ),
                                ],
                              ),
                      ),
                      Consumer<HomeScouterModel>(builder: (context, model, _) {
                        return Expanded(
                          child: Stack(
                            alignment: Alignment.topCenter,
                            children: [
                              !loading
                                  ? Row(
                                      children: [
                                        Expanded(
                                          child: Container(
                                            child: Scrollbar(
                                              child: ListView.builder(
                                                itemCount: comparabili!.length,
                                                itemBuilder: (context, index) {
                                                  Comparabile c = comparabili!
                                                      .elementAt(index);
                                                  if (model.selectedZone ==
                                                          "None" ||
                                                      model.selectedZone ==
                                                          c.zonaOmi) {
                                                    return ScouterHomeEntry(
                                                      comparabile: comparabili!
                                                          .elementAt(index),
                                                    );
                                                  } else {
                                                    return Container();
                                                  }
                                                },
                                              ),
                                            ),
                                          ),
                                        ),
                                        !responsive
                                            ? Expanded(
                                                child: Stack(
                                                  children: [
                                                    CustomMap(
                                                        comparabili:
                                                            comparabili /*model
                                                          .getFilteredComparabili(),*/
                                                        ),
                                                    Positioned(
                                                      top: 10,
                                                      right: 30,
                                                      child: CustomIconButton(
                                                        label: "Zone",
                                                        icon: Icon(Icons
                                                            .pin_drop_outlined),
                                                        width: 104,
                                                        height: 46,
                                                        color: Colors.white,
                                                        function:
                                                            (BuildContext ctx) {
                                                          model
                                                              .showZoneFilter();
                                                        },
                                                      ),
                                                    )
                                                  ],
                                                ),
                                              )
                                            : Container()
                                      ],
                                    )
                                  : Center(
                                      child: CircularProgressIndicator(),
                                    ),
                              /*FutureBuilder(
                                future: queryAws(),
                                builder:
                                    (context, AsyncSnapshot<dynamic> snapshot) {
                                  if (snapshot.hasError) {
                                    return Text(
                                      snapshot.error.toString(),
                                      style:
                                          Theme.of(context).textTheme.headline1,
                                    );
                                  } else if (snapshot.hasData) {
                                    if (snapshot.data == null ||
                                        snapshot.data!.length == 0) {
                                      return Text(
                                          "Servizio attualmente non disponibile");
                                    }
                                    for (Comparabile c in snapshot.data!) {
                                      if (c.sqmPrice != null &&
                                          c.zonaOmi != null) {
                                        model.comparabili.add(c);
                                      }
                                    }
                                    model.fetchingDone = true;
                                    return Row(
                                      children: [
                                        Expanded(
                                          child: Container(
                                            child: Scrollbar(
                                              child: ListView.builder(
                                                itemCount:
                                                    model.comparabili.length,
                                                itemBuilder: (context, index) {
                                                  Comparabile c = model
                                                      .comparabili
                                                      .elementAt(index);
                                                  if (model.selectedZone ==
                                                          "None" ||
                                                      model.selectedZone ==
                                                          c.zonaOmi) {
                                                    return ScouterHomeEntry(
                                                      comparabile: model
                                                          .comparabili
                                                          .elementAt(index),
                                                    );
                                                  } else {
                                                    return Container();
                                                  }
                                                },
                                              ),
                                            ),
                                          ),
                                        ),
                                        !responsive
                                            ? Expanded(
                                                child: Stack(
                                                  children: [
                                                    CustomMap(
                                                      comparabili: model
                                                          .getFilteredComparabili(),
                                                    ),
                                                    Positioned(
                                                      top: 10,
                                                      right: 30,
                                                      child: CustomIconButton(
                                                        label: "Zone",
                                                        icon: Icon(Icons
                                                            .pin_drop_outlined),
                                                        width: 104,
                                                        height: 46,
                                                        color: Colors.white,
                                                        function:
                                                            (BuildContext ctx) {
                                                          model
                                                              .showZoneFilter();
                                                        },
                                                      ),
                                                    )
                                                  ],
                                                ),
                                              )
                                            : Container()
                                      ],
                                    );
                                  } else {
                                    return Center(
                                      child: CircularProgressIndicator(),
                                    );
                                  }
                                },
                              ),*/
                              Consumer<HomeScouterModel>(
                                builder: (context, model, _) {
                                  if (model.displaySearch) {
                                    return SearchPopup();
                                  } else if (model.displayZoneFilter) {
                                    return ZoneFilter(
                                      comparabili: model.comparabili,
                                    );
                                  } else if (model.selectedComparabile !=
                                      null) {
                                    return ComparabilePopup();
                                  } else {
                                    return Container();
                                  }
                                },
                              )
                            ],
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  List<Widget> _getSearchActions() {
    return [
      CustomIconButton(
        label: "Cerca tra gli annunci",
        icon: Icon(
          Icons.search,
          color: Color(0xff6C6C6C),
        ),
        width: 100,
        height: 35,
        color: Colors.white,
        function: (BuildContext ctx) {
          Provider.of<HomeScouterModel>(ctx, listen: false).searchPopupShow();
        },
      ),
      SizedBox(width: 14),
      CustomIconButton(
        label: "Ordina",
        icon: Icon(
          Icons.double_arrow_sharp,
          color: Color(0xff6C6C6C),
        ),
        width: 100,
        height: 35,
        color: Colors.white,
        function: () => {},
      ),
      SizedBox(width: 14),
      CustomIconButton(
        label: "Filtra",
        icon: Icon(
          Icons.filter_alt_outlined,
          color: Color(0xff6C6C6C),
        ),
        width: 100,
        height: 35,
        color: Colors.white,
        function: () => {},
      )
    ];
  }

  List<Widget> _getLocationHeader() {
    return [
      Text(
        "Ricerca immobili",
        style: Theme.of(context).textTheme.headlineLarge,
      ),
      SizedBox(width: 24),
      Text(
        "Torino",
        style: Theme.of(context).textTheme.headlineLarge!.copyWith(
              color: Theme.of(context).primaryColor,
            ),
      ),
    ];
  }

  _queryAws() async {
    setState(() {
      loading = true;
    });
    var _comparabili = await queryAws();
    setState(() {
      comparabili = _comparabili;
      loading = false;
    });
  }
}
