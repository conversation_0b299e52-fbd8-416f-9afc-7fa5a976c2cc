class NewarcNotification {
  String? firebaseId;
  int? date;
  String? message;
  String? type; //Tipi: ['contact', ?]
  String? linkedId;

  NewarcNotification.fromDocument(dynamic data, String id) {
    this.firebaseId = id;

    this.date = data['date'] ?? 0;
    this.message = data['message'] ?? "Nessun messaggio";
    this.type = data['type'] ?? 'contact';
    this.linkedId = data['linkedId'];
  }

  NewarcNotification.empty() {
    this.date = DateTime.now().millisecondsSinceEpoch;
    this.type = 'contact';
  }

  Map<String, dynamic> toMap() {
    return {
      'date': this.date,
      'message': this.message,
      'type': this.type,
      'linkedId': this.linkedId
    };
  }
}
