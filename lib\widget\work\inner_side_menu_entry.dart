import 'package:flutter/material.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class InnerSideMenuEntry extends StatefulWidget {
  final int index;
  final String? label;
  final bool isSelected;
  final Function changeSelectedAgency;

  const InnerSideMenuEntry(
      {Key? key,
      required this.index,
      this.label,
      required this.isSelected,
      required this.changeSelectedAgency})
      : super(key: key);

  @override
  State<InnerSideMenuEntry> createState() => _InnerSideMenuEntryState();
}

class _InnerSideMenuEntryState extends State<InnerSideMenuEntry> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () async {
          widget.changeSelectedAgency(widget.index);
        },
        child: Container(
          height: 50,
          decoration: BoxDecoration(
            color: widget.isSelected
                ? Theme.of(context).primaryColor
                : Color(0xfff0f0f0),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                NarFormLabelWidget(
                  label: widget.label,
                  textColor: widget.isSelected ? Colors.white : Colors.black,
                  fontSize: 14,
                  fontWeight: '700',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
