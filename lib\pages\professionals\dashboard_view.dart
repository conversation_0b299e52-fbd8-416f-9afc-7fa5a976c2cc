import 'package:flutter/material.dart';

import 'package:newarc_platform/classes/professionals.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';


class Dashboard extends StatefulWidget {
  const Dashboard(
      {Key? key,
      required this.professional,
      required this.professionalsUser,
      required this.responsive})
      : super(key: key);

  final bool responsive;
  final Professional professional;
  final ProfessionalsUser professionalsUser;

  @override
  State<Dashboard> createState() => _DashboardState();
}

class _DashboardState extends State<Dashboard> {
  bool loading = false;
  int timestamp = DateTime.now().millisecondsSinceEpoch;
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ListView(
      primary: true,
      children: [
        Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NarFormLabelWidget(
                label: 'Dashboard',
                fontSize: 22,
                fontWeight: 'bold',
              ),
            ]),
        Sized<PERSON><PERSON>(height: 20),
        Container(
          height: 180,
          child: ListView(
            primary: false,
            scrollDirection: Axis.horizontal,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  NarFormLabelWidget(
                    label: 'D&P - Dashboard Professionals',
                    fontSize: 16,
                    fontWeight: 'bold',
                    textColor: Theme.of(context).primaryColor,
                  ),
                ]
              )
            ],
          ),
        ),
      ],
    );
  }
}