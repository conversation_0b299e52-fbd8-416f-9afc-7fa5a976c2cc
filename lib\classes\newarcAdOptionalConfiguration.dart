class NewarcAdOptionalConfiguration {
  String? productName;
  String? adOptionalCategoryId;
  String? adOptionalCategoryName;
  String? description;
  double? price;
  bool? isActive;
  String? id;
  List<Map>? coverImages;

  //Only for UI
  List? coverImageList;



  Map<String, Object?> toMap() {
    return {
      'productName': productName,
      'isActive': isActive,
      'id': id,
      'adOptionalCategoryId': adOptionalCategoryId,
      'adOptionalCategoryName': adOptionalCategoryName,
      'description': description,
      'price': price,
      'coverImages': coverImages,
      'coverImageList': coverImageList,
    };
  }
  NewarcAdOptionalConfiguration.empty() {
    this.productName = '';
    this.price = 0.0;
    this.isActive = false;
    this.coverImages = [];
    this.adOptionalCategoryId = '';
    this.adOptionalCategoryName = '';
    this.id = '';
    this.description = '';
    this.coverImageList = [];
  }
  NewarcAdOptionalConfiguration.fromDocument(Map<String, dynamic> data) {
    try {
      this.price = data['price'];
      this.productName = data['productName'];
      this.isActive = data['isActive'];
      this.adOptionalCategoryId = data['adOptionalCategoryId'];
      this.adOptionalCategoryName = data['adOptionalCategoryName'];
      this.description = data['description'];
      this.id = data['id'];
      this.coverImages = [];
      this.coverImageList = [];
      if (data['coverImages'] != null) {
        this.coverImages = List<Map<String, dynamic>>.from(data['coverImages']);
        this.coverImageList = data['coverImages']
            .where((img) => img['fileName'] != null && img['fileName'] != "")
            .map((img) => img['fileName'])
            .toList();
      }
    } catch (e, s) {
      print({ 'NewarcAdOptionalConfiguration Class Error ------->', e, s});
    }
  }
}