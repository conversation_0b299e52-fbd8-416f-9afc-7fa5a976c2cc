class AgencyValuatorSubmissionComment {
  String? firebaseId;
  int? date;
  String? message;
  String? authorId;
  String? contactId;
  bool? isAvailable; //if the comment is deleted then this variable will be set to false 
  
  AgencyValuatorSubmissionComment({
    this.firebaseId,
    required this.date,
    required this.message,
    required this.authorId,
    required this.contactId,
    this.isAvailable,

  }); 

  AgencyValuatorSubmissionComment.fromDocument(dynamic data, String id) {
    this.firebaseId = id;
    this.date = data['date'] ?? 0;
    this.message = data['message'] ?? "Nessun messaggio";
    this.authorId = data['authorId'];
    this.contactId = data['contactId'];
    this.isAvailable = data['isAvailable'] ?? true;
    
  }

  Map<String, Object?> toJson() {
    return {
      'date': this.date,
      'message': this.message,
      'authorId': this.authorId,
      'contactId': this.contactId,
      'isAvailable': this.isAvailable
    };
  }

  Map<String, dynamic> toMap() {
    return {
      'date': this.date,
      'message': this.message,
      'authorId': this.authorId,
      'contactId': this.contactId,
      'isAvailable': this.isAvailable
    };
  }
}
