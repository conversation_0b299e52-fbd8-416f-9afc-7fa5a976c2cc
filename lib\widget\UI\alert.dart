import 'package:flutter/material.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

NarAlertDialog(context, title, message, List<Widget> actionArray) async {
  
  if( actionArray.length == 0 ) {
    actionArray.add(
      GestureDetector(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 14, vertical: 10),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                "OK",
                style: TextStyle(color: Colors.white),
              ),
            ),
            onTap: () {
              Navigator.of(context).pop(true);
            },
          ));
  }
  await showDialog( 
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: NarFormLabelWidget(label: title, fontWeight: '900', textColor: Theme.of(context).primaryColor, fontSize: 20,),
        content: Container(
          // height: 200,
          width: 300,
          child: NarFormLabelWidget(label: message, fontWeight: '800'),
        ),
        actionsPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        actions: actionArray,
        actionsAlignment: MainAxisAlignment.spaceBetween,
      );
    },
  );
}
