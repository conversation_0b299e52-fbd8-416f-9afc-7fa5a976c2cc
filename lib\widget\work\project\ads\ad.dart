import 'dart:async';
import 'dart:developer';
import 'dart:js_interop';
import 'package:async/async.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/app_config.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/agency/custom_chart_new.dart';
import 'package:newarc_platform/widget/custom_drawer.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:google_maps_webservice/places.dart';
import 'package:image_picker/image_picker.dart';
import 'package:newarc_platform/widget/UI/input.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/textarea.dart';
import 'package:newarc_platform/widget/UI/checkbox.dart';
import 'package:newarc_platform/widget/UI/button.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/image-picker.dart';
import 'package:newarc_platform/widget/UI/alert.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/classes/property.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/widget/UI/file-picker.dart';

class AdForm extends StatefulWidget {
  NewarcProject? project;
  Property? property;
  final Function? initialFetchProperties;
  List<bool>? isInputChangeDetected = [];

  AdForm(
      {Key? key,
      this.project,
      this.property,
      this.initialFetchProperties,
      this.isInputChangeDetected })
      : super(key: key);

  static const String route = '/property/add';

  @override
  _AdFormState createState() => _AdFormState();
}

class _AdFormState extends State<AdForm> {
  bool loading = false;
  AsyncMemoizer? _memoizer;
  String progressMessage = '';

  bool isAdSectionActive = true;

  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final _formKey = GlobalKey<FormState>();
  // TextEditingController? _passwordController = new TextEditingController();
  // TextEditingController? dropdownController = new TextEditingController();
  // TextEditingController? textareaController = new TextEditingController();

  Location? geoLocation;
  Map? location;
  Map? addressComponents = {
    'street': '',
    'civico': '',
    'city': '',
    'province': '',
    'state': '',
    'country': ''
  };

  DateTime? selectedStartDate;
  DateTime? selectedEndDate;
  DateTime? selectedLimitDate;

  TextEditingController? txtconPropertyName = new TextEditingController();
  TextEditingController? txtconPropertyZoneName = new TextEditingController();
  TextEditingController? txtconPropertyTypeName = new TextEditingController();
  // TextEditingController? txtconLocation = new TextEditingController();
  TextEditingController? txtconCivic = new TextEditingController();
  TextEditingController? txtconCity = new TextEditingController();
  TextEditingController? txtconDescription = new TextEditingController();
  TextEditingController? txtconAreaMq = new TextEditingController();
  TextEditingController? txtconBaths = new TextEditingController();
  TextEditingController? txtconLocals = new TextEditingController();
  TextEditingController? txtconBedrooms = new TextEditingController();
  TextEditingController? txtconFloors = new TextEditingController();
  TextEditingController? txtconBasePrice = new TextEditingController();
  

  TextEditingController? txtconStartDate = new TextEditingController();
  TextEditingController? txtconEndDate = new TextEditingController();
  TextEditingController? txtconLimitDate = new TextEditingController();
  TextEditingController? txtconVirtualTour = new TextEditingController();
  TextEditingController? txtconCurrentVirtualTour = new TextEditingController();
  TextEditingController? publicStatusController = new TextEditingController();
  TextEditingController? txtconDateStart = new TextEditingController();
  TextEditingController? txtconDateEnd = new TextEditingController();
  TextEditingController? txtconDateLimit = new TextEditingController();

  String googleApikey =
      isProduction ? GOOGLE_PRODUCTION_API_KEY : GOOGLE_STAGING_API_KEY;

  List<String> dropdownOptions = ['Test', 'ff 2'];
  Map<String, dynamic> publicStatusOptions = {
    'Personalizzabile': false,
    "In trattativa": false,
    "Venduto": false,
    "Luxury": false,
    
  };

  
  List<String> zonesList = [];
  
  List<String> typesList = appConst.propertyTypesList;

  List<String> bathsList = List<String>.generate(10, (i) => (i + 1).toString());
  List<String> bedroomsList =
      List<String>.generate(5, (i) => (i + 1).toString());
  List<String> floorsList =
      List<String>.generate(15, (i) => (i + 1).toString());
  List<String> localsList =
      List<String>.generate(10, (i) => (i + 1).toString());

  List<String> predefinedStyleList = [
    "Base senza configurazione",
    "Stoccolma",
    "Londra",
    "Parigi"
  ];
  Map<String, String> predefinedStyleDescription = {
    "Configurazione Base":
        "Tinta pareti: grigio chiaro opaco\nPorte interne: legno rovere tamburato bianco\nPavimentazione: grès porcellanato effetto legno\nRivestimento bagno principale: grès porcellanato beige chiaro\n Rivestimento bagno secondario: grès porcellanato effetto marmo venato grigio",
    "Stoccolma":
        "Tinta pareti: grigio chiaro opaco\nPorte interne: legno rovere tamburato bianco\nPavimentazione: grès porcellanato effetto legno\nRivestimento bagno principale: grès porcellanato beige chiaro\n Rivestimento bagno secondario: grès porcellanato effetto marmo venato grigio",
    "Londra":
        "Tinta pareti: bianco opaco\nPorte interne: tamburato laccato bianco\n Pavimentazione: parquet legno rovere spazzolato chiaro\nRivestimento bagno principale: grès porcellanato effetto marmo venato grigio\n Rivestimento bagno secondario: piastrelline bianche rettangolari",
    "Parigi":
        "Tinta pareti: grigio chiaro opaco\nPorte interne: legno rovere tamburato beige\nPavimentazione: parquet legno rovere naturale\nRivestimento bagno principale: grès porcellanato beige chiaro\nRivestimento bagno secondario: grès porcellanato effetto marmo venato oro"
  };
  List<String> selectableStyleList = [
    "Sel Style 1",
    "Sel Style 2",
    "Sel Style 3",
    "Sel Style 4"
  ];

  bool hasPropertyFeatureError = false;
  bool hasStyleError = false;
  bool hasImageError = false;

  bool activateNightTimePicture = false;
  bool activateVirtualTourProject = false;
  bool activateVideoRender = false;
  bool activateVirtualTourCurrent = false;

  Map<String, bool> currentFeatures = appConst.houseFeatures;
  Map<String, bool> propertyFeatures = appConst.houseFeatures;

  Map<String, bool> peculiarities = {
    'Piano alto': false,
    'Vicinanza Metro': false,
    'Ampi balconi': false,
    'Doppia esposizione': false,
    'Tripla esposizione': false,
    'Quadrupla esposizione': false,
    'Grande zona living': false,
    'Doppi servizi': false,
    'Stabile signorile': false,
    'Stabile videosorvegliato': false,
  };

  num styleCounter = 0;
  Map<String, Widget> styles = {};
  List<Map<String, dynamic>> styleValues = [];
  List<num> styleValuesRetained = [];

  // TODO optionalFeatures should be packaged into a class
  String? optionalFeatureValue;

  Map<String, bool> optionalFeatures = {
    'Condizionatore': false,
    'Antifurto': false,
    'Assicurazione casa': false,
    'Cucina': false
  };

  Map<String, String> optionalFeaturesDescription = {
    'Condizionatore':
        'Climatizzatore Ariston Alys Plus R-32 trial split, Potenza 9000 btu, wifi, 12 velocità',
    'Antifurto':
        'Pannello di controllo, Sensori volumetrici con fotocamera, Cartelli dissuasori, Sirena, Sensori porte e finestre, App e lettore chiavi, Fumogeno zero vision',
    'Assicurazione casa':
        'Incendio e scoppio, Eventi naturali, Arredi e contenuto, Furto',
    'Cucina':
        'Cucina ArTre modello Up Design in composizione lineare 425x200 con isola frontale. Colorazione bianco opaco con top marmo. Inclusi nel prezzo poker di elettrodomestici (fuochi, forno, frigo e lavastoviglie) Hotpoint Ariston.',
    'Condizionatore 2':
        'Climatizzatore Ariston Alys Plus R-32 trial split, Potenza 9000 btu, wifi, 12 velocità',
    'Condizionatore 3':
        'Climatizzatore Ariston Alys Plus R-32 trial split, Potenza 9000 btu, wifi, 12 velocità',
  };

  Map<String, TextEditingController> optionalFeaturesPrice = {
    'Condizionatore': new TextEditingController(),
    'Antifurto': new TextEditingController(),
    'Assicurazione casa': new TextEditingController(),
    'Cucina': new TextEditingController(),
    'Condizionatore 2': new TextEditingController(),
    'Condizionatore 3': new TextEditingController()
  };

  late List<XFile> adImages;
  late List photoDayTimePaths = [];
  late List photoNightTimePaths = [];
  late List videoRenderPaths = [];
  late List photographs = [];
  late List currentPlan = [];
  late List projectPlanImages = [];
  

  List<dynamic> googlePlacesSuggestions = [];
  bool showSuggestionsPopup = false;

  List<dynamic> amenities = [];
  List<dynamic> tmpAmenities = [];
  dynamic amenitiesCount = {};
  bool isLoadingAmenities = true;
  List<PropertyOptionalFeature> optional = [];
  Property? property;


  @override
  void initState() {
    super.initState();

    log("widget.project?.id ===>  ${widget.project?.id}");
    log("widget.propertyId?.id ===>  ${widget.project?.propertyId}");

    widget.isInputChangeDetected = [false];

    txtconPropertyName!.addListener(_checkForChanges);
    txtconPropertyZoneName!.addListener(_checkForChanges);
    txtconPropertyTypeName!.addListener(_checkForChanges);
    // TextEditingController? txtconLocation = new TextEditingController();
    txtconCivic!.addListener(_checkForChanges);
    txtconCity!.addListener(_checkForChanges);
    txtconDescription!.addListener(_checkForChanges);
    txtconAreaMq!.addListener(_checkForChanges);
    txtconBaths!.addListener(_checkForChanges);
    txtconLocals!.addListener(_checkForChanges);
    txtconBedrooms!.addListener(_checkForChanges);
    txtconFloors!.addListener(_checkForChanges);

    txtconStartDate!.addListener(_checkForChanges);
    txtconEndDate!.addListener(_checkForChanges);
    txtconLimitDate!.addListener(_checkForChanges);
    publicStatusController!.addListener(_checkForChanges);
    txtconDateStart!.addListener(_checkForChanges);
    txtconDateEnd!.addListener(_checkForChanges);
    txtconDateLimit!.addListener(_checkForChanges);
    txtconVirtualTour!.addListener(_checkForChanges);
    txtconBasePrice!.addListener(_checkForChanges);
    txtconCurrentVirtualTour!.addListener(_checkForChanges);
    

    if (widget.property!.firebaseId != null) {
      txtconPropertyName!.text = widget.property!.propertyName!;
      txtconPropertyZoneName!.text = widget.property!.zone!;
      txtconPropertyTypeName!.text = widget.property!.type!;
      // txtconLocation!.text = widget.property!.location!;
      txtconCivic!.text = widget.property!.civic!;
      txtconCity!.text = widget.property!.city!;
      txtconDescription!.text = widget.property!.description!;
      txtconAreaMq!.text = widget.property!.mq!;
      txtconBaths!.text = widget.property!.baths!;
      txtconLocals!.text = widget.property!.locals!;
      txtconBedrooms!.text = widget.property!.bedrooms!;
      txtconFloors!.text = widget.property!.floors!;
      txtconVirtualTour!.text = widget.property!.virtualTour!;
      txtconCurrentVirtualTour!.text = widget.property!.currentVirtualTour!;
      txtconBasePrice!.text = widget.property!.basePrice!.toString();

      photoDayTimePaths = widget.property!.photoDayTimePaths!;
      photoNightTimePaths = widget.property!.photoNightTimePaths!;
      videoRenderPaths = widget.property!.videoRenderPaths!;
      photographs = widget.property!.photographs!;
      currentPlan = widget.property!.currentPlan!;

      projectPlanImages = widget.property!.picturePaths!;
      activateNightTimePicture = widget.property!.activateNightTimePicture!;
      activateVirtualTourProject = widget.property!.activateVirtualTourProject!;
      activateVideoRender = widget.property!.activateVideoRender!;
      activateVirtualTourCurrent = widget.property!.activateVirtualTourCurrent!;

      zonesList = appConst.cityZones[widget.property!.addressInfo!.city]!.map((e) => e.toTitleCase()).toList();

      print({'widget.property!.zone!', widget.property!.zone!});

      

      if (widget.property!.publicStatus != null) {
      
        widget.property!.publicStatus!.forEach((key,value){
          if( publicStatusOptions.containsKey(key) ) {
            publicStatusOptions[key] = value;
          }
        });
        // for( int i = 0; i < widget.property!.publicStatus!.length ; i++) {
        
        // }
        // publicStatusOptions = widget.property!.publicStatus!;
      }

      if (widget.property!.amenities != null) {
        amenities = widget.property!.amenities!;
      }

      if (widget.property!.amenitiesCount != null) {
        amenitiesCount = widget.property!.amenitiesCount!;
      }

      // if (widget.property!.addressInfo != null) {
      //   addressComponents = widget.property!.addressInfo!;
      // }

      if (widget.property!.location != null) {
        location = widget.property!.location!;
      }

      // print(amenities);
      // print(amenitiesCount);

      if (widget.property!.startDate != null) {
        var startDate =
            DateTime.fromMillisecondsSinceEpoch(widget.property!.startDate!);
        txtconStartDate!.text = (startDate.day < 10
                ? '0' + startDate.day.toString()
                : startDate.day.toString()) +
            '/' +
            (startDate.month < 10
                    ? '0' + startDate.month.toString()
                    : startDate.month.toString())
                .toString() +
            '/' +
            startDate.year.toString();

        var endDate =
            DateTime.fromMillisecondsSinceEpoch(widget.property!.endDate!);
        txtconEndDate!.text = (endDate.day < 10
                ? '0' + endDate.day.toString()
                : endDate.day.toString()) +
            '/' +
            (endDate.month < 10
                    ? '0' + endDate.month.toString()
                    : endDate.month.toString())
                .toString() +
            '/' +
            endDate.year.toString();

        var limitDate =
            DateTime.fromMillisecondsSinceEpoch(widget.property!.limitDate!);
        txtconLimitDate!.text = (limitDate.day < 10
                ? '0' + limitDate.day.toString()
                : limitDate.day.toString()) +
            '/' +
            (limitDate.month < 10
                    ? '0' + limitDate.month.toString()
                    : limitDate.month.toString())
                .toString() +
            '/' +
            limitDate.year.toString();
      }

      if (widget.property!.propertyFeatures != null) {
        var features = widget.property!.propertyFeatures;
        for (var i = 0; i < features!.length; i++) {
          // propertyFeatures[features[i]] = true;
          if( propertyFeatures.containsKey(features[i]) ) {
            propertyFeatures[features[i]] = true;
          }
        }
        
      }

      if (widget.property!.currentFeatures != null) {
        var features = widget.property!.currentFeatures;
        for (var i = 0; i < features!.length; i++) {
          // propertyFeatures[features[i]] = true;
          if( currentFeatures.containsKey(features[i]) ) {
            currentFeatures[features[i]] = true;
          }
        }
        
      }

      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        if (widget.property!.optional != null ||
            widget.property!.optional!.length > 0) {
          // optional = widget.property!.optional!;
          for (var opt = 0; opt < widget.property!.optional!.length; opt++) {
            optional.add(widget.property!.optional![opt]);

            var title = widget.property!.optional![opt].title;
            var price = widget.property!.optional![opt].price;

            optionalFeatures[title!] = true;
            optionalFeaturesPrice[title!]!.text = price.toString();
          }
          // print(optional);
          // autoLoadOptional(optional);
        }

        if (widget.property!.styles != null) {
          for (var i = 0; i < widget.property!.styles!.length; i++) {
            autoLoadStyleFileds(widget.property!.styles![i]);
          }

          setState(() {});
        }
      });
    } else {
      // print("setting controllers");
      // txtconPropertyName!.text = 'test';
      // txtconPropertyZoneName!.text = zonesList.first;
      // txtconPropertyTypeName!.text = typesList.first;
      // // txtconLocation!.text = 'Locatio';
      // txtconCivic!.text = '';
      // txtconCity!.text = 'Torino';
      // txtconDescription!.text = 'Description';
      // txtconAreaMq!.text = '100';
      // txtconBaths!.text = '2';
      // txtconLocals!.text = '3';
      // txtconBedrooms!.text = '4';
      // txtconFloors!.text = '5';
      // propertyFeatures['Ascensore'] = true;
      // txtconStartDate!.text = '01/03/2023';
      // txtconEndDate!.text = '02/03/2023';
      // txtconLimitDate!.text = '03/03/2023';

      // if (widget.property!.styles!.length == 0 ) {
      //   for (var i = 0; i < widget.property!.styles!.length; i++) {
      //     autoLoadStyleFileds(widget.property!.styles![i]);
      //   }

      //   setState(() {});
      // }
    }

    setState(() {
      isLoadingAmenities = false;
    });

    txtconPropertyName!.addListener(manageGooglePlaces);

    adImages = [];
  }

  void _checkForChanges() {
    setState(() {
      widget.isInputChangeDetected![0] = true;
    });
  }

  @override
  void dispose() {
    // Clean up the controller when the widget is removed from the widget tree.
    // This also removes the _printLatestValue listener.
    // txtconPropertyName!.dispose();
    
    txtconPropertyName!.removeListener(_checkForChanges);
    txtconPropertyZoneName!.removeListener(_checkForChanges);
    txtconPropertyTypeName!.removeListener(_checkForChanges);
    // TextEditingController? txtconLocation = new TextEditingController();
    txtconCivic!.removeListener(_checkForChanges);
    txtconCity!.removeListener(_checkForChanges);
    txtconDescription!.removeListener(_checkForChanges);
    txtconAreaMq!.removeListener(_checkForChanges);
    txtconBaths!.removeListener(_checkForChanges);
    txtconLocals!.removeListener(_checkForChanges);
    txtconBedrooms!.removeListener(_checkForChanges);
    txtconFloors!.removeListener(_checkForChanges);

    txtconStartDate!.removeListener(_checkForChanges);
    txtconEndDate!.removeListener(_checkForChanges);
    txtconLimitDate!.removeListener(_checkForChanges);
    publicStatusController!.removeListener(_checkForChanges);
    txtconDateStart!.removeListener(_checkForChanges);
    txtconDateEnd!.removeListener(_checkForChanges);
    txtconDateLimit!.removeListener(_checkForChanges);
    txtconVirtualTour!.removeListener(_checkForChanges);
    txtconBasePrice!.removeListener(_checkForChanges);
    txtconBasePrice!.removeListener(_checkForChanges);
    // widget.initialFetchProperties!();
    super.dispose();
  }

  formatDateForParsing(String dateString) {
    List splittedDate = dateString.split('/');
    // print(splittedDate);
    return splittedDate[2] + '-' + splittedDate[1] + '-' + splittedDate[0];
  }

  autoLoadOptional(optional) {
    if (optional.length > 0) {
      // print(optional);

      // optional.forEach((key, value) {

      //   print( optional[key]['title'] );
      //   // print(optional[key].toJson() );
      //   // optionalFeatures[key] = true;
      //   // optionalFeaturesPrice[key]!.text = value;
      // });
    }
  }

  autoLoadStyleFileds(PropertyConfigStyles data) async {
    // List<String> imgs = [];
    // if( data.picturePaths != null ) {
    //   for (var i = 0; i < data.picturePaths!.length; i++) {
    //     imgs.add(await printUrl( widget.firebaseId, data.picturePaths![i]) );
    //   }
    // }

    styleValues.add({
      '_key': 'styleGroup-' + styleCounter.toString(),
      'styleName': new TextEditingController(text: data.styleName ?? ''),
      'picturePaths': [],
      'pictures': data.picturePaths,
      'price': new TextEditingController(
          text: data.price == null ? '' : data.price.toString()),

      /*'predefined': new TextEditingController(text: data.predefined ?? ''),
      'predefined_price':
          new TextEditingController(text: data.predefinedPrice ?? ''),
      'predefined_images': [],
      'selectable': new TextEditingController(text: data.selectable ?? ''),
      'selectable_price':
          new TextEditingController(text: data.selectablePrice ?? ''),
      'selectable_images': []*/
    });

    styleValuesRetained.add(styleCounter);

    styles.addAll({
      'styleGroup-' + styleCounter.toString():
          styleFieldsGroup(context, styleCounter),
    });

    styleCounter++;
  }

  addStyleFields(BuildContext context) {
    styleValues.add({
      '_key': 'styleGroup-' + styleCounter.toString(),
      'styleName': new TextEditingController(),
      'picturePaths': [],
      'price': new TextEditingController(),

      /*'predefined': new TextEditingController(),
      'predefined_price': new TextEditingController(),
      'predefined_images': [],
      'selectable': new TextEditingController(),
      'selectable_price': new TextEditingController(),
      'selectable_images': []*/
    });

    // Forced to set first predefined style as default for now.
    styleValues[0]['styleName'].text = predefinedStyleList[0];

    styleValuesRetained.add(styleCounter);

    styles.addAll({
      'styleGroup-' + styleCounter.toString():
          styleFieldsGroup(context, styleCounter),
    });

    styleCounter++;
  }

  Widget styleFieldsGroup(BuildContext context, num styleCounterIndex) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
                flex: 70,
                child: NarFormLabelWidget(
                  label:
                      styleValues[styleCounterIndex.toInt()]['styleName'].text,
                  fontWeight: 'bold',
                  fontSize: 17,
                )),
            Expanded(
              flex: 30,
              child: Row(
                children: [
                  NarFormLabelWidget(
                    label: 'Prezzo',
                    fontSize: 13,
                    fontWeight: '500',
                    textColor: Color.fromRGBO(105, 105, 105, 1),
                  ),
                  SizedBox(width: 10),
                  Expanded(
                    child: NarInputWidget(
                      hintText: "",
                      obscureText: false,
                      textInputType: TextInputType.number,
                      actionKeyboard: TextInputAction.done,
                      suffixIcon: Icon(
                        Icons.euro,
                        color: Color.fromRGBO(105, 105, 105, 1),
                        size: 13,
                      ),
                      fontColor: Colors.black,
                      borderRadius: 8,
                      borderColor: Color(0xffdbdbdb),
                      //functionValidate: (),
                      controller: styleValues[styleCounterIndex.toInt()]
                          ['price'],
                      // focusNode: _passwordControllerFocus,
                      onSubmitField: () {},
                      parametersValidate: "",
                      // prefixIcon: Icon(Icons.keyboard_hide),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(width: 17),
        SizedBox(width: 17),
        Container(
          // height: 100,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NarFormLabelWidget(label: '', fontWeight: '800'),
              NarImagePickerWidget(
                  imagesToDisplayInList: 0,
                  removeButton: true,
                  removeButtonText: 'Elimina',
                  removeButtonTextColor: Color(0xff797979),
                  showMoreButtonText: '+ espandi',
                  removeButtonPosition: 'bottom',
                  uploadButtonPosition: 'back',
                  displayFormat: 'row',
                  imageDimension: 65,
                  borderRadius: 8,
                  fontSize: 12,
                  fontWeight: '600',
                  text: 'Aggiungi immagini',
                  borderSideColor: Theme.of(context).primaryColor,
                  imageContainerPadding:
                      EdgeInsets.symmetric(vertical: 0, horizontal: 0),
                  imageContainerUploadButtonAlignment: 'end',
                  // leadingIcon: Icon(
                  //   Icons.add,
                  //   size: 10,
                  // ),
                  hoverColor: Theme.of(context).primaryColor,
                  images: styleValues[styleCounterIndex.toInt()]
                      ['picturePaths'],
                  preloadedImages: styleValues[styleCounterIndex.toInt()]
                      ['pictures'],
                  firebaseId: widget.property!.firebaseId != null
                      ? widget.property!.firebaseId
                      : '',
                  pageContext: context)
            ],
          ),
        ),
      ],
    );
  }


  Future<AgencyUser> getAgencyUser(String uid) async {
    DocumentSnapshot<Map<String, dynamic>?> firestoreUserDoc =
        await fetchDocument('users/${uid}');
    Map<String, dynamic>? firestoreUser = firestoreUserDoc.data();

    DocumentSnapshot<Map<String, dynamic>?> firestoreAgencyDoc =
        await fetchDocument('agencies/${firestoreUser!["agencyId"]}');
    Map<String, dynamic>? firestoreAgency = firestoreAgencyDoc.data();

    AgencyUser agencyUser = AgencyUser(firestoreUser, firestoreUserDoc.id,
        firestoreAgency!, firestoreAgencyDoc.id);

    return agencyUser;
  }

  void changeFocus(
      BuildContext context, FocusNode currentFocus, FocusNode nextFocus) {
    currentFocus.unfocus();
    FocusScope.of(context).requestFocus(nextFocus);
  }

  Future<bool> updateActiveStatus(Property property) async {
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    return await _db
        .collection(appConfig.COLLECT_NEWARC_HOME)
        .doc(property.firebaseId)
        .update(property.toMap())
        .then((value) {
      return true;
    }).onError((error, stackTrace) {
      // print({error, stackTrace});
      return false;
    });
  }

  List<bool> isExpanded = [
    true,
    true,
    true,
    true,
    true,
    true,
    true,
  ];

  Widget collapsableSection(title, int index, Widget content ) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(width: 1.78, color: Color(0xffC8C8C8)),
        color: Colors.white
        
      ),
      padding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
      margin: EdgeInsets.only(bottom: 15),
      child: 
        
      Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              NarFormLabelWidget(
                label: title,
                fontSize: 20,
                textColor: Colors.black,
                fontWeight: 'bold',
              ),
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: (){
                    setState((){
                      if( isExpanded[index] ) isExpanded[index] = false;
                      else isExpanded[index] = true;
                      
                    });
                  },
                  child: SvgPicture.asset('assets/icons/arrow_down.svg', color: Color(0xff7E7E7E), width: 14,)
                ),
              ),
            ],
          ),
          isExpanded[index]
          ? content
          : Container(),

        ],
      ) ,

    );
  }

  @override
  Widget build(BuildContext context) {
    if (styleCounter == 0 && widget.property!.firebaseId == null) {
      addStyleFields(context);
    }
    return Form(
      key: _formKey,
      child: Stack(
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Row(
                    children: [
                      NarFormLabelWidget(
                        label: 'Annuncio di vendita',
                        fontSize: 24,
                        fontWeight: '700',
                        textColor: Color.fromRGBO(0, 0, 0, 1),
                      ),
                      
                    ],
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      NarFormLabelWidget(
                        label: 'Pubblica sul sito',
                        fontSize: 17,
                        fontWeight: 'bold',
                      ),
                      Switch(
                        // This bool value toggles the switch.
                        value: widget.property!.isActive == null
                            ? false
                            : widget.property!.isActive!,
                        activeColor:
                            Theme.of(context).primaryColor,
                        onChanged: (bool value) async {
                          // This is called when the user toggles the switch.
          
                          setState(() {
                            widget.property!.isActive = value;
                            widget.isInputChangeDetected![0] =
                                true;
                          });
          
                          try {
                            await updateActiveStatus(
                                widget.property!);
                          } catch (e) {
                            widget.property!.isActive = !value;
                          }
                        },
                      ),
                    ],
                  )
                ],
              ),
              SizedBox(height: 35),
      
              collapsableSection( 
                'Stato',
                0,
                Column(
                      children: [
                        SizedBox(
                          height: 10,
                        ),
                        NarCheckboxWidget(
                          label: 'Public Status Options',
                          columns: 2,
                          fontSize: 13,
                          values: publicStatusOptions,
                        ),
                      ],
                    )
              ),
      
              SizedBox(height:15),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  NarFormLabelWidget(
                    label: "Dati immobile",
                    fontSize: 25,
                    fontWeight: 'bold',
                    textColor: Colors.black,
                    textAlign: TextAlign.center
                  ),
                ],
              ),
              SizedBox(height: 15),
      
              collapsableSection( 
                'Identificazione',
                1,
                Column(
                      children: [
                        SizedBox(
                          height: 10,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Expanded(
                              flex: 3,
                              child: Column(
                                crossAxisAlignment:
                                    CrossAxisAlignment.start,
                                children: [
                                  NarSelectBoxWidget(
                                    label: "Tipologia",
                                    flex: 3,
                                    options: typesList,
                                    controller: txtconPropertyTypeName,
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: 17),
                    
                            CustomTextFormField(
                                flex: 2,
                                label: 'Mq',
                                hintText: "",
                                controller: txtconAreaMq,
                            ),
                            SizedBox(width: 17),
                            Expanded(
                              flex: 1,
                              child: Column(
                                crossAxisAlignment:
                                    CrossAxisAlignment.start,
                                children: [
                                  NarSelectBoxWidget(
                                    label: "Locali",
                                    options: localsList,
                                    controller: txtconLocals,
                                    parametersValidate: "Required!",
                                    validationType: 'required',
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: 17),
                            
                            Expanded(
                              flex: 1,
                              child: Column(
                                crossAxisAlignment:
                                    CrossAxisAlignment.start,
                                children: [
                                  NarSelectBoxWidget(
                                    label: 'Bagni',
                                    options: bathsList,
                                    controller: txtconBaths,
                                    parametersValidate: "Required!",
                                    validationType: 'required',
                                  ),
                                ],
                              ),
                            ),
      
                            SizedBox(width: 17),
                            
                            Expanded(
                              flex: 1,
                              child: Column(
                                crossAxisAlignment:
                                    CrossAxisAlignment.start,
                                children: [
                                  NarSelectBoxWidget(
                                    label: "Piano",
                                    options: floorsList,
                                    controller: txtconFloors,
                                    parametersValidate: "Required!",
                                    validationType: 'required',
                                  ),
                                ],
                              ),
                            ),
                            
                          ],
                        ),
                        SizedBox(height: 10),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomTextFormField(
                                flex: 3,
                                label: 'Indirizzo completo',
                                hintText: "",
                                controller: txtconPropertyName,
                                enabled: false,),
                            SizedBox(width: 17),
                            appConst.cityZones.keys.contains(txtconCity!.text)
                            ? Expanded(
                              flex: 2,
                              child: Column(
                                crossAxisAlignment:
                                    CrossAxisAlignment.start,
                                children: [
                                  NarSelectBoxWidget(
                                    label: "Zona",
                                    options: appConst.cityZones[txtconCity!.text]?.map((zone) => zone.toString().toTitleCase() ).toList().cast<String>(),
                                    controller: txtconPropertyZoneName,
                                  ),
                                ],
                              ),
                            )
                            : CustomTextFormField(
                              flex: 2,
                              label: 'Zona',
                              controller: txtconPropertyZoneName,
                            ),
                          ],
                        ),
      
                        SizedBox(height: 10),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if( styleValues.length > 0 ) CustomTextFormField(
                                flex: 2,
                                label: 'Prezzo base',
                                hintText: "",
                                controller: styleValues[0]['price'],
                                isMoney: true,
                                suffixIcon: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [ NarFormLabelWidget(label: '€', fontSize: 14, textColor: Color(0xff7F7F7F) ) ],),
                                ),
                            SizedBox(width: 17),
                            Expanded(
                              flex: 3,
                              child: SizedBox(height: 0),
                            )
                          ],
                        ),
                      ],
                    )
              ),
              
              collapsableSection( 
                'Descrizione',
                2,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 10,
                        ),
                        NarFormLabelWidget(
                          label: 'Inserisci descrizione',
                          fontSize: 13,
                          fontWeight: '600',
                          textColor: Colors.black,
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        NarTextareaWidget(
                          hintText: "",
                          maxLines: 8,
                          minLines: 8,
                          actionKeyboard: TextInputAction.done,
                          controller: txtconDescription,
                          onSubmitField: () {},
                        ),
                      ],
                    )
              ),
      
              collapsableSection( 
                'Caratteristiche',
                3,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 10,
                    ),
                    NarFormLabelWidget(
                      label: 'Attuali',
                      fontSize: 18,
                      fontWeight: 'bold',
                      textColor: Colors.black,
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    NarCheckboxWidget(
                        label: 'Current Features',
                        columns: 4,
                        fontSize: 13,
                        values: currentFeatures,
                        childAspectRatio: 5),
      
                    if( widget.project!.type != 'Newarc Subito' && widget.project!.type != 'Newarc Insieme' ) Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 10,
                        ),
                        Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            border: Border.all(
                              width: 1,
                              color: Color(0xffDBDBDB),
                            ),
                          ),
                          height: 1,
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        NarFormLabelWidget(
                          label: 'Progetto',
                          fontSize: 18,
                          fontWeight: 'bold',
                          textColor: Colors.black,
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        NarCheckboxWidget(
                            label: 'Property Features',
                            columns: 4,
                            fontSize: 13,
                            values: propertyFeatures,
                            childAspectRatio: 5),
                      ],
                    )
                  ],
                )
              ),
              
              SizedBox(height:15),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  NarFormLabelWidget(
                    label: "Media",
                    fontSize: 25,
                    fontWeight: 'bold',
                    textColor: Colors.black,
                    textAlign: TextAlign.center
                  ),
                ],
              ),
              SizedBox(height: 15),
      
              collapsableSection( 
                'Progetto',
                4,
                Column(
                  children: [
                    SizedBox(
                      height: 10,
                    ),
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(13),
                        border: Border.all(
                          width: 1,
                          color: Color(0xffE1E1E1),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Render diurni',
                                fontSize: 16,
                                fontWeight: '700',
                                textColor: Color.fromRGBO(0, 0, 0, 1),
                              ),
                              SizedBox(
                                height: 15,
                              ),
                              /*hasImageError == true
                              ? Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    NarFormLabelWidget(
                                      label: 'Carica almeno un\'immagine',
                                      fontWeight: '600',
                                      textColor: Colors.red,
                                    ),
                                    SizedBox(
                                      height: 10,
                                    )
                                  ],
                                )
                              : SizedBox(
                                  height: 0,
                                ),*/
                              
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
      
                                children: [
                                  Expanded(
                                    flex: 9,
                                    child: NarFilePickerWidget(
                                      allowMultiple: true,
                                      filesToDisplayInList: 0,
                                      removeButton: true,
                                      isDownloadable: false,
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-widget',
                                      containerWidth: 65,
                                      containerHeight: 65,
                                      containerBorderRadius: 8,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: photoDayTimePaths,
                                      pageContext: context,
                                      storageDirectory: "projects/${widget.project!.id}/ads/day-time/",
                                      removeExistingOnChange: false,
                                      progressMessage: [''],
                                      notAccent: true,
                                      showTitle: false,
                                      onUploadCompleted: () {
                                        setState(() {});
                                      },
                                      splashColor: Colors.black
                                    ),
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: NarFilePickerWidget(
                                      allowMultiple: true,
                                      filesToDisplayInList: 0,
                                      removeButton: true,
                                      isDownloadable: false,
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-button',
                                      containerWidth: 65,
                                      containerHeight: 65,
                                      containerBorderRadius: 8,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: photoDayTimePaths,
                                      pageContext: context,
                                      storageDirectory: "projects/${widget.project!.id}/ads/day-time/",
                                      removeExistingOnChange: false,
                                      progressMessage: [''],
                                      notAccent: true,
                                      splashColor: Color(0xffE5E5E5),
                                      height: 35,
                                      buttonWidth: 125,
                                      buttonTextColor: Colors.black,
                                      onUploadCompleted: () {
                                        setState(() {});
                                        
                                      },
                                                                  ),
                                  ),
                                ]
                              )
                            ]),
                      ),
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(13),
                        border: Border.all(
                          width: 1,
                          color: Color(0xffE1E1E1),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  NarFormLabelWidget(
                                    label: 'Render notturni',
                                    fontSize: 16,
                                    fontWeight: '700',
                                    textColor: Color.fromRGBO(0, 0, 0, 1),
                                  ),
                                  Transform.scale(
                                    // padding: const EdgeInsets.all(0),
                                    scale: 0.9,
                                    child: Switch(
                                      // This bool value toggles the switch.
                                      value: activateNightTimePicture,
                                      activeColor: Theme.of(context).primaryColor,
                                      onChanged: (bool value) async {
                                        
                                        setState(() {
                                          activateNightTimePicture = value;
                                        });
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 15,
                              ),
                              hasImageError == true
                              ? Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    NarFormLabelWidget(
                                      label: 'Carica almeno un\'immagine',
                                      fontWeight: '600',
                                      textColor: Colors.red,
                                    ),
                                    SizedBox(
                                      height: 10,
                                    )
                                  ],
                                )
                              : SizedBox(
                                  height: 0,
                                ),
                              
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Expanded(
                                    flex: 9,
                                    child: NarFilePickerWidget(
                                      allowMultiple: true,
                                      filesToDisplayInList: 0,
                                      removeButton: true,
                                      isDownloadable: false,
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-widget',
                                      containerWidth: 65,
                                      containerHeight: 65,
                                      containerBorderRadius: 8,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica Progetto',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: photoNightTimePaths,
                                      pageContext: context,
                                      storageDirectory: "projects/${widget.project!.id}/ads/night-time/",
                                      removeExistingOnChange: false,
                                      progressMessage: [''],
                                      notAccent: true,
                                      showTitle: false,
                                      onUploadCompleted: () {
                                        setState(() {});
                                      },
                                                                  ),
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: NarFilePickerWidget(
                                      allowMultiple: true,
                                      filesToDisplayInList: 0,
                                      removeButton: true,
                                      isDownloadable: false,
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-button',
                                      containerWidth: 65,
                                      containerHeight: 65,
                                      containerBorderRadius: 8,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: photoNightTimePaths,
                                      pageContext: context,
                                      storageDirectory: "projects/${widget.project!.id}/ads/night-time/",
                                      removeExistingOnChange: false,
                                      progressMessage: [''],
                                      notAccent: true,
                                      splashColor: Color(0xffE5E5E5),
                                      height: 35,
                                      buttonWidth: 125,
                                      buttonTextColor: Colors.black,
                                      onUploadCompleted: () {
                                        setState(() {});
                                      },
                                    ),
                                  ),
                                ]
                              )
                            ]),
                      ),
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(13),
                        border: Border.all(
                          width: 1,
                          color: Color(0xffE1E1E1),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Planimetria progettuale',
                                fontSize: 16,
                                fontWeight: '700',
                                textColor: Color.fromRGBO(0, 0, 0, 1),
                              ),
                              SizedBox(
                                height: 15,
                              ),
                              hasImageError == true
                              ? Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    NarFormLabelWidget(
                                      label: 'Carica almeno un\'immagine',
                                      fontWeight: '600',
                                      textColor: Colors.red,
                                    ),
                                    SizedBox(
                                      height: 10,
                                    )
                                  ],
                                )
                              : SizedBox(
                                  height: 0,
                                ),
                              // NarImagePickerWidget(
                              //   imagesToDisplayInList: 0,
                              //   removeButton: true,
                              //   removeButtonText: 'Elimina',
                              //   removeButtonTextColor: Color(0xff797979),
                              //   showMoreButtonText: '+ show more',
                              //   removeButtonPosition: 'bottom',
                              //   uploadButtonPosition: 'back',
                              //   displayFormat: 'row',
                              //   imageDimension: 65,
                              //   borderRadius: 8,
                              //   fontSize: 12,
                              //   fontWeight: '600',
                              //   text: 'Carica',
                              //   imageContainerPadding: EdgeInsets.symmetric( vertical: 0, horizontal: 0),
                              //   imageContainerUploadButtonAlignment: 'end',
                              //   borderSideColor: Theme.of(context).primaryColor,
                              //   hoverColor: Color.fromRGBO(133, 133, 133, 1),
                              //   images: adImages,
                              //   firebaseId: widget.property!.firebaseId != null ? widget.property!.firebaseId : '',
                              //   preloadedImages: (widget.property!.firebaseId == null || widget.property! .picturePaths == null)
                              //       ? []
                              //       : widget.property!.picturePaths,
                              //   pageContext: context
                              // ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Expanded(
                                    flex: 9,
                                    child: NarFilePickerWidget(
                                      allowMultiple: true,
                                      filesToDisplayInList: 0,
                                      removeButton: true,
                                      isDownloadable: false,
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-widget',
                                      containerWidth: 65,
                                      containerHeight: 65,
                                      containerBorderRadius: 8,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica Progetto',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: projectPlanImages,
                                      pageContext: context,
                                      storageDirectory: "newarcHomes/${widget.property!.firebaseId}/",
                                      removeExistingOnChange: false,
                                      progressMessage: [''],
                                      notAccent: true,
                                      showTitle: false,
                                      onUploadCompleted: () {
                                        setState(() {});
                                      },
                                                                  ),
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: NarFilePickerWidget(
                                      allowMultiple: true,
                                      filesToDisplayInList: 0,
                                      removeButton: true,
                                      isDownloadable: false,
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-button',
                                      containerWidth: 65,
                                      containerHeight: 65,
                                      containerBorderRadius: 8,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: projectPlanImages,
                                      pageContext: context,
                                      storageDirectory: "newarcHomes/${widget.property!.firebaseId}/",
                                      removeExistingOnChange: false,
                                      progressMessage: [''],
                                      notAccent: true,
                                      splashColor: Color(0xffE5E5E5),
                                      height: 35,
                                      buttonWidth: 125,
                                      buttonTextColor: Colors.black,
                                      onUploadCompleted: () {
                                        setState(() {});
                                      },
                                    ),
                                  ),
                                ]
                              )
                            ]),
                      ),
                    ),
      
                    SizedBox(
                      height: 10,
                    ),
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(13),
                        border: Border.all(
                          width: 1,
                          color: Color(0xffE1E1E1),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  NarFormLabelWidget(
                                    label: 'Virtual tour progetto',
                                    fontSize: 16,
                                    fontWeight: '700',
                                    textColor: Color.fromRGBO(0, 0, 0, 1),
                                  ),
                                  Transform.scale(
                                    scale: 0.9,
                                    child: Switch(
                                      // This bool value toggles the switch.
                                      value: activateVirtualTourProject,
                                      activeColor: Theme.of(context).primaryColor,
                                      onChanged: (bool value) async {
                                        
                                        setState(() {
                                          activateVirtualTourProject = value;
                                        });
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              
                              SizedBox(
                                height: 15,
                              ),
                              Row(
                                children: [
                                  CustomTextFormField(
                                    label: 'Link del virtual tour',
                                    hintText: "",
                                    controller: txtconVirtualTour,
                                    minLines: 1,
                                    onTap: () async {},
                                    // prefixIcon: Icon(Icons.keyboard_hide),
                                  ),
                                  
                                ],
                              ),
                            ]),
                      ),
                    ),
      
                    SizedBox(
                      height: 10,
                    ),
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(13),
                        border: Border.all(
                          width: 1,
                          color: Color(0xffE1E1E1),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  NarFormLabelWidget(
                                    label: 'Video render',
                                    fontSize: 16,
                                    fontWeight: '700',
                                    textColor: Color.fromRGBO(0, 0, 0, 1),
                                  ),
                                  Transform.scale(
                                    scale: 0.9,
                                    child: Switch(
                                      // This bool value toggles the switch.
                                      value: activateVideoRender,
                                      activeColor: Theme.of(context).primaryColor,
                                      onChanged: (bool value) async {
                                        
                                        setState(() {
                                          activateVideoRender = value;
                                        });
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 15,
                              ),
                              hasImageError == true
                              ? Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    NarFormLabelWidget(
                                      label: 'Carica almeno un\'immagine',
                                      fontWeight: '600',
                                      textColor: Colors.red,
                                    ),
                                    SizedBox(
                                      height: 10,
                                    )
                                  ],
                                )
                              : SizedBox(
                                  height: 0,
                                ),
                              
      
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Expanded(
                                    flex: 9,
                                    child: NarFilePickerWidget(
                                      allowMultiple: true,
                                      filesToDisplayInList: 0,
                                      removeButton: true,
                                      isDownloadable: false,
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-widget',
                                      containerWidth: 65,
                                      containerHeight: 65,
                                      containerBorderRadius: 8,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica Progetto',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: videoRenderPaths,
                                      pageContext: context,
                                      storageDirectory: "projects/${widget.project!.id}/ads/video-render/",
                                      removeExistingOnChange: false,
                                      progressMessage: [''],
                                      notAccent: true,
                                      showTitle: false,
                                      onUploadCompleted: () {
                                        setState(() {});
                                      },
                                    ),
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: NarFilePickerWidget(
                                      allowMultiple: true,
                                      filesToDisplayInList: 0,
                                      removeButton: true,
                                      isDownloadable: false,
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-button',
                                      containerWidth: 65,
                                      containerHeight: 65,
                                      containerBorderRadius: 8,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: videoRenderPaths,
                                      pageContext: context,
                                      storageDirectory: "projects/${widget.project!.id}/ads/video-render/",
                                      removeExistingOnChange: false,
                                      progressMessage: [''],
                                      notAccent: true,
                                      splashColor: Color(0xffE5E5E5),
                                      height: 35,
                                      buttonWidth: 125,
                                      buttonTextColor: Colors.black,
                                      onUploadCompleted: () {
                                        setState(() {});
                                      },
                                    ),
                                  ),
                                ]
                              )
                            ]),
                      ),
                    ),
                  ],
                )
              ),
      
              if( widget.project!.type != 'Newarc Subito' && widget.project!.type != 'Newarc Insieme' ) 
              collapsableSection( 
                'Stato attuale',
                5,
                Column(
                  children: [
                    SizedBox(
                      height: 10,
                    ),
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(13),
                        border: Border.all(
                          width: 1,
                          color: Color(0xffE1E1E1),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Fotografie',
                                fontSize: 16,
                                fontWeight: '700',
                                textColor: Color.fromRGBO(0, 0, 0, 1),
                              ),
                              SizedBox(
                                height: 15,
                              ),
                              // hasImageError == true
                              // ? Column(
                              //     mainAxisAlignment: MainAxisAlignment.start,
                              //     crossAxisAlignment: CrossAxisAlignment.start,
                              //     children: [
                              //       NarFormLabelWidget(
                              //         label: 'Carica almeno un\'immagine',
                              //         fontWeight: '600',
                              //         textColor: Colors.red,
                              //       ),
                              //       SizedBox(
                              //         height: 10,
                              //       )
                              //     ],
                              //   )
                              // : SizedBox(
                              //     height: 0,
                              //   ),
                              
      
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Expanded(
                                    flex: 9,
                                    child: NarFilePickerWidget(
                                      allowMultiple: true,
                                      filesToDisplayInList: 0,
                                      removeButton: true,
                                      isDownloadable: false,
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-widget',
                                      containerWidth: 65,
                                      containerHeight: 65,
                                      containerBorderRadius: 8,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica Progetto',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: photographs,
                                      pageContext: context,
                                      storageDirectory: "projects/${widget.project!.id}/ads/photographs/",
                                      removeExistingOnChange: false,
                                    
                                      progressMessage: [''],
                                      notAccent: true,
                                      showTitle: false,
                                      onUploadCompleted: () {
                                      
                                      },
                                                                  ),
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: NarFilePickerWidget(
                                      allowMultiple: true,
                                      filesToDisplayInList: 0,
                                      removeButton: true,
                                      isDownloadable: false,
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-button',
                                      containerWidth: 65,
                                      containerHeight: 65,
                                      containerBorderRadius: 8,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: photographs,
                                      pageContext: context,
                                      storageDirectory: "projects/${widget.project!.id}/ads/photographs/",
                                      removeExistingOnChange: false,
                                      progressMessage: [''],
                                      notAccent: true,
                                      splashColor: Color(0xffE5E5E5),
                                      height: 35,
                                      buttonWidth: 125,
                                      buttonTextColor: Colors.black,
                                      onUploadCompleted: () {
                                        setState(() {});
                                      },
                                    ),
                                  ),
                                ]
                              )
                            ]),
                      ),
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(13),
                        border: Border.all(
                          width: 1,
                          color: Color(0xffE1E1E1),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Planimetria attuale',
                                fontSize: 16,
                                fontWeight: '700',
                                textColor: Color.fromRGBO(0, 0, 0, 1),
                              ),
                              SizedBox(
                                height: 15,
                              ),
                              // hasImageError == true
                              // ? Column(
                              //     mainAxisAlignment: MainAxisAlignment.start,
                              //     crossAxisAlignment: CrossAxisAlignment.start,
                              //     children: [
                              //       NarFormLabelWidget(
                              //         label: 'Carica almeno un\'immagine',
                              //         fontWeight: '600',
                              //         textColor: Colors.red,
                              //       ),
                              //       SizedBox(
                              //         height: 10,
                              //       )
                              //     ],
                              //   )
                              // : SizedBox(
                              //     height: 0,
                              //   ),
                              
      
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Expanded(
                                    flex: 9,
                                    child: NarFilePickerWidget(
                                      allowMultiple: true,
                                      filesToDisplayInList: 0,
                                      removeButton: true,
                                      isDownloadable: false,
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-widget',
                                      containerWidth: 65,
                                      containerHeight: 65,
                                      containerBorderRadius: 8,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica Progetto',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: currentPlan,
                                      pageContext: context,
                                      storageDirectory: "projects/${widget.project!.id}/ads/current-plan/",
                                      removeExistingOnChange: false,
                                      progressMessage: [''],
                                      notAccent: true,
                                      showTitle: false,
                                      onUploadCompleted: () {
                                        setState(() {});
                                      },
                                                                  ),
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: NarFilePickerWidget(
                                      allowMultiple: true,
                                      filesToDisplayInList: 0,
                                      removeButton: true,
                                      isDownloadable: false,
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-button',
                                      containerWidth: 65,
                                      containerHeight: 65,
                                      containerBorderRadius: 8,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                                      allFiles: currentPlan,
                                      pageContext: context,
                                      storageDirectory: "projects/${widget.project!.id}/ads/current-plan/",
                                      removeExistingOnChange: false,
                                      progressMessage: [''],
                                      notAccent: true,
                                      splashColor: Color(0xffE5E5E5),
                                      height: 35,
                                      buttonWidth: 125,
                                      buttonTextColor: Colors.black,
                                      onUploadCompleted: () {
                                        setState(() {});
                                      },
                                    ),
                                  ),
                                ]
                              )
                            ]),
                      ),
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(13),
                        border: Border.all(
                          width: 1,
                          color: Color(0xffE1E1E1),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  NarFormLabelWidget(
                                    label: 'Virtual tour attuale',
                                    fontSize: 16,
                                    fontWeight: '700',
                                    textColor: Color.fromRGBO(0, 0, 0, 1),
                                  ),
                                  Transform.scale(
                                    scale: 0.9,
                                    child: Switch(
                                      // This bool value toggles the switch.
                                      value: activateVirtualTourCurrent,
                                      activeColor: Theme.of(context).primaryColor,
                                      onChanged: (bool value) async {
                                        
                                        setState(() {
                                          activateVirtualTourCurrent = value;
                                        });
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              
                              SizedBox(
                                height: 15,
                              ),
                              Row(
                                children: [
                                  CustomTextFormField(
                                    label: 'Link del virtual tour',
                                    hintText: "",
                                    controller: txtconCurrentVirtualTour,
                                    minLines: 1,
                                    onTap: () async {},
                                    // prefixIcon: Icon(Icons.keyboard_hide),
                                  ),
                                  
                                ],
                              ),
                            ]),
                      ),
                    ),
                  ],
                )
              ),
          
              // hasPropertyFeatureError == true
              //     ? Column(
              //         mainAxisAlignment: MainAxisAlignment.start,
              //         crossAxisAlignment:
              //             CrossAxisAlignment.start,
              //         children: [
              //           NarFormLabelWidget(
              //             label: 'Seleziona almeno un\'opzione',
              //             fontWeight: '600',
              //             textColor: Colors.red,
              //           ),
              //           SizedBox(
              //             height: 10,
              //           )
              //         ],
              //       )
              //     : SizedBox(
              //         height: 0,
              //       ),
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //   crossAxisAlignment: CrossAxisAlignment.start,
              //   children: [
              //     Expanded(
              //       flex: 2,
              //       child: Column(
              //         crossAxisAlignment:
              //             CrossAxisAlignment.start,
              //         mainAxisAlignment: MainAxisAlignment.start,
              //         mainAxisSize: MainAxisSize.min,
              //         children: [
              //           Row(
              //             children: [
              //               SizedBox(
              //                 width: 8,
              //               ),
              //               NarFormLabelWidget(
              //                 label: 'Dotazioni',
              //                 fontSize: 17,
              //                 fontWeight: '700',
              //                 textColor: Colors.black,
              //               ),
              //             ],
              //           ),
              //           SizedBox(height: 10),
              //           NarCheckboxWidget(
              //               label: 'Property Features',
              //               columns: 2,
              //               values: propertyFeatures,
              //               childAspectRatio: 8),
              //         ],
              //       ),
              //     ),
              //     SizedBox(width: 40),
              //     Expanded(
              //       flex: 1,
              //       child: Column(
              //         crossAxisAlignment:
              //             CrossAxisAlignment.start,
              //         mainAxisSize: MainAxisSize.min,
              //         children: [
              //           Row(
              //             children: [
              //               SizedBox(
              //                 width: 8,
              //               ),
              //               NarFormLabelWidget(
              //                 label: 'Particolarità',
              //                 fontSize: 17,
              //                 fontWeight: '700',
              //                 textColor: Colors.black,
              //               ),
              //             ],
              //           ),
              //           SizedBox(height: 10),
              //           NarCheckboxWidget(
              //               label: 'peculiarities',
              //               columns: 1,
              //               values: peculiarities,
              //               childAspectRatio: 8),
              //         ],
              //       ),
              //     ),
              //   ],
              // ),
          
              // SizedBox(
              //   height: 40,
              // ),
              // Container(
              //   width: double.infinity,
              //   decoration: BoxDecoration(
              //     borderRadius: BorderRadius.circular(13),
              //     border: Border.all(
              //       width: 1,
              //       color: Color.fromRGBO(219, 219, 219, 1),
              //     ),
              //   ),
              //   child: Padding(
              //     padding: const EdgeInsets.all(20.0),
              //     child: Column(
              //         crossAxisAlignment: CrossAxisAlignment.start,
              //         children: [
              //           NarFormLabelWidget(
              //             label: 'Planimetrie',
              //             fontSize: 17,
              //             fontWeight: '700',
              //             textColor: Color.fromRGBO(0, 0, 0, 1),
              //           ),
              //           SizedBox(
              //             height: 10,
              //           ),
              //           hasImageError == true
              //               ? Column(
              //                   mainAxisAlignment:
              //                       MainAxisAlignment.start,
              //                   crossAxisAlignment:
              //                       CrossAxisAlignment.start,
              //                   children: [
              //                     NarFormLabelWidget(
              //                       label:
              //                           'Carica almeno un\'immagine',
              //                       fontWeight: '600',
              //                       textColor: Colors.red,
              //                     ),
              //                     SizedBox(
              //                       height: 10,
              //                     )
              //                   ],
              //                 )
              //               : SizedBox(
              //                   height: 0,
              //                 ),
              //           NarImagePickerWidget(
              //               imagesToDisplayInList: 0,
              //               removeButton: true,
              //               removeButtonText: 'Elimina',
              //               showMoreButtonText: '+ show more',
              //               removeButtonPosition: 'bottom',
              //               uploadButtonPosition: 'back',
              //               displayFormat: 'row',
              //               imageDimension: 65,
              //               borderRadius: 8,
              //               fontSize: 12,
              //               fontWeight: '600',
              //               text: 'Aggiungi planimetrie',
              //               imageContainerPadding:
              //                   EdgeInsets.symmetric(
              //                       vertical: 0, horizontal: 0),
              //               imageContainerUploadButtonAlignment:
              //                   'end',
              //               borderSideColor:
              //                   Theme.of(context).primaryColor,
              //               hoverColor:
              //                   Color.fromRGBO(133, 133, 133, 1),
              //               images: adImages,
              //               firebaseId:
              //                   widget.property!.firebaseId !=
              //                           null
              //                       ? widget.property!.firebaseId
              //                       : '',
              //               preloadedImages: (widget.property!
              //                               .firebaseId ==
              //                           null ||
              //                       widget.property!
              //                               .picturePaths ==
              //                           null)
              //                   ? []
              //                   : widget.property!.picturePaths,
              //               pageContext: context),
              //         ]),
              //   ),
              // ),
          
              // SizedBox(
              //   height: 30,
              // ),
              // Container(
              //   decoration: BoxDecoration(
              //     borderRadius: BorderRadius.circular(13),
              //     border: Border.all(
              //       width: 1,
              //       color: Color.fromRGBO(219, 219, 219, 1),
              //     ),
              //   ),
              //   child: Padding(
              //     padding: EdgeInsets.all(15),
              //     child: Column(
              //       crossAxisAlignment: CrossAxisAlignment.start,
              //       children: [
              //         NarFormLabelWidget(
              //           label: 'Stato',
              //           fontSize: 20,
              //           fontWeight: '800',
              //           textColor: Color.fromRGBO(0, 0, 0, 1),
              //         ),
              //         SizedBox(
              //           height: 10,
              //         ),
              //         NarCheckboxWidget(
              //           label: 'Public Status Options',
              //           columns: 3,
              //           values: publicStatusOptions,
              //         ),
              //       ],
              //     ),
              //   ),
              // ),
          
              // SizedBox(height: 30),
          
              // hasStyleError == true
              //     ? Column(
              //         mainAxisAlignment: MainAxisAlignment.start,
              //         crossAxisAlignment:
              //             CrossAxisAlignment.start,
              //         children: [
              //           NarFormLabelWidget(
              //             label: 'Select at least one style',
              //             fontWeight: '600',
              //             textColor: Colors.red,
              //           ),
              //           SizedBox(
              //             height: 10,
              //           )
              //         ],
              //       )
              //     : SizedBox(
              //         height: 0,
              //       ),
          
              // NarFormLabelWidget(
              //   label: 'Stile base e configurazione',
              //   fontSize: 22,
              //   fontWeight: '700',
              //   textColor: Color.fromRGBO(0, 0, 0, 1),
              // ),
              // SizedBox(height: 10),
              // // styleFieldsGroup(),
              // Container(
              //   decoration: BoxDecoration(
              //     borderRadius: BorderRadius.circular(13),
              //     border: Border.all(
              //       width: 1,
              //       color: Color.fromRGBO(219, 219, 219, 1),
              //     ),
              //   ),
              //   child: Padding(
              //     padding: const EdgeInsets.all(15.0),
              //     child: Column(
              //       children: styles.length > 0
              //           // ? styles.map((widget) => widget ).toList()
              //           ? styles.values.map((e) => e).toList()
              //           : [
              //               SizedBox(
              //                 height: 0,
              //               )
              //             ],
              //     ),
              //   ),
              // ),
          
              // NarButtonWidget(
              //   borderRadius: 5,
              //   onClick: () {
              //     addStyleFields(context);
              //     setState(() {});
              //   },
              //   fontSize: 14,
              //   fontWeight: '600',
              //   text: 'Aggiungi stile',
              //   borderSideColor:
              //       Theme.of(context).primaryColor,
              //   leadingIcon: Icon(
              //     Icons.add,
              //     size: 10,
              //   ),
              //   hoverColor:
              //       Color.fromRGBO(133, 133, 133, 1),
              // ),
              // SizedBox(height: 30),
          
              // // Optional starts
              // Container(
              //   width: double.infinity,
              //   decoration: BoxDecoration(
              //     borderRadius: BorderRadius.circular(13),
              //     border: Border.all(
              //       width: 1,
              //       color: Color.fromRGBO(219, 219, 219, 1),
              //     ),
              //   ),
              //   child: Padding(
              //     padding: const EdgeInsets.all(15.0),
              //     child: Column(
              //       crossAxisAlignment: CrossAxisAlignment.start,
              //       children: [
              //         NarFormLabelWidget(
              //           label: 'Optional',
              //           fontSize: 20,
              //           fontWeight: '900',
              //           textColor: Color.fromRGBO(0, 0, 0, 1),
              //         ),
              //         SizedBox(height: 10),
              //         Container(
              //           width: MediaQuery.of(context).size.width *
              //               0.60,
              //           child: NarCheckboxWidget(
              //             label: 'Optional Features',
              //             columns: 1,
              //             childAspectRatio: 19,
              //             values: optionalFeatures,
              //             appendPosition: 'side',
              //             additionalValue: optionalFeaturesPrice,
              //             suffixIcon: Icons.euro,
              //             appendText: NarFormLabelWidget(
              //                 label: "Prezzo", fontWeight: '500'),
              //           ),
              //         ),
              //       ],
              //     ),
              //   ),
              // ),
          
              // Optional ends
          
              // SizedBox(
              //   height: 30,
              // ),
              // Container(
              //   decoration: BoxDecoration(
              //     borderRadius: BorderRadius.circular(13),
              //     border: Border.all(
              //       width: 1,
              //       color: Color.fromRGBO(219, 219, 219, 1),
              //     ),
              //   ),
              //   child: Padding(
              //     padding: const EdgeInsets.all(20.0),
              //     child: Column(
              //       crossAxisAlignment: CrossAxisAlignment.start,
              //       children: [
              //         NarFormLabelWidget(
              //           label: 'Date',
              //           fontSize: 17,
              //           fontWeight: '700',
              //           textColor: Color.fromRGBO(0, 0, 0, 1),
              //         ),
              //         SizedBox(
              //           height: 10,
              //         ),
              //         Row(
              //           children: [
              //             CustomTextFormField(
              //               label: 'Inizio lavori',
              //               hintText: "",
              //               controller: txtconStartDate,
              //               validator: (value) {
              //                 if (value == null ||
              //                     value.isEmpty) {
              //                   return 'Inserisci una mail valida';
              //                 }
              //                 return null;
              //               },
              //               onTap: () async {
              //                 DateTime? pickedDate =
              //                     await showDatePicker(
              //                         context: context,
              //                         initialDate: txtconStartDate!
              //                                     .text ==
              //                                 ''
              //                             ? DateTime.now()
              //                             : DateTime.tryParse(
              //                                 formatDateForParsing(
              //                                     txtconStartDate!
              //                                         .text))!,
              //                         firstDate: DateTime(1950),
              //                         lastDate: DateTime(2300));
          
              //                 if (pickedDate != null) {
              //                   // print(pickedDate);
              //                   String formattedDate =
              //                       DateFormat('dd/MM/yyyy')
              //                           .format(pickedDate);
              //                   setState(() {
              //                     txtconStartDate!.text =
              //                         formattedDate; //set output date to TextField value.
              //                   });
              //                 } else {}
              //               },
              //               // prefixIcon: Icon(Icons.keyboard_hide),
              //             ),
              //             SizedBox(
              //               width: 17,
              //             ),
              //             CustomTextFormField(
              //               label: 'Fine lavori',
              //               hintText: "",
              //               controller: txtconEndDate,
              //               validator: (value) {
              //                 if (value == null ||
              //                     value.isEmpty) {
              //                   return 'Inserisci una mail valida';
              //                 }
              //                 return null;
              //               },
              //               onTap: () async {
              //                 DateTime? pickedDate =
              //                     await showDatePicker(
              //                         context: context,
              //                         initialDate: txtconEndDate!
              //                                     .text ==
              //                                 ''
              //                             ? DateTime.now()
              //                             : DateTime.tryParse(
              //                                 formatDateForParsing(
              //                                     txtconEndDate!
              //                                         .text))!,
              //                         firstDate: DateTime(1950),
              //                         lastDate: DateTime(2300));
          
              //                 if (pickedDate != null) {
              //                   String formattedDate =
              //                       DateFormat('dd/MM/yyyy')
              //                           .format(pickedDate);
              //                   setState(() {
              //                     txtconEndDate!.text =
              //                         formattedDate; //set output date to TextField value.
              //                   });
              //                 } else {}
              //               },
              //               // prefixIcon: Icon(Icons.keyboard_hide),
              //             ),
              //             SizedBox(
              //               width: 17,
              //             ),
              //             CustomTextFormField(
              //               hintText: "",
              //               label: 'Limite configurazione',
              //               controller: txtconLimitDate,
              //               validator: (value) {
              //                 if (value == null ||
              //                     value.isEmpty) {
              //                   return 'Inserisci una mail valida';
              //                 }
              //                 return null;
              //               },
              //               onTap: () async {
              //                 DateTime? pickedDate =
              //                     await showDatePicker(
              //                         context: context,
              //                         initialDate: txtconLimitDate!
              //                                     .text ==
              //                                 ''
              //                             ? DateTime.now()
              //                             : DateTime.tryParse(
              //                                 formatDateForParsing(
              //                                     txtconLimitDate!
              //                                         .text))!,
              //                         firstDate: DateTime(1950),
              //                         lastDate: DateTime(2300));
          
              //                 if (pickedDate != null) {
              //                   String formattedDate =
              //                       DateFormat('dd/MM/yyyy')
              //                           .format(pickedDate);
              //                   setState(() {
              //                     txtconLimitDate!.text =
              //                         formattedDate; //set output date to TextField value.
              //                   });
              //                 } else {}
              //               },
              //               // prefixIcon: Icon(Icons.keyboard_hide),
              //             ),
              //           ],
              //         ),
              //       ],
              //     ),
              //   ),
              // ),
          
              // SizedBox(
              //   height: 30,
              // ),
              // Container(
              //   decoration: BoxDecoration(
              //     borderRadius: BorderRadius.circular(13),
              //     border: Border.all(
              //       width: 1,
              //       color: Color.fromRGBO(219, 219, 219, 1),
              //     ),
              //   ),
              //   child: Padding(
              //     padding: const EdgeInsets.all(20.0),
              //     child: Column(
              //       crossAxisAlignment: CrossAxisAlignment.start,
              //       children: [
              //         NarFormLabelWidget(
              //           label: 'Virtual Tour',
              //           fontSize: 17,
              //           fontWeight: '700',
              //           textColor: Color.fromRGBO(0, 0, 0, 1),
              //         ),
              //         SizedBox(
              //           height: 10,
              //         ),
              //         Row(
              //           children: [
              //             CustomTextFormField(
              //               label: 'Link del virtual tour',
              //               hintText: "",
              //               controller: txtconVirtualTour,
              //               minLines: 4,
              //               validator: (value) {
              //                 if (value == null ||
              //                     value.isEmpty) {
              //                   return '';
              //                 }
              //                 return null;
              //               },
              //               onTap: () async {},
              //               // prefixIcon: Icon(Icons.keyboard_hide),
              //             ),
                          
              //           ],
              //         ),
              //       ],
              //     ),
              //   ),
              // ),
          
              SizedBox(
                height: 60,
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  NarFormLabelWidget(label: progressMessage),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      isLoadingAmenities == true
                          ? NarFormLabelWidget(
                              label: 'Please wait...',
                              fontWeight: '800',
                              fontSize: 15,
                            )
                          : BaseNewarcButton(
                              buttonText: "Salva",
                              onPressed: () async {
                                if (optionalFeatures.length > 0) {
                                  optional.clear();
                                  optionalFeatures
                                      .forEach((key, opt) {
                                    if (opt == true) {
                                      optional.add(
                                          PropertyOptionalFeature(
                                              title: key,
                                              price: int.tryParse(
                                                  optionalFeaturesPrice[
                                                          key]!
                                                      .text
                                                      .trim()),
                                              description:
                                                  optionalFeaturesDescription[
                                                      key]));
                                    }
                                  });
                                }
          
                                List<String> selectedPropertyFeatures = [];
                                propertyFeatures.forEach((featureName, selectStatus) {
                                  if (selectStatus == true) {
                                    selectedPropertyFeatures.add(featureName);
                                  }
                                });
      
                                List<String> selectedCurrentFeatures = [];
                                currentFeatures.forEach((featureName, selectStatus) {
                                  if (selectStatus == true) {
                                    selectedCurrentFeatures.add(featureName);
                                  }
                                });
          

                                List<PropertyConfigStyles> configuredStyles = [];

          
                                if (styleValuesRetained.length > 0) {
                                  styleValuesRetained.forEach( (styleConfigIndex) {
                                    String price = styleValues[styleConfigIndex.toInt()]['price']!.text;
                                    price = price.replaceAll('.', '').replaceAll(',','.'); 
                                    configuredStyles.add(
                                        PropertyConfigStyles(
                                            isDefault: styleConfigIndex ==
                                                0,
                                            description: predefinedStyleDescription[styleValues[styleConfigIndex.toInt()]['styleName']! .text],
                                            styleName: styleValues[styleConfigIndex.toInt()] ['styleName']! .text,
                                            // price: styleValues[styleConfigIndex.toInt()] ['price']!.text,
                                            price: price.toString(),
                                            // picturePaths: styleValues[styleConfigIndex.toInt()]['picturePaths'],
                                            picturePaths: styleValues[styleConfigIndex.toInt()]['pictures'] != null && styleValues[styleConfigIndex.toInt()]['pictures'].length > 0
                                                ? styleValues[styleConfigIndex.toInt()] ['pictures']
                                                : [],
                                            pictures: styleValues[styleConfigIndex.toInt()] ['picturePaths'] .cast<XFile>()));
                                  });
                                }

          
                                setState(() {
                                  isLoadingAmenities = true;
                                });

          
                                Property property = Property(
                                  firebaseId: widget .property!.firebaseId ?? '',
                                  // propertyName:
                                  //    txtconPropertyName!.text,
                                  zone: txtconPropertyZoneName! .text,
                                  type: txtconPropertyTypeName! .text,
                                  propertyName: txtconPropertyName!.text,
                                  // addressComponents: addressComponents,
                                  location: location,
                                  civic: txtconCivic!.text,
                                  city: txtconCity!.text,
                                  description: txtconDescription!.text,
                                  mq: txtconAreaMq!.text,
                                  baths: txtconBaths!.text,
                                  locals: txtconLocals!.text,
                                  bedrooms: txtconBedrooms!.text,
                                  floors: txtconFloors!.text,
                                  propertyFeatures: selectedPropertyFeatures,
                                  currentFeatures: selectedCurrentFeatures,
                                  styles: configuredStyles,
                                  startDate: Timestamp.fromDate(DateTime.tryParse(formatDateForParsing(txtconStartDate!.text))!).millisecondsSinceEpoch,
                                  endDate: Timestamp.fromDate(DateTime.tryParse(formatDateForParsing(txtconEndDate!.text))!) .millisecondsSinceEpoch,
                                  limitDate: Timestamp.fromDate(DateTime.tryParse(formatDateForParsing(txtconLimitDate!.text))!) .millisecondsSinceEpoch,
                                  // pictures: adImages,
                                  insertTimestamp: Timestamp.now() .millisecondsSinceEpoch,
                                  updateTimestamp: Timestamp.now() .millisecondsSinceEpoch,
                                  insertUid: '',
                                  amenities: amenities,
                                  amenitiesCount: amenitiesCount,
                                  optional: optional,
                                  publicStatus: publicStatusOptions,
                                  isActive: widget.property != null ? widget.property!.isActive : true,
                                  
                                  virtualTour: txtconVirtualTour!.text,
                                  currentVirtualTour: txtconCurrentVirtualTour!.text,
                                  
                                  picturePaths: projectPlanImages,
                                  photoDayTimePaths: photoDayTimePaths,
                                  photoNightTimePaths: photoNightTimePaths,
                                  videoRenderPaths: videoRenderPaths,
                                  photographs: photographs,
                                  currentPlan: currentPlan,
                                  
                                  activateNightTimePicture:activateNightTimePicture,
                                  activateVirtualTourProject:activateVirtualTourProject,
                                  activateVideoRender:activateVideoRender,
                                  activateVirtualTourCurrent:activateVirtualTourCurrent
      
                                );
          
                                setState(() {
                                  progressMessage =
                                      'Salvataggio in corso';
                                });
          
                                String message = "";
          
                                if (widget.property!.firebaseId ==
                                    null) {
                                  await property
                                      .addProperty(property)
                                      .then((firebaseId) async {
                                    message =
                                        "Inserimento/aggiornamento avvenuto con successo.";
          
                                    // print({
                                    //   'firebaseid',
                                    //   firebaseId
                                    // });
                                    widget.property!.firebaseId =
                                        firebaseId;
          
                                    setState(() {
                                      progressMessage = message;
                                      widget.isInputChangeDetected![0] = false;  
                                    });
          
                                    /* Save the new created Property id
                                              to the Project */
          
                                    widget.project!.propertyId =
                                        firebaseId;
                                    final FirebaseFirestore _db =
                                        FirebaseFirestore
                                            .instance;
                                    await _db
                                        .collection(appConfig
                                            .COLLECT_NEWARC_PROJECTS)
                                        .doc(widget.project!.id)
                                        .update(widget.project!
                                            .toMap());
          
                                    widget.property!.setFromObject(property);
          
                                    // widget.property = property;
          
                                    adImages.clear();
                                  }).onError((error, stackTrace) {
                                    message =
                                        "Si è verificato un errore: " +
                                            error.toString();
          
                                    print({error, stackTrace});
          
                                    setState(() {
                                      progressMessage = message;
                                    });
                                  });
                                } else {
                                  // Update an existing document
          
                                  property.updateTimestamp =
                                      Timestamp.now()
                                          .millisecondsSinceEpoch;
                                  property.updateUid = '';
                                  property.firebaseId =
                                      widget.property!.firebaseId;
                                  await property
                                      .updateProperty()
                                      .then((value) {
                                    message =
                                        "Inserimento/aggiornamento avvenuto con successo.";
          
                                    setState(() {
                                      progressMessage = message;
                                      widget.isInputChangeDetected![0] = true;  
                                    });
          
                                    widget.property!
                                        .setFromObject(property);
                                    adImages.clear();
                                  }).onError((error, stackTrace) {
                                    message =
                                        "Si è verificato un errore: " +
                                            error.toString();
                                    print({error, stackTrace});
          
                                    setState(() {
                                      progressMessage = message;
                                    });
                                  });
                                }

                                setState(() {
                                  isLoadingAmenities = false;
                                });
                              },
                            ),
                    ],
                  ),
                ],
              ),
            ],
          ),
          
          !showSuggestionsPopup
          ? Container()
          : Positioned(
              top: 200,
              right: 0,
              left: 0,
              child: Container(
                decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(15)),
                height: 400,
                width: 150,
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        IconButton(
                            onPressed: () {
                              setState(() {
                                showSuggestionsPopup = false;
                              });
                            },
                            icon: Icon(Icons.close))
                      ],
                    ),
                    Flexible(
                      child: Padding(
                        padding:
                            const EdgeInsets.only(left: 15.0, right: 15.0),
                        child: TextFormField(
                          controller: txtconPropertyName,
                          decoration: InputDecoration(
                            hintText: "Inserisci indirizzo",
                            border: InputBorder.none,
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                        itemCount: googlePlacesSuggestions.length,
                        itemBuilder: (context, index) {
                          return ListTile(
                            leading: Icon(Icons.place),
                            title: Text(googlePlacesSuggestions
                                .elementAt(index)['description']),
                            onTap: () async {
                              txtconPropertyName!.text =
                                  googlePlacesSuggestions
                                      .elementAt(index)['description'];
      
                              // Fetch the amenities if the name is different from the existing name.
                              if (widget.property!.firebaseId == null ||
                                  txtconPropertyName!.text !=
                                      widget.property!.propertyName) {
                                dynamic result = await geocodeAddress();
      
                                Map<String, dynamic> loc = result['results']
                                    [0]['geometry']['location'];
                                geoLocation = Location(
                                    lat: loc['lat'], lng: loc['lng']);
                                location = {
                                  'lat': loc['lat'],
                                  'lng': loc['lng']
                                };
      
                                getAmenities(loc['lat'], loc['lng']);
      
                                var gmapComponents = result['results'][0]
                                    ['address_components'];
                                Map addType = {
                                  'route': 'street',
                                  'street_number': 'civico',
                                  'locality': 'city',
                                  'administrative_area_level_2': 'province',
                                  'administrative_area_level_3': 'state',
                                  'country': 'country'
                                };
      
                                for (var gmapi = 0;
                                    gmapi < gmapComponents.length;
                                    gmapi++) {
                                  for (var ggi = 0;
                                      ggi <
                                          gmapComponents[gmapi]['types']
                                              .length;
                                      ggi++) {
                                    if (addType.keys.contains(
                                        gmapComponents[gmapi]['types']
                                            [ggi])) {
                                      var compType = addType[
                                          gmapComponents[gmapi]['types']
                                              [ggi]];
                                      addressComponents![compType] =
                                          gmapComponents[gmapi]
                                              ['short_name'];
      
                                      switch (compType) {
                                        case 'city':
                                          txtconCity!.text =
                                              gmapComponents[gmapi]
                                                  ['short_name'];
                                          break;
                                        case 'civico':
                                          txtconCivic!.text =
                                              gmapComponents[gmapi]
                                                  ['short_name'];
                                          break;
                                        // case 'street':
                                        //   txtconPropertyName!.text = gmapComponents[gmapi]['short_name'];
                                        //   break;
                                        default:
                                      }
                                    }
                                  }
                                }
      
                                // print(addressComponents);
      
                                // if( addressComponents['city'] != '' ) {
      
                                // }
                              }
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
                          
  }

  Future _httpRequestViaServer({url}) async {
    HttpsCallable callable = FirebaseFunctions.instance.httpsCallable(
      'getDataFromUrl',
    );
    try {
      final HttpsCallableResult result = await callable.call(
        <String, dynamic>{
          'url': url,
        },
      );
      return (result.data);
    } on FirebaseFunctionsException catch (e) {
      // print('caught firebase functions exception');
      // print(e.code);
      // print(e.message);
      // print(e.details);
      return null;
    } catch (e) {
      // print('caught generic exception');
      // print(e);
      return null;
    }
  }

  Timer? _timer;
  manageGooglePlaces() async {
    if (null != _timer) {
      _timer!.cancel();
    }
    _timer = Timer(Duration(seconds: 3), () async {
      dynamic result = await _httpRequestViaServer(
          url:
              "https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${txtconPropertyName!.text}&key=${googleApikey}&language=it&components=country:it&types=address&bounds=45.0119,7.5765|45.1386,7.7761");
      setState(() {
        googlePlacesSuggestions = result['predictions'];
        showSuggestionsPopup = true;
      });
    });
  }

  geocodeAddress() async {
    dynamic result = await _httpRequestViaServer(
        url:
            "https://maps.googleapis.com/maps/api/geocode/json?address=${txtconPropertyName!.text}&key=${googleApikey}");

    // Clear amenities array
    amenities.clear();
    // Clear amenities count
    amenitiesCount = {};

    setState(() {
      showSuggestionsPopup = false;
    });

    return result;
  }

  getAmenities(lat, lng) async {
    amenities = [];
    var ame_types = ['school', 'market', 'park'];

    isLoadingAmenities = true;

    for (var i = 0; i < ame_types.length; i++) {
      dynamic result = await _httpRequestViaServer(
          url:
              "https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${lat},${lng}&radius=400&type=${ame_types[i]}&key=${googleApikey}");

      if (result['results'].length > 0) {
        List amenity = [];

        for (var j = 0; j < result['results'].length; j++) {
          tmpAmenities.add({
            'name': result['results'][j]['name'],
            'location': result['results'][j]['geometry']['location'],
            'vicinity': result['results'][j]['vicinity']
          });
        }

        if (result['next_page_token'] != 'undefined') {
          await fetchNextAmenity(
              i, lat, lng, ame_types[i], result['next_page_token']);
        }

        amenitiesCount[ame_types[i]] = tmpAmenities.length;
        // amenities.add({ame_types[i]: tmpAmenities});
        amenities.add({ame_types[i]: tmpAmenities});
        // print(ame_types[i]);
        // print(tmpAmenities);
        tmpAmenities = [];
      }
    }
    setState(() {
      isLoadingAmenities = false;
    });

    // print(amenities.length);
    // print(amenities_count);
    // print(amenities);
  }

  fetchNextAmenity(counter, lat, lng, amenity, token) async {
    await Future.delayed(Duration(seconds: 2));
    dynamic result = await _httpRequestViaServer(
        url:
            "https://maps.googleapis.com/maps/api/place/nearbysearch/json?pagetoken=${token}&key=${googleApikey}");

    if (result['results'].length > 0) {
      // List amenity = [];
      // amenities.addAll(other)
      for (var j = 0; j < result['results'].length; j++) {
        tmpAmenities.add({
          'name': result['results'][j]['name'],
          'location': result['results'][j]['geometry']['location'],
          'vicinity': result['results'][j]['vicinity']
        });
      }

      if (result['next_page_token'] != 'undefined' &&
          result['next_page_token'] != "") {
        await fetchNextAmenity(
            counter, lat, lng, amenity, result['next_page_token']);
      }

      // return amenity;
    }
  }



  enrichAddres() async {
    // print("geocoding for: " + txtconPropertyName!.text);
    dynamic result = await _httpRequestViaServer(
        url:
            "https://maps.googleapis.com/maps/api/place/json?address=${txtconPropertyName!.text}&key=${googleApikey}");
    //print(result);
    return result;
  }
}
