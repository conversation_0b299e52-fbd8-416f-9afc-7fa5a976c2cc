import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/moodboard.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:intl/intl.dart';



class MoodboardViewController extends GetxController {
  bool loadingMoodboards = true;
  bool loadingRenovators = true;
  List<Moodboard> moodboards = [];
  List<Moodboard> displayQuotations = [];
  List<NewarcUser> renovators = [];

  List<NewarcProject> newarcProjects = [];
  List<RenovationContact> renovationContacts = [];
  String query = "";
  String filterFieldWhere = "";
  String filterValueWhere = "";
  bool isValueNull = false;

  List<Map> filters = [];

  List statusList = [
    {'no': 'primo-incontro', 'keyword': 'Primo incontro'},
    {'no': 'da-preventivare', 'keyword': 'Da prev.'},
    {'no': 'preventivo-inviato', 'keyword': 'Prev. inviato'},
    {'no': 'preventivo-rifiutato', 'keyword': 'Prev. rifiutato'},
    {'no': 'acquisito', 'keyword': 'Acquisito'},
  ];

  List<DocumentSnapshot> documentList = [];
  int totalRecords = 0;
  String currentlyShowing = '';
  int recordsPerPage = 20;
  int pageCounter = 1;
  int totalPages = 0;
  bool disablePreviousButton = true;
  bool disableNextButton = false;
  List<dynamic> cacheFirestore = [];
  TextEditingController contSearchRef = new TextEditingController();

  TextEditingController assignmentController = new TextEditingController();
  TextEditingController agencyController = new TextEditingController();
  TextEditingController searchTextController = new TextEditingController();
  TextEditingController filterCity = new TextEditingController();
  TextEditingController filterNewarcType = new TextEditingController();

  TextEditingController contactNameController = new TextEditingController();
  TextEditingController contactSurnameController = new TextEditingController();
  TextEditingController contactEmailController = new TextEditingController();
  TextEditingController contactPhoneController = new TextEditingController();
  TextEditingController streetAddresController = new TextEditingController();
  TextEditingController renovatorController = new TextEditingController();
  TextEditingController newarcProjectsController = new TextEditingController();
  TextEditingController newarcClientsController = new TextEditingController();
  TextEditingController connectProjectSelectedCityController = new TextEditingController();
  TextEditingController connectClientSelectedCityController = new TextEditingController();
  TextEditingController connectProjectSelectedTypeController = new TextEditingController();

  TextEditingController contCity = new TextEditingController();

  List<Agency> agencyList = [];
  String formProgressMessage = '';

  bool? maxCodeEvaluated = false;

  String connectProjectSelectedCity = 'Torino';
  String? connectProjectSelectedType;

  String? selectedClientId;

  NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);
}
