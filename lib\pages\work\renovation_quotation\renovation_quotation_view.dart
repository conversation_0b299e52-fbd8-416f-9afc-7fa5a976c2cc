import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/classes/renovationQuotation.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/pages/work/renovation_quotation/renovation_quotation_controller.dart';
import 'package:newarc_platform/pages/work/renovation_quotation/renovation_quotation_data_source.dart';
import 'package:newarc_platform/utils/common_utils.dart';
import 'package:newarc_platform/utils/download_contract_pdf.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/image-select-box.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/address_completion_google.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/widget/UI/form-label.dart';
import '../../../classes/newarcProjectFixedAssetsPropertyPagamento.dart';
import '../../../utils/color_schema.dart';
import '../../../utils/downloadQuotationPDF.dart';
import '../../../utils/download_computo_metrico_pdf.dart';
import '../../../widget/UI/checkbox.dart';
import '../../../widget/UI/color-bg-dropdown.dart';
import '../../../widget/UI/select-box.dart';
import 'package:newarc_platform/activitiesForEstimate.dart' as aef;

class RenovationQuotationView extends StatefulWidget {
  final responsive;
  final Function? updateViewCallback;
  final NewarcUser newarcUser;

  const RenovationQuotationView(
      {Key? key, required this.responsive, required this.newarcUser, this.updateViewCallback})
      : super(key: key);

  @override
  State<RenovationQuotationView> createState() =>
      _RenovationQuotationViewState();
}

class _RenovationQuotationViewState extends State<RenovationQuotationView> {
  Key? paddingKey;
  final controller =
  Get.put<RenovationQuotationController>(RenovationQuotationController());

  NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 2);

  List<String> allowedRenoContactIds = [];

  Future<Widget>? _fetchQuotationsForDropdownFuture;


  @override
  void initState() {
    super.initState();
    controller.clearFilter();

    WidgetsBinding.instance.addPostFrameCallback((_){

      initialFetchContacts();
      fetchRenovators();
      fetchRenovationContacts();
    });
  }

  List<Map> searchRef = [];
  bool isCopyFromExisting = false;
  bool loadingPdfDownload = false;

  fetchRenovators() async {
    if (controller.renovators.length > 0) {
      controller.loadingRenovators = false;
      return;
    }
    List<NewarcUser> _renovators = [];
    setState(() {
      controller.loadingRenovators = true;
    });

    QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
    Query<Map<String, dynamic>> collectionSnapshotQuery;

    collectionSnapshotQuery =
        FirebaseFirestore.instance.collection(appConfig.COLLECT_USERS);

    collectionSnapshot = await collectionSnapshotQuery
        .where('type', isEqualTo: 'newarc')
        .where('role', isEqualTo: 'renovator')
        .get();

    _renovators.add(NewarcUser.empty());

    if (collectionSnapshot.docs.length > 0) {
      searchRef.add({'value': '', 'label': ''});

      for (var element in collectionSnapshot.docs) {
        try {
          NewarcUser _tmp = NewarcUser.fromDocument(element.data(), element.id);
          _renovators.add(_tmp);

          searchRef.add({
            'value': _tmp.id,
            'label': _tmp.firstName! + ' ' + _tmp.lastName!
          });
        } catch (e) {
          print("ERROR ---> $e");
        }
      }
    }

    setState(() {
      controller.renovators = _renovators;
      controller.loadingRenovators = false;
    });

    return true;
  }

  fetchQuotationContactsByUserId() async {
    Query<Map<String, dynamic>> collectionSnapshotQuery;

    collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_RENOVATION_CONTACTS)
    .where('assignedRenovatorId', isEqualTo: widget.newarcUser.id);

    QuerySnapshot<Map<String, dynamic>> collectionSnapshot = await collectionSnapshotQuery.get();

    if( collectionSnapshot.docs.length > 0 ) {
      for (var element in collectionSnapshot.docs) {
        try {
          allowedRenoContactIds.add(element.id);
        } catch (e, s) {
          print({e, s});
        }
      }
    }
    print({'allowedRenoContactIds', allowedRenoContactIds});
  }

  Future<Widget> fetchRenovationContacts() async {

    try {
      List<RenovationContact> _renovators = [];
      List<Map<String, String>> dropdownItems = [];

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_RENOVATION_CONTACTS);

      collectionSnapshot = await collectionSnapshotQuery
          // .where('assignedQuotation', isNull: true)
          .orderBy('name', descending: false)
          .get();

      if (collectionSnapshot.docs.length > 0) {
        for (var element in collectionSnapshot.docs) {
          try {
            if (element.data().containsKey('name') && element.data().containsKey('surname')) {
              RenovationContact _tmp = RenovationContact.fromDocument(element.data(), element.id);
              String contactsDropdownLabelName = "${_tmp.personInfo?.name ?? _tmp.name} ${_tmp.personInfo?.surname ?? _tmp.surname}";
              String contactsDropdownLabelAddress = "${_tmp.streetAddress}";
              if (_tmp.addressInfo?.streetName != null && _tmp.addressInfo?.streetNumber != null && _tmp.addressInfo?.city != null){
                contactsDropdownLabelAddress = "${_tmp.addressInfo!.toShortAddress()}";
              }
              
              dropdownItems.add({
                'value': element.id,
                'label': "${contactsDropdownLabelName} - ${contactsDropdownLabelAddress}"
              });
              _renovators.add(_tmp);
            } else {
              log("Skipping invalid document: ${element.data()}");
            }
          } catch (e) {
            print("ERROR ---> $e");
          }
        }
      }

      controller.renovationContacts = _renovators;

      return DropdownButtonFormField<String>(
        isExpanded: true,
        value:  null,
        icon: Padding(
          padding: EdgeInsets.only(right: 8),
          child: SvgPicture.asset(
            'assets/icons/arrow_down.svg',
            width: 12,
            color: Color(0xff7e7e7e),
          ),
        ),
        style: TextStyle(
          overflow: TextOverflow.ellipsis,
          color: Colors.black,
          fontSize: 12.0,
          fontWeight: FontWeight.bold,
          fontStyle: FontStyle.normal,
        ),
        borderRadius: BorderRadius.circular(8),
        decoration: InputDecoration(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8)),
            borderSide: BorderSide(
              color: Color.fromRGBO(227, 227, 227, 1),
              width: 1,
            ),
          ),
          hintStyle: TextStyle(
            color: Colors.grey,
            fontSize: 15.0,
            fontWeight: FontWeight.w800,
            fontStyle: FontStyle.normal,
            letterSpacing: 0,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(8)),
            borderSide: BorderSide(
              color: Color.fromRGBO(227, 227, 227, 1),
              width: 1,
            ),
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          fillColor: Colors.white,
        ),
        onChanged: (String? value) {
          controller.renovationContactController.text = value ?? "";
        },
        validator: (value) {
          if (value == null || value.isEmpty) {
            return "Required!";
          }
          return null;
        },
        dropdownColor: Colors.white,
        items: dropdownItems.map<DropdownMenuItem<String>>((item) {
          return DropdownMenuItem<String>(
            value: item['value'],
            child:NarFormLabelWidget(
              label: item['label']!,
              textColor: Colors.black,
              fontSize: 14,
              fontWeight: '600',
            ),
          );
        }).toList(),
      );
    } catch (e, s) {
      print('error: $e');
      print(s);
      return NarFormLabelWidget(label: 'Error');
    }
  }


  @override
  void dispose() {
    // controller.quotations = [];
    super.dispose();
  }

  Future<List<String>> getRenovatorAssociatedContacts() async {
    Query<Map<String, dynamic>> collectionSnapshotQuery;
    QuerySnapshot<Map<String, dynamic>> collectionSnapshot;

    collectionSnapshotQuery = FirebaseFirestore.instance
        .collection(appConfig.COLLECT_RENOVATION_CONTACTS);

    if (controller.referenceFilterController.text != '') {
      collectionSnapshotQuery = collectionSnapshotQuery.where(
          'assignedRenovatorId',
          isEqualTo: controller.referenceFilterController.text);
    }

    collectionSnapshot = await collectionSnapshotQuery.get();
    List<String> renovationContactIds = [];

    if (collectionSnapshot.docs.length > 0) {
      for (var i = 0; i < collectionSnapshot.docs.length; i++) {
        renovationContactIds.add(collectionSnapshot.docs[i].id);
      }
    }

    return renovationContactIds;
  }

  Future<void> initialFetchContacts({bool force = false}) async {

    if (controller.quotations.isNotEmpty && !force) return;

    if( allowedRenoContactIds.isEmpty && widget.newarcUser.isFilterPerAccountEnabled! ) {
      await fetchQuotationContactsByUserId();
    }

    setState(() {
      controller.quotations = [];
      controller.loadingQuotations = true;
    });

    try {

      final chunks = <List<String>>[];
      var i = 0;

      do {

        
        QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
        Query<Map<String, dynamic>> collectionSnapshotQuery;

        collectionSnapshotQuery = FirebaseFirestore.instance
            .collection(appConfig.COLLECT_RENOVATION_QUOTATION);

        List<String> renovationContactIds = [];
        if (controller.referenceFilterController.text != '') {
          renovationContactIds = await getRenovatorAssociatedContacts();
          if (renovationContactIds.isNotEmpty) {
            collectionSnapshotQuery = collectionSnapshotQuery.where(
              'renovationContactId',
              whereIn: renovationContactIds,
            );
          }
        }
        if (controller.preventiveStateFilterController.text != '') {
          collectionSnapshotQuery = collectionSnapshotQuery.where('status',
              isEqualTo: controller.preventiveStateFilterController.text);
        }
        collectionSnapshotQuery = collectionSnapshotQuery.where('isArchived', isEqualTo: false );

        /* If Filter for the user is enabled */
        if( widget.newarcUser.isFilterPerAccountEnabled! ) {
          
          collectionSnapshotQuery = collectionSnapshotQuery.where('renovationContactId', whereIn: allowedRenoContactIds.sublist(
              i,
              i + 30 > allowedRenoContactIds.length
                  ? allowedRenoContactIds.length
                  : i + 30,
            ) );
        
        }

        collectionSnapshotQuery = collectionSnapshotQuery.orderBy('created', descending: true);

        collectionSnapshot = await collectionSnapshotQuery.get();

        // collectionSnapshot = await collectionSnapshotQuery.where('isArchived', isEqualTo: false).orderBy('created', descending: true).get();

        for (var i = 0; i < collectionSnapshot.docs.length; i++) {
          RenovationQuotation tmpRenoQuote = RenovationQuotation.fromDocument(
              collectionSnapshot.docs[i].data(), collectionSnapshot.docs[i].id);

          if (controller.maxCodeEvaluated == false &&
              controller.maxCode! < tmpRenoQuote.codeCounter!) {
            controller.maxCode = tmpRenoQuote.codeCounter!;
          }
        }

        controller.documentList = collectionSnapshot.docs;

        await generateQuotationRow(collectionSnapshot);
        
        i += 30;

      } while(  i < allowedRenoContactIds.length );

      setState(() {
        controller.loadingQuotations = false;
      });
    } catch (e, s) {
      setState(() {
        controller.loadingQuotations = false;
      });
      print({'Following error',e, s});
    }
  }

  Future<Widget> fetchQuotationsForDropdown({required VoidCallback onSelectedRevision}) async {
    try {
      List<Map<String, dynamic>> allQuotations = [];
      List<Map<String, String>> quotationItems = [];
      List<Map<String, String>> versionItems = [];
      List<Map<String, String>> revisionItems = [];

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_RENOVATION_QUOTATION)
          .where('isArchived', isEqualTo: false)
          .orderBy('created', descending: true)
          .get();

      if (collectionSnapshot.docs.isNotEmpty) {
        for (var element in collectionSnapshot.docs) {
          var data = element.data();
          RenovationContact contact = controller.renovationContacts.firstWhere((contact)=> contact.id == data["renovationContactId"]);
          String address = "${contact.addressInfo?.streetName ?? ""} ${contact.addressInfo?.streetNumber ?? ""}, ${contact.addressInfo?.city ?? ""}";
          if (data.containsKey('code') && data.containsKey('version') && data.containsKey('revision')) {
            allQuotations.add({
              'id': element.id,
              'code': data['code'],
              'version': data['version'].toString(),
              'revision': data['revision'].toString(),
              'address': address,
              'tipologia': data['tipologia'] ?? "",
            });
          }
        }
      }

      // Extract unique quotations for "Copia da"
      quotationItems = allQuotations
          .where((quote) => quote['version'].toString() == '1' && quote['revision'].toString() == '0')
          .map((quote) => {
        'value': quote['code'].toString(),
        'label': "${quote['code']} - ${quote['address']}"
      }).toList();



      void filterVersions(String quotationCode) {
        Set<String> uniqueVersions = allQuotations
            .where((q) => q['code'] == quotationCode)
            .map<String>((q) => q['version'])
            .toSet();
            versionItems = uniqueVersions.map((version) => {'value': version, 'label': "V$version"}).toList();
        controller.selectedVersion = null;
        revisionItems = [];
        controller.selectedRevision = null;
      }

      void filterRevisions(String quotationCode, String version) {
        Set<String> uniqueRevisions = allQuotations
            .where((q) => q['code'] == quotationCode && q['version'] == version)
            .map<String>((q) => q['revision'])
            .toSet();

        revisionItems = uniqueRevisions.map((revision) => {'value': revision, 'label': "R$revision"}).toList();
        controller.selectedRevision = null;
      }

      return StatefulBuilder(
        builder: (context, setState) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // --------------------  Copia da Dropdown
              DropdownButtonFormField<String>(
                isExpanded: true,
                // value: controller.selectedQuotationCode,
                value: null,
                icon: Padding(
                  padding: EdgeInsets.only(right: 8),
                  child: SvgPicture.asset(
                    'assets/icons/arrow_down.svg',
                    width: 12,
                    color: Color(0xff7e7e7e),
                  ),
                ),
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: Colors.black,
                  fontSize: 12.0,
                  fontWeight: FontWeight.bold,
                  fontStyle: FontStyle.normal,
                ),
                borderRadius: BorderRadius.circular(8),
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(8)),
                    borderSide: BorderSide(color: Color.fromRGBO(227, 227, 227, 1), width: 1),
                  ),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  fillColor: Colors.white,
                ),
                onChanged: (String? value) {
                  if (value != null) {
                    setState(() {
                      controller.selectedQuotationCode = value;
                      controller.selectedVersion = null;
                      controller.selectedRevision = null;
                      filterVersions(value);
                    });
                  }
                },
                validator: (value) => value == null || value.isEmpty ? "Required!" : null,
                dropdownColor: Colors.white,
                items: quotationItems.map<DropdownMenuItem<String>>((item) {
                  return DropdownMenuItem<String>(
                    value: item['value'],
                    child: NarFormLabelWidget(
                      label: item['label']!,
                      textColor: Colors.black,
                      fontSize: 14,
                      fontWeight: '600',
                    ),
                  );
                }).toList(),
              ),

              SizedBox(height: 4),
              NarFormLabelWidget(
                label: "Versione",
                textColor: Color(0xff696969),
                fontSize: 14,
                fontWeight: '600',
              ),
              SizedBox(height: 4),

              // --------------------  Version Dropdown
              DropdownButtonFormField<String>(
                isExpanded: true,
                // value: controller.selectedVersion,
                value: null,
                icon: Padding(
                  padding: EdgeInsets.only(right: 8),
                  child: SvgPicture.asset(
                    'assets/icons/arrow_down.svg',
                    width: 12,
                    color: Color(0xff7e7e7e),
                  ),
                ),
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: Colors.black,
                  fontSize: 12.0,
                  fontWeight: FontWeight.bold,
                  fontStyle: FontStyle.normal,
                ),
                borderRadius: BorderRadius.circular(8),
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(8)),
                    borderSide: BorderSide(color: Color.fromRGBO(227, 227, 227, 1), width: 1),
                  ),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  fillColor: Colors.white,
                ),
                onChanged: controller.selectedQuotationCode == null
                    ? null
                    : (String? value) {
                  if (value != null) {
                    setState(() {
                      controller.selectedVersion = value;
                      controller.selectedRevision = null;
                      filterRevisions(controller.selectedQuotationCode!, value);
                    });
                  }
                },
                validator: (value) => value == null || value.isEmpty ? "Required!" : null,
                dropdownColor: Colors.white,
                items: versionItems.map<DropdownMenuItem<String>>((item) {
                  return DropdownMenuItem<String>(
                    value: item['value'],
                    child: NarFormLabelWidget(
                      label: item['label']!,
                      textColor: Colors.black,
                      fontSize: 14,
                      fontWeight: '600',
                    ),
                  );
                }).toList(),
              ),

              SizedBox(height: 4),
              NarFormLabelWidget(
                label: "Revisione",
                textColor: Color(0xff696969),
                fontSize: 14,
                fontWeight: '600',
              ),
              SizedBox(height: 4),

              // -------------------- Revision Dropdown
              DropdownButtonFormField<String>(
                isExpanded: true,
                // value: controller.selectedRevision,
                value: null,
                icon: Padding(
                  padding: EdgeInsets.only(right: 8),
                  child: SvgPicture.asset(
                    'assets/icons/arrow_down.svg',
                    width: 12,
                    color: Color(0xff7e7e7e),
                  ),
                ),
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  color: Colors.black,
                  fontSize: 12.0,
                  fontWeight: FontWeight.bold,
                  fontStyle: FontStyle.normal,
                ),
                borderRadius: BorderRadius.circular(8),
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(8)),
                    borderSide: BorderSide(color: Color.fromRGBO(227, 227, 227, 1), width: 1),
                  ),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  fillColor: Colors.white,
                ),
                onChanged: controller.selectedVersion == null
                    ? null
                    : (String? value) {
                  if (value != null) {
                    Map<String, dynamic> selectedQuotationData =  allQuotations.firstWhere((quote) => quote['code'] == controller.selectedQuotationCode && quote['version'].toString() == controller.selectedVersion && quote['revision'].toString() == value,orElse: ()=>{});
                    setState(() {
                      controller.selectedRevision = value;
                      if(selectedQuotationData.isNotEmpty){
                        controller.tipologiaController.text = selectedQuotationData['tipologia'].toString().toCapitalized();
                      }
                    });
                    onSelectedRevision();
                  }
                },
                validator: (value) => value == null || value.isEmpty ? "Required!" : null,
                dropdownColor: Colors.white,
                items: revisionItems.map<DropdownMenuItem<String>>((item) {
                  return DropdownMenuItem<String>(
                    value: item['value'],
                    child: NarFormLabelWidget(
                      label: item['label']!,
                      textColor: Colors.black,
                      fontSize: 14,
                      fontWeight: '600',
                    ),
                  );
                }).toList(),
              ),
            ],
          );
        },
      );
    } catch (e) {
      log('Error: $e');
      return Text('Error loading quotations', style: TextStyle(color: Colors.red));
    }
  }


  generateQuotationRow(collectionSnapshot) async {
    List<RenovationQuotation> _quotation = [];
    
    for (var element in collectionSnapshot.docs) {
      try {
        var _tmp = RenovationQuotation.fromDocument(element.data(), element.id);

        DocumentSnapshot<Map<String, dynamic>> renoContactSnapshot =
        await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
            .doc(_tmp.renovationContactId)
            .get();
        if( renoContactSnapshot.data() != null ) {
          _tmp.renovationContact = RenovationContact.fromDocument(renoContactSnapshot.data()!, renoContactSnapshot.id);
        }


        _quotation.add(_tmp);
      } catch (e, s) {
        print({e, s});
      }
    }

    // Grouping quotations based on the same code
    Map<String, List<RenovationQuotation>> groupedQuotations = {};
    for (var quote in _quotation) {
      String code = quote.code ?? "";
      groupedQuotations.putIfAbsent(code, () => []).add(quote);
    }
    //  Determining each group's oldest creation date
    Map<String, DateTime> groupCreatedMap = {};
    groupedQuotations.forEach((code, groupDocs) {
      // Find the minimum creation timestamp in the group.
      DateTime groupCreated = groupDocs
          .map((doc) => DateTime.fromMillisecondsSinceEpoch(doc.created!))
          .reduce((a, b) => a.isBefore(b) ? a : b);
      groupCreatedMap[code] = groupCreated;
    });
    // Sort groups by their oldest creation date
    final sortedGroupEntries = groupedQuotations.entries.toList()
      ..sort((entryA, entryB) {
        // Compare group created dates descending:
        // (i.e. the group with the later "oldest" creation comes first)
        DateTime createdA = groupCreatedMap[entryA.key]!;
        DateTime createdB = groupCreatedMap[entryB.key]!;
        return createdB.compareTo(createdA);
      });
    // Sort each group's documents by version and revision
    sortedGroupEntries.forEach((entry) {
      entry.value.sort((a, b) {
        final int versionA = a.version as int;
        final int versionB = b.version as int;
        if (versionA != versionB) {
          return versionA.compareTo(versionB);
        }
        final int revisionA = a.revision as int;
        final int revisionB = b.revision as int;
        return revisionA.compareTo(revisionB);
      });
    });
    // Convert sorted group entries into a list of lists
    List<List<RenovationQuotation>> sortedGroups = sortedGroupEntries.map((entry) => entry.value).toList();

    controller.expandedGroups.clear();
    for (var i = 0; i < sortedGroups.length; i++) {
      controller.displayQuotations.add(sortedGroups[i]);  
    }
    
    for (var i = 0; i < _quotation.length; i++) {
      controller.quotations.add(_quotation[i]);
    }
    
    controller.loadingQuotations = false;
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> showAddContactPopup(StateSetter _setState) async {
    controller.contactNameController.clear();
    controller.contactSurnameController.clear();
    controller.contactEmailController.clear();
    controller.contactPhoneController.clear();
    controller.contactAddressInfo = BaseAddressInfo.empty();
    controller.formProgressMessage = '';
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, setState) {
            return Center(
                child: BaseNewarcPopup(
                    title: "Aggiungi contatto",
                    buttonText: "Aggiungi contatto",
                    onPressed: () async {
                      setState(() {
                        controller.formProgressMessage = 'Saving';
                      });

                      try {
                      Map<String, dynamic> contactData = {
                        'name': controller.contactNameController.text,
                        'surname': controller.contactSurnameController.text,
                        'email': controller.contactEmailController.text,
                        'phone': controller.contactPhoneController.text,
                      };

                      if (!controller.contactAddressInfo.isValidAddress()) {
                        setState(() {
                          controller.formProgressMessage = 'Indirizzo non valido';
                        });
                        return null;
                      }

                        //? ------ for getting value use selectedRenovationQuotationForCopy
                        RenovationQuotation? selectedRenovationQuotationForCopy;

                        if((controller.selectedQuotationCode?.isNotEmpty ?? false) && (controller.selectedVersion?.isNotEmpty ?? false) && (controller.selectedRevision?.isNotEmpty ?? false)){
                          selectedRenovationQuotationForCopy = controller.quotations.firstWhere((val)=> val.code == controller.selectedQuotationCode && val.version.toString() == controller.selectedVersion && val.revision.toString() == controller.selectedRevision);
                        }

                        Map<String, dynamic> data = {
                          'name': controller.contactNameController.text,
                          'surname': controller.contactSurnameController.text,
                          'email': controller.contactEmailController.text,
                          'phone': controller.contactPhoneController.text,
                          'created': DateTime.now().millisecondsSinceEpoch,
                          'modificationDate': DateTime.now().millisecondsSinceEpoch,
                          'streetAddress': "${controller.contactAddressInfo.toShortAddress()}",
                          'city': controller.contactAddressInfo.city,
                          'addressInfo': controller.contactAddressInfo.toMap(),
                          'personInfo': contactData,
                          'assignedRenovatorId':
                              controller.renovatorController.text,
                          'files': [],
                          'assignedQuotation': null,
                        };

                        RenovationContact renovationContact = RenovationContact(data);

                        DocumentReference<Map<String, dynamic>> renoContact =
                        await FirebaseFirestore.instance
                            .collection(
                            appConfig.COLLECT_RENOVATION_CONTACTS)
                            .add(renovationContact.toMap());

                        Map<String, dynamic> renodata = {
                          'renovationContactId': renoContact.id,
                          'paymentMode': '',
                          'constructionDuration': '',
                          'created': DateTime.now().millisecondsSinceEpoch,
                          'modificationDate': DateTime.now().millisecondsSinceEpoch,
                          'status': 'in-attesa',
                        };

                        RenovationContact selectedRenovationContact = renovationContact;

                        RenovationQuotation renovationQuotation = RenovationQuotation(renodata);

                        DocumentReference<Map<String, dynamic>> quotationId;

                        String serviceCode = appConst.composeProjectCode['project']!['ristrutturazione']!;
                        String renoRegion = selectedRenovationContact.addressInfo?.region ?? appConst.cityToRegionConverter[selectedRenovationContact.city]; 
                        String renoRegionCode = appConst.composeProjectCode['region']![renoRegion]!;

                        DateTime createdDate = DateTime.fromMillisecondsSinceEpoch(renovationQuotation.created ?? DateTime.now().millisecondsSinceEpoch);
                        String yearCode = createdDate.year.toString().substring(2); // Get last two digits of the year
                        int year = createdDate.year;
                        
                        int lastQuotationNumber = await getLastQuotationNumber(year, renoRegion);
                        String quotationNumber = (lastQuotationNumber + 1).toString().padLeft(4, '0'); // Ensure 4 digits with leading zeros
                        String _code = "PR_${serviceCode}${renoRegionCode}${yearCode}${quotationNumber}";


                        if(isCopyFromExisting){
                          //---------- for copy quotation
                          selectedRenovationQuotationForCopy?.renovationContactId = renoContact.id;
                          selectedRenovationQuotationForCopy?.code = _code;
                          selectedRenovationQuotationForCopy?.year = createdDate.year;
                          selectedRenovationQuotationForCopy?.created = DateTime.now().millisecondsSinceEpoch;
                          selectedRenovationQuotationForCopy?.codeCounter = lastQuotationNumber + 1;
                          selectedRenovationQuotationForCopy?.status = "in-attesa";
                          selectedRenovationQuotationForCopy?.version = 1;
                          selectedRenovationQuotationForCopy?.revision = 0;


                          quotationId = await FirebaseFirestore.instance
                              .collection( appConfig.COLLECT_RENOVATION_QUOTATION)
                              .add(selectedRenovationQuotationForCopy!.toMap());

                          //
                          await FirebaseFirestore.instance
                              .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
                              .doc(renoContact.id)
                              .update({'assignedQuotation': quotationId.id});

                          selectedRenovationQuotationForCopy.id = quotationId.id;
                          selectedRenovationQuotationForCopy.renovationContact = RenovationContact.fromDocument(
                              selectedRenovationContact.toMap(),
                              selectedRenovationContact.id ?? ''
                          );

                          Future.microtask(() {
                            widget.updateViewCallback!(
                                'renovation-quotation-single',
                                projectArguments: {
                                  'renovationQuotation': selectedRenovationQuotationForCopy
                                });
                          });

                          setState(() {
                            controller.formProgressMessage = 'Saved';
                          });
                        }else{
                          //----------- for new quotation
                          renovationQuotation.code = _code;
                          renovationQuotation.codeCounter = lastQuotationNumber + 1;
                          renovationQuotation.year = createdDate.year;
                          renovationQuotation.version = 1;
                          renovationQuotation.revision = 0;


                          quotationId = await FirebaseFirestore.instance
                              .collection(
                              appConfig.COLLECT_RENOVATION_QUOTATION)
                              .add(renovationQuotation.toMap());

                          //
                          await FirebaseFirestore.instance
                              .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
                              .doc(renoContact.id)
                              .update({'assignedQuotation': quotationId.id});


                          renovationQuotation.id = quotationId.id;
                          renovationQuotation.renovationContact = RenovationContact.fromDocument(
                              selectedRenovationContact.toMap(),
                              selectedRenovationContact.id ?? ''
                          );


                          Future.microtask(() {
                            widget.updateViewCallback!(
                                'renovation-quotation-single',
                                projectArguments: {
                                  'renovationQuotation': renovationQuotation
                                });
                          });

                          setState(() {
                            controller.formProgressMessage = 'Saved';
                          });
                        }

                        return true;
                      } catch (e, s) {
                        setState(() {
                          controller.formProgressMessage = 'Error';
                        });
                        print({e, s});
                        // return false;
                      }

                      return true;
                    },
                    // To make custom popup using the bluprint of  RenovationContactPopup
                    column: Container(
                      width: 400,
                      height: 400,
                      child: ListView(
                        children: [
                          AddressSearchBar(
                            label: "Indirizzo Ristrutturazione", 
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Inserisci un indirizzo valido';
                              }
                            },
                            onPlaceSelected: (selectedPlace){
                              
                              BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace['place']);
                              if (selectedAddress.isValidAddress()){
                                controller.contactAddressInfo = selectedAddress;
                              } else {
                                controller.contactAddressInfo = BaseAddressInfo.empty();
                              }
                            }
                          ),
                          SizedBox(height: 10),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              CustomTextFormField(
                                label: "Nome",
                                validator: (value) {
                                  if (value == '') {
                                    return 'Required!';
                                  }

                                  return null;
                                },
                                controller: controller.contactNameController,
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              CustomTextFormField(
                                label: "Cognome",
                                controller: controller.contactSurnameController,
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              CustomTextFormField(
                                label: "E-Mail",
                                controller: controller.contactEmailController,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return null;
                                  }
                                  final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                                  if (!emailRegex.hasMatch(value)) {
                                    return 'Inserisci un indirizzo email valido';
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              CustomTextFormField(
                                label: "Telefono",
                                controller: controller.contactPhoneController,
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          controller.loadingRenovators
                              ? CircularProgressIndicator(
                            color: Theme.of(context).primaryColor,
                          )
                              : Column(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    NarFormLabelWidget(
                                      label: "Assegnazione",
                                      textColor: Color(0xff696969),
                                      fontSize: 13,
                                      fontWeight: '500',
                                    ),
                                    SizedBox(height: 4),
                                    NarImageSelectBoxWidget(
                                      options: controller.renovators
                                          .where((e) =>
                                              e.isActive == true &&
                                              e.isArchived == false)
                                          .map((e) {
                                        return {
                                          'value': e.id,
                                          'label':
                                              e.firstName! + " " + e.lastName!
                                        };
                                      }).toList(),
                                      controller:
                                          controller.renovatorController,
                                      validationType: 'required',
                                      parametersValidate: 'Required!',
                                      onChanged: (dynamic val) {
                                        controller.renovators
                                            .where((element) =>
                                                element.id ==
                                                controller
                                                    .renovatorController.text)
                                            .first;
                                  setState(() {});
                                },
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          Row(
                            children: [
                              NarFormLabelWidget(
                                  label: controller.formProgressMessage)
                            ],
                          )
                        ],
                      ),
                    )));
          });
        });
  }

  Future<int> getLastQuotationNumber(year, region) async {
    // Query Firestore for the last quotation of the given year for given region
    try {
      // Saves id of renovation contacts in the region
      final regionQuerySnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
          .where('addressInfo.region', isEqualTo: region)
          .get();
      List<String> _regionContactsIds = [];
      for (var doc in regionQuerySnapshot.docs) {
        _regionContactsIds.add(doc.id);
      }
      // Saves id of renovation contacts in the city if not already found in the region
      // needed for retrocompatibility (renovationContacts without addressInfo.region)
      final cityQuerySnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
          .where('city', isEqualTo: appConst.regionToCityConverter[region] ?? 'city different from torino, milano, roma')
          .get();
      for (var doc in cityQuerySnapshot.docs) {
        if (!_regionContactsIds.contains(doc.id)) {
          _regionContactsIds.add(doc.id);
        }
      }

      final querySnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_RENOVATION_QUOTATION)
          .where('year', isEqualTo: year)
          .orderBy('codeCounter', descending: true)
          .get();

      /// Firestore whereIn method allows for list of max 30 elems, filtering by region in app
      List<RenovationQuotation> _yearQuotations = [];
      for (var doc in querySnapshot.docs) {
        RenovationQuotation _quotation = RenovationQuotation.fromDocument(doc.data(), doc.id);
        _quotation.id = doc.id;
        _yearQuotations.add(_quotation);
      }
      List<RenovationQuotation> _regionYearQuotations = _yearQuotations.where((test) => _regionContactsIds.contains(test.renovationContactId)).toList();

      if (_regionYearQuotations.isNotEmpty) {
        return _regionYearQuotations.first.codeCounter as int;
      } else {
      
      }
      return 0; // Start from 0 if no quotations exist for the year and region
    } catch (e,s) {
      print('getLastQuotationNumber error: $e');
      print(s);
      return 0;
    }

  }

  showNewQuotation(BuildContext context) async {

    controller.formProgressMessage = '';
    return showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (context, _setState) {
            return Center(
              child: BaseNewarcPopup(
                title: 'Nuovo preventivo',
                buttonText: "Crea preventivo",
                onPressed: () async {
                  setState(() {
                    controller.formProgressMessage = 'Saving';
                  });

                  try {
                    //? ------ for getting value use selectedRenovationQuotationForCopy

                    RenovationQuotation? selectedRenovationQuotationForCopy;
                    if((controller.selectedQuotationCode?.isNotEmpty ?? false) && (controller.selectedVersion?.isNotEmpty ?? false) && (controller.selectedRevision?.isNotEmpty ?? false)){
                      selectedRenovationQuotationForCopy = controller.quotations.firstWhere((val)=> val.code == controller.selectedQuotationCode && val.version.toString() == controller.selectedVersion && val.revision.toString() == controller.selectedRevision);
                    }

                    Map<String, dynamic> data = {
                      'code': '',
                      'codeCounter': 0,
                      'renovationContactId': controller.renovationContactController.text,
                      'paymentMode': '',
                      'constructionDuration': '',
                      'created': DateTime.now().millisecondsSinceEpoch,
                      'status': 'in-attesa',
                    };

                    RenovationContact selectedRenovationContact = controller.renovationContacts.firstWhere((e) => e.id == controller.renovationContactController.text);

                    RenovationQuotation renovationQuotation = RenovationQuotation(data);
                    
                    // create and assign internal code (code) for renovationQuotation
                    String serviceCode = appConst.composeProjectCode['project']!['ristrutturazione']!;
                    String renoRegion = selectedRenovationContact.addressInfo?.region ?? appConst.cityToRegionConverter[selectedRenovationContact.city]; 
                    String renoRegionCode = appConst.composeProjectCode['region']![renoRegion]!;

                    renovationQuotation.tipologia = controller.tipologiaController.text.trim().toLowerCase();

                    DateTime createdDate = DateTime.fromMillisecondsSinceEpoch(renovationQuotation.created ?? DateTime.now().millisecondsSinceEpoch);
                    String yearCode = createdDate.year.toString().substring(2); // Get last two digits of the year
                    int year = createdDate.year;

                    int lastQuotationNumber = await getLastQuotationNumber(year, renoRegion);
                    String quotationNumber = (lastQuotationNumber + 1).toString().padLeft(4, '0'); // Ensure 4 digits with leading zeros
                    String _code = "PR_${serviceCode}${renoRegionCode}${yearCode}${quotationNumber}";


                    DocumentReference<Map<String, dynamic>> quotationId;
                    if(isCopyFromExisting){
                      //? ------------- For duplicate quotation

                      selectedRenovationQuotationForCopy?.renovationContactId = controller.renovationContactController.text;
                      selectedRenovationQuotationForCopy?.code = _code;
                      selectedRenovationQuotationForCopy?.year = DateTime.now().year;
                      selectedRenovationQuotationForCopy?.created = DateTime.now().millisecondsSinceEpoch;
                      selectedRenovationQuotationForCopy?.codeCounter = lastQuotationNumber + 1;
                      selectedRenovationQuotationForCopy?.status = "in-attesa";
                      selectedRenovationQuotationForCopy?.version = 1;
                      selectedRenovationQuotationForCopy?.revision = 0;
                      selectedRenovationQuotationForCopy?.comment = '';

                      quotationId = await FirebaseFirestore.instance
                          .collection( appConfig.COLLECT_RENOVATION_QUOTATION)
                          .add(selectedRenovationQuotationForCopy!.toMap());

                      //
                      await FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
                          .doc(controller.renovationContactController.text)
                          .update({'assignedQuotation': quotationId.id});

                      selectedRenovationQuotationForCopy.id = quotationId.id;
                      selectedRenovationQuotationForCopy.renovationContact = RenovationContact.fromDocument(
                          selectedRenovationContact.toMap(),
                          selectedRenovationContact.id ?? ''
                      );


                      Future.microtask((){
                        widget.updateViewCallback!('renovation-quotation-single',
                            projectArguments: {
                              'renovationQuotation': selectedRenovationQuotationForCopy
                            });
                      });

                      setState(() {
                        controller.formProgressMessage = 'Saved';
                      });
                    }else{
                      //?  ------------- For new quotation
                      renovationQuotation.year = createdDate.year;
                      renovationQuotation.codeCounter = lastQuotationNumber + 1;
                      renovationQuotation.code = _code;
                      renovationQuotation.version = 1;
                      renovationQuotation.revision = 0;
                      quotationId =
                      await FirebaseFirestore.instance
                          .collection( appConfig.COLLECT_RENOVATION_QUOTATION)
                          .add(renovationQuotation.toMap());

                      await FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
                          .doc(controller.renovationContactController.text)
                          .update({'assignedQuotation': quotationId.id});

                      renovationQuotation.id = quotationId.id;
                      renovationQuotation.renovationContact = RenovationContact.fromDocument(
                          selectedRenovationContact.toMap(),
                          selectedRenovationContact.id ?? ''
                      );


                      Future.microtask((){
                        widget.updateViewCallback!('renovation-quotation-single',
                            projectArguments: {
                              'renovationQuotation': renovationQuotation
                            });
                      });

                      setState(() {
                        controller.formProgressMessage = 'Saved';
                      });
                    }

                    return true;
                  } catch (e, s) {
                    setState(() {
                      controller.formProgressMessage = 'Error';
                    });
                    print({e, s});
                    return false;
                  }
                },
                // To make custom popup using the bluprint of  RenovationContactPopup
                column: Container(
                  width: 400,
                  child: Column(
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 0, bottom: 0, left: 0),
                            child: Row(
                              children: [
                                Switch(
                                  value: isCopyFromExisting,
                                  activeTrackColor: Theme.of(context).primaryColor,
                                  activeColor: AppColor.white,
                                  inactiveThumbColor: AppColor.white,
                                  inactiveTrackColor: Color(0xffA9A9A9),
                                  onChanged: (val) async {
                                    controller.selectedQuotationCode = "";
                                    controller.selectedVersion = "";
                                    controller.selectedRevision = "";
                                    controller.tipologiaController.clear();
                                    setState(() {
                                      isCopyFromExisting = val;
                                    });
                                    _fetchQuotationsForDropdownFuture = fetchQuotationsForDropdown(onSelectedRevision: (){
                                      _setState((){});
                                    });
                                    _setState((){});
                                  },
                                ),
                                SizedBox(width: 4),
                                NarFormLabelWidget(
                                  label: "Copia da esistente",
                                  textColor: Color(0xff696969),
                                  fontSize: 15,
                                  fontWeight: '500',
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: isCopyFromExisting ? 20 : 0),
                          isCopyFromExisting ?
                          NarFormLabelWidget(
                            label: "Copia da",
                            textColor: Color(0xff696969),
                            fontSize: 14,
                            fontWeight: '600',
                          ) : SizedBox.shrink(),
                          SizedBox(height: isCopyFromExisting ? 4 : 0),
                          isCopyFromExisting
                              ?
                          FutureBuilder<Widget>(
                              future: _fetchQuotationsForDropdownFuture,
                              builder: (context, snapshot) {
                                if (snapshot.hasData) {
                                  return Container(child: snapshot.data);
                                } else if (snapshot.hasError) {
                                  return Container(
                                    width: 30,
                                    height: 30,
                                    decoration: BoxDecoration(
                                      borderRadius:
                                      BorderRadius.circular(100),
                                      color: const Color.fromARGB(255, 19, 17, 17),
                                    ),
                                  );
                                }

                                return Center(
                                  child: CircularProgressIndicator(
                                    color: Theme.of(context).primaryColor,
                                  ),
                                );
                              })
                              : SizedBox.shrink(),
                          SizedBox(height: 20),
                          NarFormLabelWidget(
                            label: "Seleziona contatto",
                            textColor: Color(0xff696969),
                            fontSize: 14,
                            fontWeight: '600',
                          ),
                          SizedBox(height: 4),
                          FutureBuilder<Widget>(
                              future: fetchRenovationContacts(),
                              builder: (context, snapshot) {
                                if (snapshot.hasData) {
                                  return Container(child: snapshot.data);
                                } else if (snapshot.hasError) {
                                  return Container(
                                    width: 30,
                                    height: 30,
                                    decoration: BoxDecoration(
                                      borderRadius:
                                      BorderRadius.circular(100),
                                      color: const Color.fromARGB(
                                          255, 19, 17, 17),
                                    ),
                                  );
                                }

                                return Center(
                                  child: CircularProgressIndicator(
                                    color: Theme.of(context).primaryColor,
                                  ),
                                );
                              })
                        ],
                      ),
                      SizedBox(height: 20),
                      NarFormLabelWidget(
                        label: 'oppure',
                        fontSize: 14,
                      ),
                      SizedBox(height: 20),
                      Container(
                        width: 180,
                        child: BaseNewarcButton(
                            buttonText: "Nuovo contatto",
                            notAccent: true,
                            onPressed: () {
                              Navigator.pop(context);
                              showAddContactPopup(setState);
                            }),
                      ),
                      SizedBox(height: 20),
                      NarSelectBoxWidget(
                        options: ["Ristrutturazione completa","Ristrutturazione parziale","Ristrutturazione bagno"],
                        controller: controller.tipologiaController,
                        validationType: 'required',
                        parametersValidate: 'Obbligatorio!',
                        label: "Tipologia ristrutturazione",
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          NarFormLabelWidget(label: controller.formProgressMessage)
                        ],
                      )
                    ],
                  ),
                ),
              ),
            );
          });
        });
  }

  TextEditingController contSearchQuotation = new TextEditingController();

  List<Map> status = [
    {
      'value': 'in-attesa',
      'label': 'In Attesa',
      'bgColor': Color(0xffD4D4D4),
      'textColor': Colors.black
    },
    {
      'value': 'accettato',
      'label': 'Accettato',
      'bgColor': Color(0xff39C14F),
      'textColor': Colors.white
    },
    {
      'value': 'rifiutato',
      'label': 'Rifiutato',
      'bgColor': Color(0xffDD0000),
      'textColor': Colors.white
    },
    {
      'value': 'da-modificare',
      'label': 'Da modificare',
      'bgColor': Color(0xffF2CB00),
      'textColor': Colors.white
    }
  ];

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }

    return LayoutBuilder(builder: (context, constraints) {
      return Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              NarFormLabelWidget(
                label: 'Preventivi ristrutturazioni',
                fontSize: 19,
                fontWeight: '700',
              ),
              Expanded(
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () {
                          showNewQuotation(context);
                        },
                        child: Container(
                          height: 32,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Padding(
                            padding:
                            const EdgeInsets.symmetric(horizontal: 20.0),
                            child: Text(
                              "Nuovo preventivo",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 13,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              NarFilter(
                showSearchInput: true,
                searchTextEditingControllers: controller.searchTextController,
                searchHintText: "Cerca per indirizzo, per nome o per codice...",
                onChanged: (String? searchQuery)async{
                  if(searchQuery?.isNotEmpty ?? false){
                    if (searchQuery != null && searchQuery.isNotEmpty) {
                      List<RenovationQuotation> allQuotations = controller.displayQuotations.expand((list) => list).toList();

                      List<RenovationQuotation> filtered = allQuotations.where((project) {
                        final address = project.renovationContact?.addressInfo;
                        final code = project.code?.toLowerCase() ?? "";
                        final city = address?.city?.toLowerCase() ?? project.renovationContact?.city?.toLowerCase() ?? "";
                        final streetName = address?.streetName?.toLowerCase() ?? '';
                        final fullAddress = address?.fullAddress?.toLowerCase() ?? project.renovationContact?.city?.toLowerCase() ?? "";
                        return code.contains(searchQuery.toLowerCase()) ||
                            city.contains(searchQuery.toLowerCase()) ||
                            streetName.contains(searchQuery.toLowerCase()) ||
                            fullAddress.contains(searchQuery.toLowerCase());
                      }).toList();


                      setState(() {
                          controller.displayQuotations = filtered.isNotEmpty ? processAndSortQuotations(filtered) : [];
                      });

                    }
                  }else{
                    await initialFetchContacts(force: true);
                  }
                },
                suffixIconOnTap: ()async{
                  await initialFetchContacts(force: true);
                  if(controller.searchTextController.text.trim().isNotEmpty){
                    List<RenovationQuotation> allQuotations = controller.displayQuotations.expand((list) => list).toList();

                    List<RenovationQuotation> filtered = allQuotations.where((project) {
                      final address = project.renovationContact?.addressInfo;
                      final code = project.code?.toLowerCase() ?? "";
                      final city = address?.city?.toLowerCase() ?? project.renovationContact?.city?.toLowerCase() ?? "";
                      final streetName = address?.streetName?.toLowerCase() ?? '';
                      final fullAddress = address?.fullAddress?.toLowerCase() ?? project.renovationContact?.city?.toLowerCase() ?? "";
                      return code.contains(controller.searchTextController.text.toLowerCase()) ||
                          city.contains(controller.searchTextController.text.toLowerCase()) ||
                          streetName.contains(controller.searchTextController.text.toLowerCase()) ||
                          fullAddress.contains(controller.searchTextController.text.toLowerCase());
                    }).toList();

                    setState(() {
                      controller.displayQuotations = filtered.isNotEmpty ? processAndSortQuotations(filtered) : [];
                    });
                  }else{
                    await initialFetchContacts(force: true);
                  }
                },
                textEditingControllers: [
                  controller.referenceFilterController,
                  controller.preventiveStateFilterController,
                ],
                selectedFilters: [
                  controller.referenceSelectedFilter,
                  controller.preventiveStateSelectedFilter,
                ],
                filterFields: [
                  {
                    'Architetto': NarImageSelectBoxWidget(
                      options: searchRef,
                      onChanged: (dynamic val) {
                        controller.referenceSelectedFilter = val['label'];
                        setState(() {});
                      },
                      controller: controller.referenceFilterController,
                    ),
                  },
                  {
                    'Stato preventivo': NarImageSelectBoxWidget(
                      options: [{"value": "", "label": ""}, ...status],
                      onChanged: (dynamic val) {
                        controller.preventiveStateSelectedFilter = val['label'];
                        setState(() {});
                      },
                      controller: controller.preventiveStateFilterController,
                    ),
                  },
                ],
                onSubmit: () async {
                  await initialFetchContacts(force: true);
                },
                onReset: () async {
                  controller.clearFilter();
                  await initialFetchContacts(force: true);
                },
              ),
            ],
          ),
          SizedBox(height: 10),
          Container(
            height: constraints.maxHeight / 1.2,
            child: Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Opacity(
                        opacity: controller.loadingQuotations ? 0.5 : 1,
                        child: NewarcDataTable(
                          rowsPerPage: 9999,
                          dividerThickness: 0,
                          empty: Text(""),
                          columns: [
                            DataColumn2(
                              size: ColumnSize.L,
                              label: Text(
                                'Indirizzo',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Architetto',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Codice',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Inserimento',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.M,
                              label: Text(
                                'Commenti',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Stato preventivo',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.M,
                              label: Text(
                                'Azioni',
                              ),
                            ),
                          ],
                          source: RenovationQuotationDataSource(
                            initialFetchContacts: initialFetchContacts,
                            onOpenAzioniDialog: (RenovationQuotation row,String type,List<RenovationQuotation> group){
                              showAzioniDialog(context: context, dialogType: type, row: row,group: group);
                            },
                            status: status,
                            context: context,
                            groupedQuotations: controller.displayQuotations,
                            renovators: controller.renovators,
                          ),
                        ),
                      ),
                      if (controller.loadingQuotations)
                        Positioned.fill(
                          child: Center(
                            child: CircularProgressIndicator(
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                      // dataTablePagination(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }

  List<List<RenovationQuotation>> processAndSortQuotations(List<RenovationQuotation> quotations) {
    // Grouping quotations based on the same code
    Map<String, List<RenovationQuotation>> groupedQuotations = {};
    for (var quote in quotations) {
      String code = quote.code ?? "";
      groupedQuotations.putIfAbsent(code, () => []).add(quote);
    }

    // Determining each group's oldest creation date
    Map<String, DateTime> groupCreatedMap = {};
    groupedQuotations.forEach((code, groupDocs) {
      DateTime groupCreated = groupDocs
          .map((doc) => DateTime.fromMillisecondsSinceEpoch(doc.created!))
          .reduce((a, b) => a.isBefore(b) ? a : b);
      groupCreatedMap[code] = groupCreated;
    });

    // Sort groups by their oldest creation date (descending)
    final sortedGroupEntries = groupedQuotations.entries.toList()
      ..sort((entryA, entryB) {
        DateTime createdA = groupCreatedMap[entryA.key]!;
        DateTime createdB = groupCreatedMap[entryB.key]!;
        return createdB.compareTo(createdA);
      });

    // Sort each group's documents by version and revision
    sortedGroupEntries.forEach((entry) {
      entry.value.sort((a, b) {
        final int versionA = a.version as int;
        final int versionB = b.version as int;
        if (versionA != versionB) {
          return versionA.compareTo(versionB);
        }
        final int revisionA = a.revision as int;
        final int revisionB = b.revision as int;
        return revisionA.compareTo(revisionB);
      });
    });

    // Return the sorted list of lists
    return sortedGroupEntries.map((entry) => entry.value).toList();
  }


  Future<void> downloadRenovationQuotationPDF({required RenovationQuotation row,required Map<String, bool> categoryMap})async{
      Map<String, Map<String, List<Map>>> renovationData = {};
      Map<String, double> categoryTotal = {};
      Map<String, Map<String, double>> subCategoryTotal = {};
      Map<String, Map<String, Map<String, dynamic>>> subCategoryTotalForOnlyToShow = {};
      Map<String, Map<String, dynamic>> categoryTotalForOnlyToShow = {};

      List<RenovationActivityCategory> copiedActivity = row.renovationActivity!
          .map((category) => RenovationActivityCategory.fromDocument(category.toMap(), category.index ?? 0))
          .toList();

      String? selectedPagamentoCategory = categoryMap.entries.firstWhere((e) => e.value == true).key;

      Map<String, List<String>> pagamentoToActivityMapCopy = Map.from(pagamentoToActivityMap);


      if (selectedPagamentoCategory.isEmpty) return;


      // Only filter if it's not "Completo"
      bool skipFiltering = selectedPagamentoCategory == "Completo";
      List<String>? allowedActivityCategories = skipFiltering
          ? null
          : pagamentoToActivityMapCopy[selectedPagamentoCategory];


      for (var i = 0; i < copiedActivity.length; i++) {
          RenovationActivityCategory tmpRenovationActivityCategory =  copiedActivity[i];
          String category = tmpRenovationActivityCategory.category!;

          if (!skipFiltering && (allowedActivityCategories == null || !allowedActivityCategories.contains(category))) {
            continue; // filter out if not in allowed list
          }

          if (!renovationData.containsKey(category)) {
            renovationData[category] = {};
          }
          if (!categoryTotal.containsKey(category)) {
            categoryTotal[category] = 0;
          }
          if (!categoryTotalForOnlyToShow.containsKey(category)) {
            categoryTotalForOnlyToShow[category] = {
              "catTotal": 0.0,
              "catTotalForPdf": 0.0,
              "isDiscounted": false,
              "catDiscountedAmount": 0.0,
            };
          }

          double catTotal = 0;
          double catTotalForPdf = 0;
          bool isCategoryDiscounted = false;
          final estimateCategories = aef.activitiesForEstimate[category];

          for (var j = 0; j < tmpRenovationActivityCategory.activity!.length; j++) {
            RenovationActivity activity = tmpRenovationActivityCategory.activity![j];
            String subCategory = activity.subCategory!;
            double total = (activity.quantity ?? 0) * (activity.unitPrice ?? 0);


            if(activity.isForceAc ?? false) {
              if(activity.isManualActivity ?? false){
                activity.quantity = 1;
                activity.measurementUnit = 'AC';
              }else{
                if (estimateCategories != null) {
                  final subCategoryItems = estimateCategories[subCategory];
                  if (subCategoryItems != null) {
                    final matchingItem = subCategoryItems.firstWhere(
                          (e){
                        return (e['code']?.toString().trim() ?? '') == (activity.code?.toString().trim() ?? '');
                      },
                      orElse: () => {},
                    );
                    if (matchingItem.isNotEmpty) {
                      activity.quantity = 1;
                      activity.measurementUnit = 'AC';
                    }
                  }
                }
              }

            }


            Map<String, dynamic> rowData = {
              'index': activity.index,
              'title': activity.title,
              'measurementUnit': activity.measurementUnit,
              'quantity': activity.quantity,
              'unitPrice': localCurrencyFormatMain.format(activity.isForceAc ?? false ? total :activity.unitPrice).trim(),
              'description': activity.description,
              'priceLevel': activity.priceLevel,
              'subCategory': activity.subCategory,
              'code': activity.code,
              'comment': activity.comment,
              'isDiscounted': activity.isDiscounted,
              'isManualActivity': activity.isManualActivity,
              'activityDiscountAmount': activity.activityDiscountAmount,
              'total': 0.0,
            };

            if (!renovationData[category]!.containsKey(subCategory)) {
              renovationData[category]![subCategory] = [];
            }
            if (!subCategoryTotal.containsKey(category)) {
              subCategoryTotal[category] = {};
            }
            if (!subCategoryTotal[category]!.containsKey(subCategory)) {
              subCategoryTotal[category]![subCategory] = 0;
            }
            if (!subCategoryTotalForOnlyToShow.containsKey(category)) {
              subCategoryTotalForOnlyToShow[category] = {};
            }
            if (!subCategoryTotalForOnlyToShow[category]!.containsKey(subCategory)) {
              subCategoryTotalForOnlyToShow[category]![subCategory] = {
                "subCategoryTotal": 0.0,
                "subCategoryTotalWithoutDiscount": 0.0,
                "isDiscounted": false,
              };
            }

            bool isSubCategoryDiscounted = false;

            if (!activity.isDiscounted!) {
              subCategoryTotal[category]?[subCategory] = (subCategoryTotal[category]?[subCategory] ?? 0) + total;
              subCategoryTotalForOnlyToShow[category]![subCategory]!["subCategoryTotal"] += total;
              subCategoryTotalForOnlyToShow[category]![subCategory]!["subCategoryTotalWithoutDiscount"] += total;
              rowData['total'] = total;
              catTotal += total;
              catTotalForPdf += total;
            } else {
              double activityDiscountAmount = (double.tryParse( rowData['activityDiscountAmount'].toString()) ?? 0.0);
              if(![0.0,0].contains(activityDiscountAmount)){
                isSubCategoryDiscounted = true;
              }
              double totalWithDiscount = total - activityDiscountAmount;
              rowData['total'] = total;
              catTotal += totalWithDiscount;
              catTotalForPdf += total;
              subCategoryTotal[category]?[subCategory] = (subCategoryTotal[category]?[subCategory] ?? 0) + totalWithDiscount;
              subCategoryTotalForOnlyToShow[category]![subCategory]!["subCategoryTotal"] += totalWithDiscount;
              subCategoryTotalForOnlyToShow[category]![subCategory]!["subCategoryTotalWithoutDiscount"] += total;
              isSubCategoryDiscounted = true;
            }

            // If any entry in this subcategory was discounted, mark the whole subcategory as discounted
            if (isSubCategoryDiscounted) {
              subCategoryTotalForOnlyToShow[category]![subCategory]!["isDiscounted"] = true;
              isCategoryDiscounted = true;
            }

            renovationData[category]![subCategory]!.add(rowData);
          }

          categoryTotal[category] = catTotal;

          double catDiscountedAmount = catTotal;
          if((row.discount?.isNotEmpty ?? false) && row.discount!.contains("%")){
            double wholeQuotationDiscountDouble = double.tryParse(row.discount!.replaceAll("%", "")) ?? 0.0;
            double catValueWithDiscount = catTotal * wholeQuotationDiscountDouble / 100;
            catDiscountedAmount = catTotal - catValueWithDiscount;
          }
          categoryTotalForOnlyToShow[category] = {
            "catTotal": catTotal,
            "catTotalForPdf": catTotalForPdf,
            "isDiscounted": isCategoryDiscounted,
            "catDiscountedAmount": catDiscountedAmount ?? 0.0,
          };
        }

      await pdfDesign(row, renovationData, categoryTotal, subCategoryTotal,categoryTotalForOnlyToShow, subCategoryTotalForOnlyToShow,selectedPagamentoCategory.toString().trim());

  }

  void downloadComputoMatricoQuotationPDF({required RenovationQuotation row,required List<String> selectedLavoriSubCategory,required List<String> selectedFornitureSubCategory})async{
    try{
      if(selectedLavoriSubCategory.isEmpty && selectedFornitureSubCategory.isEmpty) return;
      
      Map<String, Map<String, List<Map>>> renovationData = {};
      Map<String, Map> subCategoryTotal = {};
      if (row.renovationActivity != null) {
        for (var i = 0; i < row.renovationActivity!.length; i++) {
          RenovationActivityCategory tmpRenovationActivityCategory = row.renovationActivity![i];
          String category = tmpRenovationActivityCategory.category!;

          if (!renovationData.containsKey(category)) {
            renovationData[category] = {};
          }

          for (var j = 0; j < tmpRenovationActivityCategory.activity!.length; j++) {
            String subCategory = tmpRenovationActivityCategory.activity![j].subCategory!;

            Map rowData = {
              'index': tmpRenovationActivityCategory.activity![j].index,
              'title': tmpRenovationActivityCategory.activity![j].title,
              'measurementUnit': tmpRenovationActivityCategory.activity![j].measurementUnit,
              'quantity': tmpRenovationActivityCategory.activity![j].quantity,
              'unitPrice': tmpRenovationActivityCategory.activity![j].unitPrice,
              'description': tmpRenovationActivityCategory.activity![j].description,
              'priceLevel': tmpRenovationActivityCategory.activity![j].priceLevel,
              'subCategory': tmpRenovationActivityCategory.activity![j].subCategory,
              'code': tmpRenovationActivityCategory.activity![j].code,
              'comment': tmpRenovationActivityCategory.activity![j].comment,
              'isDiscounted': tmpRenovationActivityCategory.activity![j].isDiscounted,
              'isManualActivity': tmpRenovationActivityCategory.activity![j].isManualActivity,
              'isIncludedInComputoMatric': tmpRenovationActivityCategory.activity![j].isIncludedInComputoMatric,
              'total': 0,
            };

            if (!renovationData[category]!.containsKey(subCategory)) {
              renovationData[category]![subCategory] = [];
            }

            if( !subCategoryTotal.containsKey(category) ){
              subCategoryTotal[category] = {};
            }
            if (!subCategoryTotal[category]!.containsKey(subCategory)) {
              subCategoryTotal[category]![subCategory] = 0;
            }

            if(tmpRenovationActivityCategory.activity![j].isIncludedInComputoMatric ?? true){
              renovationData[category]![subCategory]!.add(rowData);
            }
          }
        }
      }
      await computoMatrixPdfDesign(quotation: row,renovationData: renovationData,subCategoryTotal: subCategoryTotal,selectedLavoriSubCategory: selectedLavoriSubCategory,selectedFornitureSubCategory: selectedFornitureSubCategory);
    }finally{

    }
  }

  void showAzioniDialog({required BuildContext context,required String dialogType,required RenovationQuotation row,required List<RenovationQuotation> group}) async {

    String getDialogTitle(){
      switch (dialogType) {
        case 'edit':
          return "Modifica preventivo";
        case 'new_version':
          return "Nuova Versione";
        case 'new_revision':
          return "Nuova Revisione";
        case 'download_pdf':
          return "Scarica PDF";
        case 'download_contract':
          return "Scarica Contract";
        case 'calculator_pdf':
          return "Scarica CME";
        case 'status_modification':
          return "Modifica Stato";
        case 'delete':
          return "Elimina preventivo";
        case 'comment':
          return "Commenta";
        default:
          return "";
      }
    }

    String getButtonTitle(){
      switch (dialogType) {
        case 'edit':
          return "Modifica";
        case 'new_version':
          return "Crea nuova versione";
        case 'new_revision':
          return "Crea nuova revisione";
        case 'download_contract':
          return "Scarica";
        case 'download_pdf':
          return "Scarica";
        case 'calculator_pdf':
          return "Seleziona";
        case 'status_modification':
          return "Modifica Stato";
        case 'delete':
          return "Elimina";
        case 'comment':
          return "Salva";
        default:
          return "";
      }
    }
    final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
    return showDialog(
        context: context,
        builder: (BuildContext _context) {
          controller.contStatus.clear();
          TextEditingController commentController = TextEditingController();

          List<Map<String, String>> dropdownItems = [];

          if (group.isNotEmpty) {
            for (var element in group) {

              dropdownItems.add({
                'value': element.id ?? "",
                'label': "${element.code ?? ""}_V${element.version ?? ""}_R${element.revision ?? ""}",
              });
            }
          }

          BuildContext parentContext = context;

          bool isActivityEmpty = false;
          bool isLoadingPagamento = false;

          return StatefulBuilder(builder: (context,setStateDialog){
            return Center(
              child: BaseNewarcPopup(
                buttonColor: Theme.of(context).primaryColor.withOpacity(isLoadingPagamento ? 0.5 : 1.0),
                title: getDialogTitle(),
                buttonText: getButtonTitle(),
                onPressed: () async {
                  RenovationQuotation? selectedRenovationQuotation;

                    if(controller.groupRenovationQuotationController.text.isNotEmpty){
                      selectedRenovationQuotation = controller.quotations.firstWhere((val)=> val.id == controller.groupRenovationQuotationController.text);
                    }

                  // Find max version among same revisions
                  // int? selectedRevision = selectedRenovationQuotation?.revision;
                  int? selectedVersion = selectedRenovationQuotation?.version;
                  int maxVersion = group
                      .map((quote) => quote.version ?? 0)
                      .fold(0, (prev, element) => element > prev ? element : prev);

                  int maxRevision = group
                      .where((quote) => quote.version == selectedVersion)
                      .map((quote) => quote.revision ?? 0)
                      .fold(0, (prev, element) => element > prev ? element : prev);

                  if(dialogType.trim() == "edit"){
                      //?------------------------ EDIT ----------------------------
                      widget.updateViewCallback!(
                          'renovation-quotation-single',
                          projectArguments: {
                            'renovationQuotation': selectedRenovationQuotation
                          });
                    }
                    else if(dialogType.trim() == "new_version"){
                      //?------------------------ NEW VERSION ----------------------------
                      log("---------------------- NEW VERSION -----------------------");

                      try{
                        dropdownItems.forEach((val){

                        });

                          selectedRenovationQuotation?.version = (maxVersion + 1);
                          selectedRenovationQuotation?.revision = 0;
                          selectedRenovationQuotation?.created = DateTime.now().millisecondsSinceEpoch;
                          selectedRenovationQuotation?.modificationDate = DateTime.now().millisecondsSinceEpoch;
                          selectedRenovationQuotation?.status = CommonUtils.inAttesa;
                          selectedRenovationQuotation?.comment = "";
                          if(isActivityEmpty){
                          selectedRenovationQuotation?.renovationActivity = [];
                          }

                          //*------add new version quotation
                          await FirebaseFirestore.instance
                              .collection(appConfig.COLLECT_RENOVATION_QUOTATION)
                              .add(selectedRenovationQuotation!.toMap());

                          controller.quotations.clear();
                          controller.expandedGroups.clear();
                          controller.displayQuotations.clear();
                          initialFetchContacts(force: true);


                      }catch(e,s){
                        log("---------- ERROR While Creating New Version ------> ${e.toString()}");
                        log("---------- STACKTRACE While Creating New Version ------> ${s.toString()}");
                      }finally{}

                    }
                    else if(dialogType.trim() == "new_revision"){
                      //?------------------------ NEW REVISION ----------------------------

                      log("---------------------- NEW REVISION -----------------------");
                      try{
                        selectedRenovationQuotation?.revision = (maxRevision! + 1);
                        selectedRenovationQuotation?.created = DateTime.now().millisecondsSinceEpoch;
                        selectedRenovationQuotation?.modificationDate = DateTime.now().millisecondsSinceEpoch;
                        selectedRenovationQuotation?.modificationDate = DateTime.now().millisecondsSinceEpoch;
                        selectedRenovationQuotation?.status = CommonUtils.inAttesa;
                        selectedRenovationQuotation?.comment = "";
                        if(isActivityEmpty){
                          selectedRenovationQuotation?.renovationActivity = [];
                        }

                        //*------add new revision quotation
                        await FirebaseFirestore.instance
                            .collection(appConfig.COLLECT_RENOVATION_QUOTATION)
                            .add(selectedRenovationQuotation!.toMap());

                        controller.quotations.clear();
                        controller.expandedGroups.clear();
                        controller.displayQuotations.clear();
                        initialFetchContacts(force: true);
                      }catch(e,s){
                        log("---------- ERROR While Creating New revision ------> ${e.toString()}");
                        log("---------- STACKTRACE While Creating New revision ------> ${s.toString()}");
                      }finally{}

                    }
                    else if(dialogType.trim() == "download_pdf"){
                      setStateDialog(() {
                        isLoadingPagamento = true;
                      });
                      //?------------------------ DOWNLOAD MAIN QUOTATION PDF ----------------------------
                    await fetchPagamento(renovationQuotation: selectedRenovationQuotation!);

                    List<String>? cat = selectedRenovationQuotation.pagamento
                        ?.map((paga) => paga.categoryName ?? "")
                        .where((e) => e.isNotEmpty)
                        .toList();

                    Map<String, bool> categoryMap = Map.fromEntries(
                      renovationCategoryMap.entries.where((entry) =>
                      entry.key == "Completo" || (cat?.contains(entry.key) ?? false)),
                    );

                      setStateDialog(() {
                        isLoadingPagamento = false;
                      });

                      Future.delayed(Duration(milliseconds: 200),(){
                        showSelectCategoryForPreventivoDialog(context: parentContext, categoryMap: categoryMap, selectedRenovationQuotation: selectedRenovationQuotation!);
                      });
                    }
                    else if(dialogType.trim() == "calculator_pdf"){
                      //?------------------------ DOWNLOAD COMPUTO MATRICO QUOTATION PDF ----------------------------
                    Map<String, bool> lavoriCategoryMap = {};
                    Map<String, bool> fornitureCategoryMap = {};
                    aef.activitiesForEstimate.forEach((mainCategory, categoryMap) {
                      if (mainCategory == "C - Lavori interni") {
                        categoryMap.forEach((subCategory, _) {
                          lavoriCategoryMap[subCategory] = false;
                        });
                      }else if(mainCategory == "M - Forniture") {
                        categoryMap.forEach((subCategory, _) {
                          fornitureCategoryMap[subCategory] = false;
                        });
                      }
                    });

                    Future.delayed(Duration(milliseconds: 200),(){
                      showSelectCategoryForCMEDialog(context: parentContext,lavoriSubCategoryMap: lavoriCategoryMap,fornitureSubCategoryMap: fornitureCategoryMap,selectedRenovationQuotation: selectedRenovationQuotation!);
                    });

                    }
                    else if(dialogType.trim() == "status_modification"){
                      //?------------------------ STATUS MODIFICATION ----------------------------
                      await FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_RENOVATION_QUOTATION)
                          .doc(selectedRenovationQuotation?.id ?? "")
                          .update({'status': controller.contStatus.text,"modificationDate":DateTime.now().millisecondsSinceEpoch});
                      int rowIndex = group.indexWhere((element){
                        log("element id ${element.id}");
                        log("element code ${element.code}");
                        return element.id == selectedRenovationQuotation?.id;
                      });

                      log("rowIndex ${rowIndex}");
                      if (rowIndex != -1) {
                        group[rowIndex].status = controller.contStatus.text;
                      }
                      setState(() {});
                    }
                    else if(dialogType.trim() == "delete"){
                      //?------------------------ DELETE ----------------------------
                      Future.delayed(Duration(milliseconds: 200),(){
                        showDeleteConfirmDialog(context: parentContext, id: selectedRenovationQuotation?.id ?? "");
                      });
                    }
                    else if(dialogType.trim() == "comment"){
                      //?------------------------ COMMENT ----------------------------
                    try{
                      //*------comment quotation
                      RenovationQuotation? selectedRenovationQuotation;

                      if(controller.groupRenovationQuotationController.text.isNotEmpty){
                        selectedRenovationQuotation = controller.quotations.firstWhere((val)=> val.id == controller.groupRenovationQuotationController.text);
                      }
                      await FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_RENOVATION_QUOTATION)
                          .doc(selectedRenovationQuotation?.id)
                          .update({
                        "comment": commentController.text.trim(),
                      });
                      controller.quotations.clear();
                      controller.expandedGroups.clear();
                      controller.displayQuotations.clear();
                      initialFetchContacts(force: true);
                    }catch(e,s){
                      log("---------- ERROR While Commenting quotation ------> ${e.toString()}");
                      log("---------- STACKTRACE While Commenting quotation ------> ${s.toString()}");
                    }
                    }
                  else if(dialogType.trim() == "download_contract"){
                    //?------------------------ CONTRACT PDF ----------------------------
                    try{
                      //*------CONTRACT PDF
                      RenovationQuotation? selectedRenovationQuotation;

                      if(controller.groupRenovationQuotationController.text.isNotEmpty){
                        selectedRenovationQuotation = controller.quotations.firstWhere((val)=> val.id == controller.groupRenovationQuotationController.text);
                      }
                      Future.delayed(Duration(milliseconds: 200),(){
                        showAddContractDialog(renovationQuotation: selectedRenovationQuotation!,context: parentContext);
                      });

                    }catch(e,s){
                      log("---------- ERROR While Downloading Contract PDF ------> ${e.toString()}");
                      log("---------- STACKTRACE While Downloading Contract PDF ------> ${s.toString()}");
                    }
                  }

                },
                column: Container(
                  width: 400,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label:  "Seleziona preventivo" ,
                            textColor: Color(0xff696969),
                            fontSize: 14,
                            fontWeight: '600',
                          ),
                          SizedBox(height: 4),

                          DropdownButtonFormField<String>(
                            key: _formKey,
                            isExpanded: true,
                            value: null,
                            icon: Padding(
                              padding: EdgeInsets.only(right: 8),
                              child: SvgPicture.asset(
                                'assets/icons/arrow_down.svg',
                                width: 12,
                                color: Color(0xff7e7e7e),
                              ),
                            ),
                            style: TextStyle(
                              overflow: TextOverflow.ellipsis,
                              color: Colors.black,
                              fontSize: 12.0,
                              fontWeight: FontWeight.bold,
                              fontStyle: FontStyle.normal,
                            ),
                            borderRadius: BorderRadius.circular(8),
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(8)),
                                borderSide: BorderSide(
                                  color: Color.fromRGBO(227, 227, 227, 1),
                                  width: 1,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(8)),
                                borderSide: BorderSide(
                                  color: Color.fromRGBO(227, 227, 227, 1),
                                  width: 1,
                                ),
                              ),
                              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              fillColor: Colors.white,
                              hintStyle: TextStyle(
                                color: Colors.grey,
                                fontSize: 15.0,
                                fontWeight: FontWeight.w800,
                                fontStyle: FontStyle.normal,
                                letterSpacing: 0,
                              ),
                            ),
                            onChanged: (String? value) {
                              if (value != null) {
                                controller.groupRenovationQuotationController.text = value;
                                RenovationQuotation selectedQuotation = group.firstWhere((element) {
                                  return element.id == value;
                                });
                                commentController.text = selectedQuotation.comment ?? "";
                                controller.updateStatus(selectedQuotation.status ?? "");
                              }
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return "Required!";
                              }
                              return null;
                            },
                            dropdownColor: Colors.white,
                            items: dropdownItems.map<DropdownMenuItem<String>>((item) {
                              return DropdownMenuItem<String>(
                                value: item['value'],
                                child:NarFormLabelWidget(
                                  label: item['label']!,
                                  textColor: Colors.black,
                                  fontSize: 14,
                                  fontWeight: '600',
                                ),
                              );
                            }).toList(),
                          ),

                          SizedBox(height: 20),

                          //------------

                          dialogType == "comment" ?
                          CustomTextFormField(
                              label: 'Inserisci commento',
                              hintText: "",
                              minLines: 3,
                              isExpanded: false,
                              controller: commentController,
                            suffixIcon: SizedBox(
                              height: 15,
                              width: 15,
                              child: IconButton(
                                hoverColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                onPressed: () {
                                  commentController.clear();
                                },
                                icon: Image.asset(
                                  "assets/icons/trash-process.png",
                                  fit: BoxFit.contain,
                                  height: 15,
                                  width: 15,
                                  color: AppColor.redColor,
                                ),
                              ),
                            ),
                          ) : SizedBox.shrink(),

                          dialogType == "new_version" || dialogType == "new_revision" ?
                          Row(
                            children: [
                              Switch(
                                value: isActivityEmpty,
                                activeTrackColor: Theme.of(context).primaryColor,
                                activeColor: AppColor.white,
                                inactiveThumbColor: AppColor.white,
                                inactiveTrackColor: Color(0xffA9A9A9),
                                onChanged: (val) async {
                                  setStateDialog(() {
                                    isActivityEmpty = val;
                                  });
                                },
                              ),
                              SizedBox(width: 4),
                              NarFormLabelWidget(
                                label: "Svuota",
                                textColor: AppColor.black,
                                fontSize: 15,
                                fontWeight: '500',
                              ),
                            ],
                          ) : SizedBox.shrink(),

                          dialogType == "status_modification" ?
                          NarFormLabelWidget(
                            label: "Assegna stato",
                            textColor: Color(0xff696969),
                            fontSize: 14,
                            fontWeight: '600',
                          ) : SizedBox.shrink(),
                          SizedBox(height: dialogType == "status_modification" ? 4 : 0),
                          dialogType == "status_modification"
                              ?
                          Obx((){
                            //*------ don't remove this log
                            log("OBX controller: ${controller.selectedStatus.value}");
                            return Container(
                              width: double.infinity,
                              height: 55,
                              child: NarColorBgDropdown(
                                iconColor: Colors.white,
                                controller: controller.contStatus,
                                downArrowSize: 15,
                                labelTextStyle: TextStyle(
                                  color: AppColor.black,
                                  fontFamily: 'Raleway-700',
                                  fontSize: 14,
                                ),
                                options: status,
                                onChanged: () async {},
                              ),
                            );
                          }) : SizedBox.shrink(),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          });
        });
  }

  void showDeleteConfirmDialog({required BuildContext context,required String id}) async {
    return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context,setStateDialog){
            return Center(
              child: BaseNewarcPopup(
                key: ValueKey("Attenzione"),
                buttonColor: Theme.of(context).primaryColor,
                title: "Attenzione!",
                buttonText: "Elimina",
                onPressed: () async {
                  try{
                    //*------delete quotation
                    await FirebaseFirestore.instance
                        .collection(appConfig.COLLECT_RENOVATION_QUOTATION)
                        .doc(id)
                        .delete();
                    initialFetchContacts(force: true);
                  }catch(e,s){
                    log("---------- ERROR While Deleting quotation ------> ${e.toString()}");
                    log("---------- STACKTRACE While Deleting quotation ------> ${s.toString()}");
                  }
                },
                column: Container(
                  width: 400,
                  padding: EdgeInsets.symmetric(vertical: 25),
                  child: Center(
                    child: NarFormLabelWidget(
                      label:  "Vuoi davvero eliminare questo preventivo?" ,
                      textColor: Color(0xff696969),
                      fontSize: 18,
                      fontWeight: '600',
                    ),
                  ),
                )),
            );
          });
        });
  }

  void showSelectCategoryForCMEDialog({required BuildContext context,required Map<String, bool> lavoriSubCategoryMap,required Map<String, bool> fornitureSubCategoryMap,required RenovationQuotation selectedRenovationQuotation}) async {

    return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context,setStateDialog){
            return Center(
              child: BaseNewarcPopup(
                  key: ValueKey("Scarica CME"),
                  buttonColor: Theme.of(context).primaryColor,
                  title: "Scarica CME",
                  buttonText: "Scarica",
                  onPressed: () async {
                    List<String> selectedLavoriSubCategory = [
                      ...lavoriSubCategoryMap.entries.where((e) => e.value).map((e) => e.key),
                    ];

                    List<String> selectedFornitureSubCategory = [
                      ...fornitureSubCategoryMap.entries.where((e) => e.value).map((e) => e.key),
                    ];

                    downloadComputoMatricoQuotationPDF(row: selectedRenovationQuotation,selectedLavoriSubCategory: selectedLavoriSubCategory,selectedFornitureSubCategory: selectedFornitureSubCategory);

                  },
                  column: SizedBox(
                    width: 800,
                    height: MediaQuery.of(context).size.height * .50,
                    child:  Container(
                      width: 600,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(7),
                        border: Border.all(color: Color(0xFFA4A4A4),width: 1)
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Container(
                              // width: 300,
                              height: MediaQuery.of(context).size.height * .45,
                              padding: EdgeInsets.symmetric(vertical: 25),
                              child: ListView(
                                shrinkWrap: true,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(left: 10),
                                    child: NarFormLabelWidget(
                                      label: "Lavori",
                                      fontSize: 14,
                                      fontWeight: "700",
                                    ),
                                  ),
                                  SizedBox(height: 10,),
                                  NarCheckboxWidget(
                                    label: "",
                                    values: lavoriSubCategoryMap,
                                    columns: 1,
                                    fontSize: 13,
                                    listTileBoxWidthHeight: 20,
                                    childAspectRatio: 10,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Container(width: 1,color: Color(0xFFA4A4A4),height: MediaQuery.of(context).size.height * .50,),
                          Expanded(
                            child: Container(
                              // width: 300,
                              height: MediaQuery.of(context).size.height * .45,
                              padding: EdgeInsets.symmetric(vertical: 25),
                              child: ListView(
                                shrinkWrap: true,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(left: 10),
                                    child: NarFormLabelWidget(
                                      label: "Forniture",
                                      fontSize: 14,
                                      fontWeight: "700",
                                    ),
                                  ),
                                  SizedBox(height: 10,),
                                  NarCheckboxWidget(
                                    label: "",
                                    values: fornitureSubCategoryMap,
                                    columns: 1,
                                    fontSize: 13,
                                    listTileBoxWidthHeight: 20,
                                    childAspectRatio: 10,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  )),
            );
          });
        });
  }

  void showSelectCategoryForPreventivoDialog({required BuildContext context,required Map<String, bool> categoryMap,required RenovationQuotation selectedRenovationQuotation}) async {
    return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context,setStateDialog){
            return Center(
              child: BaseNewarcPopup(
                  key: ValueKey("Scarica Preventivo"),
                  buttonColor: Theme.of(context).primaryColor,
                  title: "Scarica Preventivo",
                  buttonText: "Scarica",
                  onPressed: () async {
                    _showDownloadingDialog(context);
                    try{
                      await downloadRenovationQuotationPDF(row: selectedRenovationQuotation,categoryMap: categoryMap);
                    }catch(e){
                      log("Error while creating renovation pdf ===> ${e.toString()}");
                    }finally{
                      Future.delayed(Duration(milliseconds: 200), () {
                        Navigator.of(context, rootNavigator: true).pop();
                      });
                    }

                  },
                  column: SizedBox(
                    width: 800,
                    height: MediaQuery.of(context).size.height * .50,
                    child:  Container(
                      width: 600,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(7),
                          border: Border.all(color: Color(0xFFA4A4A4),width: 1)
                      ),
                      child: Container(
                        // width: 300,
                        height: MediaQuery.of(context).size.height * .45,
                        padding: EdgeInsets.symmetric(vertical: 25),
                        child: ListView(
                          shrinkWrap: true,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 10),
                              child: NarFormLabelWidget(
                                label: "Seleziona tiplogia preventivo",
                                fontSize: 14,
                                fontWeight: "700",
                              ),
                            ),
                            SizedBox(height: 10,),
                            SizedBox(
                              width: 80,
                              child: NarCheckboxWidget(
                                label: "",
                                values: categoryMap,
                                singleSelected: true,
                                columns: 1,
                                fontSize: 13,
                                listTileBoxWidthHeight: 20,
                                childAspectRatio: 18,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  )),
            );
          });
        });
  }

  Future<void> fetchPagamento({required RenovationQuotation renovationQuotation}) async {
    List<NewarcProjectPagamento>? _pagamento = List<NewarcProjectPagamento>.from(renovationQuotation.pagamento ?? [],);

    try {
      for (NewarcProjectPagamento pagamento in _pagamento) {
        if (!(pagamento.isManualCategory ?? false)) {
          DocumentSnapshot<Map<String, dynamic>> collectionQuery = await FirebaseFirestore.instance
              .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYCATEGORY)
              .doc(pagamento.newarcProjectFixedAssetsPropertyCategoryId)
              .get();

          if (collectionQuery.exists) {
            pagamento.categoryName = collectionQuery.data()?["name"];
          }
        }

        for (var rate in pagamento.rate ?? []) {
          DocumentSnapshot<Map<String, dynamic>> perQuery = await FirebaseFirestore.instance
              .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYPERCENTAGE)
              .doc(rate.newarcProjectFixedAssetsPercentageId)
              .get();

          if (perQuery.exists) {
            rate.percentage = perQuery.data()?["percentage"];
          }
        }
      }

      renovationQuotation.pagamento = _pagamento;

      setState(() {});
    } catch (e, s) {
      print('Error while fetching Pagamento: $e');
      print(s);
    }
  }

  void _showDownloadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black54,
      builder: (_) {
        return Scaffold(
          backgroundColor: Colors.transparent,
          body: Center(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              decoration: BoxDecoration(
                //color: Colors.black87,
                borderRadius: BorderRadius.circular(30),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: AppColor.white,),
                  SizedBox(height: 10,),
                  NarFormLabelWidget(
                    textAlign: TextAlign.center,
                    label: "Creando PDF...",
                    fontSize: 18,
                    fontWeight: '700',
                    textColor: AppColor.white,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void showAddContractDialog({required RenovationQuotation renovationQuotation,required BuildContext context}) async {
    RenovationContract? _renovationContract = renovationQuotation.renovationContract;
    TextEditingController firstNameCtr = TextEditingController(text: _renovationContract?.firstName ?? "");
    TextEditingController lastNameCtr = TextEditingController(text: _renovationContract?.lastName ?? "");
    TextEditingController taxIdCodeCtr = TextEditingController(text: _renovationContract?.taxIdCode ?? "");
    TextEditingController urbanSectionCtr = TextEditingController(text: _renovationContract?.urbanSection ?? "");
    TextEditingController paperCtr = TextEditingController(text: _renovationContract?.paper ?? "");
    TextEditingController particleCtr = TextEditingController(text: _renovationContract?.particle ?? "");
    TextEditingController subalternCtr = TextEditingController(text: _renovationContract?.subaltern ?? "");
    TextEditingController requiredWorkingDaysCtr = TextEditingController(text: _renovationContract?.requiredWorkingDays != null ? _renovationContract?.requiredWorkingDays.toString() : "");
    TextEditingController acceptedRemovalPercentageCtr = TextEditingController(text: _renovationContract?.acceptedRemovalPercentage != null ? _renovationContract?.acceptedRemovalPercentage.toString() : "");
    TextEditingController safetyChargesPriceCtr = TextEditingController(text: _renovationContract?.safetyChargesPrice != null ? localCurrencyFormatMain.format(_renovationContract?.safetyChargesPrice).toString() : "");
    TextEditingController constructionManagerFirstNameCtr = TextEditingController(text: _renovationContract?.constructionManagerInfo?.name ?? "");
    TextEditingController constructionManagerLastNameCtr = TextEditingController(text: _renovationContract?.constructionManagerInfo?.surname ?? "");
    TextEditingController constructionManagerEmailCtr = TextEditingController(text: _renovationContract?.constructionManagerInfo?.email ?? "");
    TextEditingController constructionManagerPhoneCtr = TextEditingController(text: _renovationContract?.constructionManagerInfo?.phone ?? "");
    TextEditingController constructionManagerVATNumberCtr = TextEditingController(text: _renovationContract?.constructionManagerInfo?.VATNumber ?? "");
    TextEditingController constructionManagerRegisterCtr = TextEditingController(text: _renovationContract?.constructionManagerInfo?.register ?? "");
    TextEditingController constructionManagerRegistrationNumberCtr = TextEditingController(text: _renovationContract?.constructionManagerInfo?.registrationNumber ?? "");
    TextEditingController constructionManagerCityRegisterCtr = TextEditingController(text: _renovationContract?.constructionManagerInfo?.cityRegister ?? "");

    TextEditingController dateOfBirthCtr = TextEditingController(text: _renovationContract?.dateOfBirth != null ? getFormattedDate(_renovationContract?.dateOfBirth) : "");
    TextEditingController startWorkDateCtr = TextEditingController(text: _renovationContract?.startWorkDate != null ? getFormattedDate(_renovationContract?.startWorkDate) : "");
    TextEditingController endWorkDateCtr = TextEditingController(text: _renovationContract?.endWorkDate != null ? getFormattedDate(_renovationContract?.endWorkDate) : "");
    int? dateOfBirth = _renovationContract?.dateOfBirth;
    int? startWorkDate = _renovationContract?.startWorkDate;
    int? endWorkDate = _renovationContract?.endWorkDate;
    BuildContext parentContext = context;
    return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context,_setState){
            return Center(
              child: BaseNewarcPopup(
                  key: ValueKey("Compila il Contratto d’Appalto"),
                  buttonColor: Theme.of(context).primaryColor,
                  title: "Compila il Contratto d’Appalto!",
                  buttonText: "Crea contratto",
                  onPressed: () async {
                    _showDownloadingDialog(parentContext);
                    try{
                      _renovationContract?.firstName = firstNameCtr.text.trim();
                      _renovationContract?.lastName = lastNameCtr.text.trim();
                      _renovationContract?.taxIdCode = taxIdCodeCtr.text.trim();
                      _renovationContract?.dateOfBirth = dateOfBirth;
                      _renovationContract?.startWorkDate = startWorkDate;
                      _renovationContract?.endWorkDate = endWorkDate;
                      _renovationContract?.urbanSection = urbanSectionCtr.text.trim();
                      _renovationContract?.paper = paperCtr.text.trim();
                      _renovationContract?.particle = particleCtr.text.trim();
                      _renovationContract?.subaltern = subalternCtr.text.trim();
                      _renovationContract?.acceptedRemovalPercentage = int.tryParse(acceptedRemovalPercentageCtr.text.trim()) ?? 0;
                      _renovationContract?.requiredWorkingDays = int.tryParse(requiredWorkingDaysCtr.text.trim()) ?? 0;
                      _renovationContract?.safetyChargesPrice = double.tryParse(safetyChargesPriceCtr.text.trim().replaceAll(".", ",").replaceAll(",", ".")) ?? 0;
                      _renovationContract?.constructionManagerInfo?.name = constructionManagerFirstNameCtr.text.trim();
                      _renovationContract?.constructionManagerInfo?.surname = constructionManagerLastNameCtr.text.trim();
                      _renovationContract?.constructionManagerInfo?.phone = constructionManagerPhoneCtr.text.trim();
                      _renovationContract?.constructionManagerInfo?.email = constructionManagerEmailCtr.text.trim();
                      _renovationContract?.constructionManagerInfo?.VATNumber = constructionManagerVATNumberCtr.text.trim();
                      _renovationContract?.constructionManagerInfo?.register = constructionManagerRegisterCtr.text.trim();
                      _renovationContract?.constructionManagerInfo?.cityRegister = constructionManagerCityRegisterCtr.text.trim();
                      _renovationContract?.constructionManagerInfo?.registrationNumber = constructionManagerRegistrationNumberCtr.text.trim();
                      renovationQuotation.renovationContract = _renovationContract;
                      setState(() {});
                      //*------Create contract
                      await FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_RENOVATION_QUOTATION)
                          .doc(renovationQuotation.id)
                          .update({"renovationContract" : _renovationContract!.toMap()});

                      //---- Fetch Payment category and %
                      for (NewarcProjectPagamento pagamento in renovationQuotation.pagamento!) {
                        if (!(pagamento.isManualCategory ?? false)) {
                          DocumentSnapshot<Map<String, dynamic>> collectionQuery = await FirebaseFirestore.instance
                              .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYCATEGORY)
                              .doc(pagamento.newarcProjectFixedAssetsPropertyCategoryId)
                              .get();

                          if (collectionQuery.exists) {
                            pagamento.categoryName = collectionQuery.data()?["name"];
                          }
                        }

                        for (var rate in pagamento.rate ?? []) {
                          DocumentSnapshot<Map<String, dynamic>> perQuery = await FirebaseFirestore.instance
                              .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYPERCENTAGE)
                              .doc(rate.newarcProjectFixedAssetsPercentageId)
                              .get();

                          if (perQuery.exists) {
                            rate.percentage = perQuery.data()?["percentage"];
                          }
                        }
                      }

                      downloadContractPDF(contract: _renovationContract,quotation: renovationQuotation);
                    }catch(e,s){
                      log("---------- ERROR While Creating contract ------> ${e.toString()}");
                      log("---------- STACKTRACE While Creating contract ------> ${s.toString()}");
                    }finally{
                      Future.delayed(Duration(milliseconds: 200), () {
                        Navigator.of(context, rootNavigator: true).pop();
                      });
                    }
                  },
                  column: Container(
                    width: 750,
                    height: MediaQuery.of(context).size.height * 0.85,
                    child: ListView(
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            NarFormLabelWidget(
                              label:  "Dati cliente" ,
                              fontSize: 17,
                              fontWeight: '600',
                            ),
                            SizedBox(height: 15,),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CustomTextFormField(
                                  isExpanded: true,
                                  label: "Nome",
                                  controller: firstNameCtr,
                                ),
                                SizedBox(width: 17,),
                                CustomTextFormField(
                                  isExpanded: true,
                                  label: "Cognome",
                                  controller: lastNameCtr,
                                ),
                              ],
                            ),
                            SizedBox(height: 10,),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CustomTextFormField(
                                  isExpanded: true,
                                  isReadOnlyBoarder: true,
                                  readOnly: true,
                                  fillColor: Colors.white,
                                  disabledColor: Colors.white,
                                  onTap: () async {
                                    DateTime? pickedDate = await showDatePicker(
                                      context: context,
                                      initialDate: DateTime.now(),
                                      firstDate: DateTime(1950),
                                      lastDate: DateTime(2300),
                                    );
                                    if (pickedDate != null) {
                                      setState(() {
                                        dateOfBirth = pickedDate.millisecondsSinceEpoch;
                                      });
                                      String formattedDate = DateFormat('dd/MM/yyyy').format(pickedDate);
                                      dateOfBirthCtr.text = formattedDate;
                                    } else {
                                      log("No Date Selected");
                                    }
                                  },
                                  suffixIcon: Container(
                                    width: 17,
                                    height: 17,
                                    padding: EdgeInsets.all(5),
                                    margin: EdgeInsets.only(right: 7),
                                    child: SvgPicture.asset("assets/icons/calendar.svg",height: 17,width: 17,),
                                  ),
                                  label: "Data di nascita",
                                  controller: dateOfBirthCtr,
                                ),
                                SizedBox(width: 17,),
                                Expanded(
                                     child: AddressSearchBar(
                                      label: 'Indirizzo di residenza',
                                    onPlaceSelected: (selectedPlace) async {
                                      if (BaseAddressInfo.fromMap(selectedPlace["place"]).isValidAddress()){
                                        _setState(() {
                                          _renovationContract?.baseAddressInfo = BaseAddressInfo.fromMap(selectedPlace["place"]);
                                        });
                                      } else{
                                        setState(() {
                                          _renovationContract?.baseAddressInfo = null;
                                        });
                                      }
                                    },
                                    initialAddress: _renovationContract!.baseAddressInfo!.fullAddress,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 10,),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                CustomTextFormField(
                                  isExpanded: true,
                                  label: "Codice Fiscale",
                                  controller: taxIdCodeCtr,
                                ),
                                SizedBox(width: 17,),
                                Expanded(child: SizedBox()),
                              ],
                            ),
                            SizedBox(height: 30,),
                            NarFormLabelWidget(
                              label:  "Dati catastali immobile" ,
                              fontSize: 17,
                              fontWeight: '600',
                            ),
                            SizedBox(height: 15,),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CustomTextFormField(
                                  isExpanded: true,
                                  label: "Sezione Urbana",
                                  controller: urbanSectionCtr,
                                ),
                                SizedBox(width: 17,),
                                CustomTextFormField(
                                  isExpanded: true,
                                  label: "Foglio",
                                  controller: paperCtr,
                                ),
                              ],
                            ),
                            SizedBox(height: 15,),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CustomTextFormField(
                                  isExpanded: true,
                                  label: "Particella",
                                  controller: particleCtr,
                                ),
                                SizedBox(width: 17,),
                                CustomTextFormField(
                                  isExpanded: true,
                                  label: "Subalterno",
                                  controller: subalternCtr,
                                ),
                              ],
                            ),
                            SizedBox(height: 30,),
                            NarFormLabelWidget(
                              label:  "Ristrutturazione" ,
                              fontSize: 17,
                              fontWeight: '600',
                            ),
                            SizedBox(height: 15,),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CustomTextFormField(
                                  isExpanded: true,
                                  isReadOnlyBoarder: true,
                                  readOnly: true,
                                  fillColor: Colors.white,
                                  disabledColor: Colors.white,
                                  onTap: () async {
                                    DateTime? pickedDate = await showDatePicker(
                                      context: context,
                                      initialDate: DateTime.now(),
                                      firstDate: DateTime(1950),
                                      lastDate: DateTime(2300),
                                    );
                                    if (pickedDate != null) {
                                      setState(() {
                                        startWorkDate = pickedDate.millisecondsSinceEpoch;
                                      });
                                      String formattedDate = DateFormat('dd/MM/yyyy').format(pickedDate);
                                      startWorkDateCtr.text = formattedDate;
                                    } else {
                                      log("No Date Selected");
                                    }
                                  },
                                  suffixIcon: Container(
                                    width: 17,
                                    height: 17,
                                    padding: EdgeInsets.all(5),
                                    margin: EdgeInsets.only(right: 7),
                                    child: SvgPicture.asset("assets/icons/calendar.svg",width: 17,height: 17,),
                                  ),
                                  label: "Inizio dei lavori",
                                  controller: startWorkDateCtr,
                                ),
                                SizedBox(width: 17,),
                                CustomTextFormField(
                                  isExpanded: true,
                                  isNumber: true,
                                  label: "Giorni lavorativi necessari",
                                  controller: requiredWorkingDaysCtr,
                                ),
                              ],
                            ),
                            SizedBox(height: 15,),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CustomTextFormField(
                                  isExpanded: true,
                                  isReadOnlyBoarder: true,
                                  readOnly: true,
                                  fillColor: Colors.white,
                                  disabledColor: Colors.white,
                                  onTap: () async {
                                    DateTime? pickedDate = await showDatePicker(
                                      context: context,
                                      initialDate: DateTime.now(),
                                      firstDate: DateTime(1950),
                                      lastDate: DateTime(2300),
                                    );
                                    if (pickedDate != null) {
                                      setState(() {
                                        endWorkDate = pickedDate.millisecondsSinceEpoch;
                                      });
                                      String formattedDate = DateFormat('dd/MM/yyyy').format(pickedDate);
                                      endWorkDateCtr.text = formattedDate;
                                    } else {
                                      log("No Date Selected");
                                    }
                                  },
                                  suffixIcon: Container(
                                    width: 17,
                                    height: 17,
                                    padding: EdgeInsets.all(5),
                                    margin: EdgeInsets.only(right: 7),
                                    child: SvgPicture.asset("assets/icons/calendar.svg",width: 17,height: 17,),
                                  ),
                                  label: "Fine dei lavori",
                                  controller: endWorkDateCtr,
                                ),
                                SizedBox(width: 17,),
                                CustomTextFormField(
                                  isExpanded: true,
                                  isMoney: true,
                                  isShowPrefillMoneyIcon: false,
                                  label: "Prezzo oneri sicurezza",
                                  controller: safetyChargesPriceCtr,
                                  suffixIcon: Container(
                                    width: 20,
                                    height: 20,
                                    alignment: Alignment.center,
                                    padding: EdgeInsets.all(2),
                                    child: NarFormLabelWidget(
                                      label:  "€" ,
                                      fontSize: 14,
                                      fontWeight: '500',
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 15,),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CustomTextFormField(
                                  isExpanded: true,
                                  isNumber: true,
                                  label: "Percentuale rimozioni accettate",
                                  controller: acceptedRemovalPercentageCtr,
                                  suffixIcon: Container(
                                    width: 20,
                                    height: 20,
                                    alignment: Alignment.center,
                                    padding: EdgeInsets.all(2),
                                    child: NarFormLabelWidget(
                                      label:  "%" ,
                                      fontSize: 14,
                                      fontWeight: '500',
                                    ),
                                  ),
                                ),
                                SizedBox(width: 17,),
                                Expanded(child: SizedBox()),
                              ],
                            ),
                            SizedBox(height: 30,),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                NarFormLabelWidget(
                                  label:  "Direttore Lavori Esterno" ,
                                  fontSize: 17,
                                  fontWeight: '600',
                                ),
                                Switch(
                                  value: _renovationContract.isExternalWorkManager ?? false,
                                  activeColor: AppColor.white,
                                  activeTrackColor: Theme.of(context).primaryColor,
                                  onChanged: (bool value) async {
                                    _setState(() {
                                      _renovationContract.isExternalWorkManager = !(_renovationContract.isExternalWorkManager ?? false);
                                    });
                                  },
                                ),
                              ],
                            ),
                            _renovationContract.isExternalWorkManager ?? false ?
                            Column(
                              children: [
                                SizedBox(height: 15,),
                                Row(
                                  children: [
                                    CustomTextFormField(
                                      isExpanded: true,
                                      label: "Nome direttore lavori",
                                      controller: constructionManagerFirstNameCtr,
                                    ),
                                    SizedBox(width: 17,),
                                    CustomTextFormField(
                                      isExpanded: true,
                                      label: "Cognome direttore lavori",
                                      controller: constructionManagerLastNameCtr,
                                    ),
                                  ],
                                ),
                                SizedBox(height: 10,),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: AddressSearchBar(
                                        label: 'Indirizzo di residenza',
                                        onPlaceSelected: (selectedPlace) async {
                                          if (BaseAddressInfo.fromMap(selectedPlace["place"]).isValidAddress()){
                                            _setState(() {
                                              _renovationContract.constructionManagerInfo?.addressInfo = BaseAddressInfo.fromMap(selectedPlace["place"]);
                                            });
                                          } else{
                                            setState(() {
                                              _renovationContract.constructionManagerInfo?.addressInfo = null;
                                            });
                                          }
                                        },
                                        initialAddress: _renovationContract.constructionManagerInfo?.addressInfo!.fullAddress,
                                      ),
                                    ),
                                    SizedBox(width: 17,),
                                    CustomTextFormField(
                                      isExpanded: true,
                                      isNumber: true,
                                      label: "Telefono",
                                      controller: constructionManagerPhoneCtr,
                                    ),
                                  ],
                                ),
                                SizedBox(height: 10,),
                                Row(
                                  children: [
                                    CustomTextFormField(
                                      label: "Email/Pec",
                                      controller: constructionManagerEmailCtr,
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return null;
                                        }
                                        final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                                        if (!emailRegex.hasMatch(value)) {
                                          return 'Inserisci un indirizzo email valido';
                                        }
                                        return null;
                                      },
                                    ),
                                    SizedBox(width: 17,),
                                    CustomTextFormField(
                                      isExpanded: true,
                                      label: "Partita Iva",
                                      controller: constructionManagerVATNumberCtr,
                                    ),
                                  ],
                                ),
                                SizedBox(height: 10,),
                                Row(
                                  children: [
                                    CustomTextFormField(
                                      label: "Albo",
                                      controller: constructionManagerRegisterCtr,
                                      isExpanded: true,
                                    ),
                                    SizedBox(width: 17,),
                                    CustomTextFormField(
                                      isExpanded: true,
                                      label: "Città Albo",
                                      controller: constructionManagerCityRegisterCtr,
                                    ),
                                  ],
                                ),
                                SizedBox(height: 10,),
                                Row(
                                  children: [
                                    CustomTextFormField(
                                      label: "Numero iscrizione",
                                      controller: constructionManagerRegistrationNumberCtr,
                                      isExpanded: true,
                                    ),
                                    SizedBox(width: 17,),
                                    Expanded(child: SizedBox()),

                                  ],
                                ),
                              ],
                            ) : SizedBox()


                          ],
                        )
                      ],
                    ),
                  )),
            );
          });
        });
  }
}
