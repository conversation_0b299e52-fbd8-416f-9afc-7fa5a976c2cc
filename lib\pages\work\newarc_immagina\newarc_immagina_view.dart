
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/pages/work/newarc_immagina/newarc_immagina_controller.dart';
import 'package:newarc_platform/pages/work/newarc_immagina/newarc_immagina_data_source.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/common_utils.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

class NewarcImmagina extends StatefulWidget {
  NewarcImmagina({super.key, required this.newarcUser, this.updateViewCallback, this.projectArguments = const {}});
  final NewarcUser newarcUser;
  final Function? updateViewCallback;
  final Map? projectArguments;

  @override
  State<NewarcImmagina> createState() => _NewarcImmaginaState();
}

class _NewarcImmaginaState extends State<NewarcImmagina> {
  final controller = Get.put<NewarcImmaginaController>(NewarcImmaginaController());

  @override
  void initState() {
    controller.isRequest = widget.projectArguments?["isFromRequest"] ?? false;
    controller.clearFilter();
    fetchAllAgencies();
    initialFetch(force: true);
    super.initState();
  }

  void reloadAfterPop({bool force = false}) {
    controller.projects = [];
    controller.documentList = [];
    controller.filters = [];
    controller.cacheFireStore = [];
    controller.totalRecords = 0;
    controller.currentlyShowing = '';
    controller.recordsPerPage = 20;
    controller.pageCounter = 1;
    controller.totalPages = 0;
    controller.disablePreviousButton = true;
    controller.disableNextButton = false;
    controller.statusFilterController.clear();
    controller.agencyFilterController.clear();
    controller.realizzazioneFilterController.clear();
    controller.venditaFilterController.clear();

    controller.cacheFireStore.clear();
    initialFetch(force: force);
  }

  Widget dataTablePagination() {
    return Visibility(
      visible: controller.currentlyShowing.isNotEmpty,
      child: Padding(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: IconTheme.merge(
            data: const IconThemeData(opacity: 0.54),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "${controller.currentlyShowing} di  ${controller.totalRecords.toString()}",
                  style: TextStyle(
                    fontFamily: '',
                    fontSize: 12.0,
                    color: Colors.black.withOpacity(0.54),
                  ),
                ),
                SizedBox(width: 32.0),
                IconButton(
                  icon: const Icon(Icons.chevron_left),
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    if (controller.disablePreviousButton == true) return;
                    if (controller.loadingProperties == true) return;
                    fetchPrevProperties();
                  },
                ),
                SizedBox(width: 24.0),
                IconButton(
                  padding: EdgeInsets.zero,
                  icon: const Icon(Icons.chevron_right),
                  onPressed: () {
                    if (controller.disableNextButton == true) return;
                    if (controller.loadingProperties == true) return;

                    fetchNextProperties();
                  },
                ),
                SizedBox(width: 14.0),
              ],
            ),
          )),
    );
  }

  Future<void> initialFetch({bool force = false,bool reloadAll = false}) async {

    if (controller.projects.isNotEmpty && !force && !reloadAll) return;

    controller.pageCounter = 1;

    setState(() {
      controller.loadingProperties = true;
      controller.projects = [];
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshotCounter;
      Query<Map<String, dynamic>> collectionSnapshotCounterQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).where("isArchived", isEqualTo: false).where("isWorkArchived", isEqualTo: false);

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).where("isArchived", isEqualTo: false).where("isWorkArchived", isEqualTo: false);
      /// Filter on requestStatus depending on isRequest
      if (controller.isRequest) {
        collectionSnapshotQuery = collectionSnapshotQuery.where("requestStatus",
          whereNotIn: [CommonUtils.daCompletare, CommonUtils.confermata, CommonUtils.inLavorazione, CommonUtils.completato],
        );
        collectionSnapshotCounterQuery = collectionSnapshotCounterQuery.where("requestStatus",
          whereNotIn: [CommonUtils.daCompletare, CommonUtils.confermata, CommonUtils.inLavorazione, CommonUtils.completato],
        );
      } else {
        collectionSnapshotQuery = collectionSnapshotQuery.where("requestStatus",
          whereIn: [CommonUtils.confermata, CommonUtils.inLavorazione, CommonUtils.completato],
        );
        collectionSnapshotCounterQuery = collectionSnapshotCounterQuery.where("requestStatus",
          whereIn: [CommonUtils.confermata, CommonUtils.inLavorazione, CommonUtils.completato],
        );
      }
      /// If user is renderist, show only associate projects
      /*if (widget.newarcUser.role == 'renderist'){
        collectionSnapshotQuery = collectionSnapshotQuery.where('renderistTeamIdList', arrayContains: widget.newarcUser.id);
        collectionSnapshotCounterQuery = collectionSnapshotCounterQuery.where('renderistTeamIdList', arrayContains: widget.newarcUser.id);
      }*/

      if (widget.newarcUser.isFilterPerAccountEnabled! ){
        collectionSnapshotQuery = collectionSnapshotQuery.where('renderistTeamIdList', arrayContains: widget.newarcUser.id);
        collectionSnapshotCounterQuery = collectionSnapshotCounterQuery.where('renderistTeamIdList', arrayContains: widget.newarcUser.id);
      }

      /// Filter on filters
      if (controller.filters.length > 0) {
        for (var i = 0; i < controller.filters.length; i++) {
          if (controller.filters[i]['search'] == 'equal') {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
            collectionSnapshotCounterQuery = collectionSnapshotCounterQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }
      }
      if(reloadAll){
        collectionSnapshot = await collectionSnapshotQuery.get();
      }else{
        // if (controller.isRequest){
        //   collectionSnapshot = await collectionSnapshotQuery.orderBy('insertionTimestamp', descending: true).limit(controller.recordsPerPage,).get();
        // } else {
          collectionSnapshot = await collectionSnapshotQuery.orderBy('statusChangedDate', descending: true).limit(controller.recordsPerPage,).get();
        // }
      }

      // if (controller.isRequest){
      //   collectionSnapshotCounter = await collectionSnapshotCounterQuery.orderBy('insertionTimestamp', descending: true).get();
      // } else {
        collectionSnapshotCounter = await collectionSnapshotCounterQuery.orderBy('statusChangedDate', descending: true).get();
      // }

      controller.totalRecords = collectionSnapshotCounter.docs.length;

      controller.totalPages = (controller.totalRecords / controller.recordsPerPage).ceil();

      if (controller.totalRecords > controller.recordsPerPage) {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      } else {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      }
      controller.documentList = collectionSnapshot.docs;

      await generateDataRows(collectionSnapshot);
      if (mounted) {
        setState(() {
          controller.loadingProperties = false;
        });
      }
    } catch (e,s) {
      print({'immg', e, s});
      if (mounted) {
        setState(() {
          controller.loadingProperties = false;
          print(e);
        });
      }
    }
  }

  fetchNextProperties() async {
    setState(() {
      controller.loadingProperties = true;
    });

    controller.pageCounter++;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).where("isArchived", isEqualTo: false).where("isWorkArchived", isEqualTo: false);
      int indexOfSnapshot = isRecordExists(controller.pageCounter);

      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFireStore[indexOfSnapshot]['snapshot'];
      } else {
        if (controller.isRequest) {
          collectionSnapshotQuery = collectionSnapshotQuery.where("requestStatus",
            whereNotIn: [CommonUtils.daCompletare, CommonUtils.confermata, CommonUtils.inLavorazione, CommonUtils.completato],
          );
        } else {
          collectionSnapshotQuery = collectionSnapshotQuery.where("requestStatus",
            whereIn: [CommonUtils.confermata, CommonUtils.inLavorazione, CommonUtils.completato],
          );
        }

        /*if (widget.newarcUser.role == 'renderist'){
          collectionSnapshotQuery = collectionSnapshotQuery.where('renderistTeamIdList', arrayContains: widget.newarcUser.id);
        }*/

        if (widget.newarcUser.isFilterPerAccountEnabled! ){
          collectionSnapshotQuery = collectionSnapshotQuery.where('renderistTeamIdList', arrayContains: widget.newarcUser.id);
        }

        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }

        // if (controller.isRequest){
        //   collectionSnapshotQuery =  collectionSnapshotQuery.orderBy('insertionTimestamp', descending: true);
        // } else {
          collectionSnapshotQuery =  collectionSnapshotQuery.orderBy('statusChangedDate', descending: true);
        // }

        collectionSnapshot = await collectionSnapshotQuery
            .startAfterDocument(controller.documentList[controller.documentList.length - 1])
            .limit(controller.recordsPerPage)
            .get();
      }

      controller.documentList = collectionSnapshot.docs;
      generateDataRows(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingProperties = false;
      });
    }
  }

  fetchPrevProperties() async {
    setState(() {
      controller.loadingProperties = true;
    });

    controller.pageCounter--;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).where("isArchived", isEqualTo: false).where("isWorkArchived", isEqualTo: false);

      int indexOfSnapshot = isRecordExists(controller.pageCounter);
      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFireStore[indexOfSnapshot]['snapshot'];
      } else {
        if (controller.isRequest) {
          collectionSnapshotQuery = collectionSnapshotQuery.where("requestStatus",
            whereNotIn: [CommonUtils.daCompletare, CommonUtils.confermata, CommonUtils.inLavorazione, CommonUtils.completato],
          );
        } else {
          collectionSnapshotQuery = collectionSnapshotQuery.where("requestStatus",
            whereIn: [CommonUtils.confermata, CommonUtils.inLavorazione, CommonUtils.completato],
          );
        }

        /*if (widget.newarcUser.role == 'renderist'){
          collectionSnapshotQuery = collectionSnapshotQuery.where('renderistTeamIdList', arrayContains: widget.newarcUser.id);
        }*/
        if (widget.newarcUser.isFilterPerAccountEnabled! ){
          collectionSnapshotQuery = collectionSnapshotQuery.where('renderistTeamIdList', arrayContains: widget.newarcUser.id);
        }

        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }

        // if (controller.isRequest){
        //   collectionSnapshotQuery =  collectionSnapshotQuery.orderBy('insertionTimestamp', descending: true);
        // } else {
          collectionSnapshotQuery =  collectionSnapshotQuery.orderBy('statusChangedDate', descending: true);
        // }

        collectionSnapshot = await collectionSnapshotQuery
            .endBeforeDocument(controller.documentList[controller.documentList.length - 1])
            .limit(controller.recordsPerPage)
            .get();
      }

      controller.documentList = collectionSnapshot.docs;

      generateDataRows(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingProperties = false;
      });
    }
  }

  int isRecordExists(int pageCounter) {
    String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
    return controller.cacheFireStore.indexWhere(
      (record) => record['key'] == pageCounter && record['filtersKey'] == filtersKey,
    );
  }

  generateDataRows(QuerySnapshot<Map<String, dynamic>> collectionSnapshot) async{
    if (isRecordExists(controller.pageCounter) < 0) {
      String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
      controller.cacheFireStore.add({
        'key': controller.pageCounter,
        'snapshot': collectionSnapshot,
        'filtersKey': filtersKey,
      });
    }

    if (controller.pageCounter == 1) {
      controller.disablePreviousButton = true;
    } else {
      controller.disablePreviousButton = false;
    }

    if (controller.pageCounter == controller.totalPages) {
      controller.disableNextButton = true;
    } else {
      controller.disableNextButton = false;
    }

    List<ImmaginaProject> _projects = [];
    for (var element in collectionSnapshot.docs) {
      ImmaginaProject _tmp = ImmaginaProject.fromDocument(element.data(), element.id);
      _projects.add(_tmp);
    }

    int lastRecordNumber = controller.pageCounter * controller.recordsPerPage;
    if (_projects.length == controller.recordsPerPage) {
      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + lastRecordNumber.toString();
    } else if (_projects.length > 0 && _projects.length < controller.recordsPerPage) {
      int prevLastRecordNumber = (controller.pageCounter - 1) * controller.recordsPerPage;

      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + (prevLastRecordNumber + _projects.length).toString();
    }
    if (mounted) {
      setState(() {
        controller.projects = _projects;
        controller.loadingProperties = false;
      });
    }
  }

  Future<void> fetchAllAgencies() async {
    List<Agency> agencyList = [];

    try {
      QuerySnapshot<Map<String, dynamic>> snapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_AGENCIES)
          .get();

      for (var doc in snapshot.docs) {
        if (doc.exists) {
          Agency agency = Agency.fromDocument(doc.data(), doc.id);
          agencyList.add(agency);
        }
      }
    } catch (e) {
      print('Error fetching agencies: $e');
    }

    setState(() {
      controller.agencyList = agencyList;
    });
  }

  _initialFetch({bool force = false}) {
    initialFetch(force: force);
  }

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }
    return LayoutBuilder(
      builder: (context, constraints) {
        return  Column(
          children: [
            Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                _headerTitle(),
              ],
            ),
            _filter(),
            SizedBox(height: 10),
            Container(
              height: constraints.maxHeight / 1.2,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: AppColor.white,
                border: Border.all(width: 1.5, color: AppColor.borderColor),
              ),
              child: Column(
                children: [
                  Expanded(
                    child: Stack(
                      children: [
                        Opacity(
                          opacity: 1,
                          child: controller.isRequest
                              ? NewarcDataTable(
                                  rowsPerPage: 20,
                                  isHasDecoration: false,
                                  hidePaginator: true,
                                  onPageChanged: (val) {
                                    print("page : $val");
                                  },
                                  source: RequestSource(
                                    isRequest: controller.isRequest,
                                    projects: controller.projects,
                                    onAgencyNameTap: (val) {
                                      _showAgencyDetailDialog(email: val['email'], name: val['name'], phone: val['phone']);
                                    },
                                    onAddressTap: (project) {
                                      bool isProf = project.isForProfessionals;
                                      widget.projectArguments!.clear();
                                      widget.projectArguments!.addAll({
                                        'projectFirebaseId': project.id,
                                        'updateViewCallback': widget.updateViewCallback,
                                        'initialFetchProperties': _initialFetch(
                                          force: true,
                                        ),
                                        'isFromRequest': true,
                                        'isForProfessionals': isProf,
                                      });

                                      widget.updateViewCallback!('immagina-request-review', projectArguments: widget.projectArguments);
                                    },
                                  ),
                                  columns: [
                                    DataColumn2(label: Text("Codice"),size: ColumnSize.S),
                                    DataColumn2(label: Text("Indirizzo"),size: ColumnSize.L),
                                    DataColumn2(label: Text("Agenzia"),size: ColumnSize.S),
                                    DataColumn2(label: Text("Richiesta"),size: ColumnSize.S),
                                    DataColumn2(label: Text("Data richiesta"),size: ColumnSize.S),
                                    DataColumn2(label: Text("Stato"),size: ColumnSize.S),
                                  ],
                                )
                              : NewarcDataTable(
                                  rowsPerPage: 20,
                                  isHasDecoration: false,
                                  hidePaginator: true,
                                  onPageChanged: (val) {
                                    print("page : $val");
                                  },
                                  source: RequestSource(
                                    isRequest: controller.isRequest,
                                    projects: controller.projects,
                                    onAgencyNameTap: (val) {
                                      _showAgencyDetailDialog(email: val['email'], name: val['name'], phone: val['phone']);
                                    },
                                    onAddressTap: (project) {
                                      widget.projectArguments!.clear();
                                      widget.projectArguments!.addAll({
                                        'projectFirebaseId': project.id,
                                        'updateViewCallback': widget.updateViewCallback,
                                        'initialFetchProperties': _initialFetch(
                                          force: true,
                                        ),
                                        'isFromRequest': false,
                                      });

                                      widget.updateViewCallback!('immagina-project-review', projectArguments: widget.projectArguments);
                                    },
                                  ),
                                  columns: [
                                    DataColumn2(label: Text("Codice"),size: ColumnSize.S),
                                    DataColumn2(label: Text("Priorità"),size: ColumnSize.S),
                                    DataColumn2(label: Text("Indirizzo"),size: ColumnSize.L),
                                    DataColumn2(label: Text("Team"),size: ColumnSize.S),
                                    DataColumn2(label: Text("Agenzia"),size: ColumnSize.S),
                                    DataColumn2(label: Text("Richiesta"),size: ColumnSize.S),
                                    DataColumn2(label: Text("Accettazione"),size: ColumnSize.S),
                                    DataColumn2(label: Text("Realizzazione"),size: ColumnSize.M),
                                    DataColumn2(label: Text("Vendita"),size: ColumnSize.S),
                                  ],
                                ),
                        ),
                        if (controller.loadingProperties)
                          Positioned.fill(
                            child: Center(
                              child: CircularProgressIndicator(
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerRight,
                    child: dataTablePagination(),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _showAgencyDetailDialog({required String email, phone, name}) {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 30,
              ),
              NarFormLabelWidget(
                label: name,
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 18),
          contentPadding: EdgeInsets.symmetric(horizontal: 40, vertical: 18),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 340,
                child: Row(
                  children: [
                    CustomTextFormField(
                      isHaveBorder: false,
                      flex: 1,
                      fillColor: Color(0xffF5F5F5),
                      readOnly: true,
                      label: "Telefono",
                      controller: TextEditingController(
                        text: phone,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 20,
              ),
              SizedBox(
                width: 340,
                child: Row(
                  children: [
                    CustomTextFormField(
                      isHaveBorder: false,
                      flex: 1,
                      fillColor: Color(0xffF5F5F5),
                      readOnly: true,
                      label: "Email",
                      controller: TextEditingController(
                        text: email,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 20,
              ),
            ],
          ),
          backgroundColor: AppColor.white,
        );
      },
    );
  }

  Container _tabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xffECECEC),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10),
          topRight: Radius.circular(10),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () => setState(() {
                  controller.isRequest = true;
                  reloadAfterPop(force: true);
                }),
                child: Container(
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: controller.isRequest ? Radius.circular(12) : Radius.circular(0),
                    ),
                    color: controller.isRequest ? Colors.white : Colors.transparent,
                  ),
                  child: Center(
                    child: NarFormLabelWidget(
                      label: 'Richieste',
                      fontSize: 15,
                      fontWeight: '700',
                      textColor: Colors.black,
                    ),
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () => setState(() {
                  controller.isRequest = false;
                  reloadAfterPop(force: true);
                }),
                child: Container(
                  height: 40,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                      color: !controller.isRequest ? Colors.white : Colors.transparent),
                  child: Center(
                    child: NarFormLabelWidget(
                      label: 'Progetti',
                      fontSize: 15,
                      fontWeight: '700',
                      textColor: Colors.black,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  NarFilter _filter() {
    return NarFilter(
      showSearchInput: true,
      searchHintText: "Cerca per indirizzo o per codice...",
      searchTextEditingControllers: controller.searchTextController,
      onChanged: (String? searchQuery)async{
        if(searchQuery?.isNotEmpty ?? false){
          if (searchQuery != null && searchQuery.isNotEmpty) {
            List<ImmaginaProject> filtered = controller.projects.where((project) {
              final address = project.addressInfo;
              final code = project.projectId.toLowerCase() ?? "";
              final city = address?.city?.toLowerCase() ?? project.city?.toLowerCase() ?? "";
              final streetName = address?.streetName?.toLowerCase() ?? '';
              final fullAddress = address?.fullAddress?.toLowerCase() ?? project.streetName?.toLowerCase() ?? "";
              return code.contains(searchQuery.toLowerCase()) ||
                  city.contains(searchQuery.toLowerCase()) ||
                  streetName.contains(searchQuery.toLowerCase()) ||
                  fullAddress.contains(searchQuery.toLowerCase());
            }).toList();

            setState(() {
              controller.projects = filtered;
            });
          }
        }else{
          await initialFetch(force: true);
        }
      },
      suffixIconOnTap: ()async{
        await initialFetch(force: true,reloadAll: true);
        if(controller.searchTextController.text.trim().isNotEmpty){
          List<ImmaginaProject> filtered = controller.projects.where((project) {
            final address = project.addressInfo;
            final code = project.projectId.toLowerCase() ?? "";
            final city = address?.city?.toLowerCase() ?? project.city?.toLowerCase() ?? "";
            final streetName = address?.streetName?.toLowerCase() ?? '';
            final fullAddress = address?.fullAddress?.toLowerCase() ?? project.streetName?.toLowerCase() ?? "";
            return code.contains(controller.searchTextController.text.toLowerCase()) ||
                city.contains(controller.searchTextController.text.toLowerCase()) ||
                streetName.contains(controller.searchTextController.text.toLowerCase()) ||
                fullAddress.contains(controller.searchTextController.text.toLowerCase());
          }).toList();

          setState(() {
            controller.projects = filtered;
          });
        }else{
          await initialFetch(force: true);
        }
      },
      selectedFilters: [
        controller.statusSelectedFilter,controller.agencyFilter,controller.realizzazioneFilter,controller.venditaFilter
      ],
      textEditingControllers: [controller.statusFilterController,controller.agencyFilterController,controller.realizzazioneFilterController,controller.venditaFilterController],
      filterFields: controller.isRequest ? [
        {
          'Status': NarSelectBoxWidget(
            options: [
              CommonUtils.daCompletare.toCapitalized(),
              CommonUtils.inAnalisi.toCapitalized(),
              CommonUtils.bloccata.toCapitalized(),
            ],
            onChanged: (value) {
              controller.filters = [
                {
                  'field': 'requestStatus',
                  'value': controller.statusFilterController.text.toLowerCase(),
                  'search': 'equal',
                }
              ];
            },
            controller: controller.statusFilterController,
          ),
        },
      ] : [
        {
          'Agenzia': NarSelectBoxWidget(
            options: controller.agencyList
                .map((e) => e.name!)
                .toSet()
                .toList(),
            controller: controller.agencyFilterController,
            onChanged: (value) {

              Agency _selectedAgency = controller.agencyList.where((agency) => agency.name! == controller.agencyFilterController.text).first;
              controller.filters.removeWhere((element) {
                return element['field'] == 'agencyId';
              });

              controller.filters.add({'field': 'agencyId', 'value': _selectedAgency.id, 'search': 'equal'});
              controller.agencyFilter = controller.agencyFilterController.text;
            },
          ),
        },
        {
          'Realizzazione': NarSelectBoxWidget(
            options: [
              CommonUtils.inLavorazione.toCapitalized(),
              CommonUtils.completato.toCapitalized(),
            ],
            controller: controller.realizzazioneFilterController,
            onChanged: (value) {
                controller.filters.removeWhere((element) {
                  return element['field'] == 'requestStatus';
                });
                controller.filters.add({'field': 'requestStatus', 'value': controller.realizzazioneFilterController.text.toLowerCase(), 'search': 'equal'});

              controller.realizzazioneFilter = controller.realizzazioneFilterController.text;

            },
          ),
        },
        {
          'Vendita': NarSelectBoxWidget(
            options: [
              CommonUtils.venduto.toCapitalized(),
              CommonUtils.nonVenduto.toCapitalized(),
            ],
            controller: controller.venditaFilterController,
            onChanged: (value) {
                controller.filters.removeWhere((element) {
                  return element['field'] == 'isAgencyArchived';
                });
                controller.filters.removeWhere((element) {
                  return element['field'] == 'isHouseSold';
                });

                if(value == CommonUtils.venduto.toCapitalized()){
                  controller.filters.addAll([{'field': 'isAgencyArchived', 'value': true, 'search': 'equal'},{'field': 'isHouseSold', "value": true, 'search': 'equal'}]);
                }
                if(value == CommonUtils.nonVenduto.toCapitalized()){
                  controller.filters.addAll([{'field': 'isAgencyArchived', 'value': true, 'search': 'equal'},{'field': 'isHouseSold', "value": false, 'search': 'equal'}]);
                }

              controller.venditaFilter = controller.venditaFilterController.text;
            },
          ),
        },
      ],
      onSubmit: () async {
        await initialFetch(force: true);
      },
      onReset: () async {
        controller.clearFilter();
        await initialFetch(force: true);
      },
    );
  }

  NarFormLabelWidget _headerTitle() {
    return NarFormLabelWidget(
      label: controller.isRequest ? "Richieste" : 'Progetti attivi',
      fontSize: 19,
      fontWeight: '700',
      textColor: Colors.black,
    );
  }
}
