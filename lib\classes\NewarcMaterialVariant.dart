
import 'NewarcMaterialSupplier.dart';

class NewarcMaterialVariant {
  String? firebaseId;
  int? insertTimestamp;
  double? modificationTimestamp;
  String? newarcMaterialDimensionID;
  String? newarcMaterialDimensionMetaID;
  String? naMaterialId;
  String? code;
  List<NewarcMaterialSupplier>? newarcMaterialSuppliers;
  bool? isArchive; // default to false
  String? creationUid; //the Firebase Id of the logged in user
  String? modificationUid;
  int? codeCounter;


  String? newarcMaterialVariantColorId; //--for MATERIALS - Porte Interne

// UI-related fields
  String? format; // For 'Formato'
  String? spessore; // For 'Spessore'
  String? color; //used in  MATERIALS - Porte Interne



  Map<String, Object?> toMap() {
    return {
      'insertTimestamp': insertTimestamp,
      'creationUid': creationUid,
      'modificationTimestamp': modificationTimestamp,
      'newarcMaterialDimensionID': newarcMaterialDimensionID,
      'newarcMaterialDimensionMetaID': newarcMaterialDimensionMetaID,
      'code': code,
      'newarcMaterialSuppliers': newarcMaterialSuppliers,
      'isArchive': isArchive,
      'modificationUid': modificationUid,
      'codeCounter': codeCounter,
      'newarcMaterialVariantColorId': newarcMaterialVariantColorId,
    };
  }

  NewarcMaterialVariant.empty() {
    this.firebaseId = '';
    this.insertTimestamp = null;
    this.modificationTimestamp = null;
    this.creationUid = '';
    this.newarcMaterialDimensionID = '';
    this.newarcMaterialDimensionMetaID = '';
    this.code = '';
    this.newarcMaterialSuppliers = [];
    this.isArchive = false;
    this.modificationUid = '';
    this.codeCounter = 0;
    this.newarcMaterialVariantColorId = '';
  }

  NewarcMaterialVariant.fromDocument(Map<String, dynamic> data, String id) {
    this.firebaseId = id;
    try {
      this.insertTimestamp = data['insertTimestamp'];
      this.modificationTimestamp = data['modificationTimestamp'];
      this.creationUid = data['creationUid'];
      this.newarcMaterialDimensionID = data['newarcMaterialDimensionID'];
      this.newarcMaterialDimensionMetaID = data['newarcMaterialDimensionMetaID'];
      this.code = data['code'];
      this.isArchive = data['isArchive'];
      this.modificationUid = data['modificationUid'];
      this.codeCounter = data['codeCounter'];
      this.newarcMaterialVariantColorId = data['newarcMaterialVariantColorId'];
      if (data['newarcMaterialSuppliers'] != null) {
        for (var i = 0; i < data['newarcMaterialSuppliers'].length; i++) {
          this.newarcMaterialSuppliers?.add(
              NewarcMaterialSupplier.fromDocument(data['newarcMaterial'][i],''));
        }
      } else {
        this.newarcMaterialSuppliers = [];
      }
    } catch (e, s) {
      print({ 'NewarcMaterialVariantSupplierPrice Class Error ------->', e, s});
    }
  }

}