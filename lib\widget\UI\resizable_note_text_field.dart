import 'package:flutter/material.dart';

import 'custom_textformfield.dart';

class Resizable<PERSON><PERSON><PERSON>ield extends StatefulWidget {
  final TextEditingController? controller;
  final String? label;
  final ValueChanged<String>? onChanged;
  final double? controllerFontSize;

  const ResizableNoteField({
    Key? key,
    required this.controller,
    this.label,
    this.onChanged,
    this.controllerFontSize = 15,
  }) : super(key: key);

  @override
  _ResizableNoteFieldState createState() => _ResizableNoteFieldState();
}

class _ResizableNoteFieldState extends State<ResizableNoteField> {
  double _textFieldHeight = 100;
  late Offset _startDragOffset;
  late double _startHeight;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          height: _textFieldHeight,
          padding: EdgeInsets.all(5),
          child:TextFormField(
            expands: true,
            minLines: null,
            maxLines: null,
            key: widget.key,
            cursorColor: Colors.black,
            controller: widget.controller,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            style: TextStyle(
              color: Colors.black,
              fontSize: widget.controllerFontSize,
              fontFamily: 'Raleway-700',
            ),
            decoration: InputDecoration(
              contentPadding: EdgeInsets.only(top: 17, bottom: 17, right: 2, left: 10),
              isCollapsed: true,
              hoverColor: Colors.transparent,
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
                borderSide: BorderSide(color: Color(0xffdbdbdb)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
                borderSide:  BorderSide(color: Color(0xffdbdbdb)),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
                borderSide: BorderSide(color: Color(0xffdbdbdb)),
              ),
            ),
            onChanged: widget.onChanged,
          ),
        ),
        Positioned(
          bottom: 4,
          right: 8,
          child: MouseRegion(
            cursor: SystemMouseCursors.resizeUpDown,
            child: GestureDetector(
              onPanStart: (details) {
                _startDragOffset = details.globalPosition;
                _startHeight = _textFieldHeight;
              },
              onPanUpdate: (details) {
                setState(() {
                  final drag = details.globalPosition.dy - _startDragOffset.dy;
                  _textFieldHeight = (_startHeight + drag).clamp(60.0, 400.0);
                });
              },
              child: Icon(Icons.drag_handle, size: 16, color: Colors.grey),
            ),
          ),
        ),
      ],
    );
  }
}
