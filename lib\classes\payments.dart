import 'dart:html';

import 'package:flutter/foundation.dart';

// class PaymentInstallment {
//   double? amount;
//   bool? paid;
//   int? datePaid;

//   PaymentInstallment.fromMap(Map<String, dynamic> data) {}

//   PaymentInstallment(this.amount, this.paid, this.datePaid);
// }

class Payment {
  String? id;
  String? title;
  double? totalCost;
  // List<PaymentInstallment>? installments;

  // Payment(this.title, this.totalCost, this.installments);

  Payment.fromMap(String id, Map<String, dynamic> data) {
    try {
      this.id = id;
      this.title = data['title'];
      this.totalCost = data['totalCost'];
      // this.installments = data['paymentInstallments'].forEach((installment) =>
          // PaymentInstallment.fromMap(data['paymentInstallments']));
    } catch (e) {
      print(e);
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'title': this.title,
      'totalCost': this.totalCost,
    };
  }
}
