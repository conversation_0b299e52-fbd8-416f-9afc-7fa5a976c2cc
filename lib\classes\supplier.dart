import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/basePersonInfo.dart';

class Supplier {
  String? id;
  String? name;
  String? formationType;
  @Deprecated('Use Supplier.operativeAddressInfo.city instead, keep in mind retrocompatibility')
  String? city;
  @Deprecated('Use Supplier.operativeAddressInfo.toShortAddress() instead, keep in mind retrocompatibility')
  String? addressAndCivicNumber;
  @Deprecated('Use Supplier.contactPersonInfo.name instead, keep in mind retrocompatibility')
  String? firstName;
  @Deprecated('Use Supplier.contactPersonInfo.surname instead, keep in mind retrocompatibility')
  String? lastName;
  @Deprecated('Used to be equivalent to now used Supplier.contactPersonInfo.phone, now represent the supplier phone, while Supplier.contactPersonInfo.phone is the contact person phone')
  String? phone;
  @Deprecated('Used to be equivalent to now used Supplier.contactPersonInfo.email, now represent the supplier email, while Supplier.contactPersonInfo.email is the contact person email')
  String? email;
  String? nationality;
  String? pec;
  String? iban;
  String? billingCode;
  String? iva;
  @Deprecated('Use Supplier.legalAddressInfo.toFullAddress() instead, keep in mind retrocompatibility')
  String? legalAddress;
  bool? isActive;
  bool? isArchived;
  List? documents;
  List? activities = [];
  
  List? projectsCompleted = [];
  List? assignedProject;

  int? insertTimestamp;
  int? modificationTimestamp;

  String? legalEntity;
  String? fiscalCode;

  BaseAddressInfo? operativeAddressInfo;
  BaseAddressInfo? legalAddressInfo;
  BasePersonInfo? contactPersonInfo;
  LegalRepresentativePersonInfo? legalRepresentativePersonInfo;

  Supplier(Map<String, dynamic> userMap, String userId) {
    this.id = userId;
    this.name = userMap['name'];
    this.formationType = userMap['formationType'];
    this.city = userMap['city'];
    this.addressAndCivicNumber = userMap['addressAndCivicNumber'];
    this.firstName = userMap['firstName'];
    this.lastName = userMap['lastName'];
    this.phone = userMap['phone'];
    this.email = userMap['email'];
    this.iban = userMap['iban'];
    this.pec = userMap['pec'];
    this.nationality = userMap['nationality'];
    this.billingCode = userMap['billingCode'];
    this.iva = userMap['iva'];
    this.legalAddress = userMap['legalAddress'];
    this.isActive = userMap['isActive'] != null ? userMap['isActive'] : true;
    this.isArchived = userMap['isArchived'] != null ? userMap['isArchived'] : false;
    this.documents = userMap['documents'] != null ? userMap['documents'] : []; 
    this.activities = userMap['activities'] != null ? userMap['activities'] : []; 
    this.projectsCompleted = userMap['projectsCompleted'] != null ? userMap['projectsCompleted'] : []; 
    this.assignedProject = userMap['assignedProject'] != null ? userMap['assignedProject'] : []; 
    this.insertTimestamp = userMap['insertTimestamp'];
    this.modificationTimestamp = userMap['modificationTimestamp'];
    this.legalEntity = userMap['legalEntity'];
    this.fiscalCode = userMap['fiscalCode'];
    this.legalAddressInfo = (userMap.containsKey('legalAddressInfo') && userMap['legalAddressInfo'] != null) ? BaseAddressInfo.fromMap(userMap['legalAddressInfo']) : null;
    this.operativeAddressInfo = (userMap.containsKey('operativeAddressInfo') && userMap['operativeAddressInfo'] != null) ? BaseAddressInfo.fromMap(userMap['operativeAddressInfo']) : null;
    this.contactPersonInfo = (userMap.containsKey('contactPersonInfo') && userMap['contactPersonInfo'] != null) ? BasePersonInfo.fromMap(userMap['contactPersonInfo']) : null;
    this.legalRepresentativePersonInfo = (userMap.containsKey('legalRepresentativePersonInfo') && userMap['legalRepresentativePersonInfo'] != null) ? LegalRepresentativePersonInfo.fromMap(userMap['legalRepresentativePersonInfo']) : null;
  }

  Supplier.empty() {
    this.id = '';
    this.name = '';
    this.formationType = '';
    this.city = '';
    this.addressAndCivicNumber = '';
    this.firstName = '';
    this.lastName = '';
    this.phone = '';
    this.email = '';
    this.iban = '';
    this.pec = '';
    this.nationality = '';
    this.billingCode = '';
    this.iva = '';
    this.legalAddress = '';
    this.isActive = true;
    this.isArchived = false;
    this.documents = [];
    this.activities = [];
    this.projectsCompleted = []; 
    this.assignedProject = []; 
    this.insertTimestamp = Timestamp.now().millisecondsSinceEpoch;
    this.modificationTimestamp = null;
    this.legalEntity = '';
    this.fiscalCode = '';
    this.legalAddressInfo = BaseAddressInfo.empty();
    this.operativeAddressInfo = BaseAddressInfo.empty();
    this.contactPersonInfo = BasePersonInfo.empty();
    this.legalRepresentativePersonInfo = LegalRepresentativePersonInfo.empty();
  }

  Supplier.fromDocument(Map<String, dynamic> data, String id) {
    this.id = id;
    this.name = data['name'];
    this.formationType = data['formationType'];
    this.city = data['city'];
    this.addressAndCivicNumber = data['addressAndCivicNumber'];
    this.firstName = data['firstName'];
    this.lastName = data['lastName'];
    this.phone = data['phone'];
    this.email = data['email'];
    this.iban = data['iban'];
    this.pec = data['pec'];
    this.nationality = data['nationality'];
    this.billingCode = data['billingCode'];
    this.iva = data['iva'];
    this.legalAddress = data['legalAddress'];
    this.isActive = data['isActive'] != null ? data['isActive'] : true;
    this.isArchived = data['isArchived'] != null ? data['isArchived'] : false;
    this.documents = data['documents'] != null ? data['documents'].cast<String>() : [];
    this.activities = data['activities'] != null ? data['activities'] : [];
    this.projectsCompleted = data['projectsCompleted'] != null ? data['projectsCompleted'] : [];
    this.assignedProject = data['assignedProject'] != null ? data['assignedProject'] : []; 
    this.insertTimestamp = data['insertTimestamp'];
    this.modificationTimestamp = data['modificationTimestamp'];
    this.legalEntity = data['legalEntity'];
    this.fiscalCode = data['fiscalCode'];
    this.legalAddressInfo = (data.containsKey('legalAddressInfo') && data['legalAddressInfo'] != null) ? BaseAddressInfo.fromMap(data['legalAddressInfo']) : null;
    this.operativeAddressInfo = (data.containsKey('operativeAddressInfo') && data['operativeAddressInfo'] != null) ? BaseAddressInfo.fromMap(data['operativeAddressInfo']) : null;
    this.contactPersonInfo = (data.containsKey('contactPersonInfo') && data['contactPersonInfo'] != null) ? BasePersonInfo.fromMap(data['contactPersonInfo']) : null;
    this.legalRepresentativePersonInfo = (data.containsKey('legalRepresentativePersonInfo') && data['legalRepresentativePersonInfo'] != null) ? LegalRepresentativePersonInfo.fromMap(data['legalRepresentativePersonInfo']) : null;
  }

  Map<String, dynamic> toMap() {
    return {
      'name' : this.name,
      'formationType' : this.formationType,
      'city' : this.city,
      'addressAndCivicNumber' : this.addressAndCivicNumber,
      'firstName' : this.firstName,
      'lastName' : this.lastName,
      'phone' : this.phone,
      'email' : this.email,
      'nationality' : this.nationality,
      'pec' : this.pec,
      'iban' : this.iban,
      'billingCode' : this.billingCode,
      'iva' : this.iva,
      'legalAddress' : this.legalAddress,
      'isActive' : this.isActive,
      'isArchived': this.isArchived,
      'documents': this.documents,
      'activities': this.activities,
      'projectsCompleted': this.projectsCompleted,
      'assignedProject': this.assignedProject,
      'insertTimestamp': this.insertTimestamp,
      'modificationTimestamp': this.modificationTimestamp,
      'legalEntity': this.legalEntity,
      'fiscalCode': this.fiscalCode,
      'legalAddressInfo': this.legalAddressInfo?.toMap(),
      'operativeAddressInfo': this.operativeAddressInfo?.toMap(),
      'contactPersonInfo': this.contactPersonInfo?.toMap(),
      'legalRepresentativePersonInfo': this.legalRepresentativePersonInfo?.toMap(),
    };
  }
}

class SupplierUser {
  String? id;
  String? supplierId;
  String? phone;
  String? email;
  String? name;
  String? surname;
  Supplier? supplier;
  User? user;
  String? type;
  String? role = 'supplier';
  String? profilePicture;
  bool? isActive;
  bool? isArchived;

  SupplierUser(Map<String, dynamic> userMap, String userId,
      Map<String, dynamic> supplierMap, String supplierId) {
    this.id = userId;
    this.name = userMap['name'];
    this.surname = userMap['surname'];
    this.phone = userMap['phone'] ?? "-";
    this.email = userMap['email'] ?? "-";
    this.supplierId = userMap['supplierId'];
    supplierMap['email'] = this.email;
    this.supplier = Supplier(supplierMap, supplierId);
    this.role = userMap['role'];
    this.profilePicture = userMap['profilePicture'];
    this.type = userMap['type'];
    this.isArchived = userMap['isArchived'] ?? false;
    this.isActive = userMap['isActive'] ?? true;
  }

  SupplierUser.fromDocument(Map<String, dynamic> data, String id) {
    this.id = id;
    this.name = data['name'];
    this.surname = data['surname'];
    this.phone = data['phone'] ?? "-";
    this.email = data['email'] ?? "-";
    this.supplierId = data['supplierId'];
    this.role = data['role'];
    this.profilePicture = data['profilePicture'];
    this.type = data['type'];
    this.isArchived = data['isArchived'] ?? false;
    this.isActive = data['isActive'] ?? true;
  }

  SupplierUser.empty();

  Map<String, dynamic> toMap() {
    return {
      'id': this.id,
      'name': this.name,
      'surname': this.surname,
      'phone': this.phone,
      'email': this.email,
      'supplierId': this.supplierId,
      'type': this.type,
      'role': this.role,
      'profilePicture': this.profilePicture,
      'isActive': this.isActive,
      'isArchived': this.isArchived,
    };
  }
}
