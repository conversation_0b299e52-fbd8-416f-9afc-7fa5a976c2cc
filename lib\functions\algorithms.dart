import 'package:collection/collection.dart';
import 'package:newarc_platform/classes/comparabile.dart';

void getScostamento(List<Comparabile> comparabili) {
  try {
    Map<String, List<Comparabile>> groupedZoneMap =
        groupBy(comparabili, (c) => c.zonaOmi!);

    groupedZoneMap.forEach((key, comparabili) {
      double sum = comparabili.fold(
          0, (previousValue, element) => previousValue + element.sqmPrice!);
      double avg = sum / comparabili.length;
      comparabili.forEach((c) {
        c.scostamento = (100 * (avg - c.sqmPrice!) / avg).round();
      });
    });

    return;
  } catch (e) {
    print("Errore scostamento");
    print(e);
    return;
  }
}
