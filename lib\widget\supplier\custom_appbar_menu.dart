import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart'; 
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/pages/login_page.dart';



class CustomAppbarMenu extends StatefulWidget {
  const CustomAppbarMenu(
      {Key? key,
      // required this.agency,
      required this.profilePicture,
      required this.onSettingsTapped})
      : super(key: key);
  // final Agency agency;
  final String? profilePicture;
  final Function? onSettingsTapped;

  @override
  State<CustomAppbarMenu> createState() => _CustomButtonTestState();
}

class _CustomButtonTestState extends State<CustomAppbarMenu> {
  // List<NewarcNotification> notifications = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: DropdownButtonHideUnderline(
        child: DropdownButton2(
          buttonStyleData: ButtonStyleData(
            overlayColor: WidgetStateProperty.all<Color>(Colors.transparent),
            decoration: BoxDecoration(
              color: Colors.transparent,
            )
          ),
          // focusColor: Colors.transparent,
          // buttonSplashColor: Colors.transparent,
          enableFeedback: false,
          customButton: Container(
            child: widget.profilePicture == null || widget.profilePicture == ''
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(40.0),
                    child: Image.asset(
                      'assets/icons/account_placeholder.png',
                      width: 40,
                    ),
                  )
                : Container(
                    height: 40,
                    width: 40,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        fit: BoxFit.cover,
                        image: NetworkImage(widget.profilePicture!),
                      ),
                      color: Colors.white,
                      borderRadius: BorderRadius.all(
                        Radius.circular(20),
                      ),
                    ),
                    child: SizedBox(
                      height: 0,
                    )),
          ),

          // customItemsHeights: [
          //   30,
          //   ...List<double>.filled(
          //       getItems(context, widget.onSettingsTapped!).length, 30),
          //   // ...List<double>.filled(
          //   //     getItems(context, widget.agency, widget.onSettingsTapped!)
          //   //         .length,
          //   //     30),
          // ],
          items: [
            DropdownMenuItem<Widget>(
              value: Container(
                child: NarFormLabelWidget(
                    label: "Account", fontSize: 16, fontWeight: '800'),
              ),
              child: Container(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    NarFormLabelWidget(
                        label: "Account", fontSize: 16, fontWeight: '800'),
                  ],
                ),
              ),
              enabled: false,
            ),
            ...getItems(context, widget.onSettingsTapped!),
            // ...getItems(context, widget.agency, widget.onSettingsTapped!),
          ],
          //onTap: (() => {}),
          onChanged: (value) {
            //value as MenuItem;
          },

          //itemHeight: 65,
          // itemPadding: const EdgeInsets.only(left: 16, right: 16),
          // scrollbarAlwaysShow: true,

          
          menuItemStyleData: MenuItemStyleData(
            height: 60,
            padding: const EdgeInsets.only(left: 16, right: 16),
            // customHeights: getCustomItemsHeights(getItems(context, widget.onSettingsTapped!), 65)
          ),
          dropdownStyleData: DropdownStyleData(
            width: 180,
            padding: const EdgeInsets.symmetric(vertical: 6),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    offset: Offset(0, 0),
                    blurRadius: 15,
                  )
                ]),
            elevation: 0,
            offset: const Offset(-150, -8),
          ),
        ),
      ),
    );
  }
}

// List<DropdownMenuItem<Widget>> getItems( BuildContext context, Agency agency, Function onSettingsTapped) {
List<DropdownMenuItem<Widget>> getItems(
    BuildContext context, Function onSettingsTapped) {
  List<DropdownMenuItem<Widget>> list = [];

  list.addAll([
    DropdownMenuItem<Widget>(
      value: getSettings(context, onSettingsTapped),
      child: getSettings(context, onSettingsTapped),
      onTap: (() {
        onSettingsTapped();
        //Navigator.of(context).pop();
      }),
    ),
    DropdownMenuItem<Widget>(
      value: getLogout(context),
      child: getLogout(context),
      onTap: () async {
        //auth
        await FirebaseAuth.instance.signOut();

        Navigator.of(context).pushReplacementNamed(
          LoginPage.route,
        );
      },
    ),
  ]);
  return list;
}

Widget getLogout(BuildContext context) {
  return Container(
    child: NarFormLabelWidget(label: "Logout", fontSize: 14, fontWeight: '500'),
  );
}

Widget getSettings(BuildContext context, Function onSettingsTapped) {
  return Container(
    child: NarFormLabelWidget(
      label: "Impostazioni",
      fontSize: 14,
      fontWeight: '500',
    ),
  );
}