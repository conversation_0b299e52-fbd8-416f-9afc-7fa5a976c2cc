import 'dart:math';

import 'package:firebase_auth/firebase_auth.dart';

// Mettere in classe separata
class Commission {
  double? amount;
  CommissionState? commissionState;

  Commission({this.amount, this.commissionState});

  factory Commission.fromJson(Map<String, dynamic> json) {
    return Commission(
      amount: json["amount"] != null ? json["amount"] as double : 0,
      commissionState: CommissionState.values[json["commissionState"] as int],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "amount": amount,
      "commissionState": commissionState!.index,
    };
  }

  Commission.mock() {
    this.amount = null;
    this.commissionState = CommissionState.toBeDefined;
  }
}

enum CommissionState { paid, toBePaid, toBeDefined }

String getCommissionStateString(CommissionState commissionState) {
  switch (commissionState) {
    case CommissionState.paid:
      return "Pagata";
    case CommissionState.toBePaid:
      return "Da Pagare";
    case CommissionState.toBeDefined:
      return "Da definire";
  }
}

class Operation {
  String? id;
  double? adPrice;
  String? addressString;
  String? city;
  Commission? bonus;
  Commission? firstCommission;
  //Map<String, dynamic>? firstCommission;
  Commission? secondCommission;
  double? minSalePrice;
  double? salePriceGoal;
  SaleState? saleState; // enum
  WorkState? workState; //enum
  int? insertionTimestamp;
  String? assignedAgencyId;

  int? purchaseDeedTimestamp;
  int? onSaleFromTimestamp;
  int? endMandateTimestamp;
  int? saleDeedTimestamp;

  Operation.fromJson(Map<String, dynamic> data, String id) {
    try {
      this.id = id;
      this.adPrice = data['adPrice'];
      this.addressString = data['addressString'];
      this.city = data['city'] ?? "";
      this.bonus = data['bonus'] != null
          ? Commission.fromJson(data['bonus'])
          : Commission.mock();
      this.firstCommission = data['firstCommission'] != null
          ? Commission.fromJson(data['firstCommission'])
          : Commission.mock();
      this.secondCommission = data['secondCommission'] != null
          ? Commission.fromJson(data['secondCommission'])
          : Commission.mock();
      this.minSalePrice = data['minSalePrice'];
      this.salePriceGoal = data['salePriceGoal'];
      this.saleState = SaleState.values
          .where((element) => element.index == data['saleState'])
          .first;
      this.workState = WorkState.values
          .where((element) => element.index == data['workState'])
          .first;
      this.insertionTimestamp = data['insertionTimestamp'];
      this.assignedAgencyId = data['assignedAgencyId'];

      this.purchaseDeedTimestamp = data['purchaseDeedTimestamp'];
      this.onSaleFromTimestamp = data['onSaleFromTimestamp'];
      this.endMandateTimestamp = data['endMandateTimestamp'];
      this.saleDeedTimestamp = data['saleDeedTimestamp'];
    } catch (e) {
      print(e);
    }
  }

  Operation.mock() {
    try {
      this.id = Random(60).nextInt(123).toString();
      this.adPrice = 123;
      this.addressString = "asdfasdfasdbfa";
      this.city = "Torino";
      this.bonus = Commission.mock();
      this.firstCommission = Commission.mock();
      this.secondCommission = Commission.mock();
      this.minSalePrice = 1234567;
      this.salePriceGoal = 1234;
      this.saleState = SaleState.forSale;
      this.workState = WorkState.toStart;
      this.insertionTimestamp = 12345;
      this.assignedAgencyId = null;
      this.purchaseDeedTimestamp = DateTime.now().millisecondsSinceEpoch;
      this.onSaleFromTimestamp = DateTime.now().millisecondsSinceEpoch;
      this.endMandateTimestamp = DateTime.now().millisecondsSinceEpoch;
      this.saleDeedTimestamp = DateTime.now().millisecondsSinceEpoch;
    } catch (e) {
      print(e);
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'adPrice': adPrice,
      'addressString': addressString,
      'city': city,
      'bonus': bonus!.toJson(),
      'firstCommission': firstCommission!.toJson(),
      'secondCommission': secondCommission!.toJson(),
      'minSalePrice': minSalePrice,
      'salePriceGoal': salePriceGoal,
      'saleState': saleState!.index,
      'workState': workState!.index,
      'insertionTimestamp': insertionTimestamp,
      'assignedAgencyId': assignedAgencyId,
      'purchaseDeedTimestamp': purchaseDeedTimestamp,
      'onSaleFromTimestamp': onSaleFromTimestamp,
      'endMandateTimestamp': endMandateTimestamp,
      'saleDeedTimestamp': saleDeedTimestamp
    };
  }

  Operation.empty() {
    this.firstCommission = Commission.mock();
    this.secondCommission = Commission.mock();
    this.bonus = Commission.mock();
    //this.purchaseDeedTimestamp = DateTime.now().millisecondsSinceEpoch;
    //this.onSaleFromTimestamp = DateTime.now().millisecondsSinceEpoch;
    //this.endMandateTimestamp = DateTime.now().millisecondsSinceEpoch;
    //this.saleDeedTimestamp = DateTime.now().millisecondsSinceEpoch;
  }
}

enum SaleState { sold, forSale, toSell }

String getSaleStateString(SaleState saleState) {
  switch (saleState) {
    case SaleState.sold:
      return "Venduto";
    case SaleState.forSale:
      return "In vendita";
    case SaleState.toSell:
      return "Da vendere";
  }
}

enum WorkState { completed, inProgress, toStart }

String getWorkStateString(WorkState workState) {
  switch (workState) {
    case WorkState.completed:
      return "Completato";
    case WorkState.inProgress:
      return "In corso";
    case WorkState.toStart:
      return "Da iniziare";
  }
}
