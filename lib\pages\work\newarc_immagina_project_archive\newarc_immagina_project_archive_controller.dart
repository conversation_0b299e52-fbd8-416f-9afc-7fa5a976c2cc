import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../../../classes/agencyUser.dart';
import '../../../classes/immaginaProject.dart';

class NewarcImmaginaProjectArchiveController extends GetxController {
  bool loadingProperties = true;
  List<ImmaginaProject> projects = [];
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  List<String> formMessages = [];
  List<DocumentSnapshot> documentList = [];
  int totalRecords = 0;
  String currentlyShowing = '';
  int recordsPerPage = 20;
  int pageCounter = 1;
  int totalPages = 0;
  bool disablePreviousButton = true;
  bool disableNextButton = false;
  List<dynamic> cacheFireStore = [];
  // bool isRequest = true;

  TextEditingController searchTextController =  TextEditingController();
  TextEditingController agencyFilterController = TextEditingController();
  TextEditingController venditaFilterController = TextEditingController();
  String agencyFilter = '';
  String realizzazioneFilter = '';
  String venditaFilter = '';
  List<Map> filters = [];
  List<Agency> agencyList = [];
}