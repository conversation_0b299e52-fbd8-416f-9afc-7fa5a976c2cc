import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/webLeads.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/user.dart';

class WebLeadsController extends GetxController {
  RxBool loadingContacts = false.obs;
  List<WebLeads> contacts = [];
  List<WebLeads> displayContacts = [];
  String query = "";
  String filterFieldWhere = "";
  String filterValueWhere = "";
  bool isValueNull = false;

  List<Map> filters = [];

  List testList = [
    {'no': 0, 'keyword': 'Filtra per stato'},
    {'no': 1, 'keyword': 'Da contattare'},
    {'no': 2, 'keyword': 'Contattato --'},
    {'no': 3, 'keyword': 'Non interessato'},
    {'no': 4, 'keyword': 'Acquisito'}
  ];

  var selectedStatus;

  List<DocumentSnapshot> documentList = [];
  int totalRecords = 0;
  String currentlyShowing = '';
  int recordsPerPage = 20;
  int pageCounter = 1;
  int totalPages = 0;
  bool disablePreviousButton = true;
  bool disableNextButton = false;
  List<dynamic> cacheFirestore = [];
  TextEditingController assignmentController = new TextEditingController();
  TextEditingController searchTextController = new TextEditingController();

  TextEditingController agencyFilterController = new TextEditingController();
  TextEditingController cityFilterController = new TextEditingController();
  TextEditingController newarcTypeFilterController = new TextEditingController();

  String agencyFilter = '';
  String cityFilter = '';
  String newarcTypeFilter = '';

  NewarcUser? newarcUser;

  List<Agency> agencyList = [];

  clearFilter() {
    agencyFilter = '';
    cityFilter = '';
    newarcTypeFilter = '';
    agencyFilterController.clear();
    cityFilterController.clear();
    newarcTypeFilterController.clear();
  }
}
