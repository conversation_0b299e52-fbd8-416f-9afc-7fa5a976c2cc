import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
//import 'package:newarc_platform/widget/agency/custom_chart.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/agency/custom_chart_new.dart';

class Dashboard extends StatefulWidget {
  const Dashboard(
      {Key? key,
      required this.agency,
      required this.agencyUser,
      required this.responsive})
      : super(key: key);

  final bool responsive;
  final Agency agency;
  final AgencyUser agencyUser;

  @override
  State<Dashboard> createState() => _DashboardState();
}

class _DashboardState extends State<Dashboard> {
  bool loading = false;
  List<AcquiredContact> contacts = [];
  int acquiredContacts = 0;
  int acquiredContactsLastMonth = 0;
  int timestamp = DateTime.now().millisecondsSinceEpoch;
  @override
  void initState() {
    //fetchContacts();
    super.initState();
  }

  Future<void> fetchContacts() async {
    QuerySnapshot<Map<String, dynamic>> collectionSnapshot =
        await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_VALUATOR_SUBMISSIONS)
            .get();

    List<AcquiredContact> _contacts = [];
    for (var element in collectionSnapshot.docs) {
      try {
        var _tmp = AcquiredContact.fromDocument(element.data(), element.id);
        _contacts.add(_tmp);
      } catch (e) {
        print("error in document ${element.id}");
        print(e);
      }
    }
    setState(() {
      contacts = _contacts;
      acquiredContacts = _contacts.length;
      acquiredContactsLastMonth = _contacts
          .where((c) => (timestamp - c.insertionTimestamp!) < 2629800000)
          .length;
      loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return ListView(
      primary: true,
      children: [
        Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NarFormLabelWidget(
                label: 'Dashboard',
                fontSize: 22,
                fontWeight: 'bold',
              ),
            ]),
        SizedBox(height: 20),
        Container(
          height: 180,
          child: ListView(
            primary: false,
            scrollDirection: Axis.horizontal,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  NarFormLabelWidget(
                    label: 'Contatti',
                    fontSize: 16,
                    fontWeight: 'bold',
                    textColor: Theme.of(context).primaryColor,
                  ),
                  SizedBox(height: 5),
                  Row(children: [
                    StreamBuilder(
                      stream: widget.agencyUser.role == 'master'
                          ? FirebaseFirestore.instance
                              .collection(
                                  appConfig.COLLECT_VALUATOR_SUBMISSIONS)
                              .snapshots()
                          : FirebaseFirestore.instance
                              .collection(
                                  appConfig.COLLECT_VALUATOR_SUBMISSIONS)
                              .where('assignedAgencyId',
                                  isEqualTo: widget.agency.id)
                              .snapshots(),
                      builder: (BuildContext context,
                          AsyncSnapshot<QuerySnapshot> snapshot) {
                        if (!snapshot.hasData) {
                          return Center(
                            child: CircularProgressIndicator(),
                          );
                        }

                        return getIndicatorWidget(
                            "Totali", snapshot.data!.docs.length.toString());
                      },
                    ),
                    SizedBox(width: 10),
                    StreamBuilder(
                      stream: widget.agencyUser.role == 'master'
                          ? FirebaseFirestore.instance
                              .collection(
                                  appConfig.COLLECT_VALUATOR_SUBMISSIONS)
                              .snapshots()
                          : FirebaseFirestore.instance
                              .collection(
                                  appConfig.COLLECT_VALUATOR_SUBMISSIONS)
                              .where('assignedAgencyId',
                                  isEqualTo: widget.agency.id)
                              .snapshots(),
                      builder: (BuildContext context,
                          AsyncSnapshot<QuerySnapshot<Map<String, dynamic>>>
                              snapshot) {
                        if (!snapshot.hasData) {
                          return Center(
                            child: CircularProgressIndicator(),
                          );
                        }
                        return getIndicatorWidget(
                          "Ultimo mese",
                          snapshot.data!.docs
                              .where((c) {
                                return (c
                                        .data()
                                        .containsKey('insertion_timestamp') &&
                                    (timestamp -
                                            c.data()['insertion_timestamp']) <
                                        2629800000);
                              })
                              .length
                              .toString(),
                        );
                      },
                    ),
                  ]),
                ],
              ),
              SizedBox(width: 20),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  NarFormLabelWidget(
                    label: 'Operazioni',
                    fontSize: 16,
                    fontWeight: 'bold',
                    textColor: Theme.of(context).primaryColor,
                  ),
                  SizedBox(height: 5),
                  Row(children: [
                    StreamBuilder(
                      stream: widget.agencyUser.role == 'master'
                          ? FirebaseFirestore.instance
                              .collection(appConfig.COLLECT_NEWARC_OPERATIONS)
                              .snapshots()
                          : FirebaseFirestore.instance
                              .collection(appConfig.COLLECT_NEWARC_OPERATIONS)
                              .where('assignedAgencyId',
                                  isEqualTo: widget.agency.id)
                              .snapshots(),
                      builder: (BuildContext context,
                          AsyncSnapshot<QuerySnapshot<Map<String, dynamic>>>
                              snapshot) {
                        if (!snapshot.hasData) {
                          return Center(
                            child: CircularProgressIndicator(),
                          );
                        }

                        return getIndicatorWidget(
                            "Operazioni in corso",
                            snapshot.data!.docs
                                .where((c) =>
                                    (c.data().containsKey('saleState') &&
                                        c.data()['saleState'] != '0'))
                                .length
                                .toString());
                      },
                    ),
                    SizedBox(width: 10),
                    StreamBuilder(
                      stream: widget.agencyUser.role == 'master'
                          ? FirebaseFirestore.instance
                              .collection(appConfig.COLLECT_NEWARC_OPERATIONS)
                              .snapshots()
                          : FirebaseFirestore.instance
                              .collection(appConfig.COLLECT_NEWARC_OPERATIONS)
                              .where('assignedAgencyId',
                                  isEqualTo: widget.agency.id)
                              .snapshots(),
                      builder: (BuildContext context,
                          AsyncSnapshot<QuerySnapshot<Map<String, dynamic>>>
                              snapshot) {
                        if (!snapshot.hasData) {
                          return Center(
                            child: CircularProgressIndicator(),
                          );
                        }

                        return getIndicatorWidget(
                            "Operazioni concluse",
                            snapshot.data!.docs
                                .where((c) =>
                                    (c.data().containsKey('saleState') &&
                                        c.data()['saleState'] == '2'))
                                .length
                                .toString());
                      },
                    ),
                  ]),
                ],
              ),
            ],
          ),
        ),
        Container(
          height: 460,
          child: Row(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  NarFormLabelWidget(
                    label: 'Provvigioni',
                    fontSize: 16,
                    fontWeight: 'bold',
                    textColor: Theme.of(context).primaryColor,
                  ),
                  SizedBox(height: 5),
                  getIndicatorWidget('Provvigioni totali', '€0k'),
                  SizedBox(height: 10),
                  getIndicatorWidget('Provvigioni acquisto', '€0k'),
                  SizedBox(height: 10),
                  getIndicatorWidget('Provvigioni vendita', '€0k')
                ],
              ),
              SizedBox(width: 10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    NarFormLabelWidget(
                      label: 'Andamento contatti',
                      fontSize: 16,
                      fontWeight: 'bold',
                      textColor: Theme.of(context).primaryColor,
                    ),
                    SizedBox(height: 5),
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: EdgeInsets.all(10),
                        height: 250,
                        width: 645,
                        child: StreamBuilder(
                          stream: widget.agencyUser.role == 'master'
                              ? FirebaseFirestore.instance
                                  .collection(
                                      appConfig.COLLECT_VALUATOR_SUBMISSIONS)
                                  .where('insertion_timestamp',
                                      isGreaterThanOrEqualTo: DateTime.now()
                                              .millisecondsSinceEpoch -
                                          691200000)
                                  .snapshots()
                              : FirebaseFirestore.instance
                                  .collection(
                                      appConfig.COLLECT_VALUATOR_SUBMISSIONS)
                                  .where('assignedAgencyId',
                                      isEqualTo: widget.agency.id)
                                  .snapshots(),
                          builder: (BuildContext context,
                              AsyncSnapshot<QuerySnapshot<Map<String, dynamic>>>
                                  snapshot) {
                            if (!snapshot.hasData) {
                              return Center(
                                child: CircularProgressIndicator(),
                              );
                            }

                            List<AcquiredContact> contacts = snapshot.data!.docs
                                .map(((e) => AcquiredContact.fromDocument(
                                    e.data(), e.id)))
                                .toList();
                            return LineChartSample2(
                              contacts: contacts,
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              /*Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Provvigioni',
                      style: subTitleStyle,
                    ),
                    getIndicatorWidget('Provvigioni totali', '€0k'),
                    SizedBox(height: 10),
                    getIndicatorWidget('Provvigioni acquisto', '€0k'),
                    SizedBox(height: 10),
                    getIndicatorWidget('Provvigioni vendita', '€0k')
                  ],
                ),
              ),
              SizedBox(width: 10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Andamento',
                      style: subTitleStyle,
                    ),
                    SizedBox(height: 5),
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: EdgeInsets.all(10),
                        height: 250,
                        child: CustomChart.withSampleData(),
                      ),
                    ),
                  ],
                ),
              ),*/
            ],
          ),
        )
      ],
    );
  }

  Widget getIndicatorWidget(String title, String value) {
    return Container(
      height: 138,
      width: 205,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 8.0, left: 8),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                )
              ],
            ),
          ),
          SizedBox(height: 18),
          loading
              ? CircularProgressIndicator(color: Theme.of(context).primaryColor)
              : Text(
                  value,
                  style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                    fontSize: 50,
                  ),
                )
        ],
      ),
    );
  }
}
