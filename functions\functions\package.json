{"name": "functions", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"dotenv": "^16.0.3", "firebase": "^9.14.0", "firebase-admin": "^10.2.0", "firebase-functions": "^4.1.0", "i": "^0.3.7", "node-mailjet": "^5.1.1", "nodemon": "^2.0.20", "pg": "^8.7.1"}, "devDependencies": {"typescript": "^4.6.4"}, "private": true}