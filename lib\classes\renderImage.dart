import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';


class RenderImageList {
  int? index;
  RenderImage? renderImageFirst;
  RenderImage? renderImageSecond;

  RenderImageList(Map<String, dynamic> data) {
    this.index = data['index'];
    this.renderImageFirst = data['renderImageFirst'];
    this.renderImageSecond = data['renderImageSecond'];    
  }

  RenderImageList.empty(){
    this.index = -1;
    this.renderImageFirst = RenderImage.empty();
    this.renderImageSecond = RenderImage.empty();
  }

  Map<String, dynamic> toMap() {
    return {
      'index': this.index,
      'renderImageFirst': this.renderImageFirst!.toMap(),
      'renderImageSecond': this.renderImageSecond!.toMap()
    };
  }
}

class RenderImage{
  
  XFile? tmpFile;
  Uint8List? imageBytes;
  String? filename;
  String? filenameUrl;
  String? location;
  String? room;
  bool? hasMatch;
  bool? isBrochure;
  bool? isNetworkImage;
  bool? isSelected;
  bool? isEnabledForWebsite;
  ImagePicker picker = ImagePicker();
  TextEditingController? contRoom;

  
  RenderImage(Map<String, dynamic> data) {
    this.imageBytes = data['imageBytes'];
    this.tmpFile = data['tmpFile'];
    this.filename = data['filename'];
    this.filenameUrl = data['filenameUrl'];
    this.location = data['location'];
    this.room = data['room'];
    this.hasMatch = data['hasMatch']??false;
    this.isBrochure = data['isBrochure']??false;
    this.isNetworkImage = data['isNetworkImage']??false;
    this.isEnabledForWebsite = data['isEnabledForWebsite']??false;
    this.picker = data['picker'];
    this.contRoom = new TextEditingController(text: data['room']);
    this.isSelected = data['isSelected']??false;
  }

  RenderImage.empty(){
    this.imageBytes = null;
    this.tmpFile = null;
    this.filename = '';
    this.filenameUrl = '';
    this.location = '';
    this.room = '';
    this.hasMatch = false;
    this.isBrochure = false;
    this.isNetworkImage = false;
    this.isEnabledForWebsite = false;
    this.picker = ImagePicker();
    this.contRoom = new TextEditingController();
    this.isSelected = false;
    
  }

  Map<String, dynamic> toMap() {
    return {
      'tmpFile': this.tmpFile,
      'filename': this.filename,
      'location': this.location,
      'room': this.room,
      'hasMatch': this.hasMatch,
      'isBrochure': this.isBrochure,
      'isEnabledForWebsite': this.isEnabledForWebsite
    };
  }

  RenderImage.fromDocument(Map<String, dynamic> data, int index) {

    try {
      // print({ 'data-->', data});
      this.tmpFile = data['tmpFile'];
      this.filename = data['filename'] ?? '';
      this.location = data['location'];
      this.room = data['room'];
      this.hasMatch = data['hasMatch']??false;
      this.isBrochure = data['isBrochure']??false;
      this.isEnabledForWebsite = data['isEnabledForWebsite']??false;
      this.isNetworkImage = data['filename'] != '' ? true : false;
      this.contRoom!.text = data['room']??'';
    } catch (e, s) {
      print({'renderImage.dart', e, s});
    }
    
  }

}

