import 'package:flutter/material.dart';

/// *  textInputType - The type of information for which to optimize the text input control.
/// *  hintText - Text that suggests what sort of input the field accepts.
/// *  prefixIcon - Pass Icon if required
/// *  defaultText - If there is predefined value is there for a text field
/// *  focusNode - Currently focus node
/// *  obscureText - Is Password field?
/// *  controller - Text controller
/// *  functionValidate - Validation function(currently I have used to check empty field)
/// *  parametersValidate - Value to validate
/// *  actionKeyboard - Keyboard action eg. next, done, search, etc
/// *  onSubmitField - Done click action
/// *  onFieldTap - On focus on TextField
class NarInputWidget extends StatefulWidget {
  final TextInputType? textInputType;
  final String? hintText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? defaultText;
  final FocusNode? focusNode;
  final bool? obscureText;
  final TextEditingController? controller;
  final String? validationType;
  final String? parametersValidate;
  final TextInputAction? actionKeyboard;
  final Function? onSubmitField;
  final Function? onFieldTap;
  final Function? onChanged;
  final FontWeight? fontWeight;
  final double? fontSize;
  final Color? fontColor;
  final FontStyle? fontStyle;
  final Color? hintFontColor;
  final double? letterSpacing;
  final double? borderRadius;
  final double? borderWidth;
  final Color? borderColor;
  final double? height;
  final bool? enabled;
  final EdgeInsets? contentPadding;
   

  const NarInputWidget(
      {Key? key,
      this.contentPadding = const EdgeInsets.only(top: 17, bottom: 17, right: 2, left: 10),
      required this.hintText,
      this.focusNode,
      this.textInputType,
      this.defaultText,
      this.obscureText = false,
      this.controller,
      this.validationType,
      this.parametersValidate,
      this.actionKeyboard = TextInputAction.next,
      this.onSubmitField,
      this.onFieldTap,
      this.prefixIcon,
      this.suffixIcon,
      this.fontColor,
      this.fontSize,
      this.fontStyle,
      this.fontWeight,
      this.hintFontColor,
      this.letterSpacing,
      this.borderRadius,
      this.borderColor,
      this.borderWidth,
      this.onChanged,
      this.height,
      this.enabled = true});

  @override
  _NarInputWidgetState createState() => _NarInputWidgetState();
}

class _NarInputWidgetState extends State<NarInputWidget> {
  // double bottomPaddingToError = 12;

  FontWeight? fontWeight;
  double? fontSize;
  Color? fontColor;
  FontStyle? fontStyle;
  Color? hintFontColor;
  double? letterSpacing;
  double? borderRadius;
  double? borderWidth;
  double? height;
  Color? borderColor;

  @override
  void initState() {
    fontWeight =
        widget.fontWeight != null ? widget.fontWeight : FontWeight.normal;

    fontSize = widget.fontSize != null ? widget.fontSize : 15;
    fontColor = widget.fontColor != null ? widget.fontColor : Colors.black;
    fontStyle = widget.fontStyle != null ? widget.fontStyle : FontStyle.normal;
    hintFontColor =
        widget.hintFontColor != null ? widget.hintFontColor : Colors.grey;
    letterSpacing = widget.letterSpacing != null ? widget.letterSpacing : 0;
    borderRadius = widget.borderRadius != null ? widget.borderRadius : 8;
    borderWidth = widget.borderWidth != null ? widget.borderWidth : 1;
    borderColor =
        widget.borderColor != null ? widget.borderColor : Color(0xffdbdbdb);

    height = widget.height != null ? widget.height : 1;

    super.initState();
  }

  @protected
  void didUpdateWidget(NarInputWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    // print({widget.enabled, widget.hintText});
    return Theme(
      data: Theme.of(context).copyWith(
        primaryColor: Theme.of(context).colorScheme.primary,
      ),
      child: TextFormField(
        // cursorColor: primaryColor,
        enabled: widget.enabled,
        obscureText: widget.obscureText ?? false,
        keyboardType: widget.textInputType,
        textInputAction: widget.actionKeyboard,
        focusNode: widget.focusNode,
        style: TextStyle(
          color: fontColor,
          fontSize: fontSize,
          fontWeight: fontWeight,
          fontStyle: fontStyle,
          letterSpacing: letterSpacing,
          fontFamily: 'Raleway-700',
        ),
        initialValue: widget.defaultText,

        decoration: InputDecoration(
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(borderRadius!)),
              borderSide: BorderSide(
                  color: Color.fromARGB(45, 219, 219, 219),
                  width: borderWidth!),
            ),
            prefixIcon: widget.prefixIcon,
            suffixIcon: widget.suffixIcon,
            hintText: widget.hintText,
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(borderRadius!)),
              borderSide: BorderSide(color: borderColor!, width: borderWidth!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(borderRadius!)),
              borderSide: BorderSide(color: borderColor!, width: borderWidth!),
            ),
            hintStyle: TextStyle(
              color: hintFontColor,
              fontSize: fontSize,
              fontWeight: fontWeight,
              fontStyle: fontStyle,
              letterSpacing: letterSpacing,
              //fontFamily: 'Visby800',
            ),
            contentPadding: widget.contentPadding,
            isDense: true,
            errorBorder: OutlineInputBorder(
              borderSide: BorderSide(
                  color: Color.fromARGB(255, 234, 28, 28), width: borderWidth!),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.black, width: borderWidth!),
            ),
            fillColor: Colors.white),
        controller: widget.controller,
        validator: (value) {
          // if (widget.functionValidate != null) {
          //   String resultValidate =
          //       widget.functionValidate!(value, widget.parametersValidate) ?? true ;
          //   if (resultValidate != null) {
          //     return resultValidate;
          //   }
          // }
          if (widget.validationType != null &&
              widget.parametersValidate != null) {
            if (widget.validationType == 'required' && value!.isEmpty) {
              return widget.parametersValidate;
            }
          }

          return null;
        },
        onFieldSubmitted: (value) {
          if (widget.onSubmitField != null) widget.onSubmitField!();
        },
        onTap: () {
          if (widget.onFieldTap != null) widget.onFieldTap!();
        },
        onChanged: (value) {
          if (widget.onChanged != null) widget.onChanged!(value);
        },
      ),
    );
  }
}
