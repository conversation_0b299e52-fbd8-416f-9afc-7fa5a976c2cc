import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/renovationContact.dart';

import '../../../classes/agencyUser.dart';

class SuggestedContactsWorkController extends GetxController {
  bool loading = false;
  // suggested contacts to be shown in table
  List<RenovationContact> contacts = [];
  // "conferma acquisizione" popup
  TextEditingController renovationPriceController = new TextEditingController();
  TextEditingController agencyCommissionController = new TextEditingController();
  String formProgressMessage = "";
  List<String> formErrorMessage = [""];
  // table filters
  TextEditingController agencyFilterController = new TextEditingController();
  TextEditingController searchController = new TextEditingController();
  String agencySelectedFilter = "";
  List<Agency> agencyList = [];

  List<Map> filters = [];

  clearFilter() {
    agencySelectedFilter = '';
    agencyFilterController.clear();
    filters.clear();
  }

  clearPopupcontrollers() {
    renovationPriceController.clear();
    agencyCommissionController.clear();
    formProgressMessage = "";
    formErrorMessage = [""];
  }
}
