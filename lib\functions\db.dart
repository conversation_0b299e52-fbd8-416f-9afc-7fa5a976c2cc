import 'dart:convert';

import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;
import 'package:newarc_platform/classes/comparabile.dart';
import 'package:newarc_platform/functions/ai.dart';
import 'package:newarc_platform/functions/algorithms.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

String endpoint = appConfig.AI_ENDPOINT;

Future<List<Comparabile>?> queryAws() async {
  try {
    HttpsCallable callable = FirebaseFunctions.instance.httpsCallable(
      'queryDatabase',
    );
    Map<String, dynamic> query = {'query': 'SELECT * FROM "202110"'};
    HttpsCallableResult result = await callable.call(query);
    List<dynamic> rows = result.data['rows'];

    List<Comparabile> comparabili = [];
    for (dynamic json_comparabile in rows) {
      Comparabile c = Comparabile.fromAwsJson(json_comparabile);
      if (c.sqmPrice != null && c.zonaOmi != null) {
        comparabili.add(c);
      }
    }
    return comparabili;
  } catch (e) {
    return [];
  }
}

dynamic query() async {
  try {
    http.Response response = await http.get(Uri.parse('$endpoint/query'));
    return jsonDecode(response.body);
  } catch (e) {
    return null;
  }
}

Future<List<Comparabile>> queryNew() async {
  try {
    http.Response response = await http.get(Uri.parse('$endpoint/query'));
    List<Map<String, dynamic>> data =
        List<Map<String, dynamic>>.from(jsonDecode(response.body));
    if (data == null) {
      data = [];
    }

    List<Comparabile> comparabili = [];
    for (Map<String, dynamic> d in data) {
      Comparabile c = Comparabile.fromJson(d);
      if (c.sqmPrice! > 0 && c.zonaOmi != 'None') {
        comparabili.add(c);
      }
    }
    getScostamento(comparabili);
    print("Start predicting");
    await predictSalePrice(comparabili);

    return comparabili;
  } catch (e) {
    print("Query error");
    print(e);
    return [];
  }
}
