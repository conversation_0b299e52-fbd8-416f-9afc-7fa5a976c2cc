import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/immaginaProject.dart';

import '../../../classes/agencyUser.dart';

class AgencyImmaginaViewController extends GetxController {
  bool loadingProperties = true;
  List<ImmaginaProject> projects = [];
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  List<String> formMessages = [];
  List<DocumentSnapshot> documentList = [];
  int totalRecords = 0;
  String currentlyShowing = '';
  int recordsPerPage = 20;
  int pageCounter = 1;
  int totalPages = 0;
  bool disablePreviousButton = true;
  bool disableNextButton = false;
  List<dynamic> cacheFireStore = [];
  bool isRequest = true;

  TextEditingController statusFilterController = TextEditingController();
  List<Map> filters = [];
  TextEditingController searchTextController =  TextEditingController();
  TextEditingController agencyFilterController =  TextEditingController();
  TextEditingController realizzazioneFilterController =  TextEditingController();
  TextEditingController venditaFilterController =  TextEditingController();

  String statusSelectedFilter = '';
  String agencySelectedFilter = '';
  String realizzazioneFilter = '';
  String venditaFilter = '';

  clearFilter(){
    venditaFilterController.clear();
    realizzazioneFilterController.clear();
    statusFilterController.clear();
    agencyFilterController.clear();
    venditaFilter = '';
    statusSelectedFilter = '';
    agencySelectedFilter = '';
    realizzazioneFilter = '';
    filters.clear();
  }

  List<Agency> agencyList = [];
}
