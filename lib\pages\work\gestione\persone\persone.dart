import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/pages/work/gestione/persone/persone_user_widget.dart';


class PersoneInsideViewController extends GetxController {
  RxBool loading = false.obs;
  NewarcUser? person;
}

class PersoneInsideView extends StatefulWidget {
  final String userId;
  final Function updateViewCallback;
  final Function({bool force}) initialFetchPeople;

  const PersoneInsideView({
    Key? key,
    required this.userId,
    required this.updateViewCallback,
    required this.initialFetchPeople,
  }) : super(key: key);

  @override
  State<PersoneInsideView> createState() => _PersoneInsideViewState();
}

class _PersoneInsideViewState extends State<PersoneInsideView> {
  final controller = Get.put<PersoneInsideViewController>(PersoneInsideViewController());

  @override
  void initState() {
    super.initState();
    fetchPersonData();
  }

  Future<void> fetchPersonData() async {
    controller.loading.value = true;

    try {
      DocumentSnapshot<Map<String, dynamic>> doc = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_USERS)
          .doc(widget.userId)
          .get();

      if (doc.exists) {
        controller.person = NewarcUser.fromDocument(doc.data()!, doc.id);
      }
    } catch (e) {
      print('Error fetching person data: $e');
    } finally {
      controller.loading.value = false;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  child: Icon(
                    Icons.arrow_back_ios_new_rounded,
                    color: Colors.black,
                    size: 21,
                  ),
                  onTap: () async {
                    widget.updateViewCallback('team');
                  },
                ),
              ),
              SizedBox(width: 20),
              NarFormLabelWidget(
                label: 'Persone',
                fontSize: 22,
                fontWeight: '700',
              ),
            ],
          ),
          SizedBox(height: 20),
          Expanded(
            child: Container(
              height: double.infinity,
              padding:
                  EdgeInsets.symmetric(horizontal: 30, vertical: 10),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(13),
                border: Border.all(color: Color(0xffe7e7e7)),
              ),
              child: controller.loading.value
                  ? Center(
                      child: CircularProgressIndicator(
                        color: Theme.of(context).primaryColor,
                      ),
                    )
                  : controller.person != null
                  ? PersoneUserWidget(
                    user: controller.person!,
                    updateViewCallback: widget.updateViewCallback,
                  )
                  : Container(
                      height: double.infinity,
                      width: double.infinity,
                      child: Center(
                        child: NarFormLabelWidget(
                          label: 'Nessun professionista da mostrare',
                        ),
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
