import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';
import 'package:newarc_platform/pages/agency/acquired_contacts_view.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class CommentPopup extends StatefulWidget {
  const CommentPopup({
    Key? key,
    required this.acquiredContact,
  }) : super(key: key);

  final AcquiredContact acquiredContact;

  @override
  State<CommentPopup> createState() => _CommentPopupState();
}

class _CommentPopupState extends State<CommentPopup> {
  TextStyle sectionTitleStyle =
      TextStyle(fontSize: 16, fontWeight: FontWeight.bold);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 530,

      width: 850, //MediaQuery.of(context).size.width * 0.6,
      margin: EdgeInsets.symmetric(horizontal: 20),
      padding: EdgeInsets.symmetric(horizontal: 30, vertical: 20),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        child: ListView(
          children: [
            Column(
              children: [
                SizedBox(height: 20),
                NarFormLabelWidget(label: 'Info Contatto', fontSize: 22, fontWeight: '900', textColor: Colors.black,),
                
                NarFormLabelWidget(label: "${widget.acquiredContact.address} ${widget.acquiredContact.streetNumber}", fontSize: 15, fontWeight: '900', textColor: Color.fromRGBO(113, 113, 113, 1),),
                
                SizedBox(height: 20),

                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    NarFormLabelWidget(label: "Nome e cognome", fontSize: 13, fontWeight: '700', textColor: Color.fromRGBO(149, 149, 149, 1),),
                    Container(
                      width: 400,
                      padding: EdgeInsets.symmetric( vertical: 12, horizontal: 27),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(7.0)), 
                        border: Border.all(
                          color: Color.fromRGBO(228, 228, 228, 1),
                          width: 1,
                        ),
                      ),
                      child: NarFormLabelWidget(label: "${widget.acquiredContact.contactFullName}", fontSize: 13, fontWeight: '800', textColor: Colors.black,)
                    ),
                  ],
                  
                ),
                SizedBox(height: 20,),

                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    NarFormLabelWidget(label: "E-mail", fontSize: 13, fontWeight: '700', textColor: Color.fromRGBO(149, 149, 149, 1),),
                    Container(
                      width: 400,
                      padding: EdgeInsets.symmetric( vertical: 12, horizontal: 27),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(7.0)), 
                        border: Border.all(
                          color: Color.fromRGBO(228, 228, 228, 1),
                          width: 1,
                        ),
                      ),
                      child: NarFormLabelWidget(label: "${widget.acquiredContact.contactEmail}", fontSize: 13, fontWeight: '800', textColor: Colors.black,)
                    ),
                  ],
                  
                ),
                SizedBox(height: 20,),

                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    NarFormLabelWidget(label: "Telefono", fontSize: 13, fontWeight: '700', textColor: Color.fromRGBO(149, 149, 149, 1),),
                    Container(
                      width: 400,
                      padding: EdgeInsets.symmetric( vertical: 12, horizontal: 27),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(7.0)), 
                        border: Border.all(
                          color: Color.fromRGBO(228, 228, 228, 1),
                          width: 1,
                        ),
                      ),
                      child: NarFormLabelWidget(label: "${widget.acquiredContact.contactPhone}", fontSize: 13, fontWeight: '800', textColor: Colors.black,)
                    ),
                  ],
                  
                ),
                
                
              ],
            ),
          ],
        ),
      ),
    );
  }
}
