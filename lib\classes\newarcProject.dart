import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/classes/agreement.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/newarcMaterial.dart';
import 'package:newarc_platform/classes/agencyCommissions.dart';
import 'package:newarc_platform/classes/fixedProperty.dart';
import 'package:newarc_platform/classes/process.dart';
import 'package:newarc_platform/classes/projectJob.dart';
import 'package:newarc_platform/classes/notificationRenovationApp.dart';

import 'newarcProjectFixedAssetsPropertyPagamento.dart';

class NewarcProject {
  String? id;
  String? name;
  @Deprecated('Use NewarcProject.addressInfo.city instead, keep in mind retrocompatibility')
  String? city;
  BaseAddressInfo? addressInfo;
  String? type;
  String? renovationContactId;
  int? created;
  List<dynamic> assignedTeam = [];
  Map? assignedVendor;
  AgencyCommissions? newAssignedAgency;
  Map? assignedAgency;
  List? designFiles;
  List? plantEngineeringFiles;
  List? floorPlanFiles;
  List? renderFiles;
  List? constructionPracticesFiles;
  List? brochureFiles;
  List? preRennovationFiles;
  List? postRennovationFiles;
  List? deedAndContractsFiles;
  List? videoFiles;
  String? propertyId;
  bool? isArchived;
  FixedProperty? fixedProperty;
  List<NewarcMaterial>? newarcMaterial = [];
  List<Process>? vendorAndProfessionals = [];
  List<ProjectJob>? projectJobs = [];
  List<Agreement>? agreements = [];
  List<NewarcProjectPagamento>? vendorAndProfessionalsUscite;
  List<NewarcProjectPagamento>? materialUscite;

  int? jobStartDate;
  int? jobEndDate;
  int? hypotheticalJobEndDate;
  String? provisionalAccountId;
  String? virtualTourLink;
  Map? economicAccount = {};
  List<NotificationRenovationApp>? userNotifications = [];

  double? budgetDitteProfRistrutturazione;
  double? budgetGastisciMaterialiRistrutturazione;




  NewarcProject(Map<String, dynamic> newarcProjectMap) {
    this.id = newarcProjectMap['id'];
    this.name = newarcProjectMap['name'];
    this.city = newarcProjectMap['city'];
    this.addressInfo = (newarcProjectMap.containsKey('addressInfo') && newarcProjectMap['addressInfo'] != null) ? BaseAddressInfo.fromMap(newarcProjectMap['addressInfo']) : null;
    this.type = newarcProjectMap['type'];
    this.created = newarcProjectMap['created'];
    this.assignedTeam = newarcProjectMap['assignedTeam'];
    this.assignedVendor = newarcProjectMap['assignedVendor'];
    this.assignedAgency = newarcProjectMap['assignedAgency'];
    this.propertyId = newarcProjectMap['propertyId'];
    this.designFiles = newarcProjectMap['designFiles'];
    this.plantEngineeringFiles = newarcProjectMap['plantEngineeringFiles'];
    this.floorPlanFiles = newarcProjectMap['floorPlanFiles'];
    this.renderFiles = newarcProjectMap['renderFiles'];
    this.constructionPracticesFiles =
        newarcProjectMap['constructionPracticesFiles'];
    this.brochureFiles = newarcProjectMap['brochureFiles'];
    this.preRennovationFiles = newarcProjectMap['preRennovationFiles'];
    this.postRennovationFiles = newarcProjectMap['postRennovationFiles'];
    this.deedAndContractsFiles = newarcProjectMap['deedAndContractsFiles'];
    this.videoFiles = newarcProjectMap['videoFiles'];
    this.isArchived = newarcProjectMap['isArchived'];
    this.fixedProperty = newarcProjectMap['fixedProperty'];
    this.newarcMaterial = newarcProjectMap['newarcMaterial'];
    this.vendorAndProfessionals = newarcProjectMap['vendorAndProfessionals'];
    this.projectJobs = newarcProjectMap['projectJobs'];
    this.agreements = newarcProjectMap['agreements'];
    this.jobStartDate = newarcProjectMap['jobStartDate'];
    this.jobEndDate = newarcProjectMap['jobEndDate'];
    this.hypotheticalJobEndDate = newarcProjectMap['hypotheticalJobEndDate'];
    this.virtualTourLink = newarcProjectMap['virtualTourLink'];
    this.provisionalAccountId = newarcProjectMap['provisionalAccountId'];
    this.economicAccount = newarcProjectMap['economicAccount'];
    this.userNotifications = newarcProjectMap['userNotifications'];
    this.budgetDitteProfRistrutturazione = newarcProjectMap['budgetDitteProfRistrutturazione'];
    this.budgetGastisciMaterialiRistrutturazione = newarcProjectMap['budgetGastisciMaterialiRistrutturazione'];
    this.vendorAndProfessionalsUscite = newarcProjectMap['vendorAndProfessionalsUscite'];
    this.materialUscite = newarcProjectMap['materialUscite'];
  }
  NewarcProject.empty() {
    this.id = '';
    this.name = '';
    this.city = '';
    this.addressInfo = BaseAddressInfo.empty();
    this.type = '';
    this.created = Timestamp.now().millisecondsSinceEpoch;
    this.assignedTeam = [];
    this.assignedVendor = {};
    this.assignedAgency = {};
    this.designFiles = [];
    this.plantEngineeringFiles = [];
    this.floorPlanFiles = [];
    this.renderFiles = [];
    this.constructionPracticesFiles = [];
    this.brochureFiles = [];
    this.preRennovationFiles = [];
    this.postRennovationFiles = [];
    this.deedAndContractsFiles = [];
    this.videoFiles = [];
    this.propertyId = null;
    this.renovationContactId = '';
    this.isArchived = false;
    this.fixedProperty = new FixedProperty.empty();
    this.vendorAndProfessionals = [];
    this.newarcMaterial = [];
    this.projectJobs = [];
    this.agreements = [];
    this.jobStartDate = 0;
    this.jobEndDate = 0;
    this.hypotheticalJobEndDate = 0;
    this.virtualTourLink = '';
    this.provisionalAccountId = null;
    this.economicAccount = {};
    this.userNotifications = [];
    this.budgetDitteProfRistrutturazione = 1.0;
    this.budgetGastisciMaterialiRistrutturazione = 1.0;
    this.vendorAndProfessionalsUscite = [];
    this.materialUscite = [];
  }

  NewarcProject.fromDocument(Map<String, dynamic> data, String id) {
    this.id = id;
    this.budgetDitteProfRistrutturazione = data['budgetDitteProfRistrutturazione'] == null ? 1.0 : data['budgetDitteProfRistrutturazione'];
    this.budgetGastisciMaterialiRistrutturazione = data['budgetGastisciMaterialiRistrutturazione'] == null ? 1.0 : data['budgetGastisciMaterialiRistrutturazione'];
    this.name = data['name'] == null ? '' : data['name'];
    this.city = data['city'] == null ? '' : data['city'];
    this.addressInfo = (data.containsKey('addressInfo') && data['addressInfo'] != null) ? BaseAddressInfo.fromMap(data['addressInfo']) : null;
    this.type = data['type'] == null ? '' : data['type'];
    this.created = data['created'] == null ? '' : data['created'];
    this.assignedTeam =
        data['assignedTeam'] == null ? [] : data['assignedTeam'];
    this.assignedVendor =
        data['assignedVendor'] == null ? {} : data['assignedVendor'];
    this.assignedAgency =
        data['assignedAgency'] == null ? {} : data['assignedAgency'];
    this.designFiles = data['designFiles'] == null ? [] : data['designFiles'];
    this.plantEngineeringFiles = data['plantEngineeringFiles'] == null
        ? []
        : data['plantEngineeringFiles'];
    this.floorPlanFiles =
        data['floorPlanFiles'] == null ? [] : data['floorPlanFiles'];
    this.renderFiles = data['renderFiles'] == null ? [] : data['renderFiles'];
    this.constructionPracticesFiles = data['constructionPracticesFiles'] == null
        ? []
        : data['constructionPracticesFiles'];
    this.brochureFiles =
        data['brochureFiles'] == null ? [] : data['brochureFiles'];
    this.preRennovationFiles =
        data['preRennovationFiles'] == null ? [] : data['preRennovationFiles'];
    this.postRennovationFiles = data['postRennovationFiles'] == null
        ? []
        : data['postRennovationFiles'];
    this.deedAndContractsFiles = data['deedAndContractsFiles'] == null
        ? []
        : data['deedAndContractsFiles'];

    this.videoFiles = data['videoFiles'] ?? [];

    this.propertyId = data['propertyId'] == null ? null : data['propertyId'];
    this.renovationContactId = data['renovationContactId'] == null
        ? null
        : data['renovationContactId'];
    this.isArchived = data['isArchived'] == null ? false : data['isArchived'];

    this.fixedProperty = data['fixedProperty'] != null
        ? FixedProperty.fromDocument(data['fixedProperty'])
        : FixedProperty.empty();

    if (data['newarcMaterial'] != null) {
      for (var i = 0; i < data['newarcMaterial'].length; i++) {
        this
            .newarcMaterial!
            .add(NewarcMaterial.fromDocument(data['newarcMaterial'][i], i));
      }
    } else {
      this.newarcMaterial = [];
    }

    if (data['vendorAndProfessionals'] != null) {
      for (var i = 0; i < data['vendorAndProfessionals'].length; i++) {
        this
            .vendorAndProfessionals!
            .add(Process.fromDocument(data['vendorAndProfessionals'][i], i));
      }
    }

    this.vendorAndProfessionalsUscite = [];
    if (data['vendorAndProfessionalsUscite'] != null) {
      for (var i = 0; i < data['vendorAndProfessionalsUscite'].length; i++) {
        this.vendorAndProfessionalsUscite!.add(NewarcProjectPagamento.fromDocument(data['vendorAndProfessionalsUscite'][i],""));
      }
    }
    this.materialUscite = [];
    if (data['materialUscite'] != null) {
      for (var i = 0; i < data['materialUscite'].length; i++) {
        this.materialUscite!.add(NewarcProjectPagamento.fromDocument(data['materialUscite'][i],""));
      }
    }

    if (data['projectJobs'] != null) {
      for (var i = 0; i < data['projectJobs'].length; i++) {
        this.projectJobs!.add(ProjectJob.fromDocument(data['projectJobs'][i], i));
      }
    }
    if (data['agreements'] != null) {
      for (var i = 0; i < data['agreements'].length; i++) {
        this.agreements!.add(Agreement.fromDocument(data['agreements'][i], i));
      }
    }

    

    this.jobStartDate = data['jobStartDate'] == null ? 0 : data['jobStartDate'];
    this.jobEndDate = data['jobEndDate'] == null ? 0 : data['jobEndDate'];
    this.hypotheticalJobEndDate = data['hypotheticalJobEndDate'] == null
        ? 0
        : data['hypotheticalJobEndDate'];
    this.virtualTourLink = data['virtualTourLink'] ?? data['virtualTourLink'];
    this.provisionalAccountId = data['provisionalAccountId'] == null
        ? ''
        : data['provisionalAccountId'];
    this.economicAccount =
        data['economicAccount'] == null ? {} : data['economicAccount'];
    
    if (data['userNotifications'] != null) {
      for (var i = 0; i < data['userNotifications'].length; i++) {
        this
            .userNotifications!
            .add(NotificationRenovationApp.fromDocument(data['userNotifications'][i]));
      }
    }
  }

  copy(NewarcProject newarcProject) {
    this.id = newarcProject.id;
    this.name = newarcProject.name;
    this.budgetDitteProfRistrutturazione = newarcProject.budgetDitteProfRistrutturazione;
    this.budgetGastisciMaterialiRistrutturazione = newarcProject.budgetGastisciMaterialiRistrutturazione;
    this.city = newarcProject.city;
    this.addressInfo = newarcProject.addressInfo;
    this.type = newarcProject.type;
    this.created = newarcProject.created;
    this.assignedTeam = newarcProject.assignedTeam;
    this.assignedVendor = newarcProject.assignedVendor;
    this.assignedAgency = newarcProject.assignedAgency;
    this.designFiles = newarcProject.designFiles;
    this.plantEngineeringFiles = newarcProject.plantEngineeringFiles;
    this.floorPlanFiles = newarcProject.floorPlanFiles;
    this.renderFiles = newarcProject.renderFiles;
    this.constructionPracticesFiles = newarcProject.constructionPracticesFiles;
    this.brochureFiles = newarcProject.brochureFiles;
    this.preRennovationFiles = newarcProject.preRennovationFiles;
    this.postRennovationFiles = newarcProject.postRennovationFiles;
    this.deedAndContractsFiles = newarcProject.deedAndContractsFiles;
    this.videoFiles = newarcProject.videoFiles;
    this.propertyId = newarcProject.propertyId;
    this.renovationContactId = newarcProject.renovationContactId;
    this.isArchived = newarcProject.isArchived;
    this.fixedProperty = newarcProject.fixedProperty;
    this.newarcMaterial = newarcProject.newarcMaterial;
    this.vendorAndProfessionals = newarcProject.vendorAndProfessionals;
    this.projectJobs = newarcProject.projectJobs;
    this.agreements = newarcProject.agreements;
    this.jobStartDate = newarcProject.jobStartDate;
    this.jobEndDate = newarcProject.jobEndDate;
    this.hypotheticalJobEndDate = newarcProject.hypotheticalJobEndDate;
    this.virtualTourLink = newarcProject.virtualTourLink;
    this.provisionalAccountId = newarcProject.provisionalAccountId;
    this.economicAccount = newarcProject.economicAccount;
    this.userNotifications = newarcProject.userNotifications;
    this.vendorAndProfessionalsUscite = newarcProject.vendorAndProfessionalsUscite;
    this.materialUscite = newarcProject.materialUscite;
  }

  Map<String, dynamic> toMap() {
    return {
      'name': this.name,
      'budgetDitteProfRistrutturazione': this.budgetDitteProfRistrutturazione,
      'budgetGastisciMaterialiRistrutturazione': this.budgetGastisciMaterialiRistrutturazione,
      'city': this.city,
      'addressInfo': this.addressInfo?.toMap(),
      'type': this.type,
      'created': this.created,
      'assignedTeam': this.assignedTeam,
      'assignedVendor': this.assignedVendor,
      'assignedAgency': this.assignedAgency,
      'propertyId': this.propertyId,
      'designFiles': this.designFiles,
      'plantEngineeringFiles': this.plantEngineeringFiles,
      'floorPlanFiles': this.floorPlanFiles,
      'renderFiles': this.renderFiles,
      'constructionPracticesFiles': this.constructionPracticesFiles,
      'brochureFiles': this.brochureFiles,
      'preRennovationFiles': this.preRennovationFiles,
      'postRennovationFiles': this.postRennovationFiles,
      'isArchived': this.isArchived,
      'deedAndContractsFiles': this.deedAndContractsFiles,
      'videoFiles': this.videoFiles,
      'fixedProperty': this.fixedProperty?.toMap(),
      'newarcMaterial': this.newarcMaterial!.map((e) => e.toMap()).toList(),
      'vendorAndProfessionals': this.vendorAndProfessionals!.map((e) => e.toMap()).toList(),
      'projectJobs': this.projectJobs!.map((e) => e.toMap()).toList(),
      'agreements': this.agreements!.map((e) => e.toMap()).toList(),
      'jobStartDate': this.jobStartDate,
      'jobEndDate': this.jobEndDate,
      'hypotheticalJobEndDate': this.hypotheticalJobEndDate,
      'provisionalAccountId': this.provisionalAccountId,
      'economicAccount': this.economicAccount,
      'virtualTourLink': this.virtualTourLink,
      'userNotifications': this.userNotifications!.map((e) => e.toMap()).toList(),
      'vendorAndProfessionalsUscite': this.vendorAndProfessionalsUscite!.map((e) => e.toMap()).toList(),
      'materialUscite': this.materialUscite!.map((e) => e.toMap()).toList(),
    };
  }
}
