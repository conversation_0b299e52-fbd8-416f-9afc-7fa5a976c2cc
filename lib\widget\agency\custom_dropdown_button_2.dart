import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

getColor(String status) {
  switch (status) {
    case 'Da sbloccare':
      return Color.fromRGBO(166, 166, 166, 1);
    case 'Da contattare':
      return Color.fromRGBO(245, 198, 32, 1);
    case 'Contattato':
      return Color.fromRGBO(86, 195, 229, 1);
    case 'Completato':
      return Color.fromRGBO(72, 155, 121, 1);
  }
}

class CustomDropdown2 extends StatefulWidget {
  final bool isMaster;
  final AcquiredContact acquiredContact;
  final Function updateStage;

  const CustomDropdown2({
    Key? key,
    required this.isMaster,
    required this.acquiredContact,
    required this.updateStage,
  }) : super(key: key);

  @override
  State<CustomDropdown2> createState() => _CustomButtonTestState();
}

class _CustomButtonTestState extends State<CustomDropdown2> {
  String? contactStatus;
  GlobalKey? dropdownkey = new GlobalKey();

  @override
  void initState() {
    contactStatus = widget.acquiredContact.contactStage!;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: DropdownButton2(
        isExpanded: true,
        customButton: Container(
          height: 32,
          width: 150,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          decoration: BoxDecoration(
            color: getColor(contactStatus!),
            borderRadius: BorderRadius.circular(7),
          ),
          child: Container(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: NarFormLabelWidget(
                      label: contactStatus!,
                      textColor: Colors.white,
                      fontWeight: 'bold',
                      fontSize: 13),
                ),
                widget.isMaster
                    ? Container()
                    : Icon(
                        Icons.arrow_drop_down,
                        color: Colors.white,
                      )
              ],
            ),
          ),
        ),
        items: widget.isMaster
            ? [
                ...MenuItems.emptyList.map(
                  (item) => DropdownMenuItem<MenuItem>(
                    value: item,
                    child: MenuItems.buildItem(item),
                  ),
                ),
              ]
            : [
                ...MenuItems.firstItems.map(
                  (item) => DropdownMenuItem<MenuItem>(
                    value: item,
                    child: MenuItems.buildItem(item),
                  ),
                ),
              ],
        onChanged: (value) {
          value as MenuItem;
          setState(() {
            widget.acquiredContact.contactStage = value.text;
            contactStatus = value.text;
          });
          MenuItems.onChanged(
              context, value, widget.acquiredContact, widget.updateStage);
        },
        menuItemStyleData: MenuItemStyleData(
          height: 48,
          padding: const EdgeInsets.only(left: 16, right: 16),
        ),
        dropdownStyleData: DropdownStyleData(
          width: 200,
          padding: const EdgeInsets.symmetric(vertical: 6),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(7),
            color: Colors.white,
          ),
          elevation: 8,
          offset: const Offset(0, 8),
        ),
        // itemHeight: 48,
        // itemPadding: const EdgeInsets.only(left: 16, right: 16),
        // dropdownWidth: 200,
        // dropdownPadding: const EdgeInsets.symmetric(vertical: 6),
        // dropdownDecoration: BoxDecoration(
        //   borderRadius: BorderRadius.circular(7),
        //   color: Colors.white,
        // ),
        // dropdownElevation: 8,
        // offset: const Offset(0, 8),
      ),
    );
  }
}

class MenuItem {
  final String text;
  final IconData icon;

  const MenuItem({
    required this.text,
    required this.icon,
  });
}

class MenuItems {
  static const locked = MenuItem(text: 'Da sbloccare', icon: Icons.home);
  static const unlocked = MenuItem(text: 'Da contattare', icon: Icons.share);
  static const contacted = MenuItem(text: 'Contattato', icon: Icons.settings);
  static const completed = MenuItem(text: 'Completato', icon: Icons.logout);

  static const List<MenuItem> emptyList = [];

  static const List<MenuItem> firstItems = [
    locked,
    unlocked,
    contacted,
    completed
  ];

  static Widget buildItem(MenuItem item) {
    return Row(
      children: [
        Text(
          item.text,
          style:
              const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
        ),
        const SizedBox(
          width: 10,
        ),
        Container(
          height: 8,
          width: 8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(7),
            color: getColor(item.text),
          ),
        ),
      ],
    );
  }

  static onChanged(BuildContext context, MenuItem item,
      AcquiredContact acquiredContact, Function updateStage) {
    updateStage(acquiredContact, item.text);
    return;
  }
}
