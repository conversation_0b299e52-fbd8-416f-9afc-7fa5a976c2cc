import 'dart:developer';
import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/professionals.dart';
import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/classes/property.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/common_utils.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/button.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/immagina_project_data_insertion_pupup.dart';
import 'package:newarc_platform/widget/UI/immagina_professionals_data_insertion_pupup.dart';
import 'package:newarc_platform/widget/UI/immagina_smart_data_insertion_pupup.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/tab/popup_menu_button.dart';
import 'package:newarc_platform/widget/UI/tab/status_widget.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:path/path.dart' as p;
import 'package:flutter/services.dart';
import 'package:newarc_platform/utils/downloadAdBrochurePDF.dart';
import 'package:newarc_platform/utils/downloadAdImageBrochure.dart';
import 'dart:js' as js;

import '../../app_const.dart';

class InsideRequest {
  final String? value, key;
  bool? isSubmitted, isRequestBlocked;

  InsideRequest({
    required this.value,
    required this.key,
    this.isSubmitted = false,
    this.isRequestBlocked = false,
  });
}

class InsideRequestView extends StatefulWidget {
  InsideRequestView({
    super.key,
    this.updateViewCallback,
    this.initialFetchProperties,
    this.projectFirebaseId,
    this.agencyUser,
    this.professionalsUser,
    this.projectArguments = const {},
    this.isFromRequest = true,
    this.isForProfessionals = false,
    this.isSmart = false,
  });

  final bool? isFromRequest;
  final Map? projectArguments;
  final String? projectFirebaseId;
  final Function? initialFetchProperties;
  final AgencyUser? agencyUser;
  final ProfessionalsUser? professionalsUser;
  final Function? updateViewCallback;
  final bool? isForProfessionals;
  final bool? isSmart;

  @override
  State<InsideRequestView> createState() => _InsideRequestViewState();
}

class _InsideRequestViewState extends State<InsideRequestView> {
  bool isExpanded = true;
  bool isProjectNewarcExpanded = true;
  int lastCompletedIndex = -1;

  late List<InsideRequest> insideRequest;

  late List<XFile> planimetryImages = [];
  late List<Map<String, dynamic>> picturesImages = [];

  ImmaginaProject immaginaProject = ImmaginaProject.empty();
  bool loading = false;
  List<String> allowedPlanimetryExtensions = ['dwg', 'pdf', 'jpg', 'jpeg'];
  List<String> allowedPicturesExtensions = ['jpg', 'jpeg'];
  
  Property adData = Property.empty();

  bool downloadingPhotos = false;
  bool downloadingRender = false;
  bool downloadingVideo = false;
  bool downloadingPlan = false;
  bool downloadingImages = false;
  bool downloadingPDF = false;
  NumberFormat localCurrencyFormat = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);

  @override
  void initState() {
    log("projectFirebaseId ==> ${widget.projectFirebaseId}");
    initialFetch();
    
    super.initState();
  }

  Future<void> fetchAdData() async {

    if( immaginaProject.propertyId != null ) {

      
      DocumentSnapshot<Map<String, dynamic>> adDataDocRef = await FirebaseFirestore.instance
      .collection(appConfig.COLLECT_NEWARC_HOME)
      .doc(immaginaProject.propertyId)
      .get();

      if( adDataDocRef.exists ) {
        adData = Property.fromDocument(adDataDocRef);
        
      }

    }
  }

  void updateRequestStatus(ImmaginaProject project, List<InsideRequest> insideRequest) {
    if (project.isForProfessionals ?? false) {
      for (var request in insideRequest) {
        switch (request.key) {
          case "localizzazione":
            request.isSubmitted = project.localizzazione() && project.linkingAgency();
            break;
          case "info-generali":
            request.isSubmitted = (project.childrenProjects != null && project.childrenProjects!.isNotEmpty) 
              ? project.childrenProjects?.every((child) => child.info_generali()) 
              : false;
            break;
          case "descrizione":
            request.isSubmitted = project.descrizione();
            break;
          case "caratteristiche":
            request.isSubmitted = (project.childrenProjects != null && project.childrenProjects!.isNotEmpty) 
              ? project.childrenProjects?.every((child) => child.checkDotazioni())
              : false;
            break;
          case "planimetria":
            request.isSubmitted = (project.childrenProjects != null && project.childrenProjects!.isNotEmpty) 
              ? project.childrenProjects?.every((child) => child.planimetria())
              : false;
            request.isRequestBlocked = immaginaProject.blockedSection?.contains('Planimetrie non conformi');
            break;
          case "materiali":
            request.isSubmitted = project.wantsMaterialNotes
            ? (project.childrenProjects != null && project.childrenProjects!.isNotEmpty) 
              ? project.childrenProjects?.every((child) => child.materialsNotes != null && child.materialsNotes != "")
              : false
            : true;
            break;
          case "indicazioni-speciali":
            request.isSubmitted = project.info_aggiuntive();
            break;
          case "optionals":
            request.isSubmitted = project.requestStatus == 'da completare' ? false : true;
            break;
          default:
            break;
        }
      }
    } else if (project.isSmart ?? false){
      for (var request in insideRequest) {
        switch (request.key) {
          case "localizzazione":
            request.isSubmitted = project.localizzazione();
            break;
          case "input-picture":
            request.isSubmitted = project.fotografie();
            break;
          case "select-mode":
            request.isSubmitted = project.checkSmartServiceType();
            break;
          case "select-style":
            request.isSubmitted = project.checkSmartServiceStyle();
            break;
          case "complete-request":
            request.isSubmitted = project.receivedPayment == true;
            break;
          default:
            break;
        }
      }
    } else {
      for (var request in insideRequest) {
        switch (request.key) {
          case "localizzazione":
            request.isSubmitted = project.localizzazione();
            break;
          case "info-generali":
            request.isSubmitted = project.info_generali();
            break;
          case "descrizione":
            request.isSubmitted = project.descrizione();
            break;
          case "caratteristiche":
            request.isSubmitted = project.checkDotazioni();
            break;
          case "planimetria":
            request.isSubmitted = project.planimetria();
            request.isRequestBlocked = immaginaProject.blockedSection?.contains('Planimetrie non conformi');
            break;
          case "fotografie":
            request.isSubmitted = project.fotografie();
            request.isRequestBlocked = immaginaProject.blockedSection?.contains('Fotografie non conformi');
            break;
          case "indicazioni-speciali":
            request.isSubmitted = project.info_aggiuntive();
            break;
          default:
            break;
        }
      }
    }

    for (int i = 0; i < insideRequest.length; i++) {
      if (insideRequest[i].isSubmitted ?? true) {
        lastCompletedIndex = i;
      }
    }
  }

  initialFetch() async {
    try {
      setState(() {
        loading = true;
      });
      insideRequest = widget.isForProfessionals ?? false
      ? <InsideRequest>[
          InsideRequest(value: "Localizzazione", key: 'localizzazione'),
          InsideRequest(value: "Info generali", key: 'info-generali'),
          InsideRequest(value: "Descrizione", key: 'descrizione'),
          InsideRequest(value: "Caratteristiche", key: 'caratteristiche'),
          InsideRequest(value: "Planimetria", key: 'planimetria'),
          InsideRequest(value: "Materiali", key: 'materiali'),
          InsideRequest(value: "Info aggiuntive", key: 'indicazioni-speciali'),
          InsideRequest(value: "Optionals", key: 'optionals'),
        ]
      : widget.isSmart ?? false
        ? <InsideRequest>[
            InsideRequest(value: "Localizzazione", key: 'localizzazione'),
            InsideRequest(value: "Carica foto", key: 'input-picture'),
            InsideRequest(value: "Tipologia", key: 'select-mode'),
            InsideRequest(value: "Stile", key: 'select-style'),
            InsideRequest(value: "Completa Richiesta", key: 'complete-request'),
          ]
        : <InsideRequest>[
          InsideRequest(value: "Localizzazione", key: 'localizzazione'),
          InsideRequest(value: "Info generali", key: 'info-generali'),
          InsideRequest(value: "Descrizione", key: 'descrizione'),
          InsideRequest(value: "Caratteristiche", key: 'caratteristiche'),
          InsideRequest(value: "Planimetria", key: 'planimetria'),
          InsideRequest(value: "Fotografie", key: 'fotografie'),
          InsideRequest(value: "Info aggiuntive", key: 'indicazioni-speciali'),
        ];

      DocumentSnapshot<Map<String, dynamic>> collectionSnapshot;

      collectionSnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_IMMAGINA_PROJECTS)
          .doc(widget.projectFirebaseId).get();

      if (collectionSnapshot.data() != null) {
        immaginaProject.copy(
          ImmaginaProject.fromDocument(collectionSnapshot.data()!, widget.projectFirebaseId!),
        );
        updateRequestStatus(immaginaProject, insideRequest);
        for (var request in insideRequest) {
          print("Section: ${request.value}, Completed: ${request.isSubmitted}, Blocked: ${request.isRequestBlocked}");
        }
        
        await fetchAdData();

      }

      setState(() {
        loading = false;
      });
    } catch (e) {
      print(e);
      setState(() {
        loading = false;
      });
    }
  }

  Future<void> deleteArchivedProject(String projectId) async {
    try {
      await FirebaseFirestore.instance.collection(appConfig.COLLECT_IMMAGINA_PROJECTS).doc(projectId).update({'isArchived': true});
      log("Project $projectId successfully marked as archived.");
    } catch (e) {
      print("Error archiving project $projectId: $e");
    }
  }

  

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: LayoutBuilder(builder: (context, constraints) {
        return loading
            ? Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              onTap: () {
                                widget.projectArguments?.clear();
                                widget.projectArguments?.addAll({
                                  'isFromRequest': true,
                                });
                                widget.professionalsUser == null
                                ? widget.updateViewCallback!('progetti-attivi', projectArguments: widget.projectArguments)
                                : widget.updateViewCallback!('immagina-progetti-attivi', projectArguments: widget.projectArguments);
                              },
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                      'assets/icons/arrow_left.svg',
                                      height: 15,
                                      color: Colors.black),
                                  SizedBox(width: 15,),
                                  NarFormLabelWidget(
                                    label: 'Tutti i progetti',
                                    fontSize: 15,
                                    fontWeight: '600',
                                    textDecoration: TextDecoration.underline,
                                    textColor: AppColor.black,
                                  ),
                                ],
                              ),
                            ),
                          ),

                          NarFormLabelWidget(
                            label: "${immaginaProject.streetName ?? 'noStreetName'}, ${immaginaProject.streetNumber ?? 'noStreetNum'} ${immaginaProject.housingUnit != null ? '-' : ''} ${immaginaProject.housingUnit ?? ''}",
                            fontSize: 20,
                            fontWeight: '700',
                            textColor: Colors.black,
                          ),

                          NarFormLabelWidget(
                            label: immaginaProject.projectId ?? "",
                            fontSize: 12,
                            fontWeight: '600',
                            textColor: Color(0xFF6C6C6C),
                          ),

                        ]
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    AnimatedContainer(
                      duration: Duration(milliseconds: 800),
                      padding: EdgeInsets.only(left: 22, right: 22, top: 22, bottom: 8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        border: Border.all(
                          width: 1.7,
                          color: Color(
                            0xffC3C9CF,
                          ),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Row(
                                  children: [
                                    NarFormLabelWidget(
                                      label: "Richiesta di progetto",
                                      fontSize: 16,
                                      fontWeight: '700',
                                      textColor: Colors.black,
                                    ),
                                    SizedBox(
                                      width: 30,
                                    ),
                                    Visibility(
                                      visible: immaginaProject.requestStatus.toLowerCase() == CommonUtils.daCompletare,
                                      child: InkWell(
                                        highlightColor: Colors.transparent,
                                        splashColor: Colors.transparent,
                                        splashFactory: NoSplash.splashFactory,
                                        hoverColor: Colors.transparent,
                                        onTap: () {
                                          showDeleteDialog();
                                        },
                                        child: Row(
                                          children: [
                                            SvgPicture.asset(
                                              "assets/icons/trash.svg",
                                              color: Color(0xff656565),
                                              height: 25,
                                            ),
                                            SizedBox(
                                              width: 5,
                                            ),
                                            NarLinkWidget(
                                              onClick: () {
                                                showDeleteDialog();
                                              },
                                              text: "Elimina richiesta",
                                              textColor: Color(0xff656565),
                                              fontWeight: '700',
                                              fontSize: 12,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Row(
                                children: [
                                  StatusWidget(
                                    status: immaginaProject.requestStatus.toLowerCase(),
                                    statusColor: CommonUtils.getColor(immaginaProject.requestStatus.toLowerCase()),
                                  ),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  if (immaginaProject.requestStatus.toLowerCase() == CommonUtils.bloccata)
                                    PopupMenuOnHover(
                                      note: immaginaProject.blockedNotes,
                                      reason: immaginaProject.blockedSection ?? "",
                                      isForProfessionals: immaginaProject.isForProfessionals ?? false,
                                    ),
                                ],
                              ),
                            ],
                          ),
                          if (isExpanded) ...[
                            Column(
                              children: [
                                SizedBox(
                                  height: 24,
                                ),
                                Center(
                                  child: Wrap(
                                    alignment: WrapAlignment.start,
                                    crossAxisAlignment: WrapCrossAlignment.center,
                                    spacing: 15,
                                    runSpacing: 15,
                                    children: insideRequest.map((v) {
                                      int currentIndex = insideRequest.indexOf(v);
                                      bool isNewArcPicture =
                                          ((immaginaProject.wantsNewarcPictures == true && v.key == "fotografie") || (immaginaProject.wantsNewarcPlanimetry == true && v.key == "planimetria"));
                                      int lastCompletedIndex = insideRequest.lastIndexWhere((element) => element.isSubmitted == true);
                                      bool isSmartStyleNotNecessary = ((widget.isSmart ?? false) && v.key == 'select-style') && (immaginaProject.smartServiceType == 'nakedInteriors');

                                      Color getBorderColor() {
                                        if (isNewArcPicture) {
                                          return Color(0xffD7D7D7);
                                        } else if (v.isRequestBlocked == true) {
                                          return Color(0xffE82525);
                                        } else if (immaginaProject.requestStatus.toLowerCase() != CommonUtils.daCompletare) {
                                          return Theme.of(context).primaryColor;
                                        } else if (v.isSubmitted == true) {
                                          return Colors.transparent;
                                        } else {
                                          return Color(0xffD6D6D6);
                                        }
                                      }

                                      Color getBackgroundColor() {
                                        if (isNewArcPicture) {
                                          return AppColor.white;
                                        } else if (v.isRequestBlocked == true) {
                                          return Color(0xffE82525);
                                        } else if (immaginaProject.requestStatus.toLowerCase() != CommonUtils.daCompletare) {
                                          return Theme.of(context).primaryColor;
                                        } else if (v.isSubmitted == true) {
                                          return Color(0xff9CBFDF);
                                        } else {
                                          return Colors.transparent;
                                        }
                                      }

                                      return Container(
                                        width: 115,
                                        height: 120,
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            width: 1,
                                            color: getBorderColor(),
                                          ),
                                          borderRadius: BorderRadius.circular(10),
                                          color: getBackgroundColor(),
                                        ),
                                        padding: EdgeInsets.all(10),
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          children: [
                                            SizedBox(
                                              height: 6,
                                            ),
                                            SvgPicture.asset(
                                              isNewArcPicture || isSmartStyleNotNecessary
                                                  ? "assets/agency-black-logo.svg"
                                                  : v.isSubmitted == false
                                                    ? "assets/icons/wrong.svg"
                                                    : v.isRequestBlocked == true
                                                    ? "assets/icons/wrong.svg"
                                                      : "assets/icons/right.svg",
                                              height: isNewArcPicture || isSmartStyleNotNecessary ? 20 : 15,
                                              color: isNewArcPicture || isSmartStyleNotNecessary
                                                  ? null
                                                  : v.isSubmitted == true
                                                      ? AppColor.white
                                                      : Color(0xffD7D7D7),
                                            ),
                                            SizedBox(
                                              height: 16,
                                            ),
                                            NarFormLabelWidget(
                                              label: v.value,
                                              fontSize: 12,
                                              fontWeight: '700',
                                              textColor: isNewArcPicture || isSmartStyleNotNecessary
                                                  ? AppColor.black
                                                  : (v.isSubmitted == true)
                                                      ? Colors.white
                                                      : (v.isRequestBlocked == true)
                                                          ? AppColor.white
                                                          : AppColor.black,
                                            ),
                                            SizedBox(
                                              height: 10,
                                            ),
                                            if (immaginaProject.requestStatus.toLowerCase() == CommonUtils.daCompletare) ...[
                                              isNewArcPicture || isSmartStyleNotNecessary
                                                  ? Padding(
                                                      padding: const EdgeInsets.only(top: 10.0),
                                                      child: NarFormLabelWidget(
                                                        label: isNewArcPicture ? "Acquistato" : "Non necessario",
                                                        fontSize: 10,
                                                        fontWeight: '600',
                                                        textColor: Color(0xff838383),
                                                      ),
                                                    )
                                                  : (widget.isSmart ?? false)
                                                    ? currentIndex == lastCompletedIndex + 1
                                                        ? _buildResumeButton(v)
                                                        : SizedBox.shrink()
                                                    : v.isSubmitted == true
                                                        ? _buildModifyButton(v)
                                                        : _buildResumeButton(v)
                                                        // : currentIndex == lastCompletedIndex + 1
                                                        //     ? _buildResumeButton(v)
                                                        //     : SizedBox.shrink(),
                                            ] else if (immaginaProject.requestStatus.toLowerCase() == CommonUtils.bloccata && v.isRequestBlocked == true) ...[
                                              isNewArcPicture
                                                  ? Padding(
                                                      padding: const EdgeInsets.only(top: 10.0),
                                                      child: NarFormLabelWidget(
                                                        label: "Acquistato",
                                                        fontSize: 10,
                                                        fontWeight: '600',
                                                        textColor: Color(0xff838383),
                                                      ),
                                                    )
                                                  : _buildModifyButton(v),
                                            ] else if (immaginaProject.requestStatus.toLowerCase() == CommonUtils.inAnalisi) ...[
                                              isNewArcPicture || isSmartStyleNotNecessary
                                                  ? Padding(
                                                      padding: const EdgeInsets.only(top: 10.0),
                                                      child: NarFormLabelWidget(
                                                        label:  isSmartStyleNotNecessary ? "Non necessario" : "Acquistato",
                                                        fontSize: 10,
                                                        fontWeight: '600',
                                                        textColor: Color(0xff838383),
                                                      ),
                                                    )
                                                  : SizedBox(),
                                            ]
                                          ],
                                        ),
                                      );
                                    }).toList(),
                                  ),
                                ),
                              ],
                            ),
                          ],
                          Column(
                            children: [
                              SizedBox(
                                height: 10,
                              ),
                              Align(
                                alignment: Alignment.centerRight,
                                child: InkWell(
                                  highlightColor: Colors.transparent,
                                  splashColor: Colors.transparent,
                                  splashFactory: NoSplash.splashFactory,
                                  hoverColor: Colors.transparent,
                                  onTap: () {
                                    setState(() {
                                      isExpanded = !isExpanded;
                                    });
                                  },
                                  child: Image.asset(
                                    isExpanded ? 'assets/icons/arrow_up.png' : 'assets/icons/arrow_down.png',
                                    height: 15,
                                    color: Color(0xff6C6C6C),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
      }),
    );
  }

  Future<void> reuploadPictures() async {
    try {
      Reference ref = FirebaseStorage.instance.ref('${appConfig.COLLECT_IMMAGINA_PROJECTS}/${widget.projectFirebaseId}/fotografie');

      final listResult = await ref.listAll();
      for (var element in listResult.items) {
        await element.delete();
      }

      await FirebaseFirestore.instance.collection('immaginaProjects').doc(widget.projectFirebaseId).update({'pictures': []});

      List<Map<String, dynamic>> updatedPictures = [];
      for (int i = 0; i < picturesImages.length; i++) {
        String pictureFilename = 'fotografie/${picturesImages[i]['tag'] ?? "notag"}_$i${p.extension(picturesImages[i]['file']!.name)}';
        await uploadImageToStorage(
          appConfig.COLLECT_IMMAGINA_PROJECTS,
          widget.projectFirebaseId ?? "",
          pictureFilename,
          picturesImages[i]['file'],
        );
        updatedPictures.add({
          'tag': picturesImages[i]['tag'],
          'file': pictureFilename,
        });
      }
      await FirebaseFirestore.instance.collection('immaginaProjects').doc(widget.projectFirebaseId).update({
        'pictures': updatedPictures,
        'requestStatus': CommonUtils.inAnalisi,
        'blockedSection': null,
        'blockNotes': null,
      });
      Fluttertoast.showToast(
        msg: "Pictures re-uploaded and updated successfully!",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.green,
        textColor: Colors.white,
        fontSize: 16.0,
      );
    } catch (e) {
      Fluttertoast.showToast(
        msg: "Error while re-uploading pictures: $e",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.red,
        textColor: Colors.white,
        fontSize: 16.0,
      );
    }
  }

  Future<void> reuploadPlanemetira() async {
    try {
      Reference ref = FirebaseStorage.instance.ref('${appConfig.COLLECT_IMMAGINA_PROJECTS}/${widget.projectFirebaseId}/planimetrie');

      final listResult = await ref.listAll();
      if (listResult.items.isNotEmpty) {
        for (var element in listResult.items) {
          await element.delete();
        }
      }

      await FirebaseFirestore.instance.collection('immaginaProjects').doc(widget.projectFirebaseId).update({'planimetry': []});

      List<String> updatedPictures = [];

      for (int i = 0; i < planimetryImages.length; i++) {
        String pictureFilename = 'planimetrie/planimetry_${i + 1}' + p.extension(planimetryImages[i].name);
        await uploadImageToStorage(
          appConfig.COLLECT_IMMAGINA_PROJECTS,
          widget.projectFirebaseId ?? "",
          pictureFilename,
          planimetryImages[i],
        );
        updatedPictures.add(pictureFilename);
      }
      await FirebaseFirestore.instance.collection('immaginaProjects').doc(widget.projectFirebaseId).update({
        'planimetry': updatedPictures,
        'requestStatus': CommonUtils.inAnalisi,
        'blockedSection': null,
        'blockNotes': null,
      });
      Fluttertoast.showToast(
        msg: "Planimetry re-uploaded and updated successfully!",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.green,
        textColor: Colors.white,
        fontSize: 16.0,
      );
    } catch (e) {
      print(e);
      Fluttertoast.showToast(
        msg: "Error while re-uploading planimetry: $e",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.red,
        textColor: Colors.white,
        fontSize: 16.0,
      );
    }
  }

  Future<UploadTask?> uploadImageToStorage(String directory, String docId, String filename, XFile? file) async {
    if (file == null) {
      return null;
    }

    UploadTask uploadTask;


    Reference ref = FirebaseStorage.instance.ref().child(directory).child(docId).child(filename);


    final metadata = SettableMetadata(
      contentType: file.mimeType,
      customMetadata: {'picked-file-path': file.path},
    );

    uploadTask = ref.putData(await file.readAsBytes(), metadata);
    uploadTask.snapshotEvents.listen((TaskSnapshot taskSnapshot) {
      switch (taskSnapshot.state) {
        case TaskState.running:
          final progress = 100.0 * (taskSnapshot.bytesTransferred / taskSnapshot.totalBytes);
          // print("Upload is $progress% complete.");
          break;
        case TaskState.paused:
          // print("Upload is paused.");
          break;
        case TaskState.canceled:
          // print("Upload was canceled");
          break;
        case TaskState.error:
          // Handle unsuccessful uploads
          break;
        case TaskState.success:
          // Handle successful uploads on complete
          // ...
          break;
      }
    });

    return Future.value(uploadTask);
  }

  ///Fotografie
  Future<void> _uploadPhotografileDialog() {
    return showDialog(
      context: context,
      builder: (context) {
        return Center(
          child: Container(
            width: MediaQuery.of(context).size.width / 1.8,
            height: MediaQuery.of(context).size.height / 2,
            child: BaseNewarcPopup(
              buttonText: 'Invia Foto',
              title: "Ricarica Fotografie",
              onPressed: () async {
                if (picturesImages.isNotEmpty) {
                  await reuploadPictures();
                  initialFetch();
                }
              },
              column: _buildPicturesContainer(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPicturesContainer() {
    return StatefulBuilder(
      builder: (context, setState) {
        return Container(
          height: MediaQuery.of(context).size.height / 3,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Color(0xffd3d3d3)),
          ),
          child: Stack(
            children: [
              SingleChildScrollView(
                padding: EdgeInsets.only(top: 45, bottom: 10, left: 8, right: 100),
                child: Wrap(
                  spacing: 12,
                  runSpacing: 20,
                  children: picturesImages.map((data) => _buildPhotosPreview(data, setState)).toList(),
                ),
              ),
              Positioned(
                top: 8,
                right: 8,
                child: BaseNewarcButton(
                  color: Color(0xffE5E5E5),
                  textColor: Colors.black,
                  buttonText: "Carica",
                  fontWeight: '700',
                  fontSize: 14,
                  height: 35,
                  onPressed: () async {
                    await _showUploadPicturesDialog();
                    setState(() {});
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPhotosPreview(dynamic data, void Function(void Function()) setState) {
    return FutureBuilder<Uint8List>(
      future: data['file'].readAsBytes(),
      builder: (context, snapshot) {
        TextEditingController tempController = TextEditingController(text: data['tag'] ?? "");

        if (snapshot.connectionState == ConnectionState.done && snapshot.hasData) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.memory(
                      snapshot.data!,
                      width: 150,
                      height: 150,
                      fit: BoxFit.cover,
                    ),
                  ),
                  Positioned(
                    top: 3,
                    right: 3,
                    child: GestureDetector(
                      onTap: () => setState(() {
                        picturesImages.remove(data);
                      }),
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Theme.of(context).primaryColorDark,
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(3),
                          child: SvgPicture.asset(
                            'icons/close-popup.svg',
                            height: 10,
                            color: Theme.of(context).unselectedWidgetColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 5,
              ),
              Container(
                width: 150,
                child: NarSelectBoxWidget(
                  label: 'Seleziona ambiente',
                  controller: tempController,
                  onChanged: (value) {
                    data['tag'] = tempController.text;
                    setState(() {});
                  },
                  options: List.of(roomsList)..sort((a, b) => a.toLowerCase().compareTo(b.toLowerCase())),
                  contentPadding: EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 2),
                ),
              ),
            ],
          );
        }
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            width: 80,
            height: 80,
            color: Colors.grey[300]!,
            child: Center(
              child: Icon(Icons.hourglass_empty, color: Colors.white),
            ),
          );
        } else {
          return Container(
            width: 80,
            height: 80,
            color: Colors.red,
            child: Center(
              child: Icon(Icons.error, color: Colors.white),
            ),
          );
        }
      },
    );
  }

  Future<void> getPictureFromGallery() async {
    bool wrongExtension = false;
    final filesList = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.custom,
      allowedExtensions: allowedPicturesExtensions,
    );

    if (filesList != null) {
      for (var file in filesList.files) {
        if (allowedPicturesExtensions.contains(file.extension)) {
          picturesImages.add({'tag': null, 'file': file.xFile});
        } else {
          wrongExtension = true;
        }
      }
    }

    if (wrongExtension) {
      _showErrorDialog(context, "Errore: Estensione file non valida", "Inserisci file di fotografie con estensione: ${allowedPicturesExtensions.join(', ')}");
    }
  }

  Future<void> _showUploadPicturesDialog() {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) {
        return Center(
          child: BaseNewarcPopup(
            title: 'Carica immagini',
            noButton: true,
            column: GestureDetector(
              onTap: () async {
                await getPictureFromGallery();
                Navigator.of(context).pop(true);
              },
              child: Container(
                height: 300,
                width: 400,
                decoration: BoxDecoration(
                  color: Color.fromRGBO(240, 240, 240, 1),
                  borderRadius: BorderRadius.all(Radius.circular(10)),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.upload_file, size: 60, color: Color(0xFF808080)),
                    SizedBox(height: 30),
                    NarFormLabelWidget(
                      label: "Clicca per caricare",
                      fontWeight: '700',
                      fontSize: 18,
                      textColor: Color(0xFF808080),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _uploadPlanimetriaDialog() {
    return showDialog(
      context: context,
      builder: (context) {
        return Center(
          child: Container(
            width: MediaQuery.of(context).size.width / 1.8,
            height: MediaQuery.of(context).size.height / 1.7,
            child: BaseNewarcPopup(
              onPressed: () async {
                if (planimetryImages.isNotEmpty) {
                  await reuploadPlanemetira();
                  initialFetch();
                }
              },
              buttonText: 'Invia Planimetrie',
              title: "Ricarica Planimetrie",
              column: StatefulBuilder(
                builder: (context, setState) {
                  return Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: MediaQuery.of(context).size.height / 60),
                        child: Container(
                          height: MediaQuery.of(context).size.height / 2.5,
                          child: Column(
                            children: [
                              SizedBox(height: 10),
                              Container(
                                height: (MediaQuery.of(context).size.height / 3) - 22,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Color(0xffd3d3d3),
                                  ),
                                ),
                                child: Stack(
                                  children: [
                                    SingleChildScrollView(
                                      padding: EdgeInsets.only(top: 45, bottom: 10, left: 8, right: 100),
                                      child: Wrap(
                                        spacing: 12,
                                        runSpacing: 20,
                                        children: planimetryImages.map((imageFile) => _buildPlanmitryPreview(imageFile, setState)).toList(),
                                      ),
                                    ),
                                    Positioned(
                                      top: 8,
                                      right: 8,
                                      child: BaseNewarcButton(
                                        color: Color(0xffE5E5E5),
                                        textColor: Colors.black,
                                        buttonText: "Carica",
                                        fontWeight: '700',
                                        fontSize: 14,
                                        height: 35,
                                        onPressed: () async {
                                          await _showUploadPlanimetryDialog(context);
                                          setState(() {});
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPlanmitryPreview(XFile imageFile, void Function(void Function()) setState) {
    return FutureBuilder<Uint8List>(
      future: imageFile.readAsBytes(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done && snapshot.hasData) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: imageFile.name.split('.').last.toLowerCase() == 'dwg'
                        ? Image.asset(
                            'assets/icons/dwg.png',
                            color: Color(0xffa6a6a6),
                            width: 150,
                            height: 150,
                            fit: BoxFit.cover,
                          )
                        : imageFile.name.split('.').last.toLowerCase() == 'pdf'
                            ? Image.asset(
                                'assets/icons/pdf.png',
                                color: Color(0xffa6a6a6),
                                width: 150,
                                height: 150,
                                fit: BoxFit.cover,
                              )
                            : Image.memory(
                                snapshot.data!,
                                width: 150,
                                height: 150,
                                fit: BoxFit.cover,
                              ),
                  ),
                  Positioned(
                    top: 3,
                    right: 3,
                    child: GestureDetector(
                      onTap: () {
                        planimetryImages.remove(imageFile);
                        setState(() {});
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Theme.of(context).primaryColorDark,
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(3),
                          child: SvgPicture.asset(
                            'icons/close-popup.svg',
                            height: 10,
                            color: Theme.of(context).unselectedWidgetColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          );
        } else if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            width: 90,
            height: 90,
            color: Colors.grey[300],
            child: Center(child: CircularProgressIndicator()),
          );
        } else {
          return Container(
            width: 90,
            height: 90,
            color: Colors.grey[300],
            child: Center(child: Icon(Icons.error, color: Colors.red)),
          );
        }
      },
    );
  }

  getPlanimetryFromGallery(BuildContext context) async {
    bool wrongExtension = false;
    bool wrongSize = false;
    await FilePicker.platform
        .pickFiles(
      allowMultiple: true,
      type: FileType.custom,
      allowedExtensions: allowedPlanimetryExtensions,
    )
        .then((filesList) {
      if (filesList != null) {
        setState(() {
          filesList.files.forEach((file) {
            if (file.size < 10 * 1024 * 1024) {
              if (allowedPlanimetryExtensions.contains(file.extension)) {
                planimetryImages.add(file.xFile);
              } else {
                wrongExtension = true;
              }
            } else {
              wrongSize = true;
            }
          });
        });
      }
      if (wrongExtension) {
        _showErrorDialog(context, 'Errore: Estensione file non valida', 'Inserisci file di planimetrie con estensione: ${allowedPlanimetryExtensions.join(', ')}');
      }
      if (wrongSize) {
        _showErrorDialog(context, 'Errore: Dimensione file non valida', 'Inserisci file di planimetrie con dimensione massima 10 MB');
      }
    });
  }

  void _showErrorDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Center(
          child: BaseNewarcPopup(
            title: title,
            column: Column(
              children: [
                Text(message),
              ],
            ),
          ),
        );
      },
    );
  }

  _showUploadPlanimetryDialog(BuildContext context) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) {
        return Center(
          child: BaseNewarcPopup(
              title: 'Carica immagini',
              noButton: true,
              column: StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
                return SingleChildScrollView(
                  child: Container(
                    height: 300,
                    width: 500,
                    child: Column(
                      children: [
                        Expanded(
                          flex: 40,
                          child: GestureDetector(
                            onTap: () async {
                              await getPlanimetryFromGallery(context);
                              setState(() {});
                              Navigator.of(context).pop(true);
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                  color: Color.fromRGBO(240, 240, 240, 1),
                                  //shape: BoxShape.circle,
                                  borderRadius: BorderRadius.all(Radius.circular(10))),
                              width: 400,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.upload_file,
                                    size: 60,
                                    color: Color.fromRGBO(128, 128, 128, 1),
                                  ),
                                  SizedBox(
                                    height: 30,
                                  ),
                                  NarFormLabelWidget(
                                    label: "Clicca per caricare",
                                    fontWeight: '700',
                                    fontSize: 18,
                                    textColor: Color.fromRGBO(128, 128, 128, 1),
                                    textAlign: TextAlign.center,
                                  )
                                ],
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 30,
                        ),
                      ],
                    ),
                  ),
                );
              })),
        );
      },
    );
  }

  Widget _buildModifyButton(InsideRequest v) {
    return NarLinkWidget(
      text: "Modifica",
      textColor: Colors.white,
      fontWeight: '700',
      textAlign: TextAlign.center,
      fontSize: 12,
      onClick: () {
        if (v.isRequestBlocked == true && v.key == 'fotografie') {
          _uploadPhotografileDialog();
        } else if (v.isRequestBlocked == true && v.key == 'planimetria' && !(widget.isForProfessionals ?? false)) {
          _uploadPlanimetriaDialog();
        } else {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) {
              return Center(
                child:
                widget.isForProfessionals ?? false
                ? ImmaginaProfessionalsDataInsertionPopup(
                    procedureStep: v.key ?? "initial",
                    agencyUser: widget.agencyUser,
                    professionalsUser: widget.professionalsUser,
                    project: immaginaProject,
                    isBlockedSection: 
                      (v.isRequestBlocked == true && v.key == 'planimetria' && (widget.isForProfessionals ?? false))
                       ? true
                       : false,
                    onClose: initialFetch)
                : widget.isSmart ?? false
                  ? ImmaginaSmartDataInsertionPopup(
                    procedureStep:  v.key ?? "initial", 
                    agencyUser: widget.agencyUser,
                    project: immaginaProject,
                    onClose: initialFetch
                    )
                  : ImmaginaDataInsertionPopup(
                    procedureStep: v.key ?? "initial",
                    agencyUser: widget.agencyUser!,
                    project: immaginaProject,
                    onClose: initialFetch,
                )
              );
            },
          );
        }
      },
    );
  }

  Widget _buildResumeButton(dynamic v) {
    return NarButtonWidget(
      textHeight: 1,
      color: Theme.of(context).primaryColor,
      hoverColor: Theme.of(context).primaryColor,
      borderRadius: 100,
      height: 1,
      fontWeight: '600',
      fontSize: 12,
      splashColor: Theme.of(context).primaryColor,
      textColor: Colors.white,
      borderSideColor: Colors.transparent,
      text: 'Riprendi',
      buttonPadding: EdgeInsets.only(left: 14, right: 14),
      onClick: () {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return Center(
              child: widget.isForProfessionals ?? false
                ? ImmaginaProfessionalsDataInsertionPopup(
                  procedureStep: v.key ?? "initial",
                  agencyUser: widget.agencyUser,
                  professionalsUser: widget.professionalsUser,
                  project: immaginaProject,
                  onClose: initialFetch,)
                : widget.isSmart ?? false
                  ? ImmaginaSmartDataInsertionPopup(
                    procedureStep: v.key ?? "initial",
                    agencyUser: widget.agencyUser,
                    project: immaginaProject,
                    onClose: initialFetch,
                    )
                  : ImmaginaDataInsertionPopup(
                    procedureStep: v.key ?? "initial",
                    agencyUser: widget.agencyUser!,
                    project: immaginaProject,
                    onClose: initialFetch,
                    ),
            );
          },
        );
      },
    );
  }

  Future<void> showDeleteDialog() {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Spacer(),
              SizedBox(
                width: 30,
              ),
              NarFormLabelWidget(
                label: "Conferma eliminazione richiesta",
                textColor: AppColor.black,
                fontWeight: '700',
                fontSize: 17,
              ),
              Spacer(),
              SizedBox(
                width: 30,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                  ),
                ),
              ),
            ],
          ),
          titlePadding: EdgeInsets.symmetric(horizontal: 10, vertical: 18),
          contentPadding: EdgeInsets.symmetric(horizontal: 80, vertical: 18),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                "assets/icons/trash.svg",
                color: Color(0xff656565),
                height: 50,
              ),
              SizedBox(
                height: 14,
              ),
              NarFormLabelWidget(
                label: "Confermi di voler eliminare\nquesta richiesta di progetto?",
                textAlign: TextAlign.center,
                textColor: AppColor.black,
                height: 1.5,
                fontWeight: '600',
                fontSize: 16,
              ),
              SizedBox(
                height: 30,
              ),
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: () async {
                    await deleteArchivedProject(immaginaProject.id);
                    Navigator.pop(context);
                    widget.updateViewCallback!(
                      widget.isForProfessionals ?? false ? 'immagina-progetti-attivi' : 'progetti-attivi',
                    );
                  },
                  child: Container(
                    height: 40,
                    width: 188,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: Color(0xffE82525),
                      borderRadius: BorderRadius.circular(7),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20.0),
                      child: NarFormLabelWidget(
                        label: "Elimina richiesta",
                        textAlign: TextAlign.center,
                        textColor: AppColor.white,
                        fontWeight: '600',
                        fontSize: 15,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: AppColor.white,
        );
      },
    );
  }
}
