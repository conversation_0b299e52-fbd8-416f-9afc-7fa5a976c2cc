class NewarcAdMaterialConfiguration {
  String? materialType;
  String? naMaterialId;
  String? materialManufacturerId;
  String? materialCollectionId;
  String? materialVariantId;
  Map? coverImage;
  String? code;
  String? elementConfigurationId;
  String? id;

  //Only for UI
  List? coverImageList;
  Future<List>? collectionFuture;
  Future<List>? naMaterialFuture;


  Map<String, Object?> toMap() {
    return {
      'materialType': materialType,
      'naMaterialId': naMaterialId,
      'materialManufacturerId': materialManufacturerId,
      'materialCollectionId': materialCollectionId,
      'materialVariantId': materialVariantId,
      'coverImage': coverImage,
      'code': code,
      'elementConfigurationId': elementConfigurationId,
      'id': id,
      'coverImageList': coverImageList,
    };
  }

  NewarcAdMaterialConfiguration.empty() {
    this.materialType = '';
    this.naMaterialId = '';
    this.materialCollectionId = '';
    this.materialManufacturerId = '';
    this.materialVariantId = '';
    this.coverImage = {};
    this.code = '';
    this.elementConfigurationId = '';
    this.id = '';
    this.coverImageList = [];
  }

  NewarcAdMaterialConfiguration.fromDocument(Map<String, dynamic> data) {
    try {
      this.materialType = data['materialType'];
      this.naMaterialId = data['naMaterialId'];
      this.materialCollectionId = data['materialCollectionId'];
      this.materialManufacturerId = data['materialManufacturerId'];
      this.materialVariantId = data['materialVariantId'];
      this.coverImage = data['coverImage'];
      this.code = data['code'];
      this.elementConfigurationId = data['elementConfigurationId'];
      this.id = data['id'];
      this.coverImageList = (data['coverImage']["fileName"] != null && data['coverImage']["fileName"] != "") ? [data['coverImage']["fileName"]] : [];
    } catch (e, s) {
      print({ 'NewarcAdMaterialConfiguration Class Error ------->', e, s});
    }
  }
}