import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/utils/inputFormatters.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/classes/professionals.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/basePersonInfo.dart';
import 'package:newarc_platform/pages/work/gestione/professionals/professionals_controller.dart';
import 'package:newarc_platform/widget/UI/address_completion_google.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/image-picker.dart';
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/app_config.dart' as appConfig;


class ProfessionalsInsideView extends StatefulWidget {
  final Function updateViewCallback;
  final Professional professional;
  const ProfessionalsInsideView({super.key, required this.updateViewCallback, required this.professional});

  @override
  State<ProfessionalsInsideView> createState() => _ProfessionalsInsideViewState();
}

class _ProfessionalsInsideViewState extends State<ProfessionalsInsideView> {
  final controller = Get.put<ProfessionalsController>(ProfessionalsController());
  final _formKey = GlobalKey<FormState>();
  ProfessionalsUser? professionalsUser;
  final profilePicture = [];
  String profilePictureFilename = '';

  Future<bool> updateProfessionalActiveStatus(Professional professional) async {
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    return await _db
        .collection(appConfig.COLLECT_PROFESSIONALS)
        .doc(professional.id)
        .update(professional.toMap())
        .then((value) {
      return true;
    }).onError((error, stackTrace) {
      // print({error, stackTrace});
      return false;
    });
  }

  Future<bool> updateData(BuildContext context) async {
    setState(() {
      controller.validationMessage = "";
    });
    widget.professional.companyName = controller.professionalName.text;
    widget.professional.profession = controller.professionalProfession.text;
    widget.professional.email = controller.professionalEmail.text;
    widget.professional.phone = controller.professionalClientPhone.text;
    widget.professional.contactPersonInfo = BasePersonInfo.fromMap(
        {
          'name': controller.contactName.text,
          'surname': controller.contactSurname.text,
          'phone': controller.contactPhone.text,
          'email': controller.contactEmail.text
        }
    );

    widget.professional.legalEntity = controller.professionalLegalEntity.text;
    widget.professional.formationType = controller.formationType.text;
    widget.professional.legalAddressInfo = controller.professionalLegalAddress;
    widget.professional.fiscalCode = controller.professionalFiscalCode.text;
    widget.professional.sdi = controller.professionalBillingCode.text;
    widget.professional.vat = controller.professionalVat.text;
    widget.professional.modificationTimestamp = DateTime.now().millisecondsSinceEpoch;

    final FirebaseFirestore _db = FirebaseFirestore.instance;
    await _db
        .collection(appConfig.COLLECT_PROFESSIONALS)
        .doc(widget.professional.id)
        .update(widget.professional.toMap());

    return true;
  }

  initialFetchProfilePicture() async {
    try {
      // fetch professionalsUser object
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      collectionSnapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_USERS)
          .where('professionalId', isEqualTo: widget.professional.id)
          .get();
      if (collectionSnapshot.docs.length > 0) {
        professionalsUser = ProfessionalsUser.fromDocument(collectionSnapshot.docs.first.data(), collectionSnapshot.docs.first.id);
        setState(() {
          profilePictureFilename = professionalsUser!.profilePicture ?? "";
        });
      }
    } catch (e) {
      print('Error fetching profile picture: $e');
    }
  }

  @override
  void initState() {
    controller.initInsideViewController(widget.professional);
    initialFetchProfilePicture();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var containerWidth = MediaQuery.of(context).size.width * .75;
    return SingleChildScrollView(
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      hoverColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      onPressed: () {
                        controller.clearInsideViewController();
                        widget.updateViewCallback('professionals');
                      },
                      icon: SvgPicture.asset('assets/icons/arrow_left.svg',
                          height: 20, color: Colors.black),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    NarFormLabelWidget(
                      label: widget.professional.printName(),
                      fontSize: 20,
                      fontWeight: 'bold',
                    ),
                  ],
                ),
                Row(
                  children: [
                    NarFormLabelWidget(
                      label: 'Attiva/Disattiva',
                      fontSize: 15,
                      fontWeight: '500',
                    ),
                    Switch(
                      // This bool value toggles the switch.
                      value: widget.professional.isActive!,
                      activeColor: Theme.of(context).primaryColor,
                      onChanged: (bool value) async {
                        // This is called when the user toggles the switch.
                        setState(() {
                          widget.professional.isActive = value;
                        });
                        await updateProfessionalActiveStatus(widget.professional);
                      },
                    ),
                  ],
                )
              ],
            ),
            SizedBox(height: 35),
            NarFormLabelWidget(
              label: 'Dati Professionista',
              fontSize: 18,
              fontWeight: 'bold',
            ),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Switch(
                  value: widget.professional.hasPIva,
                  activeTrackColor: Theme.of(context).primaryColor,
                  inactiveTrackColor: Theme.of(context).primaryColorLight,
                  thumbColor: WidgetStateProperty.all(Colors.white),
                  onChanged: (bool value) {
                    setState(() {
                      widget.professional.hasPIva = value;
                    });
                  },
                ),
                SizedBox(width: 10),
                NarFormLabelWidget(
                  label: 'Registrazione con p.iva',
                  fontSize: 14,
                  fontWeight: '600',
                  textColor: widget.professional.hasPIva ? Colors.black : Colors.grey,
                ),
              ],
            ),
            SizedBox(height: 15),
            widget.professional.hasPIva
            ? Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomTextFormField(
                  label: 'Nome Azienda',
                  controller: controller.professionalName,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Obbligatorio';
                    }
                    return null;
                  },
                ),
              ],
            ) : SizedBox.shrink(),
            widget.professional.hasPIva
            ? SizedBox(
              height: 15,
            ) : SizedBox.shrink(),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: NarSelectBoxWidget(
                    label: 'Professione',
                    options: appConst.professionalsTypesList,
                    controller: controller.professionalProfession,
                    onChanged: (){
                      setState((){});
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 15),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomTextFormField(
                  label: 'E-mail (login)',
                  controller: controller.professionalEmail,
                  readOnly: true,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15
                ),
                CustomTextFormField(
                  label: 'Telefono Clienti',
                  controller: controller.professionalClientPhone,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(height: 15),
            Divider(
              color: Color(0xffd7d7d7d7),
              thickness: 1,
            ),
            SizedBox(height: 15),
            NarFormLabelWidget(
              label: 'Persona di riferimento',
              fontSize: 18,
              fontWeight: 'bold',
            ),
            SizedBox(height: 20),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Nome',
                  controller: controller.contactName,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'Cognome',
                  controller: controller.contactSurname,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Telefono',
                  controller: controller.contactPhone,
                  inputFormatters: [phoneNumberMaskFormatterInternational],
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'E-mail',
                  controller: controller.contactEmail,
                  // readOnly: true,
                ),
              ],
            ),
            SizedBox(height: 15),
            Divider(
              color: Color(0xffd7d7d7d7),
              thickness: 1,
            ),
            SizedBox(height: 15),
            NarFormLabelWidget(
              label: 'Fatturazione',
              fontSize: 18,
              fontWeight: 'bold',
            ),
            SizedBox(height: 20),
            widget.professional.hasPIva
            ? Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'Denominazione',
                  controller: controller.professionalLegalEntity,
                  textCapitalization: TextCapitalization.words,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      NarFormLabelWidget(
                        label: "Forma Societaria",
                        textColor: Color(0xff696969),
                        fontSize: 13,
                        fontWeight: '600',
                      ),
                      SizedBox(height: 4),
                      NarSelectBoxWidget(
                        options: appConst.supplierFormationTypesList,
                        controller: controller.formationType,
                        // validationType: 'required',
                        // parametersValidate: 'Required!',
                        onChanged: (value){
                          setState((){});
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ) : SizedBox.shrink(),
            widget.professional.hasPIva
            ? SizedBox(
              height: 15,
            ) : SizedBox.shrink(),
            Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: AddressSearchBar(
                    label: "Indirizzo sede legale", 
                    initialAddress: widget.professional.legalAddressInfo.fullAddress ?? "",
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Obbligatorio';
                      }
                      return null;
                    },
                    onPlaceSelected: (selectedPlace){
                      BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace['place']);
                      if (selectedAddress.isValidAddress()){
                        controller.professionalLegalAddress = selectedAddress;
                      } else {
                        controller.professionalLegalAddress = BaseAddressInfo.empty();
                      }
                    }
                  ),
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'Codice Fiscale',
                  enabled : widget.professional.hasPIva ? controller.formationType.text == 'Impresa individuale' : true,
                  controller: controller.professionalFiscalCode,
                  inputFormatters: [codiceFiscaleMaskFormatter],
                  // validator: (value) {
                  //   if (controller.formationType.text != 'Impresa individuale') {
                  //     return null;
                  //   }
                  //   if (value == null || value.isEmpty || value.length < 16) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ),
            SizedBox(height: 15),
            widget.professional.hasPIva
            ? Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                CustomTextFormField(
                  label: 'P.IVA',
                  controller: controller.professionalVat,
                  inputFormatters: [ivaMaskFormatter],
                  // validator: (value) {
                  //   if (value == null || value.isEmpty || !RegExp(r"^(IT)?[0-9]{11}$").hasMatch(value)) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
                SizedBox(
                  width: 15,
                ),
                CustomTextFormField(
                  label: 'Codice Fatturazione Elettronico',
                  controller: controller.professionalBillingCode,
                  // validator: (value) {
                  //   if (value == null || value.isEmpty) {
                  //     return 'Obbligatorio';
                  //   }
                  //   return null;
                  // },
                ),
              ],
            ) : SizedBox.shrink(),
            widget.professional.hasPIva
            ? SizedBox(
              height: 15,
            ) : SizedBox.shrink(),
            Divider(
              color: Color(0xffd7d7d7d7),
              thickness: 1,
            ),
            SizedBox(height: 15),
            Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                NarFormLabelWidget(
                  label: 'Immagine profilo',
                  fontSize: 16,
                  fontWeight: 'bold',
                ),
                SizedBox(
                  height: 15,
                ),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    NarImagePickerWidget(
                      allowMultiple: false,
                      imagesToDisplayInList: 0,
                      removeButton: false,
                      removeButtonText: 'rimuovi',
                      uploadButtonPosition: 'back',
                      showMoreButtonText: '+ espandi',
                      removeButtonPosition: 'bottom',
                      displayFormat: 'row',
                      imageDimension: 100,
                      imageBorderRadius: 50,
                      borderRadius: 7,
                      fontSize: 14,
                      fontWeight: '600',
                      text: 'Carica immagine profilo',
                      borderSideColor:
                          Theme.of(context).primaryColor,
                      hoverColor: Color.fromRGBO(133, 133, 133, 1),
                      images: profilePicture,
                      pageContext: context,
                      storageDirectory: "${appConfig.COLLECT_PROFESSIONALS}/",
                      preloadedImages:
                          profilePictureFilename == null ||
                                  profilePictureFilename == ''
                              ? []
                              : [profilePictureFilename],
                      firebaseId: professionalsUser?.id ?? "",
                      removeExistingOnChange: true
                    )
                  ],
                ),
              ]
            ),
            NarFormLabelWidget(
              label: controller.validationMessage != '' ? controller.validationMessage : '',
              fontSize: 12,
              textColor: Colors.red,
            ),
            SizedBox(
              height: 50
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              mainAxisSize: MainAxisSize.max,
              children: [
                NarFormLabelWidget(
                  label: controller.progressMessage != '' ? controller.progressMessage : '',
                  fontSize: 12,
                )
              ],
            ),
            SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                BaseNewarcButton(
                  buttonText: "Salva",
                  onPressed: () async {
                    if (_formKey.currentState!.validate()) {
                      setState(() {
                        controller.progressMessage = 'Salvataggio in corso...';
                      });
                      bool response = await updateData(context);
                      if (response == true) {
                        setState(() {
                          controller.progressMessage = '';
                        });
                        await showAlertDialog(context, "Salvataggio",
                            "Informazioni ditta salvate con successo");
                      } else {
                        setState(() {
                          controller.progressMessage =
                              'Si è verificato un errore. Contatta l\'assistenza.';
                        });
                      }
                    }
                  }
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}