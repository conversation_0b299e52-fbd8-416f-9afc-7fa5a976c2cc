import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../../../classes/immaginaProject.dart';

class AgencyImmaginaProjectArchiveController extends GetxController {
  bool loadingProperties = true;
  List<ImmaginaProject> projects = [];
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  List<String> formMessages = [];
  List<DocumentSnapshot> documentList = [];
  int totalRecords = 0;
  String currentlyShowing = '';
  int recordsPerPage = 20;
  int pageCounter = 1;
  int totalPages = 0;
  bool disablePreviousButton = true;
  bool disableNextButton = false;
  List<dynamic> cacheFireStore = [];
  // bool isRequest = true;

  TextEditingController statusFilterController = TextEditingController();
  TextEditingController searchTextController = TextEditingController();
  List<Map> filters = [];
}