import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/basePersonInfo.dart';
import 'package:newarc_platform/classes/renovationContact.dart';

import 'newarcProjectFixedAssetsPropertyPagamento.dart';

class RenovationQuotation{
  // int? indexPlace;
  
  String? id;
  String? code;
  int? codeCounter;
  int? year; 
  String? renovationContactId;
  String? paymentMode;
  String? constructionDuration;
  String? status;
  bool? isArchived;
  RenovationContact? renovationContact;

  String? discountType;
  String? discount;
  
  List<RenovationActivityCategory>? renovationActivity = [];
  int? created;
  int? modificationDate;
  int? version;
  int? revision;
  String? comment;
  String? tipologia; // type

  //-- this is quotation pagamenti part which will link with Project ---> Economics --> Entrate
  List<NewarcProjectPagamento>? pagamento;

  String? propertyType;
  String? areaMq;
  String? floor;
  String? bathroom;
  String? additionalNote;

  List<RenovationImageList>? renovationImageList;

  RenovationContract? renovationContract;



  RenovationQuotation(Map<String, dynamic> data) {
    this.id = data['id'];
    this.code = data['code'];
    this.codeCounter = data['codeCounter'];
    this.year = data['year'];
    this.renovationContactId = data['renovationContactId'];
    this.paymentMode = data['paymentMode'] == null ? '' : data['paymentMode'];
    this.constructionDuration = data['constructionDuration'] == null ? '' : data['constructionDuration'];
    this.renovationActivity = data['renovationActivity'] == null ? [] : data['renovationActivity'];
    this.created = data['created'];
    this.status = data['status'];
    this.isArchived = data['isArchived'];
    this.renovationContact = null;
    this.discountType = data['discountType'];
    this.discount = data['discount'];
    this.version = data['version'];
    this.version = data['revision'];
    this.modificationDate = data['modificationDate'];
    this.comment = data['comment'];
    this.tipologia = data['tipologia'] ?? "";
    this.propertyType = data['propertyType'] ?? "";
    this.areaMq = data['areaMq'] ?? "";
    this.floor = data['floor'] ?? "";
    this.bathroom = data['bathroom'] ?? "";
    this.additionalNote = data['additionalNote'] ?? "";
    this.renovationContract = data['renovationContract'] != null ? RenovationContract.fromDocument(data['renovationContract']) : RenovationContract.empty();
    this.pagamento = [];
    if (data['pagamento'] != null) {
      for (var i = 0; i < data['pagamento'].length; i++) {
        this.pagamento?.add(NewarcProjectPagamento.fromDocument(data['pagamento'][i],''));
      }
    }
    this.renovationImageList = [];
    if (data['renovationImageList'] != null) {
      for (var i = 0; i < data['renovationImageList'].length; i++) {
        this.renovationImageList?.add(RenovationImageList.fromDocument(data['renovationImageList'][i]));
      }
    }
  }

  RenovationQuotation.empty(){
    this.id = '';
    this.code = '';
    this.codeCounter = 0;
    this.year = 0;
    this.renovationContactId = '';
    this.paymentMode = '';
    this.constructionDuration = '';
    this.status = '';
    this.isArchived = false;
    this.renovationActivity = [];
    this.created = null;
    this.discountType = 'Percentuale';
    this.discount = '0%';
    this.version = 1;
    this.revision = 0;
    this.modificationDate = null;
    this.comment = '';
    this.tipologia = "";
    this.propertyType = "";
    this.areaMq = "";
    this.floor = "";
    this.bathroom = "";
    this.additionalNote = "";
    this.pagamento = [];
    this.renovationImageList = [];
    this.renovationContract = RenovationContract.empty();
  }

  Map<String, dynamic> toMap() {
    return {
      'code': this.code,
      'codeCounter': this.codeCounter,
      'year': this.year,
      'renovationContactId': this.renovationContactId,
      'paymentMode': this.paymentMode,
      'constructionDuration': this.constructionDuration,
      'status': this.status,
      'isArchived': this.isArchived == null ? false : this.isArchived,
      'renovationActivity': this.renovationActivity?.map((e) => e.toMap()).toList() ?? [],
      'created': this.created,
      'discountType': this.discountType,
      'discount': this.discount,
      'version': this.version,
      'revision': this.revision,
      'modificationDate': this.modificationDate,
      'comment': this.comment,
      'tipologia': this.tipologia,
      'propertyType': this.propertyType,
      'areaMq': this.areaMq,
      'floor': this.floor,
      'bathroom': this.bathroom,
      'additionalNote': this.additionalNote,
      'renovationContract': this.renovationContract?.toMap(),
      'pagamento': this.pagamento?.map((e) => e.toMap()).toList() ?? [],
      'renovationImageList': this.renovationImageList?.map((e) => e.toMap()).toList() ?? [],
    };
  }

  RenovationQuotation.fromDocument(Map<String, dynamic> data, String id) {
    try {
      this.id = id;
      this.code = data['code'] == null ? '' : data['code'];
      this.codeCounter = data['codeCounter'] == null ? 0 : data['codeCounter'];
      this.year = data['year'] == null ? 0 : data['year'];
      this.renovationContactId = data['renovationContactId'];
      this.paymentMode = data['paymentMode'] == null ? '' : data['paymentMode'];
      this.status = data['status'] == null ? '' : data['status'];
      this.isArchived = data['isArchived'] == null ? false : data['isArchived'];
      this.constructionDuration = data['constructionDuration'] == null ? '' : data['constructionDuration'];
      this.created = data['created']; 
      this.modificationDate = data['modificationDate'];
      this.version = data['version'];
      this.revision = data['revision'];
      this.discountType = data['discountType'] ??'';
      this.discount = data['discount'] ?? '';
      this.comment = data['comment'] ?? '';
      this.tipologia = data['tipologia'] ?? "";
      this.propertyType = data['propertyType'] ?? "";
      this.areaMq = data['areaMq'] ?? "";
      this.floor = data['floor'] ?? "";
      this.bathroom = data['bathroom'] ?? "";
      this.additionalNote = data['additionalNote'] ?? "";
      this.renovationContract = data['renovationContract'] != null ? RenovationContract.fromDocument(data['renovationContract']) : RenovationContract.empty();
      if( data['renovationActivity'] != null ) {
        if( data['renovationActivity'].length > 0 ) {
          for (var i = 0; i < data['renovationActivity'].length ; i++) {
            this.renovationActivity!.add( RenovationActivityCategory.fromDocument(data['renovationActivity'][i], i ) );
          }
        } else {
          this.renovationActivity = [];
        }
        
      } else {
        data['renovationActivity'] = [];
      }
      this.pagamento = [];
      if (data['pagamento'] != null) {
        for (var i = 0; i < data['pagamento'].length; i++) {
          this.pagamento?.add(NewarcProjectPagamento.fromDocument(data['pagamento'][i],''));
        }
      }
      this.renovationImageList = [];
      if (data['renovationImageList'] != null) {
        for (var i = 0; i < data['renovationImageList'].length; i++) {
          this.renovationImageList?.add(RenovationImageList.fromDocument(data['renovationImageList'][i]));
        }
      }

    } catch (e, s) {
      print({'Class renovationQuotation.dart', e, s});
    }
    
  }

}

class RenovationActivityCategory {

  int? index;
  String? category;
  List<RenovationActivity>? activity = [];
  

  RenovationActivityCategory(Map<String, dynamic> data) {
    
    this.index = data['index'] == '' ? 0 : data['index'];
    this.category = data['category'] == null ? '' : data['category'];
    this.activity = data['activity'] == null ? [] : data['activity'];
    

  }

  RenovationActivityCategory.empty(){
    this.index = 0;
    this.category = '';
    this.activity = [];
    
  }

  Map<String, dynamic> toMap() {
    return {
      'index': this.index,
      'category': this.category,
      'activity': this.activity?.map((e) => e.toMap()).toList() ?? []
    };
  }

  RenovationActivityCategory.fromDocument(Map<String, dynamic> data, int index) {
    this.index = index;
    this.category = data['category'];
    
    if( data['activity'] != null && data['activity'].length > 0 ) {

      for (var i = 0; i < data['activity'].length ; i++) {
        this.activity!.add( RenovationActivity.fromDocument(data['activity'][i], data['activity'][i]['index'] ) );
      }
      
    } else {
      data['activity'] = [];
    }
  }

}

class RenovationActivity {

  int? index;
  String? title;
  String? measurementUnit;
  double? quantity;
  double? unitPrice;
  String? description;
  String? priceLevel;
  String? subCategory;
  String? code;
  String? comment; 
  bool? isDiscounted;
  bool? isManualActivity;
  bool? isIncludedInComputoMatric;
  bool? isForceAc;
  double? activityDiscountAmount;
  String? activityDiscount;

  RenovationActivity(Map<String, dynamic> fixedProperty) {
    
    this.index = fixedProperty['index'] == '' ? 0 : fixedProperty['index'];
    this.title = fixedProperty['title'] == null ? '' : fixedProperty['title'];
    this.measurementUnit = fixedProperty['measurementUnit'] == null ? '' : fixedProperty['measurementUnit'];
    this.priceLevel = fixedProperty['priceLevel'] == null ? '' : fixedProperty['priceLevel'];
    this.quantity =  fixedProperty['quantity'] == null ? 0 : fixedProperty['quantity'];
    this.unitPrice =  fixedProperty['unitPrice'] == null ? 0 : fixedProperty['unitPrice'];
    this.description =  fixedProperty['description'] == null ? '' : fixedProperty['description'];
    this.subCategory =  fixedProperty['subCategory'] == null ? '' : fixedProperty['subCategory'];
    this.code =  fixedProperty['code'] == null ? '' : fixedProperty['code'];
    this.comment =  fixedProperty['comment'] == null ? '' : fixedProperty['comment'];
    this.isDiscounted =  fixedProperty['isDiscounted'] == null ? false : fixedProperty['isDiscounted'];
    this.isManualActivity =  fixedProperty['isManualActivity']??false;
    this.isIncludedInComputoMatric =  fixedProperty['isIncludedInComputoMatric'] ?? true;
    this.activityDiscountAmount =  fixedProperty['activityDiscountAmount'] ?? 0;
    this.activityDiscount =  fixedProperty['activityDiscount'] ?? "";
    this.isForceAc =  fixedProperty['isForceAc'] ?? false;

  }

  RenovationActivity.empty(){
    this.index = 0;
    this.title = '';
    this.measurementUnit = '';
    this.priceLevel = '';
    this.quantity = 0;
    this.unitPrice = 0;
    this.description = '';
    this.subCategory = '';
    this.code = '';
    this.comment = '';
    this.isDiscounted = false;
    this.isManualActivity = false;
    this.isIncludedInComputoMatric = true;
    this.isForceAc = true;
    this.activityDiscountAmount = 0;
    this.activityDiscount = "";
  }

  Map<String, dynamic> toMap() {
    return {
      'index': this.index,
      'title': this.title,
      'measurementUnit': this.measurementUnit,
      'priceLevel': this.priceLevel,
      'quantity': this.quantity,
      'unitPrice': this.unitPrice,
      'description': this.description,
      'subCategory': this.subCategory,
      'code': this.code,
      'comment': this.comment,
      'isDiscounted': this.isDiscounted,
      'isManualActivity': this.isManualActivity,
      'isIncludedInComputoMatric': this.isIncludedInComputoMatric,
      'activityDiscountAmount': this.activityDiscountAmount,
      'activityDiscount': this.activityDiscount,
      'isForceAc': this.isForceAc,
    };
  }

  RenovationActivity.fromDocument(Map<String, dynamic> data, int index) {
    this.index = index;
    this.title = data['title'];
    this.measurementUnit = data['measurementUnit'];
    this.priceLevel = data['priceLevel'];
    this.quantity = data['quantity'] != null ? data['quantity'] : 0;
    this.unitPrice = data['unitPrice'] != null ? data['unitPrice'] : 0;
    this.description = data['description'] != null ? data['description'] : '';
    this.subCategory = data['subCategory'] != null ? data['subCategory'] : '';
    this.code = data['code'] != null ? data['code'] : '';
    this.comment = data['comment'] != null ? data['comment'] : '';
    this.isDiscounted = data['isDiscounted'] != null ? data['isDiscounted'] : false;
    this.isManualActivity = data['isManualActivity']??false;
    this.isIncludedInComputoMatric = data['isIncludedInComputoMatric'] ?? true;
    this.activityDiscountAmount =  data['activityDiscountAmount'] ?? 0;
    this.activityDiscount =  data['activityDiscount'] ?? "";
    this.isForceAc =  data['isForceAc'] ?? false;
  }

}

class RenovationContract {
  String? firstName;
  String? lastName;
  int? dateOfBirth;
  BaseAddressInfo? baseAddressInfo;
  String? taxIdCode;
  String? urbanSection;
  String? paper;
  String? particle;
  String? subaltern;
  int? startWorkDate;
  int? endWorkDate;
  int? requiredWorkingDays;
  double?  safetyChargesPrice;
  int? acceptedRemovalPercentage;

  bool? isExternalWorkManager;
  ConstructionManagerInfo? constructionManagerInfo;


  RenovationContract.empty(){
    this.firstName = "";
    this.lastName = "";
    this.dateOfBirth = null;
    this.baseAddressInfo = BaseAddressInfo.empty();
    this.constructionManagerInfo = ConstructionManagerInfo.empty();
    this.taxIdCode = "";
    this.urbanSection = "";
    this.paper = "";
    this.subaltern = "";
    this.startWorkDate = null;
    this.endWorkDate = null;
    this.requiredWorkingDays = null;
    this.safetyChargesPrice = null;
    this.acceptedRemovalPercentage = null;
    this.isExternalWorkManager = false;
  }

  Map<String, dynamic> toMap() {
    return {
      'firstName': this.firstName,
      'lastName': this.lastName,
      'dateOfBirth': this.dateOfBirth,
      'baseAddressInfo': this.baseAddressInfo?.toMap(),
      'constructionManagerInfo': this.constructionManagerInfo?.toMap(),
      'taxIdCode': this.taxIdCode,
      'urbanSection': this.urbanSection,
      'paper': this.paper,
      'particle': this.particle,
      'subaltern': this.subaltern,
      'startWorkDate': this.startWorkDate,
      'endWorkDate': this.endWorkDate,
      'requiredWorkingDays': this.requiredWorkingDays,
      'safetyChargesPrice': this.safetyChargesPrice,
      'isExternalWorkManager': this.isExternalWorkManager,
      'acceptedRemovalPercentage': this.acceptedRemovalPercentage,
    };
  }

  RenovationContract.fromDocument(Map<String, dynamic> data) {
    try {
      this.firstName = data['firstName'] ?? "";
      this.lastName = data['lastName'] ?? "";
      this.dateOfBirth = data['dateOfBirth'];
      this.baseAddressInfo = data['baseAddressInfo'] != null ? BaseAddressInfo.fromMap(data['baseAddressInfo']) : BaseAddressInfo.empty();
      this.taxIdCode = data['taxIdCode'] ?? "";
      this.urbanSection = data['urbanSection'] ?? "";
      this.paper = data['paper'] ?? "";
      this.particle = data['particle'] ?? "";
      this.subaltern = data['subaltern'] ?? "";
      this.startWorkDate = data['startWorkDate'];
      this.endWorkDate = data['endWorkDate'];
      this.requiredWorkingDays = data['requiredWorkingDays'];
      this.safetyChargesPrice = data['safetyChargesPrice'];
      this.acceptedRemovalPercentage = data['acceptedRemovalPercentage'];
      this.isExternalWorkManager = data['isExternalWorkManager'] ?? false;
      this.constructionManagerInfo = data['constructionManagerInfo'] != null ? ConstructionManagerInfo.fromMap(data['constructionManagerInfo']) : ConstructionManagerInfo.empty();
    } catch (e, s) {
      print({'Class RenovationContract.dart', e, s});
    }

  }
}

class ConstructionManagerInfo extends BasePersonInfo{
  BaseAddressInfo? addressInfo;
  String? VATNumber;
  String? register;
  String? cityRegister;
  String? registrationNumber;

  ConstructionManagerInfo.empty() : super({}) {
    this.VATNumber = "";
    this.register = "";
    this.cityRegister = "";
    this.registrationNumber = "";
    this.addressInfo = BaseAddressInfo.empty();
  }

  ConstructionManagerInfo.fromMap(Map<String, dynamic> data) : super(data) {
    this.VATNumber = data["VATNumber"];
    this.register = data["register"];
    this.cityRegister = data["cityRegister"];
    this.registrationNumber = data["registrationNumber"];
    this.addressInfo = (data.containsKey('addressInfo') && data['addressInfo'] != null) ? BaseAddressInfo.fromMap(data['addressInfo']) : null;
  }
  Map<String, dynamic> toMap() {
    Map<String, dynamic> baseMap = super.toMap();
    baseMap.addAll({
      'VATNumber': this.VATNumber,
      'register': this.register,
      'cityRegister': this.cityRegister,
      'registrationNumber': this.registrationNumber,
      'addressInfo': this.addressInfo?.toMap(),
    });
    return baseMap;
  }
}

class RenovationImageList {
  String? category;
  List<RenovationImageSet>? renovationImageSet;

  RenovationImageList.empty(){
    this.category = "";
    this.renovationImageSet = [];
  }

  Map<String, dynamic> toMap() {
    return {
      'category': this.category,
      'renovationImageSet': this.renovationImageSet!.map((e) => e.toMap()).toList(),
    };
  }

  RenovationImageList.fromDocument(Map<String, dynamic> data) {

    try {

      this.category = data['category'] ?? "";
      this.renovationImageSet = [];
      if (data['renovationImageSet'] != null) {
        for (var i = 0; i < data['renovationImageSet'].length; i++) {
          this.renovationImageSet?.add(RenovationImageSet.fromDocument(data['renovationImageSet'][i]));
        }
      }

    } catch (e, s) {
      print({'Class RenovationImageList.dart', e, s});
    }

  }
}

class RenovationImageSet {
  int? index;
  String? setTitle;
  String? dimension;
  List<RenovationRenderImages>? renovationRenderImages;
  TextEditingController? controllerTitle;

  RenovationImageSet.empty(){
    this.index = -1;
    this.dimension = "";
    this.setTitle = "";
    this.renovationRenderImages = [];
    this.controllerTitle = TextEditingController();
  }

  Map<String, dynamic> toMap() {
    return {
      'index': this.index,
      'dimension': this.dimension,
      'setTitle': this.setTitle,
      'renovationRenderImages': this.renovationRenderImages!.map((e) => e.toMap()).toList(),
    };
  }

  RenovationImageSet.fromDocument(Map<String, dynamic> data) {
    try {
      this.index = data['index'] ?? -1;
      this.dimension = data['dimension'] ?? "";
      this.setTitle = data['setTitle'] ?? "";
      this.renovationRenderImages = [];
      this.controllerTitle = TextEditingController(text: data['setTitle']);;
      if (data['renovationRenderImages'] != null) {
        for (var i = 0; i < data['renovationRenderImages'].length; i++) {
          this.renovationRenderImages?.add(RenovationRenderImages.fromDocument(data['renovationRenderImages'][i]));
        }
      }

    } catch (e, s) {
      print({'Class RenovationImageList.dart', e, s});
    }

  }
}


class RenovationRenderImages {
  XFile? tmpFile;
  Uint8List? imageBytes;
  String? filenameUrl;
  String? filename;
  String? location;
  String? description;
  TextEditingController? contDescription;
  TextEditingController? contDimension;
  List? imageList;
  ImagePicker picker = ImagePicker();



  RenovationRenderImages(Map<String, dynamic> data) {
    this.imageBytes = data['imageBytes'];
    this.tmpFile = data['tmpFile'];
    this.filename = data['filename'];
    this.filenameUrl = data['filenameUrl'];
    this.location = data['location'];
    this.picker = data['picker'];
  }


  RenovationRenderImages.empty(){
    this.filename = "";
    this.location = "";
    this.description = "";
    this.contDescription = new TextEditingController();
    this.contDimension = new TextEditingController();
    this.imageList = [];
  }

  Map<String, dynamic> toMap() {
    this.imageList = this.filename!.isNotEmpty ? [this.filename] : [];
    return {
      'filename': this.filename,
      'location': this.location,
      'description': this.description,
    };
  }

  RenovationRenderImages.fromDocument(Map<String, dynamic> data) {
    try {
      this.filename = data['filename'] ?? "";
      this.location = data['location'] ?? "";
      this.description = data['description'] ?? "";
      this.contDescription = new TextEditingController(text: data['description']);
      this.contDimension = new TextEditingController(text: data['dimension']);
      this.imageList =data['filename'].isNotEmpty ? [data['filename']] : [];
    } catch (e, s) {
      print({'Class RenovationRenderImages.dart', e, s});
    }

  }
}