import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/classes/comparabile.dart';
import 'package:newarc_platform/classes/user.dart';

class HomeNewarcModel extends ChangeNotifier {}

class HomeNewarcArguments {
  // AgencyUser? agencyUser;
  NewarcUser? agencyUser;
  bool? isWork; 
  HomeNewarcArguments(NewarcUser agencyUser) {
    this.agencyUser = agencyUser;
    this.isWork = isWork;
  }
}
