import 'package:flutter/material.dart';
import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';


import 'package:newarc_platform/classes/professionals.dart';
import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/pages/professionals/immagina/immagina_professionals_controller.dart';
import 'package:newarc_platform/pages/professionals/immagina/immagina_professionals_data_source.dart';

import 'package:newarc_platform/widget/UI/immagina_professionals_data_insertion_pupup.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/image-select-box.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/utils/common_utils.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;


class ImmaginaProfessionalsView extends StatefulWidget {
  final ProfessionalsUser professionalsUser;
  final bool isArchived;
  final Function? updateViewCallback;
  final Map? projectArguments;

  ImmaginaProfessionalsView({
    Key? key, 
    required this.professionalsUser,
    required this.isArchived,
    required this.updateViewCallback,
    required this.projectArguments,
    }) : super(key: key);

  @override
  State<ImmaginaProfessionalsView> createState() =>
      _ImmaginaProfessionalsViewState();
}

class _ImmaginaProfessionalsViewState extends State<ImmaginaProfessionalsView> {

  late final ImmaginaProfessionalsController controller;
  Key? paddingKey;

  @override
  void initState() {
    // Create a unique controller instance for this view
    controller = ImmaginaProfessionalsController();
    print("Initializing ImmaginaProfessionalsView - isArchived: ${widget.isArchived}");
    initialFetchProjects();
    super.initState();
  }

  @override
  void didUpdateWidget(ImmaginaProfessionalsView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // If the isArchived parameter changed, reload the data
    if (oldWidget.isArchived != widget.isArchived) {
      print("isArchived changed from ${oldWidget.isArchived} to ${widget.isArchived}, reloading data");
      initialFetchProjects();
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> initialFetchProjects() async {

    setState(() {
      controller.projects = [];
      controller.loading = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_IMMAGINA_PROJECTS);

      if (controller.stateFilterController.text != '') {
        collectionSnapshotQuery = collectionSnapshotQuery.where('requestStatus',
            isEqualTo: controller.stateFilterController.text);
      }
      collectionSnapshotQuery = collectionSnapshotQuery
        .where('isArchived', isEqualTo: false)
        .where('isAgencyArchived', isEqualTo: widget.isArchived ? true : false)
        .where('professionalId', isEqualTo: widget.professionalsUser.professional!.id)
        .orderBy('insertionTimestamp', descending: true);
      collectionSnapshot = await collectionSnapshotQuery.get();

      for (var i = 0; i < collectionSnapshot.docs.length; i++) {
        ImmaginaProject tmpProj = ImmaginaProject.fromDocument(
            collectionSnapshot.docs[i].data(), collectionSnapshot.docs[i].id);
        controller.projects.add(tmpProj);
      }
      setState(() {
        controller.loading = false;
      });
    } catch (e, s) {
      setState(() {
        controller.loading = false;
      });
      log('Following error', error: e, stackTrace: s);
    }
  }

  void reloadAfterPop({bool force = false}) {
    controller.clearFilter();
    initialFetchProjects();
  }

  List<Map> suggestionStatus = [
    {
      'value': 'in lavorazione',
      'label': 'In Lavorazione',
      'bgColor': Colors.amber,
      'textColor': Colors.black
    },
    {
      'value': 'da completare',
      'label': 'Da Completare',
      'bgColor': Colors.grey,
      'textColor': Colors.black
    },
    {
      'value': 'in analisi',
      'label': 'In Analisi',
      'bgColor': Colors.amber,
      'textColor': Colors.black
    },
    {
      'value': 'completato',
      'label': 'Completato',
      'bgColor': Color(0xff39C14F),
      'textColor': Colors.white
    },
    {
      'value': 'bloccata',
      'label': 'Bloccata',
      'bgColor': Color(0xffDD0000),
      'textColor': Colors.white
    },
  ];

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }

    return LayoutBuilder(builder: (context, constraints) {
      return Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              NarFormLabelWidget(
                label: widget.isArchived ? 'Progetti archiviati': 'Progetti attivi',
                fontSize: 19,
                fontWeight: '700',
              ),
              Expanded(
                child: !widget.isArchived
                ? Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () async {
                          await showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (BuildContext context) {
                              return Center(
                                child: ImmaginaProfessionalsDataInsertionPopup(
                                  procedureStep: 'initial',
                                  professionalsUser: widget.professionalsUser,
                                  onClose: () {
                                    reloadAfterPop(force: true);
                                  },
                                ),
                              );
                            },
                          );
                        },
                        child: Container(
                          height: 32,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Padding(
                            padding:
                            const EdgeInsets.symmetric(horizontal: 20.0),
                            child: Row(
                              children: [
                                Icon(Icons.add, color: Colors.white, size: 14,),
                                SizedBox(width: 5,),
                                NarFormLabelWidget(
                                  label: 'Nuova richiesta',
                                  fontSize: 12,
                                  fontWeight: '600',
                                  letterSpacing: 1,
                                  textColor: Colors.white,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ) : SizedBox(height: 32,),
              )
            ],
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              controller.loading
                ? Container()
                : NarFilter(
                  showSearchInput: false,
                  textEditingControllers: [
                    controller.stateFilterController,
                  ],
                  selectedFilters: [
                    controller.stateSelectedFilter,
                  ],
                  filterFields: [
                    {
                      'Preventive State': NarImageSelectBoxWidget(
                        options: suggestionStatus,
                        onChanged: (dynamic val) {
                          controller.stateSelectedFilter = val['label'];
                          setState(() {});
                        },
                        controller: controller.stateFilterController,
                      ),
                    },
                  ],
                  onSubmit: () async {
                    await initialFetchProjects();
                  },
                  onReset: () async {
                    controller.clearFilter();
                    await initialFetchProjects();
                  },
                ),
            ],
          ),
          SizedBox(height: 10),
          Container(
            height: constraints.maxHeight / 1.2,
            child: Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Opacity(
                        opacity: controller.loading ? 0.5 : 1,
                        child: NewarcDataTable(
                          rowsPerPage: 20,
                          dividerThickness: 1,
                          columns: [
                            DataColumn2(
                              size: ColumnSize.L,
                              label: Text(
                                'Indirizzo',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.M,
                              label: Text(
                                'Stato progetto',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Inizio progetto',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.L,
                              label: Text(
                                'Fine progetto',
                              ),
                            ),
                          ],
                          source: ImmaginaProfessionalsDataSource(
                            initialFetchContacts: initialFetchProjects,
                            projects: controller.projects,
                            status: suggestionStatus,
                            context: context,
                            onProjectTap: (ImmaginaProject proj) {
                              bool isReq = [CommonUtils.preventivazione,CommonUtils.daCompletare,CommonUtils.inAnalisi,CommonUtils.bloccata].contains(proj.requestStatus);
                              bool isProf = proj.isForProfessionals;
                              widget.projectArguments!.clear();
                              widget.projectArguments!.addAll({
                                'isFromRequest': isReq,
                                'isForProfessionals': isProf,
                                'projectFirebaseId': proj.id,
                                'updateViewCallback': widget.updateViewCallback,
                                'isFromProjectArchive': widget.isArchived,
                              });
                              if (isReq) {
                                widget.updateViewCallback!('inside-request-professional', projectArguments: widget.projectArguments);
                              } else {
                                widget.updateViewCallback!('inside-project-professional', projectArguments: widget.projectArguments);
                              }
                            },
                          ),
                        ),
                      ),
                      if (controller.loading)
                        Positioned.fill(
                          child: Center(
                            child: CircularProgressIndicator(
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                      // dataTablePagination(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }
}