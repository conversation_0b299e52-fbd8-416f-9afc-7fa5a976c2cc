import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:newarc_platform/classes/agencyPersone.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/widget/UI/form-label.dart';
import '../../../widget/UI/custom_textformfield.dart';
import '../../../widget/UI/file-picker.dart';
import 'agency_persone_data_source.dart';



class AgencyPersoneView extends StatefulWidget {
  final responsive;
  final Function? updateViewCallback;
  final String agencyId;


  const AgencyPersoneView(
      {Key? key, required this.responsive, this.updateViewCallback,required this.agencyId})
      : super(key: key);

  @override
  State<AgencyPersoneView> createState() =>
      _AgencyPersoneViewState();
}

class _AgencyPersoneViewState extends State<AgencyPersoneView> {
  
  List<AgencyPersone> agencyPersone = [];
  bool isLoading = false;
  

  @override
  void initState() {
    super.initState();
    initialFetch();
  }
  

  @override
  void dispose() {
    agencyPersone = [];
    super.dispose();
  }


  Future<void> initialFetch({bool force = false}) async {

    if (agencyPersone.isNotEmpty && !force) return;
    setState(() {
      isLoading = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_AGENCY_PERSONA);
      
      collectionSnapshotQuery = collectionSnapshotQuery.where('agencyId', isEqualTo: widget.agencyId);
      collectionSnapshotQuery = collectionSnapshotQuery.orderBy('insertTimestamp', descending: true);


      collectionSnapshot = await collectionSnapshotQuery.get();
      

      await generateRow(collectionSnapshot);

      setState(() {
        isLoading = false;
      });
    } catch (e, s) {
      setState(() {
        isLoading = false;
      });
      log("Error While fetching Agency Persone ${e.toString()}");
      print({'Following error',e, s});
    }
  }

  Future<void> generateRow(QuerySnapshot collectionSnapshot) async {
    List<AgencyPersone> _agencyPersone = [];


    for (var element in collectionSnapshot.docs) {
      try {
        var data = element.data() as Map<String, dynamic>;
        var _tmp = AgencyPersone.fromDocument(data, element.id);

        _agencyPersone.add(_tmp);
      } catch (e, s) {
        print("Error processing document: $e\n$s");
      }
    }

    agencyPersone = _agencyPersone;
    if (mounted) {
      setState(() {});
    }
  }


  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }

    return Container(
      alignment: Alignment.topLeft,
      child: LayoutBuilder(builder: (context, constraints) {
        return Column(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                NarFormLabelWidget(
                  label: 'Gestisci persone',
                  fontSize: 19,
                  fontWeight: '700',
                ),
                Expanded(
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      MouseRegion(
                        cursor: SystemMouseCursors.click,
                        child: GestureDetector(
                          onTap: () async{
                            String id = await FirebaseFirestore
                                .instance
                                .collection(appConfig.COLLECT_AGENCY_PERSONA).doc().id;
                            AgencyPersone tempAgencyPersona = AgencyPersone.empty();
                            tempAgencyPersona.firebaseId = id;
                            showAddPersonaDialog(context: context,persone: tempAgencyPersona);
                          },
                          child: Container(
                            height: 32,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Padding(
                              padding:
                              const EdgeInsets.symmetric(horizontal: 20.0),
                              child:NarFormLabelWidget(
                                label: 'Aggiungi',
                                fontSize: 13,
                                fontWeight: '600',
                                textColor: AppColor.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
            SizedBox(height: 10),
            Container(
              height: constraints.maxHeight / 1.2,
              child: Column(
                children: [
                  Expanded(
                    child: Stack(
                      children: [
                        Opacity(
                          opacity: isLoading ? 0.5 : 1,
                          child: NewarcDataTable(
                            rowsPerPage: 20,
                            dividerThickness: 1,
                            empty: Text(""),
                            columns: [
                              DataColumn2(
                                  label: Text(
                                    '',
                                  ),
                                  fixedWidth: 55
                              ),
                              DataColumn2(
                                label: Text(
                                  'Nome e Cognome',
                                ),
                              ),
                              DataColumn2(
                                label: Text(
                                  'Telefono',
                                ),
                              ),
                              DataColumn2(
                                label: Text(
                                  'Aggiunta',
                                ),
                              ),
                              DataColumn2(
                                  label: Text(
                                    '',
                                  ),
                                  fixedWidth: 120
                              ),
                            ],
                            source: AgencyPersoneDataSource(
                              initialFetch: initialFetch,
                              context: context,
                              agencyPersone: agencyPersone,
                              onDeleteTap: (id){
                                showDeleteConfirmDialog(id: id,context: context);
                              },
                              onEditTap: (persone){
                                showAddPersonaDialog(context: context,persone: persone);
                              }
                            ),
                          ),
                        ),
                        if (isLoading)
                          Positioned.fill(
                            child: Center(
                              child: CircularProgressIndicator(
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                        // dataTablePagination(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      }),
    );
  }

  void showAddPersonaDialog({required BuildContext context,AgencyPersone? persone}) async {
    AgencyPersone? newPersone = AgencyPersone.empty();
    newPersone = persone;
    TextEditingController firstName = TextEditingController(text: newPersone?.name ?? "");
    TextEditingController lastName = TextEditingController(text: newPersone?.surname ?? "");
    TextEditingController phoneNumber = TextEditingController(text: newPersone?.phone ?? "");
    List<String> formErrorMessage = [];
    return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context,_setState){
            return Center(
              child: BaseNewarcPopup(
                  buttonColor: Theme.of(context).primaryColor,
                  title: "Aggiungi persona",
                  buttonText: "Aggiungi",
                  formErrorMessage: formErrorMessage,
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try{
                      AgencyPersone newPer = AgencyPersone.empty();
                      newPer.name = firstName.text.trim()
                          .split(' ')
                          .map((word) => word.isNotEmpty
                          ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
                          : '')
                          .join(' ');
                      newPer.surname = lastName.text.trim()
                          .split(' ')
                          .map((word) => word.isNotEmpty
                          ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
                          : '')
                          .join(' ');
                      newPer.agencyId = widget.agencyId;
                      newPer.insertTimestamp = DateTime.now().millisecondsSinceEpoch;
                      newPer.phone = phoneNumber.text;
                      if(newPersone?.profilePicture?.isNotEmpty ?? false){
                        newPer.profilePicturePath = {
                          "location": "agencyPersona/profile/${persone!.firebaseId}",
                          "filename": newPersone!.profilePicture![0],
                        };
                      }
                      
                      await FirebaseFirestore
                          .instance
                          .collection(appConfig.COLLECT_AGENCY_PERSONA)
                          .doc(persone!.firebaseId)
                          .set(newPer.toMap());
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                      initialFetch(force: true);
                    }catch(e,s){
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("---------- ERROR While Agency Persone ------> ${e.toString()}");
                    }
                  },
                  column: Container(
                    width: 400,
                    padding: EdgeInsets.symmetric(vertical: 25),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 300,
                          child: CustomTextFormField(
                            isExpanded: false,
                            textCapitalization: TextCapitalization.words,
                            label: "Nome",
                            minLines: 1,
                            controller: firstName,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return "Required!";
                              }
                              return null;
                            },
                          ),
                        ),
                        SizedBox(height: 10,),
                        SizedBox(
                          width: 300,
                          child: CustomTextFormField(
                            isExpanded: false,
                            label: "Cognome",
                            minLines: 1,
                            textCapitalization: TextCapitalization.words,
                            controller: lastName,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return "Required!";
                              }
                              return null;
                            },
                          ),
                        ),
                        SizedBox(height: 10,),
                        SizedBox(
                          width: 300,
                          child: CustomTextFormField(
                            isExpanded: false,
                            label: "Telefono",
                            controller: phoneNumber,
                            isNumber: true,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Telefono è obbligatorio';
                              }
                              final phoneRegex = RegExp(r'^\+?\d+$');
                              if (!phoneRegex.hasMatch(value)) {
                                return 'Inserisci un numero di telefono valido';
                              }
                              return null;
                            },
                          ),
                        ),
                        SizedBox(height: 40,),
                        NarFormLabelWidget(
                          label: 'Immagine profilo',
                          fontSize: 16,
                          fontWeight: '700',
                        ),
                        SizedBox(height: 25,),
                        NarFilePickerWidget(
                          allowMultiple: false,
                          filesToDisplayInList: 0,
                          removeButton: true,
                          isDownloadable: false,
                          removeButtonText: 'Elimina',
                          removeButtonTextColor: AppColor.black,
                          uploadButtonPosition: 'back',
                          showMoreButtonText: '+ espandi',
                          actionButtonPosition: 'bottom',
                          displayFormat: 'inline-widget',
                          containerWidth: 86,
                          containerHeight: 86,
                          containerBorderRadius: 100,
                          borderRadius: 7,
                          fontSize: 11,
                          fontWeight: '600',
                          text: 'Carica Profile Pic',
                          borderSideColor: Theme.of(context).primaryColor,
                          hoverColor: Color.fromRGBO(133, 133, 133, 1),
                          allFiles: newPersone?.profilePicture,
                          pageContext: context,
                          storageDirectory: "agencyPersona/profile/${persone!.firebaseId}",
                          removeExistingOnChange: true,
                          progressMessage: [''],
                          notAccent: true,
                          showTitle: false,
                          onUploadCompleted: () {
                            if (mounted) {
                              _setState(() {});
                            }
                          },
                        ),
                        SizedBox(height: 25,),

                        //----Button
                        NarFilePickerWidget(
                          allowMultiple: false,
                          filesToDisplayInList: 0,
                          removeButton: true,
                          isDownloadable: false,
                          removeButtonText: 'Elimina',
                          removeButtonTextColor: AppColor.black,
                          uploadButtonPosition: 'back',
                          showMoreButtonText: '+ espandi',
                          actionButtonPosition: 'bottom',
                          displayFormat: 'inline-button',
                          containerWidth: 65,
                          containerHeight: 65,
                          containerBorderRadius: 8,
                          borderRadius: 7,
                          fontSize: 11,
                          fontWeight: '600',
                          text: 'Carica',
                          borderSideColor: Theme.of(context).primaryColor,
                          hoverColor: Color(0xFFE0EBF9),
                          allFiles: newPersone?.profilePicture,
                          pageContext: context,
                          storageDirectory: "agencyPersona/profile/${persone.firebaseId}",
                          removeExistingOnChange: true,
                          progressMessage: [''],
                          notAccent: true,
                          splashColor: Color(0xFFE0EBF9),
                          height: 35,
                          buttonWidth: 125,
                          buttonTextColor: Theme.of(context).primaryColor,
                          onUploadCompleted: () {
                            if (mounted) {
                              _setState(() {});
                            }
                          },
                        ),
                      ],
                    ),
                  )),
            );
          });
        });
  }

  void showDeleteConfirmDialog({required BuildContext context,required String id}) async {
    return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context,setStateDialog){
            return Center(
              child: BaseNewarcPopup(
                  key: ValueKey("Attenzione"),
                  buttonColor: Theme.of(context).primaryColor,
                  title: "Attenzione!",
                  buttonText: "Elimina prsona",
                  onPressed: () async {
                    try{
                      //*------delete agency persona
                      await FirebaseFirestore.instance.collection(appConfig.COLLECT_AGENCY_PERSONA)
                          .doc(id)
                          .delete();
                      initialFetch(force: true);
                    }catch(e,s){
                      log("---------- ERROR While Deleting Agency Persona ------> ${e.toString()}");
                      log("---------- STACKTRACE While Deleting Agency Persona ------> ${s.toString()}");
                    }
                  },
                  column: Container(
                    width: 400,
                    padding: EdgeInsets.symmetric(vertical: 25),
                    child: Center(
                      child: NarFormLabelWidget(
                        label:  "Vuoi davvero eliminare questa persona?" ,
                        textColor: Color(0xff696969),
                        fontSize: 18,
                        fontWeight: '600',
                      ),
                    ),
                  )),
            );
          });
        });
  }


}
