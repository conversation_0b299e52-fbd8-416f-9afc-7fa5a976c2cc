import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';


var phoneNumberMaskFormatterIt = new MaskTextInputFormatter(
    mask: '+39 ### ### ####',
    filter: {"#": RegExp(r'[0-9]')},
    type: MaskAutoCompletionType.lazy);

var phoneNumberMaskFormatterInternational = new MaskTextInputFormatter(
    mask: '#### ### ### #### ####',
    filter: {"#": RegExp(r'[0-9]')},
    type: MaskAutoCompletionType.lazy);

var phoneNumberMaskFormatterItalian = new MaskTextInputFormatter(
    mask: '### ### #### ####',
    filter: {"#": RegExp(r'[0-9]')},
    type: MaskAutoCompletionType.lazy);

var ivaMaskFormatter = new MaskTextInputFormatter(
    mask: 'IT###########',
    filter: {"#": RegExp(r'[0-9]')},
    type: MaskA<PERSON>CompletionType.lazy);

var ibanMaskFormatter = new MaskTextInputFormatter(
  mask: 'AA## **** **** **** **** **** **** **********',
  filter: {
    'A': RegExp(r'[A-Za-z]'),        // Allows uppercase letters for the country code
    '#': RegExp(r'[0-9]'),        // Allows digits for the check digits
    '*': RegExp(r'[A-Za-z0-9]')   // Allows alphanumeric characters for the BBAN
  },
  type: MaskAutoCompletionType.lazy);

var codiceFiscaleMaskFormatter = new MaskTextInputFormatter(
  mask: 'AAAAAA##A##A***A',
  filter: {
    'A': RegExp(r'[A-Za-z]'), 
    '#': RegExp(r'[0-9]'), 
    '*': RegExp(r'[A-Za-z0-9]')
  },
  type: MaskAutoCompletionType.lazy
);