
import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/classes/user.dart';




class RenovationContactsController extends GetxController {
  bool loadingContacts = true;
  bool loadingRenovators = true;
  List<RenovationContact> contacts = [];
  List<NewarcUser> renovators = [];
  List<RenovationContact> displayContacts = [];
  String query = "";
  String filterFieldWhere = "";
  String filterValueWhere = "";
  bool isValueNull = false;

  List<Map> filters = [];

  List statusList = [
    {'no': 'primo-incontro', 'keyword': 'Primo incontro'},
    {'no': 'da-preventivare', 'keyword': 'Da prev.'},
    {'no': 'preventivo-inviato', 'keyword': 'Prev. inviato'},
    {'no': 'preventivo-rifiutato', 'keyword': 'Prev. rifiutato'},
    {'no': 'acquisito', 'keyword': 'Acquisito'},
  ];
  Map<String, List> renoFiles = {};
  List<String> progressMessage = [''];
  List<DocumentSnapshot> documentList = [];
  int totalRecords = 0;
  String currentlyShowing = '';
  int recordsPerPage = 20;
  int pageCounter = 1;
  int totalPages = 0;
  bool disablePreviousButton = true;
  bool disableNextButton = false;
  List<dynamic> cacheFirestore = [];
  TextEditingController assignmentController = new TextEditingController();
  TextEditingController agencyController = new TextEditingController();
  TextEditingController searchTextController = new TextEditingController();
  TextEditingController filterCity = new TextEditingController();
  TextEditingController filterNewarcType = new TextEditingController();
  List<Map> searchRef = [];

  TextEditingController contactNameController = new TextEditingController();
  TextEditingController contactSurnameController = new TextEditingController();
  TextEditingController contactEmailController = new TextEditingController();
  TextEditingController contactPhoneController = new TextEditingController();
  final TextEditingController renovatorController = new TextEditingController();
  BaseAddressInfo renoContactAddressInfo = BaseAddressInfo.empty();

  TextEditingController suggestedContactController = new TextEditingController();
  TextEditingController suggestedContactIDController = new TextEditingController();
  List<Map<String, String>> suggestedContactsSearchMap = [];
  bool suggestedContactsFetchedOnce = false;
  bool loadingSuggestedContactsAndAgencies = false;

  String formProgressMessage = '';
  final List<String> formMessages = [];

  ///FILTER
  final TextEditingController referenceFilterController = new TextEditingController();
  String referenceFilter = '';
}
