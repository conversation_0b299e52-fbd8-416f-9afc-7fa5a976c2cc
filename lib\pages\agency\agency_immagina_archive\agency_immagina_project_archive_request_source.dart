
import 'dart:developer';

import 'package:flutter_svg/svg.dart';
import 'package:newarc_platform/widget/UI/tab/common_icon_button.dart';

import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/common_utils.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/tab/tag_widget.dart';
import '../../../widget/UI/base_newarc_popup.dart';
import '../../../widget/UI/custom_textformfield.dart';

class AgencyImmaginaProjectArchiveRequestSource extends DataTableSource {
  AgencyImmaginaProjectArchiveRequestSource({
    this.onAddressTap,
    required this.projects,
    required this.context,
  });

  List<ImmaginaProject> projects = [];

  final BuildContext context;

  Function(ImmaginaProject)? onAddressTap;

  @override
  DataRow? getRow(int index) {
    if (index < projects.length) {
      final project = projects[index];
      var address = "${project.streetName ?? 'noStreetName'}, ${project.streetNumber ?? 'noStreetNum'} ${project.housingUnit != null ? '-' : ''} ${project.housingUnit ?? ''}";
      if (project.addressInfo != null) {
        address = "${project.addressInfo!.streetName ?? 'noStreetName'} ${project.addressInfo!.streetNumber ?? 'noStreetNum'}, ${project.addressInfo!.city ?? 'noCity'} ${project.housingUnit != null ? '-' : ''} ${project.housingUnit ?? ''}";
      }
      bool isProfessionals = project.isForProfessionals ?? false;
      return DataRow(
        cells: [
          DataCell(
            Stack(
              clipBehavior: Clip.none,
              children: [
                if (isProfessionals)
                  Positioned(
                    top: -18,
                    child: Row(
                      children: [
                        TagWidget(
                          text: "Professionals",
                          statusColor: Colors.black,
                        ),
                      ],
                    ),
                  ),
                NarLinkWidget(
                  text: address,
                  textColor: Colors.black,
                  fontWeight: '700',
                  fontSize: 12,
                  overflow: TextOverflow.ellipsis,
                  onClick: () {
                    onAddressTap!(project);
                  },
                ),
              ],
            ),
          ),

          // ///Indirizzo
          // DataCell(
          //   NarLinkWidget(
          //     text: address,
          //     textColor: Colors.black,
          //     fontWeight: '700',
          //     fontSize: 12,
          //     overflow: TextOverflow.ellipsis,
          //     onClick: () {
          //       onAddressTap!(project);
          //     },
          //   ),
          // ),

          ///Codice
          DataCell(
            NarFormLabelWidget(
              label: project.projectId,
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),

          ///Atto di vendita
          DataCell(
            NarFormLabelWidget(
              label: project.sellingPrice != null
              ? DateFormat('dd/MM/yyyy')
                  .format(DateTime.fromMillisecondsSinceEpoch(project.sellingDate ?? 0))
                  .toString()
              : "",
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          /// Acquirente
          DataCell(
            project.sellingPrice != null
            ? IconButtonWidget(
              onTap: () {
                showDialog(
                  context: context,
                  builder: (context) {
                    return Center(
                      child: BaseNewarcPopup(
                        noButton: true,
                        title: "Acquirente",
                        column: Container(
                          width: 400,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(height: 20),
                              CustomTextFormField(
                                textAlign: TextAlign.left,
                                isHaveBorder: true,
                                isCenterLabel: false,
                                flex: 0,
                                suffixIcon: null,
                                readOnly: true,
                                labelColor: AppColor.greyColor,
                                fillColor: Color(0xffF2F2F2),
                                label: "Nome e cognome acquirente",
                                controller: TextEditingController(
                                  text: project.acquirerName != null
                                      ? project.acquirerName
                                      : "",
                                ),
                              ),
                              SizedBox(height: 15),
                              CustomTextFormField(
                                textAlign: TextAlign.left,
                                isHaveBorder: true,
                                isCenterLabel: false,
                                flex: 0,
                                suffixIcon: null,
                                readOnly: true,
                                labelColor: AppColor.greyColor,
                                fillColor: Color(0xffF2F2F2),
                                label: "Telefono acquirente",
                                controller: TextEditingController(
                                  text: project.acquirerPhoneNumber != null
                                      ? project.acquirerPhoneNumber
                                      : "",
                                ),
                              ),
                              SizedBox(height: 15),
                              CustomTextFormField(
                                textAlign: TextAlign.left,
                                isHaveBorder: true,
                                isCenterLabel: false,
                                flex: 0,
                                suffixIcon: null,
                                readOnly: true,
                                labelColor: AppColor.greyColor,
                                fillColor: Color(0xffF2F2F2),
                                label: "E-mail acquirente",
                                controller: TextEditingController(
                                  text: project.acquirerEmailId != null
                                      ? project.acquirerEmailId
                                      : "",
                                ),
                              ),
                              SizedBox(height: 15),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
              isSvgIcon: true,
              icon: 'assets/icons/account.svg',
              iconColor: AppColor.greyColor,
            )
            : SizedBox(),
          ),

          /// Vendita
          DataCell(
            NarFormLabelWidget(
              label: project.sellingPrice != null
                  ? CommonUtils().formatStringToDecimal(input: project.sellingPrice.toString()) + '€'
                  : "",
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),

          /// Acquisto
          DataCell(
            NarFormLabelWidget(
              label: project.isPaidWithCredits == null
                  ? ""
                  : !project.isPaidWithCredits!
                      ? "Richiesta singola"
                      : project.appliedSuccessFee! == "0"
                      ? "1 Credito"
                      : "1 Credito + Success Fee",
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),

          /// Success Fee
          DataCell(
            NarFormLabelWidget(
              label: (() {
                if (project.appliedSuccessFee != null &&
                    project.appliedSuccessFee != "0" &&
                    project.sellingPrice != null) {
                  try {
                    final double successFee = double.tryParse(project.appliedSuccessFee!) ?? 0.0;
                    final double calculatedFee = (successFee * project.sellingPrice!) / 100;
                    return '${CommonUtils().formatStringToDecimal(input: calculatedFee.toStringAsFixed(0))}€ + IVA';
                  } catch (e) {
                    return "";
                  }
                }
                return "";
              })(),
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
          ),
        ],
      );
    }

    return null;
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => projects.length;

  @override
  int get selectedRowCount => 0;
}
