import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/immaginaProject.dart';

import '../../../classes/agencyUser.dart';



class NewarcImmaginaController extends GetxController {
  bool loadingProperties = true;
  List<ImmaginaProject> projects = [];
  List<String> formMessages = [];
  List<DocumentSnapshot> documentList = [];
  int totalRecords = 0;
  String currentlyShowing = '';
  int recordsPerPage = 20;
  int pageCounter = 1;
  int totalPages = 0;
  bool disablePreviousButton = true;
  bool disableNextButton = false;
  List<dynamic> cacheFireStore = [];
  bool isRequest = true;
  TextEditingController searchTextController = new TextEditingController();


  TextEditingController statusFilterController = TextEditingController();
  TextEditingController agencyFilterController = TextEditingController();
  TextEditingController realizzazioneFilterController = TextEditingController();
  TextEditingController venditaFilterController = TextEditingController();
  String agencyFilter = '';
  String realizzazioneFilter = '';
  String venditaFilter = '';
  String statusSelectedFilter = '';
  List<Map> filters = [];

  clearFilter(){
    agencyFilter = '';
    realizzazioneFilter = '';
    venditaFilter = '';
    statusSelectedFilter = '';
    filters.clear();
    venditaFilterController.clear();
    realizzazioneFilterController.clear();
    agencyFilterController.clear();
    statusFilterController.clear();
  }

  List<Agency> agencyList = [];
}
