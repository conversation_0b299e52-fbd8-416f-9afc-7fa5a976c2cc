import 'package:flutter/material.dart';

/// *  hintText - The type of information for which to optimize the text input control.
/// *  hintText - Text that suggests what sort of input the field accepts.
/// *  prefixIcon - Pass Icon if required
/// *  defaultText - If there is predefined value is there for a text field
/// *  focusNode - Currently focus node
/// *  obscureText - Is Password field?
/// *  controller - Text controller
/// *  functionValidate - Validation function(currently I have used to check empty field)
/// *  parametersValidate - Value to validate
/// *  actionKeyboard - Keyboard action eg. next, done, search, etc
/// *  onSubmitField - Done click action
/// *  onFieldTap - On focus on TextField
class NarDatePickerWidget extends StatefulWidget {
  final String? hintText;
  final FocusNode? focusNode;
  final String? validationType;
  final String? parametersValidate;
  final Function? onSubmitField;
  final TextInputType? textInputType;

  DateTime? firstDate = DateTime(2010);
  DateTime? lastDate = DateTime(2200);
  DateTime? selectedDate;

  NarDatePickerWidget(
      {this.hintText,
      this.focusNode,
      this.firstDate,
      this.lastDate,
      this.selectedDate,
      this.validationType,
      this.parametersValidate,
      this.onSubmitField,
      this.textInputType});

  @override
  _NarDatePickerWidgetState createState() => _NarDatePickerWidgetState();
}

class _NarDatePickerWidgetState extends State<NarDatePickerWidget> {
  // double bottomPaddingToError = 12;

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
          primaryColor: Theme.of(context).colorScheme.primary,
          inputDecorationTheme: InputDecorationTheme(
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(7)),
              borderSide:
                  BorderSide(color: Color.fromRGBO(219, 219, 219, 1), width: 2),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(7)),
              borderSide: BorderSide(color: Color.fromRGBO(219, 219, 219, 1)),
            ),
            hintStyle: TextStyle(
              color: Colors.grey,
              fontSize: 15.0,
              fontWeight: FontWeight.w800,
              fontStyle: FontStyle.normal,
              letterSpacing: 1,
              //fontFamily: 'Visby800'
            ),
            contentPadding:
                EdgeInsets.only(top: 30, bottom: 18, left: 21.0, right: 8.0),
            isDense: false,
            // errorStyle: TextStyle(
            //   // color: colorRed,
            //   fontSize: 15.0,
            //   fontWeight: FontWeight.w800,
            //   fontStyle: FontStyle.normal,
            //   letterSpacing: 1,
            //   fontFamily: 'Visby800'
            // ),
            errorBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Color.fromARGB(255, 234, 28, 28)),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.black),
            ),
            fillColor: Color.fromRGBO(245, 245, 245, 1),
          )),
      child: InputDatePickerFormField(
        keyboardType: widget.textInputType,
        firstDate: widget.firstDate!,
        lastDate: widget.lastDate!,
        initialDate: widget.selectedDate,
        fieldHintText: widget.hintText,
        fieldLabelText: '',
        onDateSubmitted: (value) {
          setState(() {
            widget.selectedDate = value;
          });
        },
        onDateSaved: (value) {
          setState(() {
            widget.selectedDate = value;
          });
        },
      ),
    );
  }
}
