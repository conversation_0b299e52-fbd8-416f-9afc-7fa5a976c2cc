import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/custom_icon_button.dart';

final Color kGreyColor = const Color(0xff696969);

class RegisterFormTab extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final String title;
  final bool isFirstTab;
  final bool isFinalTab;
  final bool isSkipable;
  final bool disabledNext;
  final Widget content;
  final PageController pageController;
  final Future<bool> Function()? beforeNext;

  RegisterFormTab({
    required this.formKey,
    required this.title,
    required this.isFirstTab,
    required this.isFinalTab,
    required this.isSkipable,
    required this.pageController,
    required this.content,
    this.disabledNext = false,
    this.beforeNext,
  }) {}

  @override
  State<RegisterFormTab> createState() => _RegisterFormTabState();
}

class _RegisterFormTabState extends State<RegisterFormTab> {
  @override
  Widget build(BuildContext context) {
    var formStatus = widget.formKey.currentState?.validate() ?? false;
    return Form(
      key: widget.formKey,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            NarFormLabelWidget(
              label: widget.title,
              fontWeight: 'bold',
              fontSize: 30,
              textAlign: TextAlign.center,
            ),
            const SizedBox(
              height: 50,
            ),
            Expanded(
              child: SingleChildScrollView(
                child: widget.content,
                scrollDirection: Axis.vertical,
              ),
            ),
            const SizedBox(
              height: 30,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                (!widget.isFirstTab)
                    ? _buildNavButton("Indietro", false, false, () {
                        if (widget.pageController.hasClients) {
                          widget.pageController.previousPage(
                            duration: const Duration(milliseconds: 400),
                            curve: Curves.easeInOut,
                          );
                        }
                      })
                    : SizedBox(
                        height: 20,
                      ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    _buildNavButton(
                        widget.isFinalTab
                            ? "Registrati gratuitamente"
                            : "Avanti",
                        true,
                        true, () async {
                      formStatus = widget.formKey.currentState!.validate();
                      if (kDebugMode) {
                        print("Form validation status: $formStatus");
                      }
                      if (!formStatus || widget.disabledNext) {
                        return;
                      }
                      if (!widget.disabledNext && widget.beforeNext != null) {
                        formStatus = await widget.beforeNext!();
                      }
                      if (!formStatus) {
                        return;
                      }
                      if (widget.pageController.hasClients) {
                        widget.pageController.nextPage(
                          duration: const Duration(milliseconds: 400),
                          curve: Curves.easeInOut,
                        );
                      }

                    },
                        hideIcon: widget.isFinalTab,
                        disabled: widget.disabledNext),
                    if (widget.isSkipable && widget.disabledNext)
                      const SizedBox(
                        height: 20,
                      ),
                    if (widget.isSkipable && widget.disabledNext)
                      _buildNavButton("Salta", false, true, () {
                        if (widget.pageController.hasClients) {
                          widget.pageController.nextPage(
                            duration: const Duration(milliseconds: 400),
                            curve: Curves.easeInOut,
                          );
                        }
                      }),
                  ],
                ),
              ],
            ),
            const SizedBox(
              height: 50,
            ),
          ],
        ),
      ),
    );
  }

  CustomIconButton _buildNavButton(
      String label, bool isDark, bool isFoward, Function() function,
      {bool hideIcon = false, bool disabled = false}) {
    return CustomIconButton(
      label: label,
      height: 45,
      leadingIcon: !isFoward,
      disabled: disabled,
      borderRadius: 25,
      padding: EdgeInsets.symmetric(
        horizontal: 20,
      ),
      textStyle: TextStyle(
        color: isDark ? Colors.white : kGreyColor,
        fontSize: 16,
        fontWeight: FontWeight.w600,
      ),
      boarderColor: !isDark ? Color(0xeeeeeeee) : null,
      boarderWidth: 2,
      icon: !hideIcon
          ? Transform.flip(
              flipX: !isFoward,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: SvgPicture.asset('assets/icons/arrow.svg',
                    color: isDark ? Colors.white : kGreyColor, width: 15),
              ),
            )
          : SizedBox.shrink(),
      color: isDark ? Theme.of(context).primaryColor : Colors.white,
      function: function,
    );
  }
}
