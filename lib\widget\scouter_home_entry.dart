import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/comparabile.dart';
import 'package:newarc_platform/functions/various.dart';
import 'package:url_launcher/url_launcher.dart';

class ScouterHomeEntry extends StatefulWidget {
  ScouterHomeEntry({Key? key, required this.comparabile}) : super(key: key);

  final Comparabile comparabile;

  @override
  _ScouterHomeEntryState createState() => _ScouterHomeEntryState();
}

class _ScouterHomeEntryState extends State<ScouterHomeEntry> {
  double borderRadius = 10;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 150,
      margin: EdgeInsets.symmetric(horizontal: 18, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
              color: Colors.grey.withOpacity(0.5),
              spreadRadius: 2,
              blurRadius: 10),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
            child: ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(borderRadius),
                bottomLeft: Radius.circular(borderRadius),
              ),
              child: Stack(
                children: [
                  Image.network(
                    widget.comparabile.images!.first,
                    height: 150,
                    fit: BoxFit.cover,
                  ),
                  Positioned(
                    top: 10,
                    left: 10,
                    child: Container(
                      height: 27,
                      padding: EdgeInsets.symmetric(horizontal: 5),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(5),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text("MQ"),
                          SizedBox(
                            width: 5,
                          ),
                          Text(widget.comparabile.superficie.toString(),
                              style: TextStyle(fontWeight: FontWeight.w800))
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 10,
                    left: 10,
                    child: Container(
                      constraints: BoxConstraints(minWidth: 65, maxWidth: 200),
                      padding: EdgeInsets.symmetric(horizontal: 5),
                      height: 27,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(5),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Flexible(
                            child: Text(
                              widget.comparabile.address!,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontWeight: FontWeight.w800,
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _getAlgoRanking(),
                      _getComparablePercentage(),
                      _getGreenIcon(),
                    ],
                  ),
                ),
                Container(
                  color: Colors.white,
                  height: 1,
                ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 10),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("Prezzo annuncio"),
                            Text(
                              (widget.comparabile.prezzo! / 1000).toString() +
                                  'k',
                            ),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(child: Text("Prezzo ristrutturato")),
                            Flexible(
                              child: Text(widget.comparabile.predictedPrice ==
                                      null
                                  ? "Loading"
                                  : formatPriceText(
                                          widget.comparabile.predictedPrice!) +
                                      'k'),
                            )
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(child: Text("Prezzo di offerta")),
                            Flexible(
                              child: Text(
                                '${formatPriceText(
                                  widget.comparabile.offerPrice![1],
                                )}k - ${formatPriceText(
                                  widget.comparabile.offerPrice![0],
                                )}k',
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                Row(
                  children: [
                    Expanded(
                      child: Container(
                          height: 27,
                          alignment: Alignment.center,
                          color: Colors.white,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                "€/MQ",
                                style: TextStyle(
                                  fontSize: 11,
                                ),
                              ),
                              SizedBox(width: 5),
                              Flexible(
                                child: Text(
                                  "${widget.comparabile.sqmPrice!.toStringAsFixed(0)}",
                                  style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w800),
                                ),
                              ),
                            ],
                          )),
                    ),
                    Expanded(
                      child: Container(
                        height: 27,
                        alignment: Alignment.center,
                        child: TextButton(
                          child: Text(
                            "Link Annuncio",
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.white,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                          onPressed: () async {
                            try {
                              await launch(widget.comparabile.url!);
                            } catch (e) {
                              print(e);
                            }
                          },
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          borderRadius: BorderRadius.only(
                            bottomRight: Radius.circular(borderRadius),
                          ),
                        ),
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _getAlgoRanking() {
    return Text(
      '91',
      style: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w900,
        color: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _getComparablePercentage() {
    return Text(
      '${widget.comparabile.scostamento}%',
      style: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w700,
        color: Color(0xff606060),
      ),
    );
  }

  Widget _getGreenIcon() {
    if (true) {
      return Image.asset(
        'assets/various/group_green.png',
        width: 25,
        height: 25,
        fit: BoxFit.cover,
      );
    } else {
      return Image.asset(
        'assets/various/group_grey.png',
        width: 25,
        height: 25,
        fit: BoxFit.cover,
      );
    }
  }
}
