
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/tab/users_stack_list.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';

class ProjectRowSource extends DataTableSource {
  ProjectRowSource({required this.projects, required this.onNameTap, required this.context});

  List<NewarcProject> projects = [];
  BuildContext context;
  Function(String) onNameTap;

  @override
  DataRow? getRow(int index) {
    if (index < projects.length) {
      final row = projects[index];
      double completedPercentage = 0;

      if (row.projectJobs!.length > 0) {
        completedPercentage = calculateProjectCompletionPercentage(row.projectJobs);
      }
      String _date = '';
      if (row.jobStartDate! > 0) {
        DateTime dateOn = DateTime.fromMillisecondsSinceEpoch(row.jobStartDate!);
        _date =
            (dateOn.day > 9 ? dateOn.day.toString() : '0' + dateOn.day.toString()) + '/' + (dateOn.month > 9 ? dateOn.month.toString() : '0' + dateOn.month.toString()) + '/' + dateOn.year.toString();
      }
      return DataRow(
        cells: [
          DataCell(
            NarLinkWidget(
              text:row.name ?? "",
              textColor: Colors.black,
              fontWeight: '700',
              fontSize: 12,
              onClick: () => onNameTap(row.id ?? "0"),
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: row.type ?? "",
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),

          ),
          DataCell(
            FutureBuilder<List<String>>(
              future: Future.wait(
                row.assignedTeam.where((user) => user['userId'] != "").map((user) => getImageUrl(user['userId'])).toList(),
              ),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  return SizedBox(
                    height: 30,
                    width: 80,
                    child: UsersStackWidget(
                      imageList: snapshot.data ?? [],
                      radius: 25,
                      itemCount: 3,
                    ),
                  );
                } else if (snapshot.hasError) {
                  return Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100),
                      color: Colors.grey,
                    ),
                  );
                }
                return SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 1,
                  ),
                );
              },
            ),
          ),
          DataCell(Stack(
            children: [
              Container(
                height: 10,
                width: 150,
                decoration: BoxDecoration(color: Color(0xffd9d9d9), borderRadius: BorderRadius.circular(100)),
              ),
              Container(
                height: 10,
                width: 150 * completedPercentage,
                decoration: BoxDecoration(color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(100)),
              ),
            ],
          )),
          DataCell(
            NarFormLabelWidget(
                label: row.assignedAgency!['agencyName'] == null ? 'Da assegnare' : row.assignedAgency!['agencyName'],
                fontSize: 12,
                fontWeight: '600',
                textColor: row.assignedAgency!['agencyName'] == null ? Color(0xff969696) : AppColor.black),

          ),
          DataCell(
            NarFormLabelWidget(
              label: _date,
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),

          ),
        ],
      );
    }
    return null;
  }
  Future<String> getImageUrl(String userId) async {
    final extensions = ['.jpeg', '.png', '.jpg'];
    for (final extension in extensions) {
      final ref = FirebaseStorage.instance.ref().child('users/$userId/profile$extension');
      try {
        return await ref.getDownloadURL();
      } catch (error) {
        continue;
      }
    }
    throw Exception('Profile image not found for user $userId');
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => projects.length;

  @override
  int get selectedRowCount => 0;
}
