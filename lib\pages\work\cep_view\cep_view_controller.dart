import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/projectEconomic.dart';
import 'package:newarc_platform/classes/renovationContact.dart';




class NewarcCEPController extends GetxController {
  bool loadingProperties = true;
  List<ProjectEconomic> projects = [];
  List<String> formMessages = [];
  BaseAddressInfo cepAddressInfo = BaseAddressInfo.empty();
  List<RenovationContact> renovationContact = [];

  List<DocumentSnapshot> documentList = [];
  int totalRecords = 0;
  String currentlyShowing = '';
  int recordsPerPage = 20;
  int pageCounter = 1;
  int totalPages = 0;
  bool disablePreviousButton = true;
  bool disableNextButton = false;
  List<dynamic> cacheFirestore = [];
  List<String> cities = [
    "Tutte",
    "Torino",
    "Milano",
    "Roma",
  ];

  String query = "";
  List<Map> filters = [];
  
  TextEditingController searchTextController = TextEditingController();
  TextEditingController progettoFilterTextController = TextEditingController();
  
  String progettoSelectedFilter = '';
  
  clearFilter(){
    filters.clear();
    progettoFilterTextController.clear();
    progettoSelectedFilter = '';
  }

  Map<String, Map<String, List<List>>> costBreakdown = {
    "Entrate": {
      "revenueExpected": [
        ["vendita", 0, false]
      ]
    },
    "Uscite": {
      "acquisitionCosts": [
        ["acquisto", 0, false],
        ["ricavoGarantito", 0, false],
        ["costiNotarili", 0, false],
        ["impostaDiRegistro", 0, false],
      ],
      "agencyCost": [
        ["commissioneIn", 0, false],
        ["commissioneOut", 0, false],
        ["bonusObiettivo", 0, false],
        ["bonusInsieme", 0, false],
      ],
      "restorationAndMaterail": [
        ["materiali", 0, false]
      ]
    }
  };
}
