import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/acquiredContact.dart';

class CustomDropdown extends StatefulWidget {
  final AcquiredContact acquiredContact;
  final Function updateStage;
  const CustomDropdown({
    Key? key,
    required this.acquiredContact,
    required this.updateStage,
  }) : super(key: key);

  @override
  State<CustomDropdown> createState() => _CustomDropdownState();
}

class _CustomDropdownState extends State<CustomDropdown> {
  String? selectedValue;
  @override
  void initState() {
    selectedValue = widget.acquiredContact.contactStage;

    super.initState();
  }

  getColor(String status) {
    switch (status) {
      case 'Da contattare':
        return Colors.blueAccent;
      case 'Contattato':
        return Colors.yellowAccent;
      case 'Non interessato':
        return Colors.redAccent;
      case 'Acquisito':
        return Theme.of(context).primaryColor;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      width: 150,
      margin: EdgeInsets.symmetric(vertical: 10),
      padding: EdgeInsets.symmetric(vertical: 5, horizontal: 5),
      decoration: BoxDecoration(
        color: getColor(selectedValue!),
        borderRadius: BorderRadius.circular(5),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton2(
          hint: Text(
            'Seleziona',
            style: TextStyle(
              fontSize: 14,
            ),
          ),
          items: ['Da contattare', 'Contattato', 'Non interessato', 'Acquisito']
              .map(
                (item) => DropdownMenuItem<String>(
                  value: item,
                  child: Container(
                    child: Text(
                      item,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black,
                      ),
                    ),
                  ),
                ),
              )
              .toList(),
          value: selectedValue,
          style: TextStyle(color: Colors.white),
          onChanged: (value) {
            setState(() {
              selectedValue = value as String;
            });
            widget.updateStage(widget.acquiredContact, selectedValue);
          },
          buttonStyleData: ButtonStyleData(
            height: 40,
            width: 140
          ),
          // buttonHeight: 40,
          // buttonWidth: 140,
          menuItemStyleData: MenuItemStyleData(
            height: 40
          ),
          // itemHeight: 40,
        ),
      ),
    );
  }
}
