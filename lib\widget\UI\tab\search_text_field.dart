import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class CommonSearchBar extends StatelessWidget {
  final TextEditingController controller;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final Function()? suffixIconOnTap;
  final String hintText;
  final Color? fillColor;

  const CommonSearchBar({
    super.key,
    required this.controller,
    this.onChanged,
    this.onSubmitted,
    this.hintText = "",
    this.fillColor,
    this.suffixIconOnTap
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      style: TextStyle(
        color: Colors.black,
        fontSize: 13,
        fontFamily: 'Raleway-700',
      ),
      cursorColor: Colors.black,
      decoration: InputDecoration(
          hintText: hintText,
          suffixIconConstraints: BoxConstraints(
            minHeight: 16,
            minWidth: 28,
            maxHeight: 16,
            maxWidth: 28,
          ),
          suffixIcon: MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: suffixIconOnTap,
              child: Container(
                margin: EdgeInsets.only(right: 10),
                child: SvgPicture.asset(
                  height: 16,
                  width: 16,
                  "assets/icons/search.svg",
                ),
              ),
            ),
          ),
          hintStyle: TextStyle(
            color: Color(0xFF737373),
            fontSize: 11,
            fontFamily: 'Raleway-600',
          ),
          fillColor: Color(0xFFF6F6F6),
          filled: true,
          disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(100.0),
            borderSide: BorderSide.none,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(100.0),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(100.0),
            borderSide: BorderSide.none,
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(100.0),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(100.0),
            borderSide: BorderSide.none,
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(100.0),
            borderSide: BorderSide.none,
          )),
    );
  }
}
