import 'dart:developer';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:country_picker/country_picker.dart';
import 'package:newarc_platform/classes/professionals.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/basePersonInfo.dart';
import 'package:newarc_platform/utils/inputFormatters.dart';
import 'package:newarc_platform/widget/UI/address_completion_google.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/image-select-box.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/multi-select-dropdown.dart';
import 'package:newarc_platform/pages/work/gestione/professionals/professionals_controller.dart';
import 'package:newarc_platform/pages/work/gestione/professionals/professionals_data_source.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;


class ProfessionalsView extends StatefulWidget {
  final responsive;
  final Function updateViewCallback;

  ProfessionalsView({
    Key? key,
    required this.responsive,
    required this.updateViewCallback,
    }) : super(key: key);

  @override
  State<ProfessionalsView> createState() =>
      _ProfessionalsViewState();
}

class _ProfessionalsViewState extends State<ProfessionalsView> {

  final controller = Get.put<ProfessionalsController>(ProfessionalsController());
  Key? paddingKey;

  @override
  void initState() {
    controller.professionFilterController.text = '';
    initialFetchProfessionals();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> initialFetchProfessionals() async {

    setState(() {
      controller.professionals = [];
      controller.loading = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
        .collection(appConfig.COLLECT_PROFESSIONALS);

      if (controller.professionFilterController.text != '') {
        collectionSnapshotQuery = collectionSnapshotQuery.where('profession',
            isEqualTo: controller.professionFilterController.text);
      }
      collectionSnapshotQuery = collectionSnapshotQuery
        .where('isArchived', isEqualTo: false);
        // .orderBy('insertTimestamp', descending: true);
      collectionSnapshot = await collectionSnapshotQuery.get();

      for (var i = 0; i < collectionSnapshot.docs.length; i++) {
        Professional tmpCont = Professional.fromDocument(
            collectionSnapshot.docs[i].data(), collectionSnapshot.docs[i].id);
        controller.professionals.add(tmpCont);
      }
      setState(() {
        controller.loading = false;
      });
    } catch (e, s) {
      setState(() {
        controller.loading = false;
      });
      log('Following error', error: e, stackTrace: s);
    }
  }

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }

    return LayoutBuilder(builder: (context, constraints) {
      return Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                label: 'Professionals',
                fontSize: 19,
                fontWeight: '700',
              ),
            ],
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              controller.loading
                  ? Container()
                  : NarFilter(
                showSearchInput: false,
                textEditingControllers: [
                  controller.professionFilterController,
                ],
                selectedFilters: [
                  controller.professionSelectedFilter,
                ],
                filterFields: [
                  {
                    'Professione': NarImageSelectBoxWidget(
                      // must be sorted in alphabetical order
                      options: (appConst.professionalsTypesList..sort()).map((toElement) => {
                        'label': toElement,
                        'value': toElement,
                      }).toList(),
                      onChanged: (dynamic val) {
                        controller.professionSelectedFilter = val['label'];
                        setState(() {});
                      },
                      controller: controller.professionFilterController,
                    ),
                  },
                ],
                onSubmit: () async {
                  await initialFetchProfessionals();
                },
                onReset: () async {
                  controller.clearFilter();
                  await initialFetchProfessionals();
                },
              ),
            ],
          ),
          SizedBox(height: 10),
          Container(
            height: constraints.maxHeight / 1.2,
            child: Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Opacity(
                        opacity: controller.loading ? 0.5 : 1,
                        child: NewarcDataTable(
                          rowsPerPage: 20,
                          dividerThickness: 1,
                          columns: [
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'P.Iva',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.L,
                              label: Text(
                                'Nome azienda/persona',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Professione',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Provincia',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.L,
                              label: Text(
                                'Indirizzo',
                              ),
                            ),
                          ],
                          source: ProfessionalsDataSource(
                            onProfessionalTap: (Professional cont) {
                              // Use Future.microtask to ensure we're not in the build phase
                              Future.microtask(() {
                                widget.updateViewCallback(
                                  "professionals-inside-view",
                                  projectArguments: {
                                    'professional': cont
                                  }
                                );
                              });
                            },
                            professionals: controller.professionals,
                            context: context,
                          ),
                        ),
                      ),
                      if (controller.loading)
                        Positioned.fill(
                          child: Center(
                            child: CircularProgressIndicator(
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }
}
