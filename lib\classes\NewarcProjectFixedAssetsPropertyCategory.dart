class NewarcProjectFixedAssetsPropertyCategory{
  String? firebaseId;
  String? name;
  int? insertTimestamp;
  String? uid;


  Map<String, Object?> toMap() {
    return {
      'name': name,
      'insertTimestamp': insertTimestamp,
      'uid': uid,
    };
  }

  NewarcProjectFixedAssetsPropertyCategory.empty() {
    this.firebaseId = '';
    this.name = '';
    this.insertTimestamp = null;
    this.uid = '';
  }

  NewarcProjectFixedAssetsPropertyCategory.fromDocument(Map<String, dynamic> data, String id) {
    this.firebaseId = id;
    try {
      this.name = data['name'];
      this.insertTimestamp = data['insertTimestamp'];
      this.uid = data['uid'];
    } catch (e, s) {
      print({ 'NewarcProjectFixedAssetsPropertyCategory Class Error ------->', e, s});
    }
  }
}