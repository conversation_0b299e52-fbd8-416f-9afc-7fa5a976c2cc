import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';

class RenovationContactPopup extends StatefulWidget {
  const RenovationContactPopup({
    Key? key,
    this.renovationContact,
  }) : super(key: key);

  final RenovationContact? renovationContact;

  @override
  State<RenovationContactPopup> createState() => _RenovationContactPopupState();
}

class _RenovationContactPopupState extends State<RenovationContactPopup> {
  late RenovationContact? renovationContact;

  @override
  void initState() {
    if (widget.renovationContact == null) {
      renovationContact = RenovationContact.empty();
    } else {
      renovationContact = widget.renovationContact;
    }
    super.initState();
  }

  TextStyle sectionTitleStyle =
      TextStyle(fontSize: 16, fontWeight: FontWeight.bold);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 400,
      child: <PERSON>umn(
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              CustomTextFormField(
                initialValue: renovationContact!.name! +
                    " " +
                    renovationContact!.surname!,
                label: "Nome e cognome",
                labelColor: Color.fromRGBO(149, 149, 149, 1),
                isPercentage: false,
                isMoney: false,
                isNullable: true,
              ),
            ],
          ),
          Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              CustomTextFormField(
                initialValue: renovationContact!.email,
                label: "E-mail",
                labelColor: Color.fromRGBO(149, 149, 149, 1),
                isPercentage: false,
                isMoney: false,
                isNullable: true,
              ),
            ],
          ),
          Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              CustomTextFormField(
                initialValue: renovationContact!.phone,
                label: "Telefono",
                labelColor: Color.fromRGBO(149, 149, 149, 1),
                isPercentage: false,
                isMoney: false,
                isNullable: true,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
