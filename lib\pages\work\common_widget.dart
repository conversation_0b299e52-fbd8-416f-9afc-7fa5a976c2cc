import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/tab/common_dropdown_widget.dart';
import 'package:newarc_platform/widget/UI/tab/common_icon_button.dart';
import 'package:newarc_platform/widget/UI/tab/icon_text_button.dart';
import 'package:newarc_platform/widget/UI/tab/status_widget.dart';
import 'package:newarc_platform/widget/UI/tab/text_button.dart';
import 'package:newarc_platform/widget/UI/tab/text_style.dart';
import 'package:newarc_platform/widget/UI/tab/tag_widget.dart';
import 'package:newarc_platform/widget/UI/tab/users_stack_list.dart';

class CommonWidget extends StatefulWidget {
  CommonWidget({super.key});

  @override
  State<CommonWidget> createState() => _CommonWidgetState();
}

class _CommonWidgetState extends State<CommonWidget> {
  final List<String> items = [
    'Status 1',
    'Status 2',
    'Status 3',
    'Status 4',
  ];
  final data = [
    {
      "linkedText": "Linked 1",
      "normalText": "Normal 1",
      "amount": "+850€",
      "dropdownValue": "Option 1",
    },
    {
      "linkedText": "Linked 2",
      "normalText": "Normal 2",
      "amount": "+900€",
      "dropdownValue": "Option 2",
    },
    {
      "linkedText": "Linked 3",
      "normalText": "Normal 3",
      "amount": "+450€",
      "dropdownValue": "Option 3",
    },
    {
      "linkedText": "Linked 4",
      "normalText": "Normal 4",
      "amount": "+1200€",
      "dropdownValue": "Option 1",
    },
    {
      "linkedText": "Linked 5",
      "normalText": "Normal 5",
      "amount": "+780€",
      "dropdownValue": "Option 2",
    },
    {
      "linkedText": "Linked 6",
      "normalText": "Normal 6",
      "amount": "+1100€",
      "dropdownValue": "Option 3",
    },
    {
      "linkedText": "Linked 7",
      "normalText": "Normal 7",
      "amount": "+950€",
      "dropdownValue": "Option 1",
    },
    {
      "linkedText": "Linked 8",
      "normalText": "Normal 8",
      "amount": "+650€",
      "dropdownValue": "Option 2",
    },
    {
      "linkedText": "Linked 9",
      "normalText": "Normal 9",
      "amount": "+730€",
      "dropdownValue": "Option 3",
    },
    {
      "linkedText": "Linked 10",
      "normalText": "Normal 10",
      "amount": "+990€",
      "dropdownValue": "Option 1",
    },
    {
      "linkedText": "Linked 11",
      "normalText": "Normal 11",
      "amount": "+870€",
      "dropdownValue": "Option 2",
    },
    {
      "linkedText": "Linked 12",
      "normalText": "Normal 12",
      "amount": "+640€",
      "dropdownValue": "Option 3",
    },
    {
      "linkedText": "Linked 13",
      "normalText": "Normal 13",
      "amount": "+1010€",
      "dropdownValue": "Option 1",
    },
    {
      "linkedText": "Linked 14",
      "normalText": "Normal 14",
      "amount": "+1230€",
      "dropdownValue": "Option 2",
    },
    {
      "linkedText": "Linked 15",
      "normalText": "Normal 15",
      "amount": "+890€",
      "dropdownValue": "Option 3",
    },
    {
      "linkedText": "Linked 16",
      "normalText": "Normal 16",
      "amount": "+750€",
      "dropdownValue": "Option 1",
    },
    {
      "linkedText": "Linked 17",
      "normalText": "Normal 17",
      "amount": "+980€",
      "dropdownValue": "Option 2",
    },
    {
      "linkedText": "Linked 18",
      "normalText": "Normal 18",
      "amount": "+580€",
      "dropdownValue": "Option 3",
    },
    {
      "linkedText": "Linked 19",
      "normalText": "Normal 19",
      "amount": "+1060€",
      "dropdownValue": "Option 1",
    },
    {
      "linkedText": "Linked 20",
      "normalText": "Normal 20",
      "amount": "+1120€",
      "dropdownValue": "Option 2",
    },
    {
      "linkedText": "Linked 21",
      "normalText": "Normal 21",
      "amount": "+1050€",
      "dropdownValue": "Option 3",
    },
    {
      "linkedText": "Linked 22",
      "normalText": "Normal 22",
      "amount": "+950€",
      "dropdownValue": "Option 1",
    },
    {
      "linkedText": "Linked 23",
      "normalText": "Normal 23",
      "amount": "+680€",
      "dropdownValue": "Option 2",
    },
    {
      "linkedText": "Linked 24",
      "normalText": "Normal 24",
      "amount": "+870€",
      "dropdownValue": "Option 3",
    },
    {
      "linkedText": "Linked 25",
      "normalText": "Normal 25",
      "amount": "+800€",
      "dropdownValue": "Option 1",
    },
  ];

  String? selectedValue;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.white,
      body: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          return Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                NarFormLabelWidget(
                  label: "Misc 22 w700",
                  fontSize: 22,
                  fontWeight: '700',
                  textColor: AppColor.black,
                ),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          NarFormLabelWidget(
                            label: "Normal Text 12 w600",
                            fontSize: 12,
                            fontWeight: '600',
                            textColor: AppColor.black,
                          ),

                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        children: [
                          NarLinkWidget(
                            text: "Linked text 12 w600",
                            textColor: Colors.black,
                            fontWeight: '700',
                            fontSize: 12,
                            onClick: () {},
                          ),

                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        children: [
                          UsersStackWidget(
                            imageList: [
                              "https://t3.ftcdn.net/jpg/02/43/12/34/360_F_243123463_zTooub557xEWABDLk0jJklDyLSGl2jrr.jpg",
                              "https://t3.ftcdn.net/jpg/02/43/12/34/360_F_243123463_zTooub557xEWABDLk0jJklDyLSGl2jrr.jpg",
                              "https://t3.ftcdn.net/jpg/02/43/12/34/360_F_243123463_zTooub557xEWABDLk0jJklDyLSGl2jrr.jpg",
                              "https://t3.ftcdn.net/jpg/02/43/12/34/360_F_243123463_zTooub557xEWABDLk0jJklDyLSGl2jrr.jpg",
                            ],
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        children: [
                          TagWidget(
                            text: "Something Green",
                          ),
                          SizedBox(height: 8),
                          TagWidget(
                            text: "Something Red",
                            statusColor: AppColor.redColor,
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        children: [
                          Row(
                            children: [
                              IconButtonWidget(
                                onTap: () {},
                                isSvgIcon: true,
                                icon: 'assets/icons/account.svg',
                                iconColor: AppColor.greyColor,
                              ),
                              SizedBox(
                                width: 8,
                              ),
                              IconButtonWidget(
                                onTap: () {},
                                isSvgIcon: true,
                                icon: 'assets/icons/edit.svg',
                                iconColor: AppColor.greyColor,
                              ),
                              SizedBox(
                                width: 8,
                              ),
                              IconButtonWidget(
                                onTap: () {},
                                isSvgIcon: true,
                                isOnlyBorder: true,
                                backgroundColor: Colors.transparent,
                                borderColor: AppColor.redColor,
                                iconColor: AppColor.redColor,
                                iconPadding: EdgeInsets.all(3),
                                icon: 'assets/icons/trash.svg',
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        children: [
                          IconTextButtonWidget(
                            text: "+850€",
                            icon: 'assets/icons/cash_in.svg',
                            textColor: AppColor.successGreenColor,
                            iconColor: AppColor.successGreenColor,
                            iconOnLeft: true,
                            onPressed: () {
                              print("Positive button pressed");
                            },
                          ),
                          SizedBox(height: 16),
                          // Negative Button
                          IconTextButtonWidget(
                            text: "-1,250€",
                            icon: 'assets/icons/cash_out.svg',
                            textColor: AppColor.redColor,
                            iconColor: AppColor.redColor,
                            iconOnLeft: false,
                            onPressed: () {
                              print("Negative button pressed");
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                NarFormLabelWidget(
                  label: "Status",
                  fontSize: 22,
                  fontWeight: '700',
                  textColor: AppColor.black,
                ),
                // Text(
                //   "Status",
                //   style: TextStyle().text22w700.textColor(
                //         AppColor.black,
                //       ),
                // ),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          StatusWidget(
                            statusColor: AppColor.redColor,
                          ),
                          SizedBox(
                            height: 8,
                          ),
                          StatusWidget(
                            statusColor: AppColor.successGreenColor,
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        children: [
                          StatusWidget(
                            statusColor: AppColor.redColor,
                            status: "Status",
                          ),
                          SizedBox(
                            height: 8,
                          ),
                          StatusWidget(
                            statusColor: AppColor.successGreenColor,
                            status: "Status",
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        children: [
                          // CustomDropdownButton(
                          //   selectedValue: selectedValue,
                          //   items: items,
                          //   hintText: 'Select Status',
                          //   containerColor: Colors.green,
                          //   onChanged: (value) {
                          //     setState(() {
                          //       selectedValue = value;
                          //     });
                          //   },
                          // ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        children: [
                          TextButtonWidget(
                            isOnlyBorder: true,
                            status: "Status",
                            textColor: AppColor.black,
                          ),
                          SizedBox(
                            height: 8,
                          ),
                          TextButtonWidget(
                            isOnlyBorder: true,
                            borderColor: AppColor.lightGreen200Color,
                            backgroundColor: AppColor.lightGreen100Color,
                            status: "Other status",
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        children: [
                          // CustomDropdownButton(
                          //   selectedValue: selectedValue,
                          //   items: items,
                          //   hintText: 'Select Status',
                          //   showCircle: false,
                          //   onChanged: (value) {
                          //     setState(() {
                          //       selectedValue = value;
                          //     });
                          //   },
                          // ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 20,
                ),
                Expanded(
                  child: NewarcDataTable(
                    source: RowSource(data: data),
                    columns: [
                      DataColumn2(
                        label: Text('Text Linked'),
                      ),
                      DataColumn2(
                        label: Text('Normal text'),
                      ),
                      DataColumn2(
                        label: Text('Icon Button'),
                      ),
                      DataColumn2(
                        label: Text('Status'),
                      ),
                      DataColumn2(
                        label: Text('Cash In-Out'),
                      ),
                      DataColumn2(
                        label: Text('Status DropDown'),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class RowSource extends DataTableSource {
  RowSource({required this.data});

  final List<Map<String, dynamic>> data;

  @override
  DataRow? getRow(int index) {
    if (index < data.length) {
      final row = data[index];
      return DataRow(
        color: MaterialStateColor.resolveWith(
          (states) => index != 1 ? Colors.transparent : AppColor.lightGreenDataTableBgColor,
        ),
        cells: [
          DataCell(
            Stack(
              clipBehavior: Clip.none,
              children: [
                Positioned(
                  top: -18,
                  child: TagWidget(
                    text: "Something",
                  ),
                ),
                NarLinkWidget(
                  text: row["linkedText"] ?? "",
                  textColor: Colors.black,
                  fontWeight: '700',
                  fontSize: 12,
                  onClick: () {},
                ),
                // InkWell(
                //   highlightColor: Colors.transparent,
                //   splashColor: Colors.transparent,
                //   splashFactory: NoSplash.splashFactory,
                //   hoverColor: Colors.transparent,
                //   onTap: () {},
                //   child: Text(
                //     row["linkedText"] ?? "",
                //     style: TextStyle().text12w700.textColor(AppColor.black).underline(),
                //   ),
                // ),
              ],
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: row["normalText"] ?? "",
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),
            // Text(
            //   row["normalText"] ?? "",
            //   style: TextStyle().text12w600.textColor(
            //         AppColor.black,
            //       ),
            // ),
          ),
          DataCell(
            Row(
              children: [
                IconButtonWidget(
                  onTap: () {},
                  isSvgIcon: true,
                  icon: 'assets/icons/account.svg',
                  iconColor: AppColor.greyColor,
                ),
                SizedBox(width: 8),
                IconButtonWidget(
                  onTap: () {},
                  isSvgIcon: true,
                  icon: 'assets/icons/edit.svg',
                  iconColor: AppColor.greyColor,
                ),
                SizedBox(width: 8),
                IconButtonWidget(
                  onTap: () {},
                  isSvgIcon: true,
                  isOnlyBorder: true,
                  backgroundColor: Colors.transparent,
                  borderColor: AppColor.redColor,
                  iconColor: AppColor.redColor,
                  icon: 'assets/icons/trash.svg',
                ),
              ],
            ),
          ),
          DataCell(
            Row(
              children: [
                StatusWidget(statusColor: AppColor.redColor),
                SizedBox(width: 10),
                StatusWidget(statusColor: AppColor.successGreenColor),
              ],
            ),
          ),
          DataCell(
            IconTextButtonWidget(
              text: row["amount"] ?? "",
              icon: 'assets/icons/cash_in.svg',
              textColor: AppColor.successGreenColor,
              iconColor: AppColor.successGreenColor,
              iconOnLeft: true,
              onPressed: () {
                print("Amount button pressed");
              },
            ),
          ),
          DataCell(
            CustomDropdownButton(
              selectedValue: row["dropdownValue"],
              items: ["Option 1", "Option 2", "Option 3"],
              hintText: 'Select Status',
              containerColor: Colors.green,
              getColor: (status) {
                switch (status) {
                  case 'Da sbloccare':
                    return Color.fromRGBO(166, 166, 166, 1);
                  default:
                    return Colors.transparent;
                }
              },
              onChanged: (value) {
                print("Selected value: $value");
              },
            ),
          ),
        ],
      );
    }
    return null;
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => data.length;

  @override
  int get selectedRowCount => 0;
}
