import 'package:flutter/material.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/work/project/entrate_pagamenti.dart';
import 'package:newarc_platform/widget/work/project/payments.dart';
import 'package:newarc_platform/widget/work/project/uscite_pagamenti.dart';
import '../../../classes/newarcProject.dart';
import '../../../classes/supplier.dart';
import '../../UI/form-label.dart';

class NewEconomic extends StatefulWidget {
  final NewarcProject? project;
  final List<Supplier>? suppliers;
  final Function? updateProject;
  const NewEconomic({super.key, this.suppliers, this.updateProject, this.project});

  @override
  State<NewEconomic> createState() => _NewEconomicState();
}

class _NewEconomicState extends State<NewEconomic> {
  int selectedTabIndex = 0;

  final List<String> tabs = ['Entrate', 'Uscite', 'Pagamenti'];
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body:   Container(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Tabs
            Container(
              decoration: BoxDecoration(
                color: Color(0xFFECECEC),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(13),
                  topRight: Radius.circular(13),
                ),
                border: Border(
                  top: BorderSide(width: 1, color: Color(0xFFECECEC)),
                  left: BorderSide(width: 1, color: Color(0xFFECECEC)),
                  right: BorderSide(width: 1, color: Color(0xFFECECEC)),
                ),
              ),
              child: Row(
                children: List.generate(tabs.length, (index) {
                  bool isSelected = selectedTabIndex == index;
                  return Expanded(
                    child: MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () => setState(() {
                          selectedTabIndex = index;
                        }),
                        child: Container(
                          height: 40,
                          decoration: BoxDecoration(
                            color: isSelected ? Colors.white : Colors.transparent,
                            borderRadius: BorderRadius.only(
                              topLeft: index == 0 ? Radius.circular(13) : Radius.zero,
                              topRight: index == tabs.length - 1 ? Radius.circular(13) : Radius.zero,
                            ),
                          ),
                          child: Center(
                            child:NarFormLabelWidget(
                              label: tabs[index],
                              fontSize: 15,
                              fontWeight: '600',
                              textColor: isSelected ? AppColor.black : AppColor.greyColor,
                            ),

                          ),
                        ),
                      ),
                    ),
                  );
                }),
              ),
            ),

            // Content Area

            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                decoration: BoxDecoration(
                  border: Border.all(color: Color(0xFFECECEC),width: 1),
                  borderRadius: BorderRadius.only(bottomLeft: Radius.circular(13),bottomRight: Radius.circular(13)),

                ),
                child: IndexedStack(
                  index: selectedTabIndex,
                  children: [
                    EntratePagamenti(project: widget.project,key: ValueKey(selectedTabIndex)),
                    UscitePagamenti(
                      project: widget.project,
                      suppliers: widget.suppliers,
                      updateProject: widget.updateProject,
                      key: ValueKey(selectedTabIndex)
                    ),
                    ProjectPayments(project: widget.project,suppliers: widget.suppliers,updateProject: widget.updateProject,key: ValueKey(selectedTabIndex),),
                  ],
                ),
              ),
            ),

          ],
        ),
      ),
    );
  }
}
