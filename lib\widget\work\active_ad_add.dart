import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/classes/property.dart';
import 'package:newarc_platform/widget/work/ads/active_ad.dart';
import 'package:newarc_platform/widget/work/project/ads/configuration.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;

import '../../utils/color_schema.dart';

class ActiveAdAdd extends StatefulWidget {
  var project;
  Property? property;
  final Function? initialFetchProperties;
  final Function? updateViewCallback;
  List<bool>? isInputChangeDetected = [];
  bool? wasArchived;

  ActiveAdAdd(
      {Key? key,
      this.project,
      this.property,
      this.updateViewCallback,
      this.wasArchived,
      this.initialFetchProperties,
      this.isInputChangeDetected })
      : super(key: key);

  static const String route = '/property/add';

  @override
  _ActiveAdAddState createState() => _ActiveAdAddState();
}

class _ActiveAdAddState extends State<ActiveAdAdd> {
  
  bool loading = false;
  bool isAdSectionActive = true;
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final _formKey = GlobalKey<FormState>();

  String _address = '';

  String pageTitle = '';

  
  String projectType = '';
  Color? projectTypeColor;
  

  @override
  initState(){

    // if( widget.project.runtimeType == NewarcProject ) {
    //   NewarcProject _pro = widget.project;
    //   _address = _pro.addressInfo!.streetName! +' '+ _pro.addressInfo!.streetNumber! +', '+_pro.addressInfo!.city!;
    // } else {
      
    //   Immag _pro = widget.project;
    //   _address = _pro.addressInfo!.streetName! +' '+ _pro.addressInfo!.streetNumber! +', '+_pro.addressInfo!.city!;
    // }
    super.initState();

    var _pro = widget.project;
    _address = _pro.addressInfo!.toShortAddress();

    
    if( widget.project!.runtimeType == NewarcProject) {
      projectType = widget.project!.type;
    } else {
      if( widget.property!.projectType! == 'Immagina for Professionals' ) {
        projectType = 'Professionals';
      } else {
        projectType = widget.property!.projectType!;
      }
    }

    String rooms = '';
    if( widget.property!.projectType == 'cut' ) {
      switch (int.tryParse(widget.property!.locals !.toString())!) {
        case 1:
          rooms = 'Monolocale';
          break;
        case 2:
          rooms = 'Bilocale';
          break;
        case 3:
          rooms = 'Trilocale';
          break;
        case 4:
          rooms = 'Quadrilocale';
          break;
        default:
          rooms = 'Plurilocale';
          break;
      }
    }
    

    
    if( widget.property!.projectType == 'cut' ) {
      pageTitle = rooms +' in '+ widget.property!.addressInfo!.toShortAddress();;
    } else {
      pageTitle = widget.property!.addressInfo!.toShortAddress();
    } 

    
  }
  
  @override
  Widget build(BuildContext context) {
    
    return Stack(
      children: [
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Stack(
              clipBehavior: Clip.none,
              // mainAxisSize: MainAxisSize.max,
              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
              // crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Positioned(
                  left: 0,
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () async {
                        if( widget.property!.projectType == 'cut' ) {

                          DocumentSnapshot<Map<String, dynamic>> cutResponse =
                          await FirebaseFirestore.instance
                              .collection(appConfig.COLLECT_NEWARC_HOME)
                              .doc(widget.project.propertyId)
                              .get();
                          Property building = Property.fromDocument(cutResponse);    
                          widget.updateViewCallback!(
                            'active-ad-single',
                            projectArguments: {
                              'property': building,
                              'project': widget.project,
                              'wasArchived': building.isArchived,
                              'isInputChangeDetected': [false]
                            }
                          );
                        } else {
                          widget.updateViewCallback!(
                            'active-ads', projectArguments: {
                            'isArchived': widget.wasArchived
                          });
                        }
                        
                        widget.initialFetchProperties;
                      },
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // IconButton(
                          //   hoverColor: Colors.transparent,
                          //   focusColor: Colors.transparent,
                          //   onPressed: () {
                          //     widget.updateViewCallback!(
                          //         'active-ads', projectArguments: {
                          //       'isArchived': widget.wasArchived
                          //     });
                          //     widget.initialFetchProperties;
                          //   },
                          //   icon: SvgPicture.asset(
                          //       'assets/icons/arrow_left.svg',
                          //       height: 20,
                          //       color: Colors.black),
                          // ),
                          SvgPicture.asset(
                              'assets/icons/arrow_left.svg',
                              height: 15,
                              color: Colors.black),
                          SizedBox(width: 10,),
                          SizedBox(width: 5,),
                          widget.property!.projectType == 'cut'
                          ? NarFormLabelWidget(
                            label: widget.property!.addressInfo!.toShortAddress(),
                            fontSize: 15,
                            fontWeight: '600',
                            textDecoration: TextDecoration.underline,
                            textColor: AppColor.black,
                          )
                          : NarFormLabelWidget(
                            label: 'Tutti gli annunci',
                            fontSize: 15,
                            fontWeight: '600',
                            textDecoration: TextDecoration.underline,
                            textColor: AppColor.black,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    NarFormLabelWidget(
                      label: pageTitle,
                      fontSize: 18,
                      fontWeight: '700',
                      textColor: Colors.black,
                    ),
                  ],
                ),
                

                widget.property!.projectType != 'cut'
                ? Positioned(
                  right: 0,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      NarFormLabelWidget(
                        label: 'Tipologia annuncio:',
                        fontSize: 13,
                        fontWeight: '600',
                        textColor: AppColor.black,
                      ),
                      SizedBox(
                        width: 10,
                      ),
                      Container(
                        padding: EdgeInsets.symmetric( vertical: 5, horizontal: 10),
                        decoration: BoxDecoration(
                          color: widget.property!.projectType! == 'Immagina for Professionals' ? Colors.black : Theme.of(context).primaryColor,
                          borderRadius:
                          BorderRadius.circular(5),
                          border: Border.all( color: Theme.of(context).primaryColor),
                        ),
                        child: NarFormLabelWidget(
                          label: projectType,
                          fontSize: 15,
                          fontWeight: 'bold',
                          textColor: Colors.white,
                        ),
                      )
                    ],
                  ),
                )
                : Container(),
              ]
          ),
        ),
        Padding(
          padding: EdgeInsets.only(top: 50),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Color(0xffECECEC),
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(13),
                        topRight: Radius.circular(13)
                    ),
                    border: Border(
                      top: BorderSide(
                          width: 1, color: Color(0xffECECEC)),
                      left: BorderSide(
                          width: 1, color: Color(0xffECECEC)),
                      right: BorderSide(
                          width: 1, color: Color(0xffECECEC)),
                    ),
                  ),
                  margin: EdgeInsets.only(top: 20),
                  child: 
                  widget.property!.projectType != 'cut' 
                  ? Row(
                    children: [
                      Expanded(
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => setState((){isAdSectionActive = true;}),
                            child: Container(
                                height: 40,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(13),
                                        topRight: isAdSectionActive ? Radius.circular(13) : Radius.circular(0)
                                    ),
                                    color: isAdSectionActive ? Colors.white : Colors.transparent
                                ),
                                child: Center(
                                  child: NarFormLabelWidget(
                                      label: 'Annuncio',
                                      fontSize: 15,
                                      textColor: Colors.black
                                  ),
                                )
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () => setState((){isAdSectionActive = false;}),
                            child: Container(
                                height: 40,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(13),
                                        topRight: Radius.circular(13)
                                    ),
                                    color: !isAdSectionActive ? Colors.white : Colors.transparent
                                ),
                                child: Center(
                                  child: NarFormLabelWidget(
                                      label: 'Configuratore',
                                      fontSize: 15,
                                      textColor: Colors.black
                                  ),
                                )
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                  : Container( 
                    height: 15, 
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(13),
                            topRight: isAdSectionActive ? Radius.circular(13) : Radius.circular(0)
                        ),
                        color: isAdSectionActive ? Colors.white : Colors.transparent
                    ),
                  ),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 1,
                      child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                          decoration: BoxDecoration(
                            border: Border(
                              top: BorderSide(width: 1, color: Colors.white),
                              bottom: BorderSide(width: 1, color: Color(0xffECECEC)),
                              left: BorderSide(width: 1, color: Color(0xffECECEC)),
                              right: BorderSide(width: 1, color: Color(0xffECECEC)),
                            ),
                          ),
                          child: isAdSectionActive
                              ? ActiveAd (
                              project: widget.project,
                              property: widget.property,
                              initialFetchProperties: widget.initialFetchProperties,
                              isInputChangeDetected: widget.isInputChangeDetected,
                              updateViewCallback: widget.updateViewCallback
                          )
                              : ConfigurationForm(
                            property: widget.property,
                          )

                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),

      ],
    );
  }

  
}
