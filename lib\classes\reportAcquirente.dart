import 'package:cloud_firestore/cloud_firestore.dart';
import 'baseAddressInfo.dart';

class ReportAcquirente {

  String? id;
  int? insertionTimestamp;
  String? agencyId;

  // Localizzazione
  @Deprecated('Use ImmaginaProject.addressInfo.marketZone instead, keep in mind retrocompatibility')
  String? marketZone;
  BaseAddressInfo? addressInfo;

  // Info Generali
  int? grossSquareFootage;
  int? rooms;
  int? numberOfBathrooms;
  String? unitFloor;
  int? listingPrice;
  String? propertyType;
  String? buyerReportStatus;
  String? stateOfFixtures ;
  String? objective ;
  String? category ;

  // Informazioni sullo stabile
  String? tipologiaStabile;
  String? stableCondition; //Condizioni stabile
  String? totalPlans; // Piani totali
  double? condominiumExpenses; // Spese condiominiali
  String? heating; // Riscaldamento
  double? heatingCosts; // Spese di riscaldamento

  // Descrizione
  String? description;
  String? descriptionNeighborhood;

  // Stima ristrutturazione
  bool? wantToIncludeEstimateForRenovation;
  // int? numberOfInternalDoors;
  // double? walkableSquareMeters;
  // double? mqOfFixtures;
  // String? qualityOfTheArea;

  // Planimetria
  late List planimetry;

  // Fotografie
  late List pictures;

  // Dotazioni e particolarità
  bool? elevator;
  bool? hasCantina;
  bool? airConditioning;
  bool? securityDoor;
  bool? fiber;
  bool? tvStation;
  bool? highEfficiencyFrames;
  bool? alarm;
  bool? terrace;
  bool? sharedGarden;
  bool? privateGarden;
  bool? hasConcierge;
  bool? motorizedSunblind;
  bool? domotizedSunblind;
  bool? domotizedLights;
  bool? centralizedHeating;
  bool? autonomousHeating;
  bool? highFloor;
  bool? metroVicinity;
  bool? bigBalconies;
  bool? doubleEsposition;
  bool? tripleEsposition;
  bool? quadrupleEsposition;
  bool? bigLiving;
  bool? doubleBathroom;
  bool? nobleBuilding;
  bool? surveiledBuilding;
  bool? swimmingPool;
  bool? hasGarage;
  String? actualEnergyClass;
  String? projectEnergyClass;
  int? constructionYear;
  late List externalEsposition;
  bool? solarPanel;
  bool? walkInCloset;

  // Indicazioni speciali
  String? propertyUpForSaleAnswer;
  bool? hasSpecialHints;
  String? specialHints;

  // Prospetto costi for buyer report
  double? landRegistryIncome;
  double? notaryCost;
  double? registrationTax;
  


  // Prodotti Newarc Immagina
  late List reportPictures;
  late List reportRenders;
  late List reportVideoRender;
  late List reportPlanimetry;
  late List reportAdPictures;
  String? reportBrochure;
  String? reportVirtualTour;
  
  //For Buyer Report
  bool? isLocalizzazioneIncludeInBuyerReport;
  bool? isInformazioniGeneraliIncludeInBuyerReport;
  bool? isInformazioniSulloStabileIncludeInBuyerReport;
  bool? isDescrizioneImmobileIncludeInBuyerReport;
  bool? isDescrizioneDelQuartiereIncludeInBuyerReport;
  bool? isCaratteristicheInBuyerReport;
  bool? isProspettoCostiInBuyerReport;
  bool? isPlanimetriaCatastaleInBuyerReport;
  bool? isFotografieInterneInBuyerReport;
  bool? isFotografieEsterneInBuyerReport;
  bool? isVirtualTourInBuyerReport;
  bool? isStimaRistrutturazioneInBuyerReport;
  bool? isHeatingAndEnergyInBuyerReport;

  String? buyerReportColor;
  Map? buyerReportCoverImage;
  String? buyerReportAgencyPersonId;
  String? coverTemplateType;
  String? fontType;
  String? photoTemplateType;


  ReportAcquirente(Map<String, dynamic> immaginaProjectMap) {
    
    this.buyerReportStatus = immaginaProjectMap['buyerReportStatus'];
    this.stateOfFixtures = immaginaProjectMap['stateOfFixtures'];
    this.tipologiaStabile = immaginaProjectMap['tipologiaStabile'];
    this.stableCondition = immaginaProjectMap['stableCondition'];
    this.totalPlans = immaginaProjectMap['totalPlans'];
    this.condominiumExpenses = immaginaProjectMap['condominiumExpenses'];
    this.heating = immaginaProjectMap['heating'];
    this.heatingCosts = immaginaProjectMap['heatingCosts'];
    this.objective = immaginaProjectMap['objective'];
    this.category = immaginaProjectMap['category'];
    
    this.wantToIncludeEstimateForRenovation = immaginaProjectMap['wantToIncludeEstimateForRenovation'];
    // this.numberOfInternalDoors = immaginaProjectMap['numberOfInternalDoors'];
    // this.walkableSquareMeters = immaginaProjectMap['walkableSquareMeters'];
    // this.mqOfFixtures = immaginaProjectMap['mqOfFixtures'];
    // this.qualityOfTheArea = immaginaProjectMap['qualityOfTheArea'];

    this.landRegistryIncome = immaginaProjectMap['landRegistryIncome'];
    this.notaryCost = immaginaProjectMap['notaryCost'];
    this.registrationTax = immaginaProjectMap['registrationTax'];

    this.id = immaginaProjectMap['id'];
    this.insertionTimestamp = immaginaProjectMap['insertionTimestamp'];
    this.agencyId = immaginaProjectMap['agencyId'];
    this.marketZone = immaginaProjectMap['marketZone'];
    this.addressInfo = (immaginaProjectMap.containsKey('addressInfo') && immaginaProjectMap['addressInfo'] != null) ? BaseAddressInfo.fromMap(immaginaProjectMap['addressInfo']) : null;
    this.grossSquareFootage = immaginaProjectMap['grossSquareFootage'];
    this.rooms = immaginaProjectMap['rooms'];
    this.numberOfBathrooms = immaginaProjectMap['numberOfBathrooms'];
    this.unitFloor = immaginaProjectMap['unitFloor'];
    this.listingPrice = immaginaProjectMap['listingPrice'];
    this.propertyType = immaginaProjectMap['propertyType'];
    this.description = immaginaProjectMap['description'];
    this.descriptionNeighborhood = immaginaProjectMap['descriptionNeighborhood'];
    this.planimetry = immaginaProjectMap['planimetry'];
    this.pictures = immaginaProjectMap['pictures'];
    this.elevator = immaginaProjectMap['elevator'];
    this.hasCantina = immaginaProjectMap['hasCantina'];
    this.airConditioning = immaginaProjectMap['airConditioning'];
    this.securityDoor = immaginaProjectMap['securityDoor'];
    this.fiber = immaginaProjectMap['fiber'];
    this.tvStation = immaginaProjectMap['tvStation'];
    this.highEfficiencyFrames = immaginaProjectMap['highEfficiencyFrames'];
    this.alarm = immaginaProjectMap['alarm'];
    this.terrace = immaginaProjectMap['terrace'];
    this.sharedGarden = immaginaProjectMap['sharedGarden'];
    this.privateGarden = immaginaProjectMap['privateGarden'];
    this.hasConcierge = immaginaProjectMap['hasConcierge'];
    this.motorizedSunblind = immaginaProjectMap['motorizedSunblind'];
    this.domotizedSunblind = immaginaProjectMap['domotizedSunblind'];
    this.domotizedLights = immaginaProjectMap['domotizedLights'];
    this.centralizedHeating = immaginaProjectMap['centralizedHeating'];
    this.autonomousHeating = immaginaProjectMap['autonomousHeating'];
    this.highFloor = immaginaProjectMap['highFloor'];
    this.metroVicinity = immaginaProjectMap['metroVicinity'];
    this.bigBalconies = immaginaProjectMap['bigBalconies'];
    this.doubleEsposition = immaginaProjectMap['doubleEsposition'];
    this.tripleEsposition = immaginaProjectMap['tripleEsposition'];
    this.quadrupleEsposition = immaginaProjectMap['quadrupleEsposition'];
    this.bigLiving = immaginaProjectMap['bigLiving'];
    this.doubleBathroom = immaginaProjectMap['doubleBathroom'];
    this.nobleBuilding = immaginaProjectMap['nobleBuilding'];
    this.surveiledBuilding = immaginaProjectMap['surveiledBuilding'];
    this.hasGarage = immaginaProjectMap['hasGarage'];
    this.swimmingPool = immaginaProjectMap['swimmingPool'];
    this.actualEnergyClass = immaginaProjectMap['actualEnergyClass'];
    this.projectEnergyClass = immaginaProjectMap['projectEnergyClass'];
    this.constructionYear = immaginaProjectMap['constructionYear'];
    this.externalEsposition = immaginaProjectMap['externalEsposition'];
    this.solarPanel = immaginaProjectMap['solarPanel'];
    this.walkInCloset = immaginaProjectMap['walkInCloset'];
    this.hasSpecialHints = immaginaProjectMap['hasSpecialHints'];
    this.specialHints = immaginaProjectMap['specialHints'];
    this.reportPictures = immaginaProjectMap['immaginaPictures'];
    this.reportRenders = immaginaProjectMap['immaginaRenders'];
    this.reportVideoRender = immaginaProjectMap['immaginaVideoRender'];
    this.reportPlanimetry = immaginaProjectMap['immaginaPlanimetry'];
    this.reportAdPictures = immaginaProjectMap['immaginaAdPictures'];
    this.reportBrochure = immaginaProjectMap['immaginaBrochure'];
    this.reportVirtualTour = immaginaProjectMap['immaginaVirtualTour'];
    this.propertyUpForSaleAnswer = immaginaProjectMap['propertyUpForSaleAnswer'];
    this.isLocalizzazioneIncludeInBuyerReport = immaginaProjectMap['isLocalizzazioneIncludeInBuyerReport'];
    this.isInformazioniGeneraliIncludeInBuyerReport = immaginaProjectMap['isInformazioniGeneraliIncludeInBuyerReport'];
    this.isInformazioniSulloStabileIncludeInBuyerReport = immaginaProjectMap['isInformazioniSulloStabileIncludeInBuyerReport'];
    this.isDescrizioneImmobileIncludeInBuyerReport = immaginaProjectMap['isDescrizioneImmobileIncludeInBuyerReport'];
    this.isDescrizioneDelQuartiereIncludeInBuyerReport = immaginaProjectMap['isDescrizioneDelQuartiereIncludeInBuyerReport'];
    this.isCaratteristicheInBuyerReport = immaginaProjectMap['isCaratteristicheInBuyerReport'];
    this.isProspettoCostiInBuyerReport = immaginaProjectMap['isProspettoCostiInBuyerReport'];
    this.isPlanimetriaCatastaleInBuyerReport = immaginaProjectMap['isPlanimetriaCatastaleInBuyerReport'];
    this.isFotografieInterneInBuyerReport = immaginaProjectMap['isFotografieInterneInBuyerReport'];
    this.isFotografieEsterneInBuyerReport = immaginaProjectMap['isFotografieEsterneInBuyerReport'];
    this.isVirtualTourInBuyerReport = immaginaProjectMap['isVirtualTourInBuyerReport'];
    this.isStimaRistrutturazioneInBuyerReport = immaginaProjectMap['isStimaRistrutturazioneInBuyerReport'];
    this.isHeatingAndEnergyInBuyerReport = immaginaProjectMap['isHeatingAndEnergyInBuyerReport'];
    this.buyerReportColor = immaginaProjectMap['buyerReportColor'];
    this.buyerReportCoverImage = immaginaProjectMap['buyerReportCoverImage'];
    this.buyerReportAgencyPersonId = immaginaProjectMap['buyerReportAgencyPersonId'];
    this.coverTemplateType = immaginaProjectMap['coverTemplateType'] ?? "";
    this.fontType = immaginaProjectMap['fontType'];
    this.photoTemplateType = immaginaProjectMap['photoTemplateType'];
  }
  

  ReportAcquirente.empty() {
    this.propertyUpForSaleAnswer = "";
    this.tipologiaStabile = null;
    this.stableCondition = null;
    this.totalPlans = null;
    this.condominiumExpenses = null;
    this.heating = null;
    this.heatingCosts = null;
    this.objective = null;
    this.category = null;
    this.wantToIncludeEstimateForRenovation = true;
    // this.numberOfInternalDoors = null;
    // this.walkableSquareMeters = null;
    // this.mqOfFixtures = null;
    // this.qualityOfTheArea = "";
    this.id = '';
    this.insertionTimestamp = Timestamp.now().millisecondsSinceEpoch;
    this.agencyId = '';
    this.landRegistryIncome = null;
    this.notaryCost = null;
    this.registrationTax = null;
    this.marketZone = null;
    this.addressInfo = BaseAddressInfo.empty();
    this.grossSquareFootage = null;
    this.rooms = null;
    this.numberOfBathrooms = null;
    this.buyerReportStatus = null;
    this.stateOfFixtures = null;
    this.unitFloor = null;
    this.listingPrice = null;
    this.propertyType = null;
    this.description = null;
    this.descriptionNeighborhood = null;
    this.hasSpecialHints = null;
    this.specialHints = null;
    this.planimetry = [];
    this.pictures = [];
    this.elevator = null;
    this.hasCantina = null;
    this.airConditioning = null;
    this.securityDoor = null;
    this.fiber = null;
    this.tvStation = null;
    this.highEfficiencyFrames = null;
    this.alarm = null;
    this.terrace = null;
    this.sharedGarden = null;
    this.privateGarden = null;
    this.hasConcierge = null;
    this.motorizedSunblind = null;
    this.domotizedSunblind = null;
    this.domotizedLights = null;
    this.centralizedHeating = null;
    this.autonomousHeating = null;
    this.highFloor = null;
    this.metroVicinity = null;
    this.bigBalconies = null;
    this.doubleEsposition = null;
    this.tripleEsposition = null;
    this.quadrupleEsposition = null;
    this.bigLiving = null;
    this.doubleBathroom = null;
    this.nobleBuilding = null;
    this.surveiledBuilding = null;
    this.swimmingPool = null;
    this.hasGarage = null;
    this.actualEnergyClass = null;
    this.projectEnergyClass = null;
    this.constructionYear = null;
    this.externalEsposition = [];
    this.solarPanel = null;
    this.walkInCloset = null;
    this.reportPictures = [];
    this.reportRenders = [];
    this.reportVideoRender = [];
    this.reportPlanimetry = [];
    this.reportAdPictures = [];
    this.reportBrochure = null;
    this.reportVirtualTour = null;
    this.isLocalizzazioneIncludeInBuyerReport = false;
    this.isInformazioniGeneraliIncludeInBuyerReport = false;
    this.isInformazioniSulloStabileIncludeInBuyerReport = false;
    this.isDescrizioneImmobileIncludeInBuyerReport = false;
    this.isDescrizioneDelQuartiereIncludeInBuyerReport = false;
    this.isCaratteristicheInBuyerReport = false;
    this.isProspettoCostiInBuyerReport = false;
    this.isPlanimetriaCatastaleInBuyerReport = false;
    this.isFotografieInterneInBuyerReport = false;
    this.isFotografieEsterneInBuyerReport = false;
    this.isVirtualTourInBuyerReport = false;
    this.isStimaRistrutturazioneInBuyerReport = false;
    this.isHeatingAndEnergyInBuyerReport = false;
    this.buyerReportColor = "";
    this.buyerReportCoverImage = {};
    this.buyerReportAgencyPersonId = "";
    this.coverTemplateType = "";
    this.fontType = "";
    this.photoTemplateType = "";
  }

  ReportAcquirente.fromDocument(Map<String, dynamic> data, String id) {
    
    this.id = id;
    
    this.propertyUpForSaleAnswer = data['propertyUpForSaleAnswer'] ?? "";
    this.buyerReportStatus = data['buyerReportStatus'] ?? null;
    this.stateOfFixtures = data['stateOfFixtures'] ?? null;
    this.tipologiaStabile = data['tipologiaStabile'] ?? null;
    this.stableCondition = data['stableCondition'] ?? null;
    this.totalPlans = data['totalPlans'] ?? null;
    this.condominiumExpenses = data['condominiumExpenses'] ?? null;
    this.heating = data['heating'] ?? null;
    this.heatingCosts = data['heatingCosts'] ?? null;
    this.objective = data['objective'] ?? null;
    this.category = data['category'] ?? null;

    this.landRegistryIncome = data['landRegistryIncome'] ?? null;
    this.notaryCost = data['notaryCost'] ?? null;
    this.registrationTax = data['registrationTax'] ?? null;
    
    this.wantToIncludeEstimateForRenovation = data['wantToIncludeEstimateForRenovation'] ?? true;
    // this.numberOfInternalDoors = data['numberOfInternalDoors'] ?? null;
    // this.walkableSquareMeters = data['walkableSquareMeters'] ?? null;
    // this.mqOfFixtures = data['mqOfFixtures'] ?? null;
    // this.qualityOfTheArea = data['qualityOfTheArea'] ?? null;
    this.hasSpecialHints = data['hasSpecialHints'];
    this.specialHints = data['specialHints'];
    this.insertionTimestamp = data['insertionTimestamp'] ?? Timestamp.now().millisecondsSinceEpoch;
    this.agencyId = data['agencyId'] ?? '';
    this.marketZone = data['marketZone'];
    this.addressInfo = (data.containsKey('addressInfo') && data['addressInfo'] != null) ? BaseAddressInfo.fromMap(data['addressInfo']) : null;
    this.grossSquareFootage = data['grossSquareFootage'];
    this.rooms = data['rooms'];
    this.numberOfBathrooms = data['numberOfBathrooms'];
    this.unitFloor = data['unitFloor'];
    this.listingPrice = data['listingPrice'];
    this.propertyType = data['propertyType'];
    this.description = data['description'];
    this.descriptionNeighborhood = data['descriptionNeighborhood'];
    this.planimetry = data['planimetry'] ?? [];
    this.pictures = data['pictures'] ?? [];
    this.elevator = data['elevator'];
    this.hasCantina = data['hasCantina'];
    this.airConditioning = data['airConditioning'];
    this.securityDoor = data['securityDoor'];
    this.fiber = data['fiber'];
    this.tvStation = data['tvStation'];
    this.highEfficiencyFrames = data['highEfficiencyFrames'];
    this.alarm = data['alarm'];
    this.terrace = data['terrace'];
    this.sharedGarden = data['sharedGarden'];
    this.privateGarden = data['privateGarden'];
    this.hasConcierge = data['hasConcierge'];
    this.motorizedSunblind = data['motorizedSunblind'];
    this.domotizedSunblind = data['domotizedSunblind'];
    this.domotizedLights = data['domotizedLights'];
    this.centralizedHeating = data['centralizedHeating'];
    this.autonomousHeating = data['autonomousHeating'];
    this.highFloor = data['highFloor'];
    this.metroVicinity = data['metroVicinity'];
    this.bigBalconies = data['bigBalconies'];
    this.doubleEsposition = data['doubleEsposition'];
    this.tripleEsposition = data['tripleEsposition'];
    this.quadrupleEsposition = data['quadrupleEsposition'];
    this.bigLiving = data['bigLiving'];
    this.doubleBathroom = data['doubleBathroom'];
    this.nobleBuilding = data['nobleBuilding'];
    this.surveiledBuilding = data['surveiledBuilding'];
    this.swimmingPool = data['swimmingPool'];
    this.hasGarage = data['hasGarage'];
    this.actualEnergyClass = data['actualEnergyClass'];
    this.projectEnergyClass = data['projectEnergyClass'];
    this.constructionYear = data['constructionYear'];
    this.externalEsposition = data['externalEsposition'] ?? [];
    this.solarPanel = data['solarPanel'];
    this.walkInCloset = data['walkInCloset'];
    this.reportPictures = data['reportPictures'] ?? [];
    this.reportRenders = data['reportRenders'] ?? [];
    this.reportVideoRender = data['reportVideoRender'] ?? [];
    this.reportPlanimetry = data['reportPlanimetry'] ?? [];
    this.reportAdPictures = data['reportAdPictures'] ?? [];
    this.reportBrochure = data['reportBrochure'];
    this.reportVirtualTour = data['reportVirtualTour'];
    this.isLocalizzazioneIncludeInBuyerReport = data['isLocalizzazioneIncludeInBuyerReport'];
    this.isInformazioniGeneraliIncludeInBuyerReport = data['isInformazioniGeneraliIncludeInBuyerReport'];
    this.isInformazioniSulloStabileIncludeInBuyerReport = data['isInformazioniSulloStabileIncludeInBuyerReport'];
    this.isDescrizioneImmobileIncludeInBuyerReport = data['isDescrizioneImmobileIncludeInBuyerReport'];
    this.isDescrizioneDelQuartiereIncludeInBuyerReport = data['isDescrizioneDelQuartiereIncludeInBuyerReport'];
    this.isCaratteristicheInBuyerReport = data['isCaratteristicheInBuyerReport'];
    this.isProspettoCostiInBuyerReport = data['isProspettoCostiInBuyerReport'];
    this.isPlanimetriaCatastaleInBuyerReport = data['isPlanimetriaCatastaleInBuyerReport'];
    this.isFotografieInterneInBuyerReport = data['isFotografieInterneInBuyerReport'];
    this.isFotografieEsterneInBuyerReport = data['isFotografieEsterneInBuyerReport'];
    this.isVirtualTourInBuyerReport = data['isVirtualTourInBuyerReport'];
    this.isStimaRistrutturazioneInBuyerReport = data['isStimaRistrutturazioneInBuyerReport'];
    this.isHeatingAndEnergyInBuyerReport = data['isHeatingAndEnergyInBuyerReport'];
    this.buyerReportColor = data['buyerReportColor'] ?? "";
    this.buyerReportCoverImage = data['buyerReportCoverImage'];
    this.buyerReportAgencyPersonId = data['buyerReportAgencyPersonId'];
    this.coverTemplateType = data['coverTemplateType'] ?? "";
    this.fontType = data['fontType'];
    this.photoTemplateType = data['photoTemplateType'];
  }

  copy(ReportAcquirente immaginaProject) {
    this.propertyUpForSaleAnswer = immaginaProject.propertyUpForSaleAnswer;

    this.buyerReportStatus = immaginaProject.buyerReportStatus;
    this.stateOfFixtures = immaginaProject.stateOfFixtures;

    this.tipologiaStabile = immaginaProject.tipologiaStabile;
    this.stableCondition = immaginaProject.stableCondition;
    this.totalPlans = immaginaProject.totalPlans;
    this.condominiumExpenses = immaginaProject.condominiumExpenses;
    this.heating = immaginaProject.heating;
    this.heatingCosts = immaginaProject.heatingCosts;
    this.objective = immaginaProject.objective;
    this.category = immaginaProject.category;
    
    this.landRegistryIncome = immaginaProject.landRegistryIncome;
    this.notaryCost = immaginaProject.notaryCost;
    this.registrationTax = immaginaProject.registrationTax;
    this.specialHints = immaginaProject.specialHints;
    this.hasSpecialHints = immaginaProject.hasSpecialHints;

    this.wantToIncludeEstimateForRenovation = immaginaProject.wantToIncludeEstimateForRenovation;
    // this.numberOfInternalDoors = immaginaProject.numberOfInternalDoors;
    // this.walkableSquareMeters = immaginaProject.walkableSquareMeters;
    // this.mqOfFixtures = immaginaProject.mqOfFixtures;
    // this.qualityOfTheArea = immaginaProject.qualityOfTheArea;
    this.id = immaginaProject.id;
    this.insertionTimestamp = immaginaProject.insertionTimestamp;
    this.agencyId = immaginaProject.agencyId;
    this.marketZone = immaginaProject.marketZone;
    this.addressInfo = immaginaProject.addressInfo;
    this.grossSquareFootage = immaginaProject.grossSquareFootage;
    this.rooms = immaginaProject.rooms;
    this.numberOfBathrooms = immaginaProject.numberOfBathrooms;
    this.unitFloor = immaginaProject.unitFloor;
    this.listingPrice = immaginaProject.listingPrice;
    this.propertyType = immaginaProject.propertyType;
    this.description = immaginaProject.description;
    this.descriptionNeighborhood = immaginaProject.descriptionNeighborhood;
    this.planimetry = immaginaProject.planimetry;
    this.pictures = immaginaProject.pictures;
    this.elevator = immaginaProject.elevator;
    this.hasCantina = immaginaProject.hasCantina;
    this.airConditioning = immaginaProject.airConditioning;
    this.securityDoor = immaginaProject.securityDoor;
    this.fiber = immaginaProject.fiber;
    this.tvStation = immaginaProject.tvStation;
    this.highEfficiencyFrames = immaginaProject.highEfficiencyFrames;
    this.alarm = immaginaProject.alarm;
    this.terrace = immaginaProject.terrace;
    this.sharedGarden = immaginaProject.sharedGarden;
    this.privateGarden = immaginaProject.privateGarden;
    this.hasConcierge = immaginaProject.hasConcierge;
    this.motorizedSunblind = immaginaProject.motorizedSunblind;
    this.domotizedSunblind = immaginaProject.domotizedSunblind;
    this.domotizedLights = immaginaProject.domotizedLights;
    this.centralizedHeating = immaginaProject.centralizedHeating;
    this.autonomousHeating = immaginaProject.autonomousHeating;
    this.highFloor = immaginaProject.highFloor;
    this.metroVicinity = immaginaProject.metroVicinity;
    this.bigBalconies = immaginaProject.bigBalconies;
    this.doubleEsposition = immaginaProject.doubleEsposition;
    this.tripleEsposition = immaginaProject.tripleEsposition;
    this.quadrupleEsposition = immaginaProject.quadrupleEsposition;
    this.bigLiving = immaginaProject.bigLiving;
    this.doubleBathroom = immaginaProject.doubleBathroom;
    this.nobleBuilding = immaginaProject.nobleBuilding;
    this.surveiledBuilding = immaginaProject.surveiledBuilding;
    this.swimmingPool = immaginaProject.swimmingPool;
    this.hasGarage = immaginaProject.hasGarage;
    this.actualEnergyClass = immaginaProject.actualEnergyClass;
    this.projectEnergyClass = immaginaProject.projectEnergyClass;
    this.constructionYear = immaginaProject.constructionYear;
    this.externalEsposition = immaginaProject.externalEsposition;
    this.solarPanel = immaginaProject.solarPanel;
    this.walkInCloset = immaginaProject.walkInCloset;
    this.reportPictures = immaginaProject.reportPictures;
    this.reportRenders = immaginaProject.reportRenders;
    this.reportVideoRender = immaginaProject.reportVideoRender;
    this.reportPlanimetry = immaginaProject.reportPlanimetry;
    this.reportAdPictures = immaginaProject.reportAdPictures;
    this.reportBrochure = immaginaProject.reportBrochure;
    this.reportVirtualTour = immaginaProject.reportVirtualTour;
    this.isLocalizzazioneIncludeInBuyerReport = immaginaProject.isLocalizzazioneIncludeInBuyerReport;
    this.isInformazioniGeneraliIncludeInBuyerReport = immaginaProject.isInformazioniGeneraliIncludeInBuyerReport;
    this.isInformazioniSulloStabileIncludeInBuyerReport = immaginaProject.isInformazioniSulloStabileIncludeInBuyerReport;
    this.isDescrizioneImmobileIncludeInBuyerReport = immaginaProject.isDescrizioneImmobileIncludeInBuyerReport;
    this.isDescrizioneDelQuartiereIncludeInBuyerReport = immaginaProject.isDescrizioneDelQuartiereIncludeInBuyerReport;
    this.isCaratteristicheInBuyerReport = immaginaProject.isCaratteristicheInBuyerReport;
    this.isProspettoCostiInBuyerReport = immaginaProject.isProspettoCostiInBuyerReport;
    this.isPlanimetriaCatastaleInBuyerReport = immaginaProject.isPlanimetriaCatastaleInBuyerReport;
    this.isFotografieInterneInBuyerReport = immaginaProject.isFotografieInterneInBuyerReport;
    this.isFotografieEsterneInBuyerReport = immaginaProject.isFotografieEsterneInBuyerReport;
    this.isVirtualTourInBuyerReport = immaginaProject.isVirtualTourInBuyerReport;
    this.isStimaRistrutturazioneInBuyerReport = immaginaProject.isStimaRistrutturazioneInBuyerReport;
    this.isHeatingAndEnergyInBuyerReport = immaginaProject.isHeatingAndEnergyInBuyerReport;
    this.buyerReportColor = immaginaProject.buyerReportColor;
    this.buyerReportCoverImage = immaginaProject.buyerReportCoverImage;
    this.buyerReportAgencyPersonId = immaginaProject.buyerReportAgencyPersonId;
    this.coverTemplateType = immaginaProject.coverTemplateType;
    this.fontType = immaginaProject.fontType;
    this.photoTemplateType = immaginaProject.photoTemplateType;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': this.id,
      'propertyUpForSaleAnswer': this.propertyUpForSaleAnswer,
      'stateOfFixtures': this.stateOfFixtures ?? null,
      'buyerReportStatus': this.buyerReportStatus ?? null,
      'insertionTimestamp': this.insertionTimestamp,
      'agencyId': this.agencyId,
      'marketZone': this.marketZone,
      'addressInfo': this.addressInfo?.toMap(),
      'grossSquareFootage': this.grossSquareFootage,
      'rooms': this.rooms,
      'numberOfBathrooms': this.numberOfBathrooms,
      'unitFloor': this.unitFloor,
      'listingPrice': this.listingPrice,
      'propertyType': this.propertyType,
      'description': this.description,
      'descriptionNeighborhood': this.descriptionNeighborhood,
      'planimetry': this.planimetry,
      'pictures': this.pictures,
      'elevator': this.elevator,
      'hasCantina': this.hasCantina,
      'airConditioning': this.airConditioning,
      'securityDoor': this.securityDoor,
      'fiber': this.fiber,
      'tvStation': this.tvStation,
      'highEfficiencyFrames': this.highEfficiencyFrames,
      'alarm': this.alarm,
      'terrace': this.terrace,
      'sharedGarden': this.sharedGarden,
      'privateGarden': this.privateGarden,
      'hasConcierge': this.hasConcierge,
      'motorizedSunblind': this.motorizedSunblind,
      'domotizedSunblind': this.domotizedSunblind,
      'domotizedLights': this.domotizedLights,
      'centralizedHeating': this.centralizedHeating,
      'autonomousHeating': this.autonomousHeating,
      'highFloor': this.highFloor,
      'metroVicinity': this.metroVicinity,
      'bigBalconies': this.bigBalconies,
      'doubleEsposition': this.doubleEsposition,
      'tripleEsposition': this.tripleEsposition,
      'quadrupleEsposition': this.quadrupleEsposition,
      'bigLiving': this.bigLiving,
      'doubleBathroom': this.doubleBathroom,
      'nobleBuilding': this.nobleBuilding,
      'surveiledBuilding': this.surveiledBuilding,
      'swimmingPool': this.swimmingPool,
      'hasGarage': this.hasGarage,
      'actualEnergyClass': this.actualEnergyClass,
      'projectEnergyClass': this.projectEnergyClass,
      'constructionYear': this.constructionYear,
      'externalEsposition': this.externalEsposition,
      'solarPanel': this.solarPanel,
      'walkInCloset': this.walkInCloset,
      'reportPictures': this.reportPictures,
      'reportRenders': this.reportRenders,
      'immaginaVideoRender': this.reportVideoRender,
      'reportVideoRender': this.reportPlanimetry,
      'reportAdPictures': this.reportAdPictures,
      'hasSpecialHints': this.hasSpecialHints,
      'specialHints': this.specialHints,
      'reportBrochure': this.reportBrochure,
      'reportVirtualTour': this.reportVirtualTour,
      'tipologiaStabile': this.tipologiaStabile,
      'stableCondition': this.stableCondition,
      'totalPlans': this.totalPlans,
      'condominiumExpenses': this.condominiumExpenses,
      'heating': this.heating,
      'heatingCosts': this.heatingCosts,
      'landRegistryIncome': this.landRegistryIncome,
      'notaryCost': this.notaryCost,
      'registrationTax': this.registrationTax,
      'wantToIncludeEstimateForRenovation': this.wantToIncludeEstimateForRenovation,
      // 'numberOfInternalDoors': this.numberOfInternalDoors,
      // 'walkableSquareMeters': this.walkableSquareMeters,
      // 'mqOfFixtures': this.mqOfFixtures,
      // 'qualityOfTheArea': this.qualityOfTheArea,
      'isLocalizzazioneIncludeInBuyerReport': this.isLocalizzazioneIncludeInBuyerReport,
      'isInformazioniGeneraliIncludeInBuyerReport': this.isInformazioniGeneraliIncludeInBuyerReport,
      'isInformazioniSulloStabileIncludeInBuyerReport': this.isInformazioniSulloStabileIncludeInBuyerReport,
      'isDescrizioneImmobileIncludeInBuyerReport': this.isDescrizioneImmobileIncludeInBuyerReport,
      'isDescrizioneDelQuartiereIncludeInBuyerReport': this.isDescrizioneDelQuartiereIncludeInBuyerReport,
      'isCaratteristicheInBuyerReport': this.isCaratteristicheInBuyerReport,
      'isProspettoCostiInBuyerReport': this.isProspettoCostiInBuyerReport,
      'isPlanimetriaCatastaleInBuyerReport': this.isPlanimetriaCatastaleInBuyerReport,
      'isFotografieInterneInBuyerReport': this.isFotografieInterneInBuyerReport,
      'isFotografieEsterneInBuyerReport': this.isFotografieEsterneInBuyerReport,
      'isVirtualTourInBuyerReport': this.isVirtualTourInBuyerReport,
      'isStimaRistrutturazioneInBuyerReport': this.isStimaRistrutturazioneInBuyerReport,
      'objective': this.objective,
      'category': this.category,
      'isHeatingAndEnergyInBuyerReport': this.isHeatingAndEnergyInBuyerReport,
      'buyerReportColor': this.buyerReportColor,
      'buyerReportCoverImage': this.buyerReportCoverImage,
      'buyerReportAgencyPersonId': this.buyerReportAgencyPersonId,
      'coverTemplateType': this.coverTemplateType,
      'fontType': this.fontType,
      'photoTemplateType': this.photoTemplateType,
    };
  }



  bool localizzazione() {
    bool response = false;
      if (this.addressInfo?.streetName != null && this.addressInfo?.streetName != "" &&
          this.addressInfo?.streetNumber != null && this.addressInfo?.streetNumber != "" &&
          this.addressInfo?.city != null && this.addressInfo?.city != "" &&
          this.addressInfo?.region != null && this.addressInfo?.region != "") {
        response = true;
      }
    return response;
  }

  bool isGenerateButtonEnable() {
    bool response = false;
    if (this.buyerReportAgencyPersonId != null && this.buyerReportAgencyPersonId != "" &&
        this.buyerReportCoverImage != null && (this.buyerReportCoverImage?.isNotEmpty ?? false)
        && this.buyerReportColor != null && this.buyerReportColor != ""
        && this.photoTemplateType != null && this.photoTemplateType != ""
        && this.fontType != null && this.fontType != ""
        && this.coverTemplateType != null && this.coverTemplateType != ""
    ) {
      response = true;
    }
    return response;
  }

  bool info_generaliForBuyerReport() {
    bool response = false;
    if ((this.grossSquareFootage != null)
    & (this.listingPrice != null)
    & (this.rooms != null)
    & (this.numberOfBathrooms != null)
    & (this.unitFloor != null)
    & (this.buyerReportStatus != null)
    & (this.propertyType != null)
    & (this.objective != null)
    & (this.category != null)
    ) {
      response = true;
    }
    return response;
  }
  bool info_sullo_stabileForBuyerReport() {
    bool response = false;
    if ((this.tipologiaStabile != null)
    & (this.totalPlans != null)
    & (this.condominiumExpenses != null)
    & (this.stableCondition != null)){
      response = true;
    }
    return response;
  }

  bool heating_energyForBuyerReport() {
    bool response = false;
    if ((this.heating != null)
    & (this.stateOfFixtures != null)
    & (this.heating != null)
    & (this.actualEnergyClass != null && this.actualEnergyClass != "")
    & (this.heatingCosts != null)){
      response = true;
    }
    return response;
  }
  bool prospettoCostiForBuyerReport() {
    bool response = false;
    if ((this.landRegistryIncome != null)
    || (this.notaryCost != null)
    || (this.registrationTax != null)){
      response = true;
    }
    return response;
  }

  bool descrizione() {
    return description != null;
  }

  //-- for buyer report
  bool descriptionNeighborhoodCheck() {
    return descriptionNeighborhood != null;
  }

  bool planimetria() {
    return planimetry.isNotEmpty;
  }

  bool fotografie() {
    bool areImagesTagged = true;
    for (var elem in pictures) {
      if (
      !elem.containsKey('tag') ||
          elem['tag'] == null ||
          elem['tag'] == ""
      ){
        areImagesTagged = false;
        break;
      }
    }
    return(pictures.isNotEmpty && areImagesTagged);
  }

  bool info_aggiuntive() {
    bool response = false;
    if (this.hasSpecialHints == false && ((this.propertyUpForSaleAnswer != null) && (this.propertyUpForSaleAnswer != ''))) {
      response = true;
    } else if (this.hasSpecialHints == true) {
      if (((this.specialHints != null) && (this.specialHints != '')) && ((this.propertyUpForSaleAnswer != null) && (this.propertyUpForSaleAnswer != ''))) {
        response = true;
      }
    }
    return response;
  }


  bool stimaRistrutturazione() {
    bool response = false;
    if (this.wantToIncludeEstimateForRenovation != null) {
      response = true;
    }
    return response;
  }
  

}