import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../classes/agencyUser.dart';
import '../../../classes/reportAcquirente.dart';

class BuyerReportController extends GetxController {
  bool loadingProperties = true;
  // List<String> cityOptions = [];
  List<ReportAcquirente> reports = [];
  List<ReportAcquirente> allReports = [];
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  List<String> formMessages = [];
  List<DocumentSnapshot> documentList = [];
  int totalRecords = 0;
  String currentlyShowing = '';
  int recordsPerPage = 20;
  int pageCounter = 1;
  int totalPages = 0;
  bool disablePreviousButton = true;
  bool disableNextButton = false;
  List<dynamic> cacheFireStore = [];


  TextEditingController statusFilterController = TextEditingController();
  List<Map> filters = [];
  TextEditingController searchTextController =  TextEditingController();


  String statusSelectedFilter = '';


  clearFilter(){
    statusFilterController.clear();
    statusSelectedFilter = '';
    filters.clear();
  }

  List<Agency> agencyList = [];
}
