

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/file-picker.dart';
import 'package:newarc_platform/widget/UI/tab/common_icon_button.dart';
import 'package:newarc_platform/widget/UI/tab/status_widget.dart';
import 'package:newarc_platform/widget/renovation_contact_popup.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:firebase_storage/firebase_storage.dart';

class RenovationContactsDataSource extends DataTableSource {
  List<RenovationContact> displayContacts;
  List<NewarcUser> renovators;
  Map<String, List> renoFiles;
  List<String> progressMessage;
  BuildContext context;
  Function(RenovationContact contact) onEditTap;

  RenovationContactsDataSource({
    required this.displayContacts,
    required this.progressMessage,
    required this.renovators,
    required this.renoFiles,
    required this.context,
    required this.onEditTap,
  });

  @override
  DataRow? getRow(int index) {
    if (index < displayContacts.length) {
      final contact = displayContacts[index];
      renoFiles.addAll({contact.id!: contact.files!});
      var address = contact.streetAddress!;

      int millisecondsSinceEpoch = contact.created!;
      var date = DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).day.toString() +
          '/' +
          DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).month.toString() +
          '/' +
          DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).year.toString();
      return DataRow(
        cells: [
          DataCell(
            NarFormLabelWidget(
              label: address,
              overflow: TextOverflow.ellipsis,
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),

          ),
          DataCell(
            NarFormLabelWidget(
              label: date,
              fontSize: 12,
              fontWeight: '600',
              textColor: AppColor.black,
            ),

          ),
          DataCell(
            IconButtonWidget(
              onTap: () {
                showContactPopup(contact);
              },
              isSvgIcon: true,
              icon: 'assets/icons/account.svg',
              iconColor: AppColor.greyColor,
            ),
          ),
          DataCell(
            Container(
              child: contact.assignedRenovatorId == null
                  ? Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100),
                  color: Colors.grey,
                ),
              )
                  : FutureBuilder<ImageProvider>(
                future: getImage(contact.assignedRenovatorId ?? ""),
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    NewarcUser _user = renovators.where((element) => element.id == contact.assignedRenovatorId).first;
                    if (_user.id == '' || snapshot.hasError) {
                      return SizedBox();
                    } else if (_user.profilePicture == '') {
                      return Row(
                        children: [
                          Container(
                            width: 30,
                            height: 30,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(100),
                              color: Colors.grey,
                            ),
                          ),
                          SizedBox(width: 10),
                          NarFormLabelWidget(
                            label: _user.firstName! + ' ' + _user.lastName!,
                            overflow: TextOverflow.ellipsis,
                            fontSize: 12,
                            fontWeight: '600',
                            textColor: AppColor.black,
                          ),

                        ],
                      );
                    }
                    return Row(
                      children: [
                        ClipOval(
                          child: Image(
                            image: snapshot.data!,
                            width: 30,
                            height: 30,

                            fit: BoxFit.cover, // Adjust as needed
                          ),
                        ),
                        SizedBox(width: 10),
                        NarFormLabelWidget(
                          label: _user.firstName! + ' ' + _user.lastName!,
                          overflow: TextOverflow.ellipsis,
                          fontSize: 12,
                          fontWeight: '600',
                          textColor: AppColor.black,
                        ),

                      ],
                    );
                  } else if (snapshot.hasError) {
                    return SizedBox();
                  }
                  return SizedBox();
                },
              ),
            ),
          ),
          DataCell(
            Row(
              // mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              // mainAxisSize: MainAxisSize.max,
              children: [
                NarFilePickerWidget(
                  allowMultiple: false,
                  displayFormat: 'inline-button',
                  borderRadius: 7,
                  fontSize: 12,
                  fontWeight: '600',
                  text: 'Carica',
                  height: 15,
                  borderSideColor: Theme.of(context).primaryColor,
                  hoverColor: Color.fromRGBO(133, 133, 133, 1),
                  allFiles: renoFiles[contact.id],
                  pageContext: context,
                  storageDirectory: 'renovation/${contact.id}/',
                  progressMessage: progressMessage,
                  removeExistingOnChange: true,
                  displayButtonAsLink: true,

                  // displayInlineWidget: displayInlineWidget,
                  onUploadCompleted: () async {
                    final FirebaseFirestore _db = FirebaseFirestore.instance;
                    contact.files = renoFiles[contact.id];

                    try {
                      await _db.collection(appConfig.COLLECT_RENOVATION_CONTACTS).doc(contact.id).update(contact.toMap());

                      // print({'saved', widget.allFiles});

                      // if (mounted) {
                      //   setState(() {
                      progressMessage.clear();
                      progressMessage.add('');
                      // });
                      // }
                    } catch (e) {
                      // if (mounted) {
                      //   setState(() {
                      progressMessage.clear();
                      progressMessage.add('Error');
                      // });
                      // }
                    }
                  },
                ),
                NarFilePickerWidget(
                  allowMultiple: false,
                  filesToDisplayInList: 0,
                  removeButton: false,
                  isDownloadable: false,
                  removeButtonText: 'Elimina',
                  uploadButtonPosition: 'back',
                  showMoreButtonText: '+ espandi',
                  actionButtonPosition: 'bottom',
                  displayFormat: 'inline-widget',
                  containerWidth: 45,
                  containerHeight: 45,
                  containerBorderRadius: 13,
                  borderRadius: 7,
                  fontSize: 11,
                  fontWeight: '600',
                  text: 'Carica Progetto',
                  borderSideColor: Theme.of(context).primaryColor,
                  hoverColor: Color.fromRGBO(133, 133, 133, 1),
                  allFiles: renoFiles[contact.id],
                  pageContext: context,
                  storageDirectory: 'renovation/${contact.id}/',
                  removeExistingOnChange: true,
                  progressMessage: progressMessage,
                  showTitle: false,
                  thumbnailIcon: IconButtonWidget(
                    onTap: () {
                      showContactPopup(contact);
                    },
                    isSvgIcon: false,
                    icon: 'assets/icons/document.png',
                    iconColor: AppColor.greyColor,
                  ),
                  // thumbnailIcon: Container(
                  //   child: Image.asset('assets/icons/document.png', height: 20),
                  //   padding: const EdgeInsets.all(6),
                  //   decoration: BoxDecoration(
                  //     color: Color(0xffF2F2F2),
                  //     borderRadius: BorderRadius.circular(7.0),
                  //   ),
                  // ),
                  onUploadCompleted: () async {
                    final FirebaseFirestore _db = FirebaseFirestore.instance;
                    contact.files = renoFiles[contact.id];

                    try {
                      await _db.collection(appConfig.COLLECT_RENOVATION_CONTACTS).doc(contact.id).update(contact.toMap());

                      // print({'saved', widget.allFiles});

                      // if (mounted) {
                      //   setState(() {
                      progressMessage.clear();
                      progressMessage.add('');
                      // });
                      // }
                    } catch (e) {
                      // if (mounted) {
                      //   setState(() {
                      progressMessage.clear();
                      progressMessage.add('Error');
                      // });
                      // }
                    }
                  },
                )
              ],
            ),
          ),
          DataCell(
            contact.renovationStatus == null
                ? SizedBox()
                : StatusWidget(
              status: contact.renovationStatus == 'in-corso' ? "In corso" : "Completato",
              statusColor: contact.renovationStatus == 'in-corso' ? Color(0xfff5c620) : Theme.of(context).primaryColor,
            ),
          ),
          DataCell(
            IconButtonWidget(
              onTap: () {
                onEditTap(contact);
              },
              isSvgIcon: true,
              icon: 'assets/icons/edit.svg',
              iconColor: AppColor.greyColor,
            ),
          ),
        ],
      );
    }

    return null;
  }

  Future<ImageProvider> getImage(String userId) async {
    final extensions = ['.jpeg', '.png', '.jpg'];
    for (final extension in extensions) {
      final ref = FirebaseStorage.instance.ref().child('users/$userId/profile$extension');
      try {
        final url = await ref.getDownloadURL();
        return NetworkImage(url);
      } catch (error) {
        continue;
      }
    }
    throw Exception('Profile image not found for user $userId');
  }

  void showContactPopup(RenovationContact acquiredContact) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: false,
      barrierColor: Colors.black.withOpacity(0.5),
      pageBuilder: (_, __, ___) {
        return Center(
          child: BaseNewarcPopup(
            title: "Info Contatto",
            column: RenovationContactPopup(renovationContact: acquiredContact),
          ),
        );
      },
    );
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => displayContacts.length;

  @override
  int get selectedRowCount => 0;
}
