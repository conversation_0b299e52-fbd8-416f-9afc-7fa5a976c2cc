import 'dart:async';
import 'dart:developer';
import 'package:async/async.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/app_config.dart';
import 'package:newarc_platform/classes/newarcProject.dart';
import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/functions/various.dart';
import 'package:newarc_platform/utils/common_utils.dart';
import 'package:newarc_platform/utils/downloadAdBrochurePDF.dart';
import 'package:newarc_platform/utils/downloadAdImageBrochure.dart';
import 'package:newarc_platform/utils/downloadCutBrochurePDF.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/block-file-picker.dart';
import 'package:newarc_platform/widget/UI/tab/icon_text_button.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:google_maps_webservice/places.dart';
import 'package:image_picker/image_picker.dart';
import 'package:newarc_platform/widget/UI/input.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/tab/status_widget.dart';
import 'package:newarc_platform/widget/UI/textarea.dart';
import 'package:newarc_platform/widget/UI/checkbox.dart';
import 'package:newarc_platform/widget/UI/image-picker.dart';
import 'package:newarc_platform/widget/UI/alert.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/classes/property.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/widget/UI/file-picker.dart';
import 'package:newarc_platform/classes/renderImage.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:qr_flutter/qr_flutter.dart';
import '../../../utils/color_schema.dart';
import '../../../utils/storage.dart';
import 'dart:html' as html;

class ActiveAd extends StatefulWidget {
  var project;
  Property? property;
  List<bool>? isInputChangeDetected = [];
  final Function? updateViewCallback;
  final Function? initialFetchProperties;

  ActiveAd(
      {Key? key,
      this.project,
      this.property,
      this.isInputChangeDetected,
      required this.updateViewCallback,
      this.initialFetchProperties})
      : super(key: key);

  static const String route = '/active-ad/active-ad-single';

  @override
  _ActiveAdState createState() => _ActiveAdState();
}

class _ActiveAdState extends State<ActiveAd> {
  bool loading = false;
  AsyncMemoizer? _memoizer;
  String progressMessage = '';

  String projectType = '';

  bool isAdSectionActive = true;
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final _formKey = GlobalKey<FormState>();
  // TextEditingController? _passwordController = new TextEditingController();
  // TextEditingController? dropdownController = new TextEditingController();
  // TextEditingController? textareaController = new TextEditingController();

  Location? geoLocation;
  Map? location;
  // Map? addressComponents = {
  //   'street': '',
  //   'civico': '',
  //   'city': '',
  //   'province': '',
  //   'state': '',
  //   'country': ''
  // };

  BaseAddressInfo? addressInfo;

  List<String> qualityAreaList = [
    'Zona normale',
    'Zona ottima',
    'Zona di pregio'
  ];

  DateTime? selectedStartDate;
  DateTime? selectedEndDate;
  DateTime? selectedLimitDate;

  TextEditingController? txtconPropertyName = new TextEditingController();
  TextEditingController? txtconPropertyZoneName = new TextEditingController();
  TextEditingController? txtconPropertyTypeName = new TextEditingController();
  // TextEditingController? txtconLocation = new TextEditingController();
  TextEditingController? txtconCivic = new TextEditingController();
  TextEditingController? txtconCity = new TextEditingController();
  TextEditingController? txtconDescription = new TextEditingController();
  TextEditingController? txtconAreaMq = new TextEditingController();
  TextEditingController? txtconBaths = new TextEditingController();
  TextEditingController? txtconLocals = new TextEditingController();
  TextEditingController? txtconBedrooms = new TextEditingController();
  TextEditingController? txtconFloors = new TextEditingController();
  TextEditingController? txtconBasePrice = new TextEditingController();

  /*TextEditingController? txtconBathsAfter = new TextEditingController();
  TextEditingController? txtconLocalsAfter = new TextEditingController();
  TextEditingController? txtconFixtureAfter = new TextEditingController();
  TextEditingController? txtconInteralDoorsAfter = new TextEditingController();*/

  TextEditingController? txtconBathsIns = new TextEditingController();
  TextEditingController? txtconLocalsIns = new TextEditingController();
  TextEditingController? txtconFixtureIns = new TextEditingController();
  TextEditingController? txtconInteralDoorsIns = new TextEditingController();
  TextEditingController? txtconWalkableIns = new TextEditingController();
  TextEditingController? txtconQualityOfTheAreaIns =
      new TextEditingController();

  /*TextEditingController? txtconDemolitionIns = new TextEditingController();
  TextEditingController? txtconConstructionIns = new TextEditingController();
  TextEditingController? txtconLinearMeterIns = new TextEditingController();
  TextEditingController? txtconGeneralWallsIns = new TextEditingController();
  TextEditingController? txtconBathroomWallsIns = new TextEditingController();*/
  TextEditingController? txtconLighRenoIns = new TextEditingController();
  TextEditingController? txtconLighRenoInsMin = new TextEditingController();
  TextEditingController? txtconLighRenoInsMax = new TextEditingController();

  TextEditingController? txtconFullRenoIns = new TextEditingController();
  TextEditingController? txtconFullRenoInsMin = new TextEditingController();
  TextEditingController? txtconFullRenoInsMax = new TextEditingController();

  TextEditingController? txtconMaterialStandardLight = new TextEditingController();
  TextEditingController? txtconMaterialPremiumLight = new TextEditingController();
  TextEditingController? txtconMaterialStandardFull = new TextEditingController();
  TextEditingController? txtconMaterialPremiumFull = new TextEditingController();
  TextEditingController? txtconFixtureFull = new TextEditingController();

  TextEditingController? txtconStartDate = new TextEditingController();
  TextEditingController? txtconEndDate = new TextEditingController();
  TextEditingController? txtconLimitDate = new TextEditingController();
  TextEditingController? txtconVirtualTour = new TextEditingController();
  TextEditingController? txtconCurrentVirtualTour = new TextEditingController();
  TextEditingController? publicStatusController = new TextEditingController();
  TextEditingController? txtconDateStart = new TextEditingController();
  TextEditingController? txtconDateEnd = new TextEditingController();
  TextEditingController? txtconDateLimit = new TextEditingController();

  TextEditingController? txtconBrochureProjectDescription =
      new TextEditingController();
  TextEditingController? txtconBrochureActualDescription =
      new TextEditingController();

  TextEditingController? txtconProjectEnergyClass = new TextEditingController();
  TextEditingController? txtconActualEnergyClass = new TextEditingController();
  TextEditingController? txtconProjectStatus = new TextEditingController();

  bool downloadingPdf = false;
  bool generatingPdf = false;
  bool downloadingImage = false;
  bool isReadyForAgency = false;
  bool isBrochureDataValid = false;

  List<Map> readyForAgencyCheckList = [];

  Map brochurePdfPath = {};

  String googleApikey =
      isProduction ? GOOGLE_PRODUCTION_API_KEY : GOOGLE_STAGING_API_KEY;

  List<String> dropdownOptions = ['Test', 'ff 2'];
  Map<String, dynamic> publicStatusOptions = {
    'Personalizzabile': false,
    "In trattativa": false,
    "Venduto": false,
    "Luxury": false,
    "Novità!": false,
    "Progetto Newarc": false,
    "Partner verificato": false,
  };

  List<String> zonesList = [];
  List<String> typesList = appConst.propertyTypesList;

  List<String> bathsList = List<String>.generate(10, (i) => (i + 1).toString());

  List<String> bedroomsList =
      List<String>.generate(5, (i) => (i + 1).toString());
  List<String> floorsList = [];

  List<String> localsList =
      List<String>.generate(10, (i) => (i + 1).toString());

  List<String> predefinedStyleList = [
    "Base senza configurazione",
    "Stoccolma",
    "Londra",
    "Parigi"
  ];
  Map<String, String> predefinedStyleDescription = {
    "Configurazione Base":
        "Tinta pareti: grigio chiaro opaco\nPorte interne: legno rovere tamburato bianco\nPavimentazione: grès porcellanato effetto legno\nRivestimento bagno principale: grès porcellanato beige chiaro\n Rivestimento bagno secondario: grès porcellanato effetto marmo venato grigio",
    "Stoccolma":
        "Tinta pareti: grigio chiaro opaco\nPorte interne: legno rovere tamburato bianco\nPavimentazione: grès porcellanato effetto legno\nRivestimento bagno principale: grès porcellanato beige chiaro\n Rivestimento bagno secondario: grès porcellanato effetto marmo venato grigio",
    "Londra":
        "Tinta pareti: bianco opaco\nPorte interne: tamburato laccato bianco\n Pavimentazione: parquet legno rovere spazzolato chiaro\nRivestimento bagno principale: grès porcellanato effetto marmo venato grigio\n Rivestimento bagno secondario: piastrelline bianche rettangolari",
    "Parigi":
        "Tinta pareti: grigio chiaro opaco\nPorte interne: legno rovere tamburato beige\nPavimentazione: parquet legno rovere naturale\nRivestimento bagno principale: grès porcellanato beige chiaro\nRivestimento bagno secondario: grès porcellanato effetto marmo venato oro"
  };
  List<String> selectableStyleList = [
    "Sel Style 1",
    "Sel Style 2",
    "Sel Style 3",
    "Sel Style 4"
  ];

  bool hasPropertyFeatureError = false;
  bool hasStyleError = false;
  bool hasImageError = false;

  bool hasErrorRenderDay = false;
  bool hasErrorRenderBeforeAfter = false;
  bool hasErrorFloorPlan = false;
  bool hasErrorCurrentFloorPlan = false;

  bool activateNightTimePicture = false;
  bool activateVirtualTourProject = false;
  bool activateVideoRender = false;
  bool activateVirtualTourCurrent = false;

  Map<String, bool> currentFeatures = {};
  Map<String, bool> propertyFeatures = {};

  Map<String, bool> peculiarities = {
    'Piano alto': false,
    'Vicinanza Metro': false,
    'Ampi balconi': false,
    'Doppia esposizione': false,
    'Tripla esposizione': false,
    'Quadrupla esposizione': false,
    'Grande zona living': false,
    'Doppi servizi': false,
    'Stabile signorile': false,
    'Stabile videosorvegliato': false,
  };

  num styleCounter = 0;
  Map<String, Widget> styles = {};
  List<Map<String, dynamic>> styleValues = [];
  List<num> styleValuesRetained = [];

  // TODO optionalFeatures should be packaged into a class
  String? optionalFeatureValue;

  Map<String, bool> optionalFeatures = {
    'Condizionatore': false,
    'Antifurto': false,
    'Assicurazione casa': false,
    'Cucina': false
  };

  Map<String, String> optionalFeaturesDescription = {
    'Condizionatore':
        'Climatizzatore Ariston Alys Plus R-32 trial split, Potenza 9000 btu, wifi, 12 velocità',
    'Antifurto':
        'Pannello di controllo, Sensori volumetrici con fotocamera, Cartelli dissuasori, Sirena, Sensori porte e finestre, App e lettore chiavi, Fumogeno zero vision',
    'Assicurazione casa':
        'Incendio e scoppio, Eventi naturali, Arredi e contenuto, Furto',
    'Cucina':
        'Cucina ArTre modello Up Design in composizione lineare 425x200 con isola frontale. Colorazione bianco opaco con top marmo. Inclusi nel prezzo poker di elettrodomestici (fuochi, forno, frigo e lavastoviglie) Hotpoint Ariston.',
    'Condizionatore 2':
        'Climatizzatore Ariston Alys Plus R-32 trial split, Potenza 9000 btu, wifi, 12 velocità',
    'Condizionatore 3':
        'Climatizzatore Ariston Alys Plus R-32 trial split, Potenza 9000 btu, wifi, 12 velocità',
  };

  Map<String, TextEditingController> optionalFeaturesPrice = {
    'Condizionatore': new TextEditingController(),
    'Antifurto': new TextEditingController(),
    'Assicurazione casa': new TextEditingController(),
    'Cucina': new TextEditingController(),
    'Condizionatore 2': new TextEditingController(),
    'Condizionatore 3': new TextEditingController()
  };

  late List<XFile> adImages;
  late List photoDayTimePaths = [];
  late List photoNightTimePaths = [];
  late List photoBeforeAfterPaths = [];
  late List videoRenderPaths = [];
  late List photographs = [];
  late List currentPlan = [];
  late List projectPlanImages = [];
  late List qrImages = [];
  late List buildingPictures = [];

  List<dynamic> googlePlacesSuggestions = [];
  // bool showSuggestionsPopup = false;

  List<dynamic> amenities = [];
  List<dynamic> tmpAmenities = [];
  dynamic amenitiesCount = {};
  bool isLoadingAmenities = true;
  List<PropertyOptionalFeature> optional = [];
  Property? property;

  final List<RenderImageList> renderImageDayList = [];
  final List<RenderImageList> renderImageNightList = [];
  final List<RenderImageList> renderImageBeforeAfterList = [];
  final List<RenderImageList> buildingPicturesImages = [];
  final List<RenderImageList> brochureCoverImage = [RenderImageList.empty()];
  final List<RenderImageList> brochureActualImages = [
    RenderImageList.empty(),
    RenderImageList.empty(),
    RenderImageList.empty(),
    RenderImageList.empty(),
    RenderImageList.empty(),
    RenderImageList.empty()
  ];
  final List<RenderImageList> brochureRenderImages = [
    RenderImageList.empty(),
    RenderImageList.empty(),
    RenderImageList.empty(),
    RenderImageList.empty(),
    RenderImageList.empty(),
    RenderImageList.empty()
  ];
  final List<RenderImageList> brochureVTImage = [RenderImageList.empty()];

  late List photoBrochureCoverPaths = [];
  late List photoBrochureCoverActualPaths = [];
  late List photoBrochureRenderPaths = [];
  late List videoBrochureVTPaths = [];
  NumberFormat localCurrencyFormat =
      NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);

  int publicationDate = DateTime.now().millisecondsSinceEpoch;

  Map<String, dynamic> rangeResFull = {
    'actual': 0,
    'range_min': 0,
    'range_max': 0,
    'material_standard': 0,
    'material_standard_min': 0,
    'material_standard_max': 0,
    'material_permium': 0,
    'material_permium_min': 0,
    'material_permium_max': 0,
    'infissi': 0,
    'infissi_min': 0,
    'infissi_max': 0,
  };
  Map<String, dynamic> rangeResLight = {
    'actual': 0,
    'range_min': 0,
    'range_max': 0,
    'material_standard': 0,
    'material_standard_min': 0,
    'material_standard_max': 0,
    'material_permium': 0,
    'material_permium_min': 0,
    'material_permium_max': 0,
    
  };

  String dataGenerallable = '';
  bool isImmaginaForProfessionalsValid = false;


  @override
  void initState() {
    super.initState();

    if( widget.property!.projectType == "Immagina for Professionals" && 
        widget.property!.cutsCount == 'Multiple') {
      isImmaginaForProfessionalsValid = true;
    }

    floorsList = appConst.getFloorsList();

    currentFeatures = Map.fromEntries(
        appConst.houseFeatures.entries.map((e) => MapEntry(e.key, e.value)));
    propertyFeatures = Map.fromEntries(
        appConst.houseFeatures.entries.map((e) => MapEntry(e.key, e.value)));

    // bathsList.add('10+');

    if (widget.project.runtimeType == NewarcProject) {
      projectType = 'Newarc';
    } else {
      projectType = 'Immagina';
    }

    /* Remove Novità! for Immagina for professionals */
    if( isImmaginaForProfessionalsValid ) {
      // publicStatusOptions.removeWhere((test, value) => test == "Novità!");
      dataGenerallable = 'Dati generali immobile';
    } else {
      dataGenerallable = 'Dati immobile';
    }

    if (widget.property!.firebaseId != null) {
      txtconPropertyName!.text = widget.property!.propertyName!;
      txtconPropertyZoneName!.text =
          widget.property!.zone != null ? widget.property!.zone! : '';
      txtconPropertyTypeName!.text = widget.property!.type ?? '';
      txtconCivic!.text = widget.property!.addressInfo!.streetNumber!;
      txtconCity!.text = widget.property!.addressInfo!.city!;
      txtconDescription!.text = widget.property!.description??'';
      txtconAreaMq!.text = widget.property!.mq!;
      txtconBaths!.text = widget.property!.baths!;
      txtconLocals!.text = widget.property!.locals!;
      txtconBedrooms!.text = widget.property!.bedrooms!;
      txtconFloors!.text = widget.property!.floors??'';
      txtconVirtualTour!.text = widget.property!.virtualTour!;
      txtconCurrentVirtualTour!.text = widget.property!.currentVirtualTour!;

      publicationDate = widget.property?.publicationDate ??
          DateTime.now().millisecondsSinceEpoch;

      txtconBathsIns!.text = widget.property!.bathsIns!;
      txtconLocalsIns!.text = widget.property!.localsIns!;
      txtconFixtureIns!.text = widget.property!.fixtureIns!;
      txtconInteralDoorsIns!.text = widget.property!.InternalDoorsIns!;
      txtconWalkableIns!.text = widget.property!.WalkableIns!;
      txtconQualityOfTheAreaIns!.text = widget.property!.qualityOfTheArea!;
      
      txtconLighRenoIns!.text = '${widget.property!.LighRenoInsMin!} - ${widget.property!.LighRenoInsMax!}';
      txtconFullRenoIns!.text = '${widget.property!.FullRenoInsMin!} - ${widget.property!.FullRenoInsMax!}';
      
      txtconMaterialStandardLight!.text = '${widget.property!.materialStandardLightMin!} - ${widget.property!.materialStandardLightMax!}';

      txtconMaterialPremiumLight!.text = '${widget.property!.materialPremiumLightMin!} - ${widget.property!.materialPremiumLightMax!}';
      txtconMaterialStandardFull!.text = '${widget.property!.materialStandardFullMin!} - ${widget.property!.materialStandardFullMax!}';
      txtconMaterialPremiumFull!.text = '${widget.property!.materialPremiumFullMin!} - ${widget.property!.materialPremiumFullMax!}';
      txtconFixtureFull!.text = '${widget.property!.materialFixtureFullMin!} - ${widget.property!.materialFixtureFullMax!}';

      txtconProjectEnergyClass!.text = widget.property!.projectEnergyClass!;
      txtconActualEnergyClass!.text = widget.property!.actualEnergyClass??'';
      txtconProjectStatus!.text = widget.property!.projectStatus ?? '';

      photoDayTimePaths = widget.property!.photoDayTimePaths!;


      brochurePdfPath = widget.property!.brochurePdfPath!;

      if (photoDayTimePaths.isNotEmpty) {
        for (int rd = 0; rd < photoDayTimePaths.length; rd++) {
          RenderImage _tmp = RenderImage({
            'imageBytes': null,
            'tmpFile': null,
            'filenameUrl': '',
            'hasMatch': false,
            'isBrochure': photoDayTimePaths[rd]['isBrochure'],
            'isEnabledForWebsite': photoDayTimePaths[rd]['isEnabledForWebsite'],
            'isNetworkImage': true,
            'picker': ImagePicker(),
            'filename': photoDayTimePaths[rd]['filename'],
            'location': photoDayTimePaths[rd]['location'],
            'room': photoDayTimePaths[rd]['room']
          });

          RenderImageList _tmpList = RenderImageList({
            'index': rd,
            'renderImageFirst': _tmp,
            'renderImageSecond': RenderImage.empty()
          });

          renderImageDayList.add(_tmpList);
        }
      }

      photoNightTimePaths = widget.property!.photoNightTimePaths!;

      if (photoNightTimePaths.isNotEmpty) {
        for (int rd = 0; rd < photoNightTimePaths.length; rd++) {
          RenderImage _tmpDay = RenderImage({
            'imageBytes': null,
            'tmpFile': null,
            'filenameUrl': '',
            'hasMatch': false,
            'isBrochure': false,
            'isNetworkImage': true,
            'picker': ImagePicker(),
            'filename': photoNightTimePaths[rd]['day']['filename'],
            'location': photoNightTimePaths[rd]['day']['location'],
            'room': photoNightTimePaths[rd]['day']['room']
          });

          RenderImage _tmpNight = RenderImage({
            'imageBytes': null,
            'tmpFile': null,
            'filenameUrl': '',
            'hasMatch': false,
            'isBrochure': false,
            'isNetworkImage': true,
            'picker': ImagePicker(),
            'filename': photoNightTimePaths[rd]['night']['filename'],
            'location': photoNightTimePaths[rd]['night']['location'],
            'room': photoNightTimePaths[rd]['night']['room']
          });

          RenderImageList _tmpList = RenderImageList({
            'index': rd,
            'renderImageFirst': _tmpDay,
            'renderImageSecond': _tmpNight
          });

          renderImageNightList.add(_tmpList);
        }
      }

      photoBeforeAfterPaths = widget.property!.photoBeforeAfterPaths!;

      if (photoBeforeAfterPaths.isNotEmpty) {
        for (int rd = 0; rd < photoBeforeAfterPaths.length; rd++) {
          RenderImage _tmpDay = RenderImage({
            'imageBytes': null,
            'tmpFile': null,
            'filenameUrl': '',
            'hasMatch': photoBeforeAfterPaths[rd]['after']['hasMatch'],
            'isBrochure': photoBeforeAfterPaths[rd]['after']['isBrochure'],
            'isNetworkImage': true,
            'picker': ImagePicker(),
            'filename': photoBeforeAfterPaths[rd]['after']['filename'],
            'location': photoBeforeAfterPaths[rd]['after']['location'],
            'room': photoBeforeAfterPaths[rd]['after']['room']
          });

          RenderImage _tmpNight = RenderImage({
            'imageBytes': null,
            'tmpFile': null,
            'filenameUrl': '',
            'hasMatch': false,
            'isBrochure': false,
            'isNetworkImage':
                photoBeforeAfterPaths[rd]['before']['filename'] != ''
                    ? true
                    : false,
            'picker': ImagePicker(),
            'filename': photoBeforeAfterPaths[rd]['before']['filename'],
            'location': photoBeforeAfterPaths[rd]['before']['location'],
            'room': photoBeforeAfterPaths[rd]['before']['room']
          });

          RenderImageList _tmpList = RenderImageList({
            'index': rd,
            'renderImageFirst': _tmpDay,
            'renderImageSecond': _tmpNight
          });

          renderImageBeforeAfterList.add(_tmpList);
        }
      }

      videoRenderPaths = widget.property!.videoRenderPaths!;
      photographs = widget.property!.photographs!;
      currentPlan = widget.property!.currentPlan!;
      qrImages = widget.property!.qrPaths!;

      photoBrochureCoverPaths = widget.property!.photoBrochureCoverPaths!;

      brochureCoverImage.clear();
      if (photoBrochureCoverPaths.isNotEmpty) {
        for (int rd = 0; rd < photoBrochureCoverPaths.length; rd++) {
          RenderImage _tmp = RenderImage({
            'imageBytes': null,
            'tmpFile': null,
            'filenameUrl': '',
            'hasMatch': false,
            'isBrochure': false,
            'isNetworkImage': true,
            'picker': ImagePicker(),
            'filename': photoBrochureCoverPaths[rd]['filename'],
            'location': photoBrochureCoverPaths[rd]['location'],
            'room': ''
          });

          RenderImageList _tmpList = RenderImageList({
            'index': rd,
            'renderImageFirst': _tmp,
            'renderImageSecond': RenderImage.empty()
          });

          brochureCoverImage.add(_tmpList);
        }
      } else {
        brochureCoverImage.add(RenderImageList.empty());
      }

      photoBrochureCoverActualPaths = widget.property!.photoBrochureCoverActualPaths!;
      brochureActualImages.clear();

      if (photoBrochureCoverActualPaths.isNotEmpty) {
        for (int rd = 0; rd < photoBrochureCoverActualPaths.length; rd++) {
          RenderImage _tmp = RenderImage({
            'imageBytes': null,
            'tmpFile': null,
            'filenameUrl': '',
            'hasMatch': false,
            'isBrochure': false,
            'isNetworkImage': true,
            'picker': ImagePicker(),
            'filename': photoBrochureCoverActualPaths[rd]['filename'],
            'location': photoBrochureCoverActualPaths[rd]['location'],
            'room': photoBrochureCoverActualPaths[rd]['room'],
          });

          RenderImageList _tmpList = RenderImageList({
            'index': rd,
            'renderImageFirst': _tmp,
            'renderImageSecond': RenderImage.empty()
          });

          brochureActualImages.add(_tmpList);
        }

        // Fill the empty image spaces
        if (photoBrochureCoverActualPaths.length < 6) {
          for (int rdi = photoBrochureCoverActualPaths.length; rdi < 6; rdi++) {
            brochureActualImages.add(RenderImageList.empty());
          }
        }
      } else {
        brochureActualImages.add(RenderImageList.empty());
        brochureActualImages.add(RenderImageList.empty());
        brochureActualImages.add(RenderImageList.empty());
        brochureActualImages.add(RenderImageList.empty());
        brochureActualImages.add(RenderImageList.empty());
        brochureActualImages.add(RenderImageList.empty());
      }

      photoBrochureRenderPaths = widget.property!.photoBrochureRenderPaths!;
      brochureRenderImages.clear();
      if (photoBrochureRenderPaths.isNotEmpty) {
        for (int rd = 0; rd < photoBrochureRenderPaths.length; rd++) {
          RenderImage _tmp = RenderImage({
            'imageBytes': null,
            'tmpFile': null,
            'filenameUrl': '',
            'hasMatch': false,
            'isBrochure': false,
            'isNetworkImage': true,
            'picker': ImagePicker(),
            'filename': photoBrochureRenderPaths[rd]['filename'],
            'location': photoBrochureRenderPaths[rd]['location'],
            'room': photoBrochureRenderPaths[rd]['room'],
          });

          RenderImageList _tmpList = RenderImageList({
            'index': rd,
            'renderImageFirst': _tmp,
            'renderImageSecond': RenderImage.empty()
          });

          brochureRenderImages.add(_tmpList);
        }

        // Fill the empty image spaces
        if (photoBrochureRenderPaths.length < 6) {
          for (int rdi = photoBrochureRenderPaths.length; rdi < 6; rdi++) {
            brochureRenderImages.add(RenderImageList.empty());
          }
        }
      } else {
        brochureRenderImages.add(RenderImageList.empty());
        brochureRenderImages.add(RenderImageList.empty());
        brochureRenderImages.add(RenderImageList.empty());
        brochureRenderImages.add(RenderImageList.empty());
        brochureRenderImages.add(RenderImageList.empty());
        brochureRenderImages.add(RenderImageList.empty());
      }

      videoBrochureVTPaths = widget.property!.videoBrochureVTPaths!;
      brochureVTImage.clear();
      if (videoBrochureVTPaths.isNotEmpty) {
        for (int rd = 0; rd < videoBrochureVTPaths.length; rd++) {
          RenderImage _tmp = RenderImage({
            'imageBytes': null,
            'tmpFile': null,
            'filenameUrl': '',
            'hasMatch': false,
            'isBrochure': false,
            'isNetworkImage': true,
            'picker': ImagePicker(),
            'filename': videoBrochureVTPaths[rd]['filename'],
            'location': videoBrochureVTPaths[rd]['location']
          });

          RenderImageList _tmpList = RenderImageList({
            'index': rd,
            'renderImageFirst': _tmp,
            'renderImageSecond': RenderImage.empty()
          });

          brochureVTImage.add(_tmpList);
        }
      } else {
        brochureVTImage.add(RenderImageList.empty());
      }

      txtconBrochureProjectDescription!.text =
          widget.property!.brochureProjectDescription!;
      txtconBrochureActualDescription!.text =
          widget.property!.brochureActualDescription!;

      projectPlanImages = widget.property!.picturePaths!;
      activateNightTimePicture = widget.property!.activateNightTimePicture!;
      activateVirtualTourProject = widget.property!.activateVirtualTourProject!;
      activateVideoRender = widget.property!.activateVideoRender!;
      activateVirtualTourCurrent = widget.property!.activateVirtualTourCurrent!;

      // buildingPictures = widget.property!.buildingPictures!;
      buildingPictures = widget.property!.buildingPictures!;

      if (buildingPictures.isNotEmpty) {
        for (int rd = 0; rd < buildingPictures.length; rd++) {
          RenderImage _tmpFirst = RenderImage({
            'imageBytes': null,
            'tmpFile': null,
            'filenameUrl': '',
            'hasMatch': false,
            'isBrochure': false,
            'isNetworkImage': true,
            'picker': ImagePicker(),
            'filename': buildingPictures[rd]['filename'],
            'location': buildingPictures[rd]['location'],
            'room': buildingPictures[rd]['room']
          });

          RenderImageList _tmpList = RenderImageList({
            'index': rd,
            'renderImageFirst': _tmpFirst
          });

          buildingPicturesImages.add(_tmpList);
        }
      }

      if (widget.property!.publicStatus != null) {
        widget.property!.publicStatus!.forEach((key, value) {
          if (publicStatusOptions.containsKey(key)) {
            publicStatusOptions[key] = value;
          }
        });
      }

      if (widget.property!.amenities != null) {
        amenities = widget.property!.amenities!;
      }

      if (widget.property!.amenitiesCount != null) {
        amenitiesCount = widget.property!.amenitiesCount!;
      }

      if (widget.project!.addressInfo != null) {
        addressInfo = widget.property!.addressInfo!;
      }

      if (widget.property!.location != null) {
        location = widget.property!.location!;
      }

      if (widget.property!.startDate != null) {
        var startDate = DateTime.fromMillisecondsSinceEpoch(widget.property!.startDate!);
        txtconStartDate!.text = (startDate.day < 10
                ? '0' + startDate.day.toString()
                : startDate.day.toString()) +
            '/' +
            (startDate.month < 10
                    ? '0' + startDate.month.toString()
                    : startDate.month.toString())
                .toString() +
            '/' +
            startDate.year.toString();

        var endDate =
            DateTime.fromMillisecondsSinceEpoch(widget.property!.endDate!);
        txtconEndDate!.text = (endDate.day < 10
                ? '0' + endDate.day.toString()
                : endDate.day.toString()) +
            '/' +
            (endDate.month < 10
                    ? '0' + endDate.month.toString()
                    : endDate.month.toString())
                .toString() +
            '/' +
            endDate.year.toString();

        var limitDate =
            DateTime.fromMillisecondsSinceEpoch(widget.property!.limitDate!);
        txtconLimitDate!.text = (limitDate.day < 10
                ? '0' + limitDate.day.toString()
                : limitDate.day.toString()) +
            '/' +
            (limitDate.month < 10
                    ? '0' + limitDate.month.toString()
                    : limitDate.month.toString())
                .toString() +
            '/' +
            limitDate.year.toString();
      }

      if (widget.property!.propertyFeatures != null) {
        var features = widget.property!.propertyFeatures;
        for (var i = 0; i < features!.length; i++) {
          if (propertyFeatures.containsKey(features[i])) {
            propertyFeatures[features[i]] = true;
          }
        }
      }

      if (widget.property!.currentFeatures != null) {
        var featuress = widget.property!.currentFeatures;
        for (var i = 0; i < featuress!.length; i++) {
          if (currentFeatures.containsKey(featuress[i])) {
            currentFeatures[featuress[i]] = true;
          }
        }
      }

      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        if (widget.property!.optional != null ||
            widget.property!.optional!.length > 0) {
          for (var opt = 0; opt < widget.property!.optional!.length; opt++) {
            optional.add(widget.property!.optional![opt]);

            var title = widget.property!.optional![opt].title;
            var price = widget.property!.optional![opt].price;

            optionalFeatures[title!] = true;
            optionalFeaturesPrice[title]!.text = price.toString();
          }
        }

        if (widget.property!.styles != null) {
          if (widget.property!.styles!.length > 0) {
            for (var i = 0; i < widget.property!.styles!.length; i++) {
              autoLoadStyleFileds(widget.property!.styles![i]);
            }
          }

          setState(() {});
        }
      });
    } else {}

    validateBrochureData();
    validateReadyForAgency();

    setState(() {
      isLoadingAmenities = false;
    });

    widget.isInputChangeDetected = [false];

    txtconPropertyName!.addListener(_checkForChanges);
    txtconPropertyZoneName!.addListener(_checkForChanges);
    txtconPropertyTypeName!.addListener(_checkForChanges);
    txtconCivic!.addListener(_checkForChanges);
    txtconCity!.addListener(_checkForChanges);
    txtconDescription!.addListener(_checkForChanges);
    txtconAreaMq!.addListener(_checkForChanges);
    txtconBaths!.addListener(_checkForChanges);
    txtconLocals!.addListener(_checkForChanges);
    txtconBedrooms!.addListener(_checkForChanges);
    txtconFloors!.addListener(_checkForChanges);

    txtconStartDate!.addListener(_checkForChanges);
    txtconEndDate!.addListener(_checkForChanges);
    txtconLimitDate!.addListener(_checkForChanges);
    publicStatusController!.addListener(_checkForChanges);
    txtconDateStart!.addListener(_checkForChanges);
    txtconDateEnd!.addListener(_checkForChanges);
    txtconDateLimit!.addListener(_checkForChanges);
    txtconVirtualTour!.addListener(_checkForChanges);
    txtconBasePrice!.addListener(_checkForChanges);
    txtconCurrentVirtualTour!.addListener(_checkForChanges);

    txtconProjectEnergyClass!.addListener(_checkForChanges);
    txtconActualEnergyClass!.addListener(_checkForChanges);

    adImages = [];

    if( widget.property!.projectType == "Immagina" ) {
      calculateRisFull();
    }
    
  }

  didUpdateWidget(ActiveAd oldWidget) {
    super.didUpdateWidget(oldWidget);
    validateBrochureData();
  }

  validateReadyForAgency() async {
    
    readyForAgencyCheckList.clear();
    isReadyForAgency = true;
    if( widget.property!.projectType == "Immagina for Professionals" && widget.property!.cutsCount == 'Multiple' ) {
      
      // if( cuts.length > 0)
      // for (var i = 0; i < cuts.length; i++) {
      //   if( isCutHasValidBrochureData(cuts[i]) == false ) {
      //     isReadyForAgency = false;
      //     readyForAgencyCheckList.add({
      //       'label': 'Taglio ${(i+1)}',
      //       'status': false
      //     });
      //   } else {
      //     readyForAgencyCheckList.add({
      //       'label': 'Taglio ${(i+1)} ${cuts.length}',
      //       'status': true
      //     });
      //   }
      // }
      

      return;
    }
    

    if (photoDayTimePaths.length == 0) {
      isReadyForAgency = false;
      readyForAgencyCheckList.add({'label': 'Render Giorno', 'status': false});
    } else {
      readyForAgencyCheckList.add({'label': 'Render Giorno', 'status': true});
    }

    if( widget.property!.projectType != "Immagina for Professionals" && widget.property!.projectType != "cut" ) {
      if (renderImageBeforeAfterList.length == 0 ) {
        isReadyForAgency = false;
        readyForAgencyCheckList
            .add({'label': 'Fotografie attuali', 'status': false});
      } else {
        readyForAgencyCheckList
            .add({'label': 'Fotografie attuali', 'status': true});
      }

      if (currentPlan.length == 0) {
        isReadyForAgency = false;
        readyForAgencyCheckList
            .add({'label': 'Planimetria attuale', 'status': false});
      } else {
        readyForAgencyCheckList
            .add({'label': 'Planimetria attuale', 'status': true});
      }
    }
    

    

    if (projectPlanImages.length == 0) {
      isReadyForAgency = false;
      readyForAgencyCheckList
          .add({'label': 'Planimetria progetto', 'status': false});
    } else {
      readyForAgencyCheckList
          .add({'label': 'Planimetria progetto', 'status': true});
    }

    if (txtconVirtualTour!.text == "") {
      isReadyForAgency = false;
      readyForAgencyCheckList
          .add({'label': 'Virtual tour progetto', 'status': false});
    } else {
      readyForAgencyCheckList
          .add({'label': 'Virtual tour progetto', 'status': true});
    }

    if (isBrochureDataValid) {
      readyForAgencyCheckList
          .add({'label': 'Immagini portali', 'status': true});
      readyForAgencyCheckList.add({'label': 'Brochure PDF', 'status': true});
    } else {
      isReadyForAgency = false;
      readyForAgencyCheckList
          .add({'label': 'Immagini portali', 'status': false});
      readyForAgencyCheckList.add({'label': 'Brochure PDF', 'status': false});
    }

    if (widget.property!.videoRenderPaths!.length > 0) {
      readyForAgencyCheckList.add({'label': 'Video Render', 'status': true});
    }
  }

  validateBrochureData() {
    if( widget.property!.projectType == "cut" || widget.property!.projectType == "Immagina for Professionals" ) {
      if(txtconBrochureProjectDescription!.text.trim() != '' &&
        brochureCoverImage[0].renderImageFirst!.filename != '' &&
        brochureVTImage[0].renderImageFirst!.filename != '' &&
        widget.property!.locals != '' &&
        widget.property!.baths != '' &&
        widget.property!.mq != '' &&
        double.tryParse(widget.property!.styles![0].price.toString())! > 0 

        ) {
          isBrochureDataValid = true;  
          setState(() {});
          return;
        } else {
          isBrochureDataValid = false;
          setState(() {});
          return;
        }
    }

    if (txtconBrochureProjectDescription!.text.trim() == '' ||
        txtconBrochureActualDescription!.text.trim() == '' ||
        brochureCoverImage[0].renderImageFirst!.filename == '' ||
        brochureVTImage[0].renderImageFirst!.filename == '') {
      isBrochureDataValid = false;
      setState(() {});
      return;
    }


    if(currentPlan.length <= 0 || projectPlanImages.length <= 0){
      isBrochureDataValid = false;
      setState(() {});
      return;
    }

    if (brochureActualImages.length == 6) {
      for (var i = 0; i < brochureActualImages.length; i++) {
        if (brochureActualImages[i].renderImageFirst!.filename == '') {
          isBrochureDataValid = false;
          setState(() {});
          return;
        }
      }
    } else {
      isBrochureDataValid = false;
      setState(() {});
      return;
    }

    if (brochureRenderImages.length == 6) {
      for (var i = 0; i < brochureRenderImages.length; i++) {
        if (brochureRenderImages[i].renderImageFirst!.filename == '') {
          isBrochureDataValid = false;
          setState(() {});
          return;
        }
      }
    } else {
      isBrochureDataValid = false;
      setState(() {});
      return;
    }



    isBrochureDataValid = true;
    setState(() {});
    return;
  }

  bool isAllCutHasValidBrochureData = false;

  isCutHasValidBrochureData(Property cut) {
    if ( cut.brochureProjectDescription == '' ||
        cut.photoBrochureCoverPaths!.length == 0 ||
        cut.videoBrochureVTPaths!.length == 0 ) {
      
      isAllCutHasValidBrochureData = false;
      return false;
    }


    if( cut.picturePaths!.length <= 0 || cut.mq == '' ||
        cut.baths == '' || cut.locals == '' || cut.virtualTour == '' ||
        double.tryParse(cut.styles![0].price.toString())! <= 0 ) {
      isAllCutHasValidBrochureData = false;
      return false;
      
    }

    

    if ( cut.photoBrochureRenderPaths!.length == 6) {
      for (var i = 0; i < cut.photoBrochureRenderPaths!.length; i++) {
        if (cut.photoBrochureRenderPaths![i]['filename'] == '') {
          isAllCutHasValidBrochureData = false;
          return false;
        }
      }
    } else {
      isAllCutHasValidBrochureData = false;
      return false;
    }


    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {  
      setState(() {
        isAllCutHasValidBrochureData = true;    
      });
    });

    return true;
  }

  getXValue(double a, double b) {
    double mqTrample = double.tryParse(txtconWalkableIns!.text) ?? 0;
    double rooms = double.tryParse(txtconLocalsIns!.text)??0;
    double bathsCount = double.tryParse(txtconBathsIns!.text)??0;
    String qualityOfArea = txtconQualityOfTheAreaIns!.text.toLowerCase();
    String floors = txtconFloors!.text;

    double x = (mqTrample * a) + (rooms * b);

    // Adding value based on the number of bathrooms
    for (int i = 1; i <= bathsCount; i++) {
      if (i == 1) {
        x += 7000;
      } else if (i == 2) {
        x += 5000;
      } else {
        x += 4000;
      }
    }

    // Adjusting value based on the quality of the area
    double qualityMultiplier = 0;
    if (qualityOfArea == "zona normale") {
      qualityMultiplier = 0; // No adjustment
    } else if (qualityOfArea == "zona ottima") {
      qualityMultiplier = 0.05;
    } else if (qualityOfArea == "zona di pregio") {
      qualityMultiplier = 0.10;
    }

    x += (x * qualityMultiplier);

    // Adjusting value based on the floor
    double floorMultiplier = 0;
    if (floors.toLowerCase().contains("terra") || floors.toLowerCase().contains("rialzato")) {
      floorMultiplier = 0; // No adjustment
    } else if (floors == "1" || floors == "2") {
      floorMultiplier = 0.05;
    } else if (int.tryParse(floors) != null && int.parse(floors) >= 3) {
      floorMultiplier = 0.1;
    }

    x += (x * floorMultiplier);
    
    return x;
  }

  calculateRisFull() {
    double x = getXValue(500, 1000);
    
    rangeResFull['actual'] = int.tryParse(x.toString())??0;
    rangeResFull['range_min'] = roundToNearestThousand(x * 0.9); 
    rangeResFull['range_max'] = roundToNearestThousand(x * 1.1); 

    txtconFullRenoIns!.text = '${rangeResFull['range_min']} - ${rangeResFull['range_max']}';
    txtconFullRenoInsMin!.text = rangeResFull['range_min'].toString();
    txtconFullRenoInsMax!.text = rangeResFull['range_max'].toString();

    double doorsIns = double.tryParse(txtconInteralDoorsIns!.text) ?? 0;
    double bathsIns = double.tryParse(txtconBathsIns!.text) ?? 0; 
    double walkableIns = double.tryParse(txtconWalkableIns!.text) ?? 0;
    double localsIns = double.tryParse(txtconLocalsIns!.text) ?? 0;
    double fixtureIns = double.tryParse(txtconFixtureIns!.text) ?? 0;

    double materialStandardFull =
        (doorsIns * 500) +
            (bathsIns * 3000) +
            (walkableIns * 25) +
            (localsIns * 180);

    double materialPremiumFull =
        (doorsIns * 800) +
            (bathsIns * 5000) +
            (walkableIns * 65) +
            (localsIns * 300);

    rangeResFull['material_standard'] = int.tryParse(materialStandardFull.toString());
    rangeResFull['material_standard_min'] = roundToNearestThousand(materialStandardFull * 0.9);
    rangeResFull['material_standard_max'] = roundToNearestThousand(materialStandardFull * 1.1);
    rangeResFull['material_permium'] = int.tryParse(materialPremiumFull.toString());
    rangeResFull['material_permium_min'] = roundToNearestThousand(materialPremiumFull * 0.9);
    rangeResFull['material_permium_max'] = roundToNearestThousand(materialPremiumFull * 1.1);
    rangeResFull['infissi'] = fixtureIns * 620;
    rangeResFull['infissi_min'] = roundToNearestThousand(rangeResFull['infissi'] * 0.9);
    rangeResFull['infissi_max'] = roundToNearestThousand(rangeResFull['infissi'] * 1.1);

    txtconMaterialStandardFull!.text = '${roundToNearestThousand(materialStandardFull * 0.9 )} - ${roundToNearestThousand(materialStandardFull * 1.1 )}';
    txtconMaterialPremiumFull!.text = '${roundToNearestThousand(materialPremiumFull * 0.9 )} - ${roundToNearestThousand(materialPremiumFull * 1.1 )}';
    txtconFixtureFull!.text = '${roundToNearestThousand(rangeResFull['infissi'] * 0.9 )} - ${roundToNearestThousand(rangeResFull['infissi'] * 1.1 )}';

    double x2 = getXValue(50, 1000);
    txtconLighRenoIns!.text = x2.toStringAsFixed(2);

    double materialStandardLight = (doorsIns * 500) + (bathsIns * 3000);
    double materialPremiumLight = (doorsIns * 800) + (bathsIns * 5000);

    rangeResLight['acutal'] = int.tryParse(x2.toString())??0; 
    rangeResLight['range_min'] = roundToNearestThousand(x2 * 0.9); 
    rangeResLight['range_max'] = roundToNearestThousand(x2 * 1.1); 
    rangeResLight['material_standard'] = int.tryParse(materialStandardLight.toString()); 
    rangeResLight['material_standard_min'] = roundToNearestThousand(materialStandardLight * 0.9); 
    rangeResLight['material_standard_max'] = roundToNearestThousand(materialStandardLight * 1.1); 
    rangeResLight['material_permium'] = int.tryParse(materialPremiumLight.toString());
    rangeResLight['material_permium_min'] = roundToNearestThousand(materialPremiumLight*0.9);
    rangeResLight['material_permium_max'] = roundToNearestThousand(materialPremiumLight*1.1);


    txtconLighRenoIns!.text = '${rangeResLight['range_min']} - ${rangeResLight['range_max']}';
    txtconMaterialStandardLight!.text = '${rangeResLight['material_standard_min']} - ${rangeResLight['material_standard_max']}';
    txtconMaterialPremiumLight!.text = '${rangeResLight['material_permium_min']} - ${rangeResLight['material_permium_max']}';

    

    

    

    txtconLighRenoInsMin!.text = rangeResLight['range_min'].toString();
    txtconLighRenoInsMax!.text = rangeResLight['range_max'].toString();

  }

  void _checkForChanges() {
    setState(() {
      widget.isInputChangeDetected![0] = true;
    });
  }

  @override
  void dispose() {
    // Clean up the controller when the widget is removed from the widget tree.
    // This also removes the _printLatestValue listener.
    // txtconPropertyName!.dispose();

    txtconPropertyName!.removeListener(_checkForChanges);
    txtconPropertyZoneName!.removeListener(_checkForChanges);
    txtconPropertyTypeName!.removeListener(_checkForChanges);
    txtconCivic!.removeListener(_checkForChanges);
    txtconCity!.removeListener(_checkForChanges);
    txtconDescription!.removeListener(_checkForChanges);
    txtconAreaMq!.removeListener(_checkForChanges);
    txtconBaths!.removeListener(_checkForChanges);
    txtconLocals!.removeListener(_checkForChanges);
    txtconBedrooms!.removeListener(_checkForChanges);
    txtconFloors!.removeListener(_checkForChanges);

    txtconStartDate!.removeListener(_checkForChanges);
    txtconEndDate!.removeListener(_checkForChanges);
    txtconLimitDate!.removeListener(_checkForChanges);
    publicStatusController!.removeListener(_checkForChanges);
    txtconDateStart!.removeListener(_checkForChanges);
    txtconDateEnd!.removeListener(_checkForChanges);
    txtconDateLimit!.removeListener(_checkForChanges);
    txtconVirtualTour!.removeListener(_checkForChanges);
    txtconBasePrice!.removeListener(_checkForChanges);
    super.dispose();
  }

  formatDateForParsing(String dateString) {
    List splittedDate = dateString.split('/');
    // print(splittedDate);
    return splittedDate[2] + '-' + splittedDate[1] + '-' + splittedDate[0];
  }

  autoLoadOptional(optional) {
    if (optional.length > 0) {
      // print(optional);

      // optional.forEach((key, value) {

      //   print( optional[key]['title'] );
      //   // print(optional[key].toJson() );
      //   // optionalFeatures[key] = true;
      //   // optionalFeaturesPrice[key]!.text = value;
      // });
    }
  }

  autoLoadStyleFileds(PropertyConfigStyles data) async {
    // List<String> imgs = [];
    // if( data.picturePaths != null ) {
    //   for (var i = 0; i < data.picturePaths!.length; i++) {
    //     imgs.add(await printUrl( widget.firebaseId, data.picturePaths![i]) );
    //   }
    // }

    styleValues.add({
      '_key': 'styleGroup-' + styleCounter.toString(),
      'styleName': new TextEditingController(text: data.styleName ?? ''),
      'picturePaths': [],
      'pictures': data.picturePaths,
      'price': new TextEditingController(
          text: data.price == null ? '' : localCurrencyFormat.format( double.tryParse(data.price.toString())) ),
    });

    styleValuesRetained.add(styleCounter);

    styles.addAll({
      'styleGroup-' + styleCounter.toString():
          styleFieldsGroup(context, styleCounter),
    });

    styleCounter++;
  }

  addStyleFields(BuildContext context) {
    styleValues.add({
      '_key': 'styleGroup-' + styleCounter.toString(),
      'styleName': new TextEditingController(),
      'picturePaths': [],
      'price': new TextEditingController(),
    });

    // Forced to set first predefined style as default for now.
    styleValues[0]['styleName'].text = predefinedStyleList[0];

    styleValuesRetained.add(styleCounter);

    styles.addAll({
      'styleGroup-' + styleCounter.toString():
          styleFieldsGroup(context, styleCounter),
    });

    styleCounter++;
  }

  Widget styleFieldsGroup(BuildContext context, num styleCounterIndex) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
                flex: 70,
                child: NarFormLabelWidget(
                  label:
                      styleValues[styleCounterIndex.toInt()]['styleName'].text,
                  fontWeight: 'bold',
                  fontSize: 17,
                )),
            Expanded(
              flex: 30,
              child: Row(
                children: [
                  NarFormLabelWidget(
                    label: 'Prezzo',
                    fontSize: 13,
                    fontWeight: '500',
                    textColor: Color.fromRGBO(105, 105, 105, 1),
                  ),
                  SizedBox(width: 10),
                  Expanded(
                    child: NarInputWidget(
                      hintText: "",
                      obscureText: false,
                      textInputType: TextInputType.number,
                      actionKeyboard: TextInputAction.done,
                      suffixIcon: Icon(
                        Icons.euro,
                        color: Color.fromRGBO(105, 105, 105, 1),
                        size: 13,
                      ),
                      fontColor: Colors.black,
                      borderRadius: 8,
                      borderColor: Color(0xffdbdbdb),
                      //functionValidate: (),
                      controller: styleValues[styleCounterIndex.toInt()]
                          ['price'],
                      // focusNode: _passwordControllerFocus,
                      onSubmitField: () {},
                      parametersValidate: "",
                      // prefixIcon: Icon(Icons.keyboard_hide),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(width: 17),
        SizedBox(width: 17),
        Container(
          // height: 100,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NarFormLabelWidget(label: '', fontWeight: '800'),
              NarImagePickerWidget(
                  imagesToDisplayInList: 0,
                  removeButton: true,
                  removeButtonText: 'Elimina',
                  removeButtonTextColor: Color(0xff797979),
                  showMoreButtonText: '+ espandi',
                  removeButtonPosition: 'bottom',
                  uploadButtonPosition: 'back',
                  displayFormat: 'row',
                  imageDimension: 65,
                  borderRadius: 8,
                  fontSize: 12,
                  fontWeight: '600',
                  text: 'Aggiungi immagini',
                  borderSideColor: Theme.of(context).primaryColor,
                  imageContainerPadding:
                      EdgeInsets.symmetric(vertical: 0, horizontal: 0),
                  imageContainerUploadButtonAlignment: 'end',
                  // leadingIcon: Icon(
                  //   Icons.add,
                  //   size: 10,
                  // ),
                  hoverColor: Theme.of(context).primaryColor,
                  images: styleValues[styleCounterIndex.toInt()]
                      ['picturePaths'],
                  preloadedImages: styleValues[styleCounterIndex.toInt()]
                      ['pictures'],
                  firebaseId: widget.property!.firebaseId != null
                      ? widget.property!.firebaseId
                      : '',
                  pageContext: context)
            ],
          ),
        ),
      ],
    );
  }

  String commonValidation(String value, String messageError) {
    var required = requiredValidator(value, messageError);
    if (required != null) {
      return required;
    }
    return '';
  }

  String requiredValidator(value, messageError) {
    if (value.isEmpty) {
      return messageError;
    }
    return '';
  }

  Future<AgencyUser> getAgencyUser(String uid) async {
    DocumentSnapshot<Map<String, dynamic>?> firestoreUserDoc =
        await fetchDocument('users/${uid}');
    Map<String, dynamic>? firestoreUser = firestoreUserDoc.data();

    DocumentSnapshot<Map<String, dynamic>?> firestoreAgencyDoc =
        await fetchDocument('agencies/${firestoreUser!["agencyId"]}');
    Map<String, dynamic>? firestoreAgency = firestoreAgencyDoc.data();

    AgencyUser agencyUser = AgencyUser(firestoreUser, firestoreUserDoc.id,
        firestoreAgency!, firestoreAgencyDoc.id);

    return agencyUser;
  }

  void changeFocus(
      BuildContext context, FocusNode currentFocus, FocusNode nextFocus) {
    currentFocus.unfocus();
    FocusScope.of(context).requestFocus(nextFocus);
  }

  Future<bool> updateActiveStatus( String firebaseId, Property property) async {
    final FirebaseFirestore _db = FirebaseFirestore.instance;
    return await _db
        .collection(appConfig.COLLECT_NEWARC_HOME)
        .doc(firebaseId)
        .update(property.toMap())
        .then((value) {
      return true;
    }).onError((error, stackTrace) {
      // print({error, stackTrace});
      return false;
    });
  }

  List<bool> isExpanded = [
    true,
    true,
    true,
    true,
    true,
    true,
    true,
  ];

  Widget collapsableSection(title, int index, Widget content) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          border: Border.all(width: 1.78, color: Color(0xffC8C8C8)),
          color: Colors.white),
      padding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
      margin: EdgeInsets.only(bottom: 15),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              NarFormLabelWidget(
                label: title,
                fontSize: 20,
                textColor: Colors.black,
                fontWeight: 'bold',
              ),
              Row(
                children: [
                  title == "Composizione Brochure" ? Row(children: [
                    StatusWidget(status: isBrochureDataValid ?  "generabile" : "non generabile",statusColor: isBrochureDataValid ? Color(0xff39C14F) : Color(0xffE82525),),
                    SizedBox(width: 10,),
                    BaseNewarcButton(
                      color: isBrochureDataValid ? Theme.of(context).primaryColor : Theme.of(context).primaryColor.withOpacity(0.3),
                      textColor: Colors.white,
                      buttonText: brochurePdfPath.isNotEmpty ?  "Ri-genera " : "Genera",
                      fontWeight: '600',
                      fontSize: 13,
                      height: 30,
                      width: 125,
                      onPressed: isBrochureDataValid ? () async {
                        final ValueNotifier<double> progress = ValueNotifier(0);
                        if(generatingPdf){
                          return;
                        }
                        setState(() {
                          generatingPdf = true;
                        });
                        _showDownloadingDialog(context,progress: progress);
                        try {
                          if (widget.property!.projectType == 'cut' || widget.property!.projectType == 'Immagina for Professionals') { 
                            
                            ImmaginaProject project = widget.project;
                            await pdfCutBrochure(widget.property!, {
                              'title': project.addressInfo! .streetName! + ' ' + project.addressInfo! .streetNumber!,
                              'agencyId': project.agencyId,
                              'professionalId': project.professionalId,
                              'virtualTourUrl': txtconVirtualTour!.text,
                            }, progress: progress);
                            brochurePdfPath = {
                              "filename":"${widget.property?.code}.pdf",
                              "location":"/newarcHomes/${widget.property?.firebaseId}/brochure-pdf/",
                            };
                            
                            log("brochurePdfPath ===> ${brochurePdfPath}");
                            
                            widget.property?.brochurePdfPath = {
                              "filename":"${widget.property?.code}.pdf",
                              "location":"/newarcHomes/${widget.property?.firebaseId}/brochure-pdf/",
                            };
                          } else if (projectType == 'Newarc') {
                            NewarcProject project =
                                widget.project;
                            await pdfAdsBrochure(
                                widget.property!, {
                              'title': project.name,
                              'agencyId': project.assignedAgency!['agencyId'],
                              'virtualTourUrl': txtconVirtualTour!.text,
                            },progress: progress);

                            brochurePdfPath = {
                              "filename":"${widget.property?.code}.pdf",
                              "location":"/newarcHomes/${widget.property?.firebaseId}/brochure-pdf/",
                            };
                            log("brochurePdfPath ===> ${brochurePdfPath}");
                            widget.property?.brochurePdfPath = {
                              "filename":"${widget.project.code}.pdf",
                              "location":"/newarcHomes/${widget.project.firebaseId}/brochure-pdf/",
                            };
                            setState(() {});

                          } else if (projectType == 'Immagina') {
                            ImmaginaProject project = widget.project;
                            await pdfAdsBrochure(widget.property!, {
                              'title': project.addressInfo! .streetName! + ' ' + project.addressInfo! .streetNumber!,
                              'agencyId': project.agencyId,
                              'virtualTourUrl': txtconVirtualTour!.text,
                            }, progress: progress);
                            brochurePdfPath = {
                              "filename":"${widget.property?.code}.pdf",
                              "location":"/newarcHomes/${widget.property?.firebaseId}/brochure-pdf/",
                            };
                            log("brochurePdfPath ===> ${brochurePdfPath}");
                            widget.property?.brochurePdfPath = {
                              "filename":"${widget.property?.code}.pdf",
                              "location":"/newarcHomes/${widget.property?.firebaseId}/brochure-pdf/",
                            };
                            setState(() {});
                          }
                        } finally {
                          Navigator.of(context, rootNavigator: true).pop();
                          setState(() {
                            generatingPdf = false;
                          });
                        }
                      } : (){},
                    ),
                    SizedBox(width: 10,),
                  ],) : SizedBox.shrink(),
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                        onTap: () {
                          setState(() {
                            if (isExpanded[index])
                              isExpanded[index] = false;
                            else
                              isExpanded[index] = true;
                          });
                        },
                        child: SvgPicture.asset(
                          'assets/icons/arrow_down.svg',
                          color: Color(0xff7E7E7E),
                          width: 14,
                        )),
                  ),
                ],
              ),
            ],
          ),
          isExpanded[index] ? content : Container(),
        ],
      ),
    );
  }

  showImmaginaRedirectPopup() {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) {
        return Center(
          child: BaseNewarcPopup(
              title: 'Invia all’agenzia',
              noButton: true,
              column: StatefulBuilder(
                  builder: (BuildContext context, StateSetter __setState) {
                return SingleChildScrollView(
                  child: Container(
                    height: 200,
                    width: 350,
                    child: Column(
                      children: [
                        Container(
                          width: 350,
                          height: 120,
                          margin: EdgeInsets.only(bottom: 20, top: 20),
                          child: Center(
                            child: NarFormLabelWidget(
                                label:
                                    "I files sono stati resi disponibili \nall’agenzia immobiliare!",
                                fontSize: 18,
                                fontWeight: 'bold',
                                letterSpacing: 0.01,
                                overflow: TextOverflow.visible,
                                textAlign: TextAlign.center,
                                textColor: isReadyForAgency
                                    ? Colors.black
                                    : Color(0xffCA5050)),
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: BaseNewarcButton(
                                  buttonText: "Chiudi",
                                  fontSize: 15,
                                  onPressed: () async {
                                    Navigator.pop(context);
                                  },
                                  // width: 152,
                                  height: 40,
                                  color: Color(0xffD7D7D7),
                                  textColor: Colors.black),
                            ),
                            SizedBox(width: 10),
                            Expanded(
                              child: BaseNewarcButton(
                                buttonText: "Vai al progetto",
                                fontSize: 15,

                                onPressed: () async {
                                  Map projectArguments = {};

                                  projectArguments.addAll({
                                    'projectFirebaseId': widget.project.getid(),
                                    'updateViewCallback':
                                        widget.updateViewCallback,
                                    'initialFetchProperties':
                                        widget.initialFetchProperties,
                                    'isFromRequest': false,
                                  });
                                  widget.initialFetchProperties;

                                  Navigator.pop(context);

                                  widget.updateViewCallback!(
                                      'immagina-project-review',
                                      projectArguments: projectArguments);
                                  // widget.updateViewCallback!('inside-request', projectArguments: projectArguments);
                                },
                                // width: 152,
                                height: 40,
                              ),
                            )
                          ],
                        )
                      ],
                    ),
                  ),
                );
              })),
        );
      },
    );
  }

  openDownloadPopup() {
    TextEditingController contDownloadType = new TextEditingController();
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) {
        return Center(
          child: BaseNewarcPopup(
              title: 'Scarica file',
              noButton: true,
              column: StatefulBuilder(
                  builder: (BuildContext context, StateSetter _setState) {
                return SingleChildScrollView(
                  child: Container(
                    height: 210,
                    width: 420,
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              flex: 1,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  NarSelectBoxWidget(
                                    label: "Seleziona",
                                    options: [
                                      'Brochure PDF',
                                      "Immagini Portali"
                                    ],
                                    controller: contDownloadType,
                                    parametersValidate: "Required!",
                                    validationType: 'required',
                                    onChanged: (value) {
                                      _setState(() {});
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        NarFormLabelWidget(
                            label: downloadingPdf || downloadingImage
                                ? 'Downloading'
                                : ''),
                        SizedBox(height: 50),
                        Container(
                          width: 150,
                          child: Opacity(
                            opacity: (contDownloadType.text == 'Brochure PDF' && !isBrochureDataValid) || (contDownloadType.text == 'Immagini Portali' && !isReadyForAgency)
                                ? 0.5
                                : 1,
                            child: BaseNewarcButton(
                              buttonText: "Scarica",
                              fontSize: 15,

                              onPressed: (contDownloadType.text == 'Brochure PDF' && !isBrochureDataValid) || (contDownloadType.text == 'Immagini Portali' && !isReadyForAgency)
                                  ? () {}
                                  : () async {
                                      if (contDownloadType.text ==
                                          'Brochure PDF') {
                                        _setState(() {
                                          downloadingPdf = true;
                                        });
                                        try {
                                          if(widget.property?.brochurePdfPath?.isNotEmpty ?? false){
                                           String url = await printUrl(widget.property?.brochurePdfPath?["location"], '', widget.property?.brochurePdfPath?["filename"]);
                                           List<String> split0 = url.split('/');
                                           List<String> split1 = split0[split0.length - 1].split('?');
                                           List<String> split2 = split1[0].split('%2F');
                                           String filename = split2[split2.length - 1];

                                           html.AnchorElement anchorElement = new html.AnchorElement(href: url);

                                           anchorElement.download = filename;
                                           // anchorElement.target = '_blank';
                                           anchorElement.click();
                                          }
                                          
                                        } finally {
                                          _setState(() {
                                            downloadingPdf = false;
                                          });
                                          Navigator.pop(context);
                                        }
                                      } else if (contDownloadType.text ==
                                          'Immagini Portali') {
                                        _setState(() {
                                          downloadingImage = true;
                                        });
                                        try {
                                          if (projectType == 'Newarc') {
                                            NewarcProject project =
                                                widget.project;
                                            await imageAdsBrochure(
                                                widget.property!, {
                                              'title': project.name,
                                              'agencyId': project
                                                  .assignedAgency!['agencyId']
                                            });
                                          } else {
                                            ImmaginaProject project =
                                                widget.project;
                                            await imageAdsBrochure(
                                                widget.property!, {
                                              'title': project.addressInfo!
                                                      .streetName! +
                                                  ' ' +
                                                  project.addressInfo!
                                                      .streetNumber!,
                                              'agencyId': project.agencyId
                                            });
                                          }
                                        } finally {
                                          _setState(() {
                                            downloadingImage = false;
                                          });
                                          Navigator.pop(context);
                                        }
                                      }
                                    },
                              // width: 152,
                              height: 40,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              })),
        );
      },
    );
  }

  openAgencyPopup() {
    
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) {
        return Center(
          child: BaseNewarcPopup(
              title: 'Invia all’agenzia',
              noButton: true,
              column: StatefulBuilder(
                  builder: (BuildContext context, StateSetter __setState) {
                return SingleChildScrollView(
                  child: Container(
                    height: 480,
                    width: 420,
                    child: Column(
                      children: [
                        Container(
                          padding: EdgeInsets.all(20),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              //shape: BoxShape.circle,
                              border: Border.all(
                                  width: 1, color: Color(0xffD7D7D7)),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10))),
                          width: 350,
                          height: 330,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Column(
                                children: readyForAgencyCheckList.map((e) {
                                  Color iconColor =
                                      Theme.of(context).primaryColor;
                                  Color textColor = Colors.black;
                                  double iconSize = 14;
                                  double spacing = 10;
                                  String _icon = 'check.svg';

                                  if (e['status'] == false) {
                                    iconColor = Color(0xffCA5050);
                                    textColor = Color(0xffCA5050);
                                    iconSize = 11;
                                    _icon = 'close-popup.svg';
                                    spacing = 12;
                                  }

                                  return Container(
                                    margin: EdgeInsets.only(bottom: 10),
                                    child: Row(
                                      children: [
                                        SvgPicture.asset(
                                          'assets/icons/${_icon}',
                                          color: iconColor,
                                          height: iconSize,
                                        ),
                                        SizedBox(width: spacing),
                                        NarFormLabelWidget(
                                            label: e['label'],
                                            fontSize: 14,
                                            fontWeight: '600',
                                            letterSpacing: 0.01,
                                            textColor: textColor)
                                      ],
                                    ),
                                  );
                                }).toList(),
                              ),
                              if( widget.property!.projectType != 'Immagina for Professionals' ) Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  NarFormLabelWidget(
                                      label: "Stima ristrutturazione",
                                      fontSize: 13,
                                      fontWeight: 'bold',
                                      letterSpacing: 0.01,
                                      textColor: Colors.black),
                                  Container(
                                    width: 320,
                                    height: 50,
                                    margin: EdgeInsets.only(top: 5),
                                    decoration: BoxDecoration(
                                        color: Colors.white,
                                        //shape: BoxShape.circle,
                                        border: Border.all(
                                            width: 1, color: Color(0xffD7D7D7)),
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(8))),
                                    child: Center(
                                      child: NarFormLabelWidget(
                                          label:
                                              widget.property!.FullRenoIns != ''
                                                  ? localCurrencyFormat.format(
                                                          double.tryParse(widget
                                                              .property!
                                                              .FullRenoIns!)) +
                                                      "€"
                                                  : '0€',
                                          fontSize: 15,
                                          fontWeight: '700',
                                          letterSpacing: 0.01,
                                          textColor: Colors.black),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Container(
                          width: 350,
                          height: 65,
                          margin: EdgeInsets.only(bottom: 20, top: 20),
                          child: Center(
                            child: NarFormLabelWidget(
                                label: isReadyForAgency
                                    ? "Vuoi rendere disponibili i file all’agenzia immobiliare e segnare il progetto Immagina come completato?"
                                    : "Completa i caricamenti per inviare",
                                fontSize: 18,
                                fontWeight: 'bold',
                                letterSpacing: 0.01,
                                overflow: TextOverflow.visible,
                                textAlign: TextAlign.center,
                                textColor: isReadyForAgency
                                    ? Colors.black
                                    : Color(0xffCA5050)),
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Opacity(
                              opacity: isReadyForAgency ? 1 : 0.5,
                              child: BaseNewarcButton(
                                buttonText: "Invia all’agenzia",
                                fontSize: 15,

                                onPressed: !isReadyForAgency
                                    ? () {}
                                    : () async {
                                        var docRef = FirebaseFirestore.instance
                                            .collection(appConfig
                                                .COLLECT_IMMAGINA_PROJECTS)
                                            .doc(widget.project.getid());
                                        await docRef.update({
                                          'requestStatus':
                                              CommonUtils.completato,
                                          // 'statusChangedDate': DateTime.now().millisecondsSinceEpoch,
                                          'projectCompletedDate': DateTime.now()
                                              .millisecondsSinceEpoch
                                        });

                                        ImmaginaProject project = widget.project!;
                                        bool isAgency = project.agencyId != null && project.agencyId != '';
                                        bool isProfessional = project.professionalId != null;
                                        DocumentSnapshot<Map<String, dynamic>> agencyData;
                                        DocumentSnapshot<Map<String, dynamic>> professionalData;
                                        if (isAgency) {
                                          agencyData = await FirebaseFirestore.instance
                                            .collection(appConfig.COLLECT_AGENCIES)
                                            .doc(project.agencyId)
                                            .get();
                                          sendEmail(
                                            templateId: CommonUtils.filesUploadedEmailTemplateId,
                                            subject: CommonUtils.filesUploadedEmailSubject,
                                            variables: {
                                              "agencyname": agencyData.data()!["name"],
                                              "immaginaprojectid": project.projectId,
                                            },
                                            recipientEmail: agencyData.data()!["email"],
                                            recipientName: agencyData.data()!["name"],
                                          );
                                        };
                                        if (isProfessional) {
                                          professionalData = await FirebaseFirestore.instance
                                            .collection(appConfig.COLLECT_PROFESSIONALS)
                                            .doc(project.professionalId)
                                            .get();
                                          sendEmail(
                                            templateId: CommonUtils.filesUploadedForProfessionalsEmailTemplateId,
                                            subject: CommonUtils.filesUploadedForProfessionalsEmailSubject,
                                            recipientEmail: professionalData.data()!["email"],
                                            recipientName: professionalData.data()!["companyName"],
                                          );
                                        };                                        

                                        var _docRef = FirebaseFirestore.instance
                                            .collection(
                                                appConfig.COLLECT_NEWARC_HOME)
                                            .doc(widget.property!.firebaseId);

                                        int newcount = (widget.property!
                                                    .sentToAgencyCounter ??
                                                0) +
                                            1;

                                        await _docRef.update(
                                            {'sentToAgencyCounter': newcount});

                                        widget.property!.sentToAgencyCounter =
                                            newcount;

                                        setState(() {});
                                        Navigator.pop(context);
                                        showImmaginaRedirectPopup();
                                      },
                                // width: 152,
                                height: 40,
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                );
              })),
        );
      },
    );
  }


  createCut() async {
    TextEditingController txtconCutType = TextEditingController();
    TextEditingController txtconCutMq = TextEditingController();
    TextEditingController txtconCutRooms = TextEditingController();
    TextEditingController txtconCutBaths = TextEditingController();
    TextEditingController txtconCutFloors = TextEditingController();
    TextEditingController txtconCutPrice = TextEditingController();

    List<String> cutFormMessages = [];
    return await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (BuildContext context, StateSetter _setState) {
          return Center(
            child: BaseNewarcPopup(
              formErrorMessage: cutFormMessages,
              buttonText: 'Aggiungi',
              onPressed: () async {
                cutFormMessages.clear();
                if (txtconCutType.text.isEmpty) {
                  cutFormMessages.add('Seleziona tipologia');
                  _setState(() {});
                  return false;
                }
                if (txtconCutMq.text.isEmpty) {
                  cutFormMessages.add('Inserisci Mq');
                  _setState(() {});
                  return false;
                }
                if (txtconCutRooms.text.isEmpty) {
                  cutFormMessages.add('Seleziona Locali');
                  _setState(() {});
                  return false;
                }
                if (txtconCutBaths.text.isEmpty) {
                  cutFormMessages.add('Seleziona Bagni');
                  _setState(() {});
                  return false;
                }
                if (txtconCutFloors.text.isEmpty) {
                  cutFormMessages.add('Seleziona Piano');
                  _setState(() {});
                  return false;
                }
                if (txtconCutPrice.text.isEmpty) {
                  cutFormMessages.add('Inserisci Prezzo');
                  
                  return false;
                }

                _setState(() { cutFormMessages.add('Aggiunta in corso...'); });
                

                Property newCut = Property.empty();

                newCut.setFromObject( widget.property! );

                newCut.type = txtconCutType.text;
                newCut.locals = txtconCutRooms.text;
                newCut.baths = txtconCutBaths.text;
                newCut.floors = txtconCutFloors.text;
                newCut.mq = txtconCutMq.text;
                newCut.buildingPictures = [];
                newCut.projectType = 'cut';
                newCut.code = widget.property!.code! + '_${(widget.property!.children!.length + 1)}';
                newCut.styles!.add(
                  PropertyConfigStyles(
                    styleName: '',
                    price: txtconCutPrice.text,
                    styleId: 'styleGroup-0',
                    isDefault: true,
                    picturePaths: [],
                    pictures: [],
                    description: ''
                  )
                );
                newCut.isActive = true;
                newCut.isArchived = false;
                newCut.insertTimestamp = Timestamp.now().millisecondsSinceEpoch;
                newCut.updateTimestamp = Timestamp.now().millisecondsSinceEpoch;
                newCut.clicks = 0;

                final FirebaseFirestore _db = FirebaseFirestore.instance;
                DocumentReference<Map<String, dynamic>> adResponse = await _db.collection(appConfig.COLLECT_NEWARC_HOME)
                .add(newCut.toMap());

                if (adResponse.id != '') {

                  newCut.firebaseId = adResponse.id;
                  widget.property!.children!.add(adResponse.id);
                  
                  await _db.collection(appConfig.COLLECT_NEWARC_HOME)
                  .doc( widget.property!.firebaseId )
                  .update({
                    'children': widget.property!.children!
                  });

                  await _db.collection(appConfig.COLLECT_NEWARC_HOME)
                  .doc( adResponse.id )
                  .update({
                    'firebaseId': adResponse.id
                  });

                  setState(() {});

                  return true;


                  
                }
                    
                
                
              },
              column: Container(
                width: 345,
                margin: EdgeInsets.symmetric(horizontal: 100),
                child: Center(
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Expanded(
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                NarFormLabelWidget(
                                  label: "Seleziona tipologia",
                                  textColor: Color(0xff696969),
                                  fontSize: 14,
                                  fontWeight: '600',
                                ),
                                SizedBox(height: 4),
                                NarSelectBoxWidget(
                                    options: appConst.propertyTypesList,
                                    onChanged: (value) {
                                      _setState(() {});
                                    },
                                    controller: txtconCutType,
                                    validationType: 'required',
                                    parametersValidate: 'Required!'),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Expanded(
                            child: CustomTextFormField(
                              label: 'Mq',
                              controller: txtconCutMq,
                            ),
                          ),
                          SizedBox(width: 4),
                          Expanded(
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                NarFormLabelWidget(
                                  label: "Locali",
                                  textColor: Color(0xff696969),
                                  fontSize: 14,
                                  fontWeight: '600',
                                ),
                                SizedBox(height: 4),
                                NarSelectBoxWidget(
                                    options: localsList,
                                    onChanged: (value) {
                                      _setState(() {});
                                    },
                                    controller: txtconCutRooms,
                                    validationType: 'required',
                                    parametersValidate: 'Required!'),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Expanded(
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                NarFormLabelWidget(
                                  label: "Bagni",
                                  textColor: Color(0xff696969),
                                  fontSize: 14,
                                  fontWeight: '600',
                                ),
                                SizedBox(height: 4),
                                NarSelectBoxWidget(
                                    options: bathsList,
                                    onChanged: (value) {
                                      _setState(() {});
                                    },
                                    controller: txtconCutBaths,
                                    validationType: 'required',
                                    parametersValidate: 'Required!'),
                              ],
                            ),
                          ),
                          SizedBox(width: 4),
                          Expanded(
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                NarFormLabelWidget(
                                  label: "Piano",
                                  textColor: Color(0xff696969),
                                  fontSize: 14,
                                  fontWeight: '600',
                                ),
                                SizedBox(height: 4),
                                NarSelectBoxWidget(
                                    options: floorsList,
                                    onChanged: (value) {
                                      _setState(() {});
                                    },
                                    controller: txtconCutFloors,
                                    validationType: 'required',
                                    parametersValidate: 'Required!'),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10),
                      
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Expanded(
                            child: CustomTextFormField(
                              label: 'Prezzo',
                              isMoney: true,
                              isShowPrefillMoneyIcon: true,
                              controller: txtconCutPrice,
                            ),
                          ),
                        ]
                      )
                    ]
                  )
                )
              ),
              title: 'Nuovo taglio',
            )
          );
        });
      }); 


  }

  

  Future<List<Property>> fetchCuts() async {
    List<Property> cuts = [];
    
    if (widget.property!.children != null && widget.property!.children!.length > 0) {
      
      for (String cutId in widget.property!.children!) {
        DocumentSnapshot<Map<String, dynamic>> cutResponse =
            await FirebaseFirestore.instance
                .collection(appConfig.COLLECT_NEWARC_HOME)
                .doc(cutId)
                .get();
        if (cutResponse.exists) {
          Property cut = Property.fromDocument(cutResponse);
          cuts.add(cut);
          
        }
      }
      return cuts;
    }
    return [];
  }



  @override
  Widget build(BuildContext context) {
    if (widget.property!.styles == null ||
        widget.property!.styles!.length == 0) {
      addStyleFields(context);
    }
    return Form(
      key: _formKey,
      child: Stack(
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      NarFormLabelWidget(
                        label: 'Annuncio di vendita',
                        fontSize: 24,
                        fontWeight: '700',
                        textColor: Color.fromRGBO(0, 0, 0, 1),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      NarFormLabelWidget(
                        label: 'Data pubblicazione',
                        fontSize: 13,
                        fontWeight: '600',
                        textColor: AppColor.greyColor,
                      ),
                      SizedBox(
                        width: 10,
                      ),
                      MouseRegion(
                        cursor: SystemMouseCursors.click,
                        child: GestureDetector(
                          onTap: () async {
                            final selectedDate = await showDatePicker(
                              context: context,
                              initialDate: DateTime.now(),
                              firstDate: DateTime(1900),
                              lastDate: DateTime(2100),
                            );
                            if (selectedDate != null) {
                              setState(() {
                                publicationDate =
                                    selectedDate.millisecondsSinceEpoch;
                              });
                            }
                          },
                          child: Container(
                            width: 145,
                            height: 45,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(7),
                                border: Border.all(color: Color(0xFFDBDBDB))),
                            padding: EdgeInsets.symmetric(
                                vertical: 8, horizontal: 10),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                NarFormLabelWidget(
                                  label: getFormattedDate(publicationDate),
                                  fontSize: 13,
                                  fontWeight: '600',
                                  textColor: AppColor.black,
                                ),
                                SvgPicture.asset(
                                  "assets/icons/calendar.svg",
                                  color: Color(0xff7B7B7B),
                                  height: 17,
                                  width: 17,
                                )
                              ],
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 20,
                      ),
                      NarFormLabelWidget(
                        label: 'Pubblica sul sito',
                        fontSize: 17,
                        fontWeight: 'bold',
                      ),
                      Switch(
                        // This bool value toggles the switch.
                        value: widget.property!.isActive == null
                            ? false
                            : widget.property!.isActive!,
                        activeColor: Theme.of(context).primaryColor,
                        onChanged: (bool value) async {
                          // This is called when the user toggles the switch.

                          setState(() {
                            widget.property!.isActive = value;
                            widget.isInputChangeDetected![0] = true;
                          });

                          try {
                            await updateActiveStatus( widget.property!.firebaseId!,  widget.property!);
                          } catch (e) {
                            widget.property!.isActive = !value;
                          }
                        },
                      ),
                    ],
                  )
                ],
              ),
              SizedBox(height: 35),
              collapsableSection(
                  'Stato',
                  0,
                  Column(
                    children: [
                      SizedBox(
                        height: 10,
                      ),
                      NarCheckboxWidget(
                        label: 'Public Status Options',
                        columns: 2,
                        fontSize: 13,
                        values: publicStatusOptions,
                      ),
                    ],
                  )),
              SizedBox(height: 15),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  NarFormLabelWidget(
                      label: dataGenerallable,
                      fontSize: 25,
                      fontWeight: 'bold',
                      textColor: Colors.black,
                      textAlign: TextAlign.center),
                ],
              ),
              SizedBox(height: 15),
              collapsableSection(
                  'Identificazione',
                  1,
                  Column(
                    children: [
                      SizedBox(
                        height: 10,
                      ),
                      
                      if( !isImmaginaForProfessionalsValid ) Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Expanded(
                            flex: 3,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                NarSelectBoxWidget(
                                  label: "Tipologia",
                                  enabled: widget.property!.projectType == 'cut' || widget.property!.projectType == 'Immagina for Professionals' ? false : true,
                                  flex: 3,
                                  options: appConst.propertyTypesList,
                                  controller: txtconPropertyTypeName,
                                  
                                ),
                              ],
                            ),
                          ),
                          SizedBox(width: 17),
                          CustomTextFormField(
                            flex: 2,
                            enabled: widget.property!.projectType == 'cut' || widget.property!.projectType == 'Immagina for Professionals' ? false : true,
                            label: 'Mq',
                            hintText: "",
                            controller: txtconAreaMq,
                          ),
                          SizedBox(width: 17),
                          Expanded(
                            flex: 1,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                NarSelectBoxWidget(
                                  label: "Locali",
                                  enabled: widget.property!.projectType == 'cut' || widget.property!.projectType == 'Immagina for Professionals' ? false : true,
                                  options: localsList,
                                  controller: txtconLocals,
                                ),
                              ],
                            ),
                          ),
                          SizedBox(width: 17),
                          Expanded(
                            flex: 1,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                NarSelectBoxWidget(
                                  label: 'Bagni',
                                  enabled: widget.property!.projectType == 'cut' || widget.property!.projectType == 'Immagina for Professionals' ? false : true,
                                  options: bathsList,
                                  controller: txtconBaths,
                                ),
                              ],
                            ),
                          ),
                          SizedBox(width: 17),
                          Expanded(
                            flex: 1,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                NarSelectBoxWidget(
                                  label: "Piano",
                                  enabled: widget.property!.projectType == 'cut' || widget.property!.projectType == 'Immagina for Professionals' ? false : true,
                                  options: floorsList,
                                  controller: txtconFloors,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomTextFormField(
                            enabled: false,
                            flex: 3,
                            label: 'Indirizzo completo',
                            hintText: "",
                            controller: txtconPropertyName,
                          ),
                          SizedBox(width: 17),
                          appConst.cityZones.keys.contains(txtconCity!.text)
                              ? Expanded(
                                  flex: 2,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      NarSelectBoxWidget(
                                        label: "Zona",
                                        options: appConst
                                            .cityZones[txtconCity!.text]
                                            ?.map((zone) =>
                                                zone.toString().toTitleCase())
                                            .toList()
                                            .cast<String>(),
                                        controller: txtconPropertyZoneName,
                                      ),
                                    ],
                                  ),
                                )
                              : CustomTextFormField(
                                  flex: 2,
                                  label: 'Zona',
                                  enabled: widget.property!.projectType == 'cut' ? false : true,
                                  controller: txtconPropertyZoneName,
                                ),
                          Expanded(
                            flex: 1,
                            child: SizedBox(height: 0),
                          )
                        ],
                      ),
                      SizedBox(height: 10),
                      if( !isImmaginaForProfessionalsValid )Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (styleValues.length > 0)
                            CustomTextFormField(
                              flex: 2,
                              label: 'Prezzo base',
                              hintText: "",
                              controller: styleValues[0]['price'],
                              isMoney: true,
                              enabled: widget.property!.projectType == 'cut' || widget.property!.projectType == 'Immagina for Professionals' ? false : true,
                              suffixIcon: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  NarFormLabelWidget(
                                      label: '€',
                                      
                                      fontSize: 14,
                                      textColor: Color(0xff7F7F7F))
                                ],
                              ),
                            ),
                          SizedBox(width: 17),
                          Expanded(
                            flex: 2,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                NarSelectBoxWidget(
                                  label: "Stato",
                                  options: ['Progetto', 'In ristrutturazione', 'Completata', 'Nuova costruzione'],
                                  controller: txtconProjectStatus,
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: SizedBox(height: 0),
                          )
                        ],
                      ),
                    ],
                  )),
              collapsableSection(
                  'Descrizione',
                  2,
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 10,
                      ),
                      NarFormLabelWidget(
                        label: 'Inserisci descrizione',
                        fontSize: 13,
                        fontWeight: '600',
                        textColor: Colors.black,
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      NarTextareaWidget(
                        hintText: "",
                        maxLines: 8,
                        minLines: 8,
                        actionKeyboard: TextInputAction.done,
                        controller: txtconDescription,
                        onSubmitField: () {},
                      ),
                    ],
                  )),
              
              if( !isImmaginaForProfessionalsValid ) collapsableSection(
                  'Caratteristiche',
                  3,
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 10,
                      ),
                      NarFormLabelWidget(
                        label: 'Attuali',
                        fontSize: 18,
                        fontWeight: 'bold',
                        textColor: Colors.black,
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Row(
                        children: [
                          SizedBox(
                            width: 150,
                            child: NarSelectBoxWidget(
                              label: 'Classe energetica',
                              options: appConst.energyClassList,
                              controller: txtconActualEnergyClass,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      NarCheckboxWidget(
                          label: 'Current Features',
                          columns: 4,
                          fontSize: 13,
                          values: currentFeatures,
                          childAspectRatio: 5),
                      if (widget.property!.projectType == 'Immagina')
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              height: 10,
                            ),
                            Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                border: Border.all(
                                  width: 1,
                                  color: Color(0xffDBDBDB),
                                ),
                              ),
                              height: 1,
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            NarFormLabelWidget(
                              label: 'Progetto',
                              fontSize: 18,
                              fontWeight: 'bold',
                              textColor: Colors.black,
                            ),
                            SizedBox(
                              height: 10,
                            ),

                            Row(
                              children: [
                                SizedBox(
                                  width: 150,
                                  child: NarSelectBoxWidget(
                                    label: 'Classe energetica',
                                    options: appConst.energyClassList,
                                    controller: txtconProjectEnergyClass,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            NarCheckboxWidget(
                                label: 'Property Features',
                                columns: 4,
                                fontSize: 13,
                                values: propertyFeatures,
                                childAspectRatio: 5),
                          ],
                        )
                    ],
                  )),
              SizedBox(height: 15),
              if ( widget.property!.projectType == 'Immagina')
                collapsableSection(
                    'Ristrutturazione',
                    4,
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 10,
                        ),
                        Row(
                          children: [
                            Expanded(
                              flex: 1,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  NarSelectBoxWidget(
                                    label: "Locali",
                                    options: localsList,
                                    controller: txtconLocalsIns,
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: 17),
                            Expanded(
                              flex: 1,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  NarSelectBoxWidget(
                                    label: 'Bagni',
                                    options: bathsList,
                                    controller: txtconBathsIns,
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: 17),
                            CustomTextFormField(
                              label: "Porte int.",
                              controller: txtconInteralDoorsIns,
                            ),
                            SizedBox(width: 17),
                            CustomTextFormField(
                              label: "Mq calpestabili",
                              controller: txtconWalkableIns,
                            ),
                            SizedBox(width: 17),
                            CustomTextFormField(
                              label: "Mq infissi",
                              controller: txtconFixtureIns,
                            ),
                            SizedBox(width: 17),
                            Expanded(
                              flex: 1,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  NarSelectBoxWidget(
                                    label: "Qualità della zona",
                                    options: qualityAreaList,
                                    controller: txtconQualityOfTheAreaIns,
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: 17),
                            Expanded(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(top: 10),
                                    child: BaseNewarcButton(
                                      onPressed: () {
                                        calculateRisFull();
                                      },
                                      buttonText: 'Calcola',
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),

                        
                        SizedBox(height: 20),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Container(
                                  width: 400,
                                  decoration: BoxDecoration( 
                                    color: Color(0xFFF8F8F8),
                                    borderRadius: BorderRadius.circular(10)
                                  ),
                                  margin: EdgeInsets.only(top: 10),
                                  padding: EdgeInsets.symmetric( horizontal: 20, vertical: 15),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Light',
                                        fontSize: 17,
                                        fontWeight: 'bold',
                                      ),
                                      SizedBox(height: 10),
                                      Row(
                                        children: [
                                          CustomTextFormField(
                                            label: "Ristrutturazione Light",
                                            controller: txtconLighRenoIns,
                                            readOnly: true,
                                            suffixIcon: Icon(
                                              Icons.euro,
                                              color: Color.fromRGBO(
                                                  105, 105, 105, 1),
                                              size: 13,
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 10),
                                      Row(
                                        children: [
                                          CustomTextFormField(
                                            label: "Materiali Standard",
                                            controller:
                                                txtconMaterialStandardLight,
                                            readOnly: true,
                                            suffixIcon: Icon(
                                              Icons.euro,
                                              color: Color.fromRGBO(
                                                  105, 105, 105, 1),
                                              size: 13,
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 10),
                                      Row(
                                        children: [
                                          CustomTextFormField(
                                            label: "Materiali Premium",
                                            controller:
                                                txtconMaterialPremiumLight,
                                            readOnly: true,
                                            suffixIcon: Icon(
                                              Icons.euro,
                                              color: Color.fromRGBO(
                                                  105, 105, 105, 1),
                                              size: 13,
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 80),
                                    ],
                                  )),
                            ),
                            SizedBox(width: 10),
                            Expanded(
                              child: Container(
                                  width: 400,
                                  decoration: BoxDecoration(
                                    color: Color(0xFFF8F8F8),
                                    borderRadius: BorderRadius.circular(10)
                                  ),
                                  margin: EdgeInsets.only(top: 10, right: 10),
                                  padding: EdgeInsets.symmetric( horizontal: 20, vertical: 15),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Full',
                                        fontSize: 17,
                                        fontWeight: 'bold',
                                      ),
                                      SizedBox(height: 10),
                                      Row(
                                        children: [
                                          CustomTextFormField(
                                            label: "Ristrutturazione Full",
                                            controller: txtconFullRenoIns,
                                            readOnly: true,
                                            suffixIcon: Icon(
                                              Icons.euro,
                                              color: Color.fromRGBO(
                                                  105, 105, 105, 1),
                                              size: 13,
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 10),
                                      Row(
                                        children: [
                                          
                                          CustomTextFormField(
                                            label: "Materiali Standard",
                                            controller: txtconMaterialStandardFull,
                                            readOnly: true,
                                            suffixIcon: Icon(
                                              Icons.euro,
                                              color: Color.fromRGBO(
                                                  105, 105, 105, 1),
                                              size: 13,
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 10),
                                      Row(
                                        children: [
                                          
                                          CustomTextFormField(
                                            label: "Materiali Premium",
                                            controller:
                                                txtconMaterialPremiumFull,
                                            readOnly: true,
                                            suffixIcon: Icon(
                                              Icons.euro,
                                              color: Color.fromRGBO(
                                                  105, 105, 105, 1),
                                              size: 13,
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 10),
                                      Row(
                                        children: [
                                          CustomTextFormField(
                                            label: "Infissi",
                                            controller: txtconFixtureFull,
                                            readOnly: true,
                                            suffixIcon: Icon(
                                              Icons.euro,
                                              color: Color.fromRGBO(
                                                  105, 105, 105, 1),
                                              size: 13,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  )),
                            ),
                            Expanded(
                              flex: 2,
                              child: Container(),
                            ),
                          ],
                        ),
                      ],
                    )),
              SizedBox(height: 15),
              
              if( !isImmaginaForProfessionalsValid ) Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      NarFormLabelWidget(
                          label: "Media",
                          fontSize: 25,
                          fontWeight: 'bold',
                          textColor: Colors.black,
                          textAlign: TextAlign.center),
                    ],
                  ),
                  SizedBox(height: 15),
                  collapsableSection(
                      // 'Progetto',
                      '',
                      5,
                      Column(
                        children: [
                          SizedBox(
                            height: 10,
                          ),
                          Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(13),
                              border: Border.all(
                                width: 1,
                                color: hasErrorRenderDay
                                    ? Color.fromARGB(255, 234, 28, 28)
                                    : Color(0xffE1E1E1),
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    NarFormLabelWidget(
                                      label: 'Render - Giorno',
                                      fontSize: 16,
                                      fontWeight: '700',
                                      textColor: Color.fromRGBO(0, 0, 0, 1),
                                    ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                    /*hasImageError == true
                                  ? Column(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        NarFormLabelWidget(
                                          label: 'Carica almeno un\'immagine',
                                          fontWeight: '600',
                                          textColor: Colors.red,
                                        ),
                                        SizedBox(
                                          height: 10,
                                        )
                                      ],
                                    )
                                  : SizedBox(
                                      height: 0,
                                    ),*/
                  
                                    Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          Expanded(
                                            flex: 9,
                                            child: NarFilePickerWidget(
                                                allowMultiple: true,
                                                filesToDisplayInList: 0,
                                                removeButton: false,
                                                isDownloadable: false,
                                                removeButtonText: 'Elimina',
                                                removeButtonTextColor:
                                                    Color(0xff797979),
                                                uploadButtonPosition: 'back',
                                                showMoreButtonText: '+ espandi',
                                                actionButtonPosition: 'bottom',
                                                displayFormat: 'inline-widget',
                                                containerWidth: 145,
                                                containerHeight: 145,
                                                containerBorderRadius: 8,
                                                borderRadius: 7,
                                                fontSize: 11,
                                                fontWeight: '600',
                                                text: 'Carica',
                                                borderSideColor:
                                                    Theme.of(context).primaryColor,
                                                hoverColor: Color.fromRGBO(
                                                    133, 133, 133, 1),
                                                isResizeImage: true,
                                                resizeImageSize: [335, 195],
                                                allFiles: renderImageDayList
                                                    .where((e) =>
                                                        e.renderImageFirst
                                                                ?.filename !=
                                                            null &&
                                                        (e
                                                                .renderImageFirst
                                                                ?.filename
                                                                ?.isNotEmpty ??
                                                            false))
                                                    .map((e) {
                                                  String? filename =
                                                      e.renderImageFirst!.filename;
                                                  int lastDot =
                                                      filename!.lastIndexOf('.');
                                                  String nameWithoutExtension =
                                                      filename.substring(
                                                          0, lastDot);
                                                  String extension =
                                                      filename.substring(lastDot);
                                                  String newFilename =
                                                      '${nameWithoutExtension}_thumbnail$extension';
                                                  return {
                                                    'filename': newFilename,
                                                    'label':
                                                        e.renderImageFirst!.room,
                                                  };
                                                }).toList(),
                                                pageContext: context,
                                                storageDirectory:
                                                    "newarcHomes/${widget.property!.firebaseId}/day-render/",
                                                removeExistingOnChange: false,
                                                progressMessage: [''],
                                                notAccent: true,
                                                showTitle: false,
                                                onUploadCompleted: () {
                                                  setState(() {});
                                                },
                                                splashColor: Colors.black),
                                          ),
                                          Expanded(
                                            flex: 2,
                                            child: NarBlockFilePickerWidget(
                                              selectionType: 'none',
                                              dialogTitle: 'Render - Giorno',
                                              onUploadCompleted: () {
                                                if (mounted) {
                                                  setState(() {});
                                                }
                                                // setState(() {});
                                              },
                                              containerHeight: 145,
                                              containerWidth: 145,
                                              containerBorderRadius: 7,
                                              renderImageList: renderImageDayList,
                                              storageLocation:
                                                  "newarcHomes/${widget.property!.firebaseId}/day-render/",
                                              gallery: [],
                                              // buttonColor: Color.fromRGBO(133, 133, 133, 1),
                                              buttonTextColor: Colors.black,
                                              isResizeImage: true,
                                              resizeImageSize: [
                                                [640, 195, 'thumbnail'],
                                                [1050, 700, 'mobile_thumbnail']
                                              ],
                                              buttonNotAccent: true,
                                              buttonWidth: 125,
                                              buttonColor: Color(0xffE5E5E5),
                                            ),
                                          ),
                                        ])
                                  ]),
                            ),
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(13),
                              border: Border.all(
                                width: 1,
                                color: Color(0xffE1E1E1),
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        NarFormLabelWidget(
                                          label: 'Render - Notte',
                                          fontSize: 16,
                                          fontWeight: '700',
                                          textColor: Color.fromRGBO(0, 0, 0, 1),
                                        ),
                                      ],
                                    ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                    hasImageError == true
                                        ? Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              NarFormLabelWidget(
                                                label: 'Carica almeno un\'immagine',
                                                fontWeight: '600',
                                                textColor: Colors.red,
                                              ),
                                              SizedBox(
                                                height: 10,
                                              )
                                            ],
                                          )
                                        : SizedBox(
                                            height: 0,
                                          ),
                                    Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          Expanded(
                                            flex: 9,
                                            child: NarFilePickerWidget(
                                              allowMultiple: true,
                                              filesToDisplayInList: 0,
                                              removeButton: false,
                                              isDownloadable: false,
                                              removeButtonText: 'Elimina',
                                              removeButtonTextColor:
                                                  Color(0xff797979),
                                              uploadButtonPosition: 'back',
                                              showMoreButtonText: '+ espandi',
                                              actionButtonPosition: 'bottom',
                                              displayFormat: 'inline-widget',
                                              containerWidth: 145,
                                              containerHeight: 145,
                                              containerBorderRadius: 8,
                                              borderRadius: 7,
                                              fontSize: 11,
                                              fontWeight: '600',
                                              text: 'Carica Progetto',
                                              borderSideColor:
                                                  Theme.of(context).primaryColor,
                                              hoverColor:
                                                  Color.fromRGBO(133, 133, 133, 1),
                                              allFiles: renderImageNightList
                                                  .where((e) =>
                                                      e.renderImageSecond
                                                              ?.filename !=
                                                          null &&
                                                      (e.renderImageSecond?.filename
                                                              ?.isNotEmpty ??
                                                          false))
                                                  .map((e) {
                                                String? filename =
                                                    e.renderImageSecond!.filename;
                                                int lastDot =
                                                    filename!.lastIndexOf('.');
                                                String nameWithoutExtension =
                                                    filename.substring(0, lastDot);
                                                String extension =
                                                    filename.substring(lastDot);
                                                String newFilename =
                                                    '${nameWithoutExtension}_thumbnail$extension';
                                                return {
                                                  'filename': newFilename,
                                                  'label': e.renderImageFirst!.room,
                                                };
                                              }).toList(),
                                              pageContext: context,
                                              storageDirectory:
                                                  "newarcHomes/${widget.property!.firebaseId}/night-render/",
                                              removeExistingOnChange: false,
                                              progressMessage: [''],
                                              notAccent: true,
                                              showTitle: false,
                                              onUploadCompleted: () {
                                                if (mounted) {
                                                  setState(() {});
                                                }
                                              },
                                            ),
                                          ),
                                          Expanded(
                                            flex: 2,
                                            child: NarBlockFilePickerWidget(
                                              dialogTitle: 'Render - Notte',
                                              selectionType: 'dayNight',
                                              onUploadCompleted: () {
                                                if (mounted) {
                                                  setState(() {});
                                                }
                                              },
                                              containerHeight: 145,
                                              containerWidth: 145,
                                              containerBorderRadius: 7,
                                              renderImageList: renderImageNightList,
                                              isResizeImage: true,
                                              resizeImageSize: [
                                                [640, 195, 'thumbnail'],
                                                [1050, 700, 'mobile_thumbnail']
                                              ],
                                              storageLocation:
                                                  "newarcHomes/${widget.property!.firebaseId}/night-render/",
                                              gallery: renderImageDayList
                                                  .map((e) => e.renderImageFirst!)
                                                  .toList(),
                                              buttonTextColor: Colors.black,
                                              buttonNotAccent: true,
                                              buttonWidth: 125,
                                              buttonColor: Color(0xffE5E5E5),
                                            ),
                                          ),
                                        ])
                                  ]),
                            ),
                          ),
                          if (projectType != 'Newarc' && widget.property!.projectType != 'cut' && widget.property!.projectType != 'Immagina for Professionals' )
                            Container(
                              width: double.infinity,
                              margin: EdgeInsets.only(top: 10),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(13),
                                border: Border.all(
                                  width: 1,
                                  color: hasErrorRenderBeforeAfter
                                      ? Color.fromARGB(255, 129, 115, 115)
                                      : Color(0xffE1E1E1),
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(20.0),
                                child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          NarFormLabelWidget(
                                            label: 'Stanze Attuali',
                                            fontSize: 16,
                                            fontWeight: '700',
                                            textColor: Color.fromRGBO(0, 0, 0, 1),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 15,
                                      ),
                                      Row(
                                          mainAxisAlignment: MainAxisAlignment.end,
                                          children: [
                                            Expanded(
                                              flex: 9,
                                              child: NarFilePickerWidget(
                                                allowMultiple: true,
                                                filesToDisplayInList: 0,
                                                removeButton: false,
                                                isDownloadable: false,
                                                removeButtonText: 'Elimina',
                                                removeButtonTextColor:
                                                    Color(0xff797979),
                                                uploadButtonPosition: 'back',
                                                showMoreButtonText: '+ espandi',
                                                actionButtonPosition: 'bottom',
                                                displayFormat: 'inline-widget',
                                                containerWidth: 145,
                                                containerHeight: 145,
                                                containerBorderRadius: 8,
                                                borderRadius: 7,
                                                fontSize: 11,
                                                fontWeight: '600',
                                                text: 'Carica Progetto',
                                                borderSideColor:
                                                    Theme.of(context).primaryColor,
                                                hoverColor: Color.fromRGBO(
                                                    133, 133, 133, 1),
                                                allFiles: renderImageBeforeAfterList
                                                    .where((e) =>
                                                        e.renderImageFirst
                                                                ?.filename !=
                                                            null &&
                                                        (e
                                                                .renderImageFirst
                                                                ?.filename
                                                                ?.isNotEmpty ??
                                                            false))
                                                    .map((e) {
                                                  String filename =
                                                      e.renderImageFirst!.filename!;
                                                  int lastDot =
                                                      filename.lastIndexOf('.');
                                                  String nameWithoutExtension =
                                                      filename.substring(
                                                          0, lastDot);
                                                  String extension =
                                                      filename.substring(lastDot);
                                                  String newFilename =
                                                      '${nameWithoutExtension}_thumbnail$extension';
                  
                                                  return {
                                                    'filename': newFilename,
                                                    'label':
                                                        e.renderImageFirst!.room,
                                                  };
                                                }).toList(),
                                                pageContext: context,
                                                storageDirectory:
                                                    "newarcHomes/${widget.property!.firebaseId}/before-after-render/",
                                                removeExistingOnChange: false,
                                                progressMessage: [''],
                                                notAccent: true,
                                                showTitle: false,
                                                onUploadCompleted: () {
                                                  setState(() {});
                                                },
                                              ),
                                            ),
                                            Expanded(
                                              flex: 2,
                                              child: NarBlockFilePickerWidget(
                                                dialogTitle: 'Stanze Attuali',
                                                selectionType: 'beforeAfter',
                                                onUploadCompleted: () {
                                                  if (mounted) {
                                                    setState(() {});
                                                  }
                                                },
                                                containerHeight: 145,
                                                containerWidth: 145,
                                                containerBorderRadius: 7,
                                                renderImageList: renderImageBeforeAfterList,
                                                storageLocation: "newarcHomes/${widget.property!.firebaseId}/before-after-render/",
                                                gallery: renderImageDayList
                                                    .map((e) => e.renderImageFirst!)
                                                    .toList(),
                                                buttonTextColor: Colors.black,
                                                buttonNotAccent: true,
                                                isResizeImage: true,
                                                resizeImageSize: [
                                                  [640, 195, 'thumbnail'],
                                                  [1050, 700, 'mobile_thumbnail']
                                                ],
                                                buttonWidth: 125,
                                                buttonColor: Color(0xffE5E5E5),
                                              ),
                                            ),
                                          ]
                                        )
                                    ]),
                              ),
                            ),
                          SizedBox(
                            height: 10,
                          ),
                          Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(13),
                              border: Border.all(
                                width: 1,
                                color: hasErrorFloorPlan
                                    ? Color.fromARGB(255, 234, 28, 28)
                                    : Color(0xffE1E1E1),
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    NarFormLabelWidget(
                                      label: 'Planimetria Progetto - PNG o JPG',
                                      fontSize: 16,
                                      fontWeight: '700',
                                      textColor: Color.fromRGBO(0, 0, 0, 1),
                                    ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                    Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          Expanded(
                                            flex: 9,
                                            child: NarFilePickerWidget(
                                              allowMultiple: true,
                                              filesToDisplayInList: 0,
                                              removeButton: true,
                                              isDownloadable: false,
                                              removeButtonText: 'Elimina',
                                              removeButtonTextColor:
                                                  Color(0xff797979),
                                              uploadButtonPosition: 'back',
                                              showMoreButtonText: '+ espandi',
                                              actionButtonPosition: 'bottom',
                                              displayFormat: 'inline-widget',
                                              containerWidth: 65,
                                              containerHeight: 65,
                                              containerBorderRadius: 8,
                                              borderRadius: 7,
                                              fontSize: 11,
                                              fontWeight: '600',
                                              text: 'Carica Progetto',
                                              borderSideColor:
                                                  Theme.of(context).primaryColor,
                                              hoverColor:
                                                  Color.fromRGBO(133, 133, 133, 1),
                                              allFiles: projectPlanImages,
                                              pageContext: context,
                                              storageDirectory:
                                                  "newarcHomes/${widget.property!.firebaseId}/",
                                              removeExistingOnChange: false,
                                              progressMessage: [''],
                                              notAccent: true,
                                              showTitle: false,
                                              onUploadCompleted: () {
                                                validateBrochureData();
                                                setState(() {});
                                              },
                                            ),
                                          ),
                                          Expanded(
                                            flex: 2,
                                            child: NarFilePickerWidget(
                                              allowMultiple: true,
                                              filesToDisplayInList: 0,
                                              removeButton: true,
                                              isDownloadable: false,
                                              removeButtonText: 'Elimina',
                                              removeButtonTextColor:
                                                  Color(0xff797979),
                                              uploadButtonPosition: 'back',
                                              showMoreButtonText: '+ espandi',
                                              actionButtonPosition: 'bottom',
                                              displayFormat: 'inline-button',
                                              containerWidth: 65,
                                              containerHeight: 65,
                                              containerBorderRadius: 8,
                                              borderRadius: 7,
                                              fontSize: 11,
                                              fontWeight: '600',
                                              text: 'Carica',
                                              borderSideColor:
                                                  Theme.of(context).primaryColor,
                                              hoverColor:
                                                  Color.fromRGBO(133, 133, 133, 1),
                                              allFiles: projectPlanImages,
                                              pageContext: context,
                                              storageDirectory:
                                                  "newarcHomes/${widget.property!.firebaseId}/",
                                              removeExistingOnChange: false,
                                              progressMessage: [''],
                                              notAccent: true,
                                              splashColor: Color(0xffE5E5E5),
                                              height: 35,
                                              buttonWidth: 125,
                                              buttonTextColor: Colors.black,
                                              onUploadCompleted: () {
                                                validateBrochureData();
                                                setState(() {});
                                              },
                                            ),
                                          ),
                                        ])
                                  ]),
                            ),
                          ),
                          if (projectType != 'Newarc' && widget.property!.projectType != 'cut' && widget.property!.projectType != 'Immagina for Professionals')
                            Container(
                              width: double.infinity,
                              margin: EdgeInsets.only(top: 10),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(13),
                                border: Border.all(
                                  width: 1,
                                  color: hasErrorCurrentFloorPlan
                                      ? Color.fromARGB(255, 234, 28, 28)
                                      : Color(0xffE1E1E1),
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(20.0),
                                child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Planimetria attuale - PNG o JPG',
                                        fontSize: 16,
                                        fontWeight: '700',
                                        textColor: Color.fromRGBO(0, 0, 0, 1),
                                      ),
                                      SizedBox(
                                        height: 15,
                                      ),
                                      Row(
                                          mainAxisAlignment: MainAxisAlignment.end,
                                          children: [
                                            Expanded(
                                              flex: 9,
                                              child: NarFilePickerWidget(
                                                allowMultiple: true,
                                                filesToDisplayInList: 0,
                                                removeButton: true,
                                                isDownloadable: false,
                                                removeButtonText: 'Elimina',
                                                removeButtonTextColor:
                                                    Color(0xff797979),
                                                uploadButtonPosition: 'back',
                                                showMoreButtonText: '+ espandi',
                                                actionButtonPosition: 'bottom',
                                                displayFormat: 'inline-widget',
                                                containerWidth: 65,
                                                containerHeight: 65,
                                                containerBorderRadius: 8,
                                                borderRadius: 7,
                                                fontSize: 11,
                                                fontWeight: '600',
                                                text: 'Carica Progetto',
                                                borderSideColor:
                                                    Theme.of(context).primaryColor,
                                                hoverColor: Color.fromRGBO(
                                                    133, 133, 133, 1),
                                                allFiles: currentPlan,
                                                pageContext: context,
                                                // storageDirectory: "projects/${widget.project!.id}/ads/current-plan/",
                                                storageDirectory:
                                                    "newarcHomes/${widget.property!.firebaseId}/current-plan/",
                                                removeExistingOnChange: false,
                                                progressMessage: [''],
                                                notAccent: true,
                                                showTitle: false,
                                                onUploadCompleted: () {
                                                  validateBrochureData();
                                                  setState(() {});
                                                },
                                              ),
                                            ),
                                            Expanded(
                                              flex: 2,
                                              child: NarFilePickerWidget(
                                                allowMultiple: true,
                                                filesToDisplayInList: 0,
                                                removeButton: true,
                                                isDownloadable: false,
                                                removeButtonText: 'Elimina',
                                                removeButtonTextColor:
                                                    Color(0xff797979),
                                                uploadButtonPosition: 'back',
                                                showMoreButtonText: '+ espandi',
                                                actionButtonPosition: 'bottom',
                                                displayFormat: 'inline-button',
                                                containerWidth: 65,
                                                containerHeight: 65,
                                                containerBorderRadius: 8,
                                                borderRadius: 7,
                                                fontSize: 11,
                                                fontWeight: '600',
                                                text: 'Carica',
                                                borderSideColor:
                                                    Theme.of(context).primaryColor,
                                                hoverColor: Color.fromRGBO(
                                                    133, 133, 133, 1),
                                                allFiles: currentPlan,
                                                pageContext: context,
                                                storageDirectory:
                                                    "newarcHomes/${widget.property!.firebaseId}/current-plan/",
                                                removeExistingOnChange: false,
                                                progressMessage: [''],
                                                notAccent: true,
                                                splashColor: Color(0xffE5E5E5),
                                                height: 35,
                                                buttonWidth: 125,
                                                buttonTextColor: Colors.black,
                                                onUploadCompleted: () {
                                                  validateBrochureData();
                                                  setState(() {});
                                                },
                                              ),
                                            ),
                                          ])
                                    ]),
                              ),
                            ),
                          SizedBox(
                            height: 10,
                          ),
                          Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(13),
                              border: Border.all(
                                width: 1,
                                color: Color(0xffE1E1E1),
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        NarFormLabelWidget(
                                          label: 'Virtual tour progetto',
                                          fontSize: 16,
                                          fontWeight: '700',
                                          textColor: Color.fromRGBO(0, 0, 0, 1),
                                        ),
                                        /*Transform.scale(
                                        scale: 0.9,
                                        child: Switch(
                                          // This bool value toggles the switch.
                                          value: activateVirtualTourProject,
                                          activeColor: Theme.of(context).primaryColor,
                                          onChanged: (bool value) async {
                  
                                            setState(() {
                                              activateVirtualTourProject = value;
                                            });
                                          },
                                        ),
                                      ),*/
                                      ],
                                    ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                    Column(
                                      children: [
                                        Row(
                                          children: [
                                            CustomTextFormField(
                                              label: 'Link del virtual tour',
                                              hintText: "",
                                              controller: txtconVirtualTour,
                                              minLines: 1,
                                              onTap: () async {},
                                              // prefixIcon: Icon(Icons.keyboard_hide),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 15,
                                        ),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            NarFormLabelWidget(
                                              label: 'QR code',
                                              fontSize: 16,
                                              fontWeight: '700',
                                              textColor: Color.fromRGBO(0, 0, 0, 1),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 5,
                                        ),
                                        Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.end,
                                            children: [
                                              txtconVirtualTour != null
                                                  ? Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment.start,
                                                      children: [
                                                          QrImageView(
                                                            data: txtconVirtualTour!
                                                                .text,
                                                            version:
                                                                QrVersions.auto,
                                                            size: 160.0,
                                                          ),
                                                        ])
                                                  : NarFormLabelWidget(
                                                      label:
                                                          'Carica il link del virtual tour per generare il QR code',
                                                      fontSize: 13,
                                                      fontWeight: '600',
                                                      textColor: Colors.black,
                                                    ),
                                            ])
                                      ],
                                    ),
                                  ]),
                            ),
                          ),
                          if (projectType != 'Newarc' && widget.property!.projectType != 'cut' && widget.property!.projectType != 'Immagina for Professionals' )
                            Container(
                              width: double.infinity,
                              margin: EdgeInsets.only(top: 10),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(13),
                                border: Border.all(
                                  width: 1,
                                  color: Color(0xffE1E1E1),
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(20.0),
                                child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          NarFormLabelWidget(
                                            label: 'Virtual tour attuale',
                                            fontSize: 16,
                                            fontWeight: '700',
                                            textColor: Color.fromRGBO(0, 0, 0, 1),
                                          ),
                                          /*Transform.scale(
                                        scale: 0.9,
                                        child: Switch(
                                          // This bool value toggles the switch.
                                          value: activateVirtualTourCurrent,
                                          activeColor: Theme.of(context).primaryColor,
                                          onChanged: (bool value) async {
                  
                                            setState(() {
                                              activateVirtualTourCurrent = value;
                                            });
                                          },
                                        ),
                                      ),*/
                                        ],
                                      ),
                                      SizedBox(
                                        height: 15,
                                      ),
                                      Row(
                                        children: [
                                          CustomTextFormField(
                                            label: 'Link del virtual tour',
                                            hintText: "",
                                            controller: txtconCurrentVirtualTour,
                                            minLines: 1,
                                            onTap: () async {},
                                            // prefixIcon: Icon(Icons.keyboard_hide),
                                          ),
                                        ],
                                      ),
                                    ]),
                              ),
                            ),
                          SizedBox(
                            height: 10,
                          ),
                          Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(13),
                              border: Border.all(
                                width: 1,
                                color: Color(0xffE1E1E1),
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        NarFormLabelWidget(
                                          label: 'Video render',
                                          fontSize: 16,
                                          fontWeight: '700',
                                          textColor: Color.fromRGBO(0, 0, 0, 1),
                                        ),
                                        
                                      ],
                                    ),
                                    SizedBox(
                                      height: 15,
                                    ),
                                    hasImageError == true
                                        ? Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              NarFormLabelWidget(
                                                label: 'Carica almeno un\'immagine',
                                                fontWeight: '600',
                                                textColor: Colors.red,
                                              ),
                                              SizedBox(
                                                height: 10,
                                              )
                                            ],
                                          )
                                        : SizedBox(
                                            height: 0,
                                          ),
                                    Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          Expanded(
                                            flex: 9,
                                            child: NarFilePickerWidget(
                                              allowMultiple: true,
                                              filesToDisplayInList: 0,
                                              removeButton: true,
                                              isDownloadable: false,
                                              removeButtonText: 'Elimina',
                                              removeButtonTextColor:
                                                  Color(0xff797979),
                                              uploadButtonPosition: 'back',
                                              showMoreButtonText: '+ espandi',
                                              actionButtonPosition: 'bottom',
                                              displayFormat: 'inline-widget',
                                              containerWidth: 65,
                                              containerHeight: 65,
                                              containerBorderRadius: 8,
                                              borderRadius: 7,
                                              fontSize: 11,
                                              fontWeight: '600',
                                              text: 'Carica Progetto',
                                              borderSideColor:
                                                  Theme.of(context).primaryColor,
                                              hoverColor:
                                                  Color.fromRGBO(133, 133, 133, 1),
                                              allFiles: videoRenderPaths,
                                              pageContext: context,
                                              // storageDirectory: "projects/${widget.project!.id}/ads/video-render/",
                                              storageDirectory:
                                                  "newarcHomes/${widget.property!.firebaseId}/video-render/",
                                              removeExistingOnChange: false,
                                              progressMessage: [''],
                                              notAccent: true,
                                              showTitle: false,
                                              onUploadCompleted: () {
                                                setState(() {});
                                              },
                                            ),
                                          ),
                                          Expanded(
                                            flex: 2,
                                            child: NarFilePickerWidget(
                                              allowMultiple: true,
                                              filesToDisplayInList: 0,
                                              removeButton: true,
                                              isDownloadable: false,
                                              removeButtonText: 'Elimina',
                                              removeButtonTextColor:
                                                  Color(0xff797979),
                                              uploadButtonPosition: 'back',
                                              showMoreButtonText: '+ espandi',
                                              actionButtonPosition: 'bottom',
                                              displayFormat: 'inline-button',
                                              containerWidth: 65,
                                              containerHeight: 65,
                                              containerBorderRadius: 8,
                                              borderRadius: 7,
                                              fontSize: 11,
                                              fontWeight: '600',
                                              text: 'Carica',
                                              borderSideColor:
                                                  Theme.of(context).primaryColor,
                                              hoverColor:
                                                  Color.fromRGBO(133, 133, 133, 1),
                                              allFiles: videoRenderPaths,
                                              pageContext: context,
                                              storageDirectory:
                                                  "newarcHomes/${widget.property!.firebaseId}/video-render/",
                                              removeExistingOnChange: false,
                                              progressMessage: [''],
                                              notAccent: true,
                                              splashColor: Color(0xffE5E5E5),
                                              height: 35,
                                              buttonWidth: 125,
                                              buttonTextColor: Colors.black,
                                              onUploadCompleted: () {
                                                setState(() {});
                                              },
                                            ),
                                          ),
                                        ])
                                  ]),
                            ),
                          ),
                        ],
                      )),
                ],
              ),

              if( isImmaginaForProfessionalsValid ) collapsableSection(
                // 'Progetto',
                '',
                5,
                Column(
                  children: [
                    SizedBox(
                      height: 10,
                    ),
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(13),
                        border: Border.all(
                          width: 1,
                          color: hasErrorRenderDay
                              ? Color.fromARGB(255, 234, 28, 28)
                              : Color(0xffE1E1E1),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              NarFormLabelWidget(
                                label: 'Render Generali',
                                fontSize: 16,
                                fontWeight: '700',
                                textColor: Color.fromRGBO(0, 0, 0, 1),
                              ),
                              SizedBox(
                                height: 15,
                              ),
                              

                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Expanded(
                                    flex: 9,
                                    child: NarFilePickerWidget(
                                      allowMultiple: true,
                                      filesToDisplayInList: 0,
                                      removeButton: false,
                                      isDownloadable: false,
                                      removeButtonText: 'Elimina',
                                      removeButtonTextColor: Color(0xff797979),
                                      uploadButtonPosition: 'back',
                                      showMoreButtonText: '+ espandi',
                                      actionButtonPosition: 'bottom',
                                      displayFormat: 'inline-widget',
                                      containerWidth: 145,
                                      containerHeight: 145,
                                      containerBorderRadius: 8,
                                      borderRadius: 7,
                                      fontSize: 11,
                                      fontWeight: '600',
                                      text: 'Carica Progetto',
                                      borderSideColor: Theme.of(context).primaryColor,
                                      hoverColor: Color.fromRGBO( 133, 133, 133, 1),
                                      allFiles: buildingPicturesImages
                                          .where((e) => 
                                            e.renderImageFirst?.filename != null 
                                            && (e.renderImageFirst?.filename?.isNotEmpty??false)
                                          ).map((e) {
                                        
                                            String filename = e.renderImageFirst!.filename!;
                                            int lastDot = filename.lastIndexOf('.');
                                            String nameWithoutExtension = filename.substring( 0, lastDot);
                                            String extension = filename.substring(lastDot);
                                            String newFilename = '${nameWithoutExtension}_thumbnail$extension';
        
                                          return {
                                            'filename': newFilename,
                                            'label': e.renderImageFirst!.room,
                                          };
                                      }).toList(),
                                      pageContext: context,
                                      storageDirectory: "newarcHomes/${widget.property!.firebaseId}/building-pictures/",
                                      removeExistingOnChange: false,
                                      progressMessage: [''],
                                      notAccent: true,
                                      showTitle: false,
                                      onUploadCompleted: () {
                                        setState(() {});
                                      },
                                    ),
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: NarBlockFilePickerWidget(
                                      dialogTitle: 'Render Generali',
                                      selectionType: 'none',
                                      onUploadCompleted: () {
                                        if (mounted) {
                                          setState(() {});
                                        }
                                      },
                                      containerHeight: 145,
                                      containerWidth: 145,
                                      containerBorderRadius: 7,
                                      renderImageList: buildingPicturesImages,
                                      storageLocation: "newarcHomes/${widget.property!.firebaseId}/building-pictures/",
                                      gallery: [],
                                      buttonTextColor: Colors.black,
                                      buttonNotAccent: true,
                                      isResizeImage: true,
                                      resizeImageSize: [
                                        [640, 195, 'thumbnail'],
                                        [1050, 700, 'mobile_thumbnail']
                                      ],
                                      buttonWidth: 125,
                                      buttonColor: Color(0xffE5E5E5),
                                    ),
                                  ),
                                ]
                              )
            
                              
                            ]),
                      ),
                    ),
                    
                    
                  ],
                )
              ),


              SizedBox(height: 15),

              if ( isImmaginaForProfessionalsValid )
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        
                        children: [
                          NarFormLabelWidget(
                            label: "Tagli",
                            fontSize: 25,
                            fontWeight: 'bold',
                            textColor: Colors.black,
                            textAlign: TextAlign.center
                          ),
                          SizedBox(height: 15),
                          if( widget.property!.children!.length > 0 ) FutureBuilder<List<Property>>(
                            future: fetchCuts(),
                            builder: (context, snapshot) {
                              if (snapshot.hasData) {

                                var cutIndex = 1;
                                readyForAgencyCheckList.clear();
                                
                                return Column(
                                  
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  mainAxisSize: MainAxisSize.max, 
                                  children: snapshot.data!.map((Property cut) {

                                    if( isCutHasValidBrochureData(cut) == false ) {
                                      isReadyForAgency = false;
                                      readyForAgencyCheckList.add({
                                        'label': 'Taglio ${cutIndex}',
                                        'status': false
                                      });
                                    } else {
                                      readyForAgencyCheckList.add({
                                        'label': 'Taglio ${cutIndex} ',
                                        'status': true
                                      });
                                    }
                                    cutIndex++;

                                    String rooms = '';
                                    switch (int.tryParse(cut.locals!.toString())!) {
                                      case 1:
                                        rooms = 'Monolocale';
                                        break;
                                      case 2:
                                        rooms = 'Bilocale';
                                        break;
                                      case 3:
                                        rooms = 'Trilocale';
                                        break;
                                      case 4:
                                        rooms = 'Quadrilocale';
                                        break;
                                      default:
                                        rooms = 'Plurilocale';
                                        break;
                                    }
                                    return Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(15),
                                        color: Color(0xffF4F4F4),
                                      ),
                                      margin: EdgeInsets.only(bottom: 10),
                                      padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          Expanded(
                                            child: Column(
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                                                      
                                              children: [
                                                NarFormLabelWidget(
                                                  label: cut.code,
                                                  fontSize: 13,
                                                  fontWeight: '600',
                                                  textColor: Color(0xff808080),
                                                ),
                                                SizedBox(height: 5),
                                                NarFormLabelWidget(
                                                  label: rooms +' in '+ cut.addressInfo!.toShortAddress(),
                                                  fontSize: 17,
                                                  fontWeight: 'bold',
                                                  textColor: Colors.black,
                                                ),
                                                SizedBox(height: 5),
                                                StatusWidget(
                                                  status: isCutHasValidBrochureData(cut) ? "Completo" : "Non completo",
                                                  statusColor: isCutHasValidBrochureData(cut) ? Color(0xff39C14F) : Color(0xffDD0000),
                                                )
                                              ],
                                            )
                                          ),
                                          Expanded(
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              
                                              children: [
                                                Container(
                                                  height: 30,
                                                  decoration: BoxDecoration(
                                                    borderRadius: BorderRadius.circular(7),
                                                    color: Colors.white,
                                                  ),
                                                  margin: EdgeInsets.only(right: 10),
                                                  padding: EdgeInsets.only(right: 15, left: 7),
                                                  child: Row(
                                                    mainAxisAlignment: MainAxisAlignment.center,
                                                    children: [
                                                      SvgPicture.asset(
                                                        'assets/icons/square-meters.svg',
                                                        color: Color(0xff929292),
                                                        width: 14,
                                                      ),
                                                      SizedBox(width: 10),
                                                      NarFormLabelWidget(
                                                        label: cut.mq.toString() + ' mq',
                                                        fontSize: 12,
                                                        fontWeight: '600',
                                                        letterSpacing: 0.2,
                                                        textColor: Color(0xff636363),
                                                      ),
                                                    ],
                                                  ),
                                                                      
                                                ),
                                                Container(
                                                  height: 30,
                                                  decoration: BoxDecoration(
                                                    borderRadius: BorderRadius.circular(7),
                                                    color: Colors.white,
                                                  ),
                                                  margin: EdgeInsets.only(right: 10),
                                                  padding: EdgeInsets.only(right: 15, left: 7),
                                                  child: Row(
                                                    mainAxisAlignment: MainAxisAlignment.center,
                                                    children: [
                                                      SvgPicture.asset(
                                                        'assets/icons/rooms.svg',
                                                        color: Color(0xff929292),
                                                        width: 14,
                                                      ),
                                                      SizedBox(width: 10),
                                                      NarFormLabelWidget(
                                                        label: cut.locals.toString() + ' locali',
                                                        fontSize: 12,
                                                        fontWeight: '600',
                                                        letterSpacing: 0.2,
                                                        textColor: Color(0xff636363),
                                                      ),
                                                    ],
                                                  ),
                                                                      
                                                ),
                                                Container(
                                                  height: 30,
                                                  decoration: BoxDecoration(
                                                    borderRadius: BorderRadius.circular(7),
                                                    color: Colors.white,
                                                  ),
                                                  margin: EdgeInsets.only(right: 10),
                                                  padding: EdgeInsets.only(right: 15, left: 7),
                                                  child: Row(
                                                    mainAxisAlignment: MainAxisAlignment.center,
                                                    children: [
                                                      Row(
                                                        children: [
                                                          SvgPicture.asset(
                                                            'assets/icons/bathrooms.svg',
                                                            color: Color(0xff929292),
                                                            width: 14,
                                                          ),
                                                          SizedBox(width: 10),
                                                          NarFormLabelWidget(
                                                            label: cut.baths.toString() + ( double.tryParse(cut.baths!.toString() )! > 1 ? ' bagni' : ' bagno'),
                                                            fontSize: 12,
                                                            fontWeight: '600',
                                                            letterSpacing: 0.2,
                                                            textColor: Color(0xff636363),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                                      
                                                ),
                                                
                                                
                                              ],
                                            )
                                          ),

                                          Expanded(
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                                                      
                                              children: [
                                                Row(
                                                  mainAxisAlignment: MainAxisAlignment.start,
                                                  crossAxisAlignment: CrossAxisAlignment.center,
                                                  children: [
                                                    NarFormLabelWidget(
                                                      label: 'Pubblica sul sito',
                                                      fontSize: 13,
                                                      fontWeight: '600',
                                                      letterSpacing: 0.2,
                                                      textColor: Colors.black,
                                                    ),
                                                    SizedBox(width: 5),
                                                    Transform.scale(
                                                      scale: 0.9,
                                                      child: Switch(
                                                        // This bool value toggles the switch.
                                                        value: cut.isActive!,
                                                        activeColor: Theme.of(context).primaryColor,
                                                        onChanged: (bool value) async {
                                                          setState(() {
                                                            cut.isActive = value;
                                                          });

                                                          try {
                                                            await updateActiveStatus( cut.firebaseId!,  cut);
                                                          } catch (e) {
                                                            cut.isActive = !value;
                                                          }              
                                                          // setState(() {
                                                          //   activateVirtualTourProject = value;
                                                          // });
                                                        },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(height: 5),
                                                BaseNewarcButton(
                                                  onPressed: () async {
                                                    
                                                    widget.updateViewCallback!(
                                                      'active-ad-single-cut',
                                                      projectArguments: {
                                                        'property': cut,
                                                        'project': widget.project,
                                                        'wasArchived': cut.isArchived,
                                                        'isInputChangeDetected': [false]
                                                      }
                                                    );
                                                  },
                                                  buttonText: "Gestisci",
                                                  height: 35,
                                                  width: 125,
                                                ),
                                              ],
                                            )
                                          ),
                                        ],
                                      ),
                                    );
                                  }).toList(),
                                );
                              } else if (snapshot.hasError) {
                                return Container(
                                  width: 30,
                                  height: 30,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(100),
                                    color: const Color.fromARGB(255, 19, 17, 17),
                                  ),
                                );
                              }

                              return CircularProgressIndicator(
                                color: Theme.of(context).primaryColor,
                              );
                            }
                          ),
                      
                          // SizedBox(height: 15),
                          // BaseNewarcButton(
                          //   buttonText: "+ aggiungi un taglio",
                          //   height: 33,
                          //   width: 180,
                          //   onPressed: () async {
                          //     createCut();
                          //   }
                          // )
                        ],
                      ),
                    ),
                  ],
                ),
                
              
              SizedBox(height: 15),
              if (widget.property!.projectType == 'Immagina' || widget.property!.projectType == 'cut' || !isImmaginaForProfessionalsValid)
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    NarFormLabelWidget(
                        label: "Brochure",
                        fontSize: 25,
                        fontWeight: 'bold',
                        textColor: Colors.black,
                        textAlign: TextAlign.center),
                  ],
                ),
              if ( widget.property!.projectType == 'Immagina' || widget.property!.projectType == 'cut' || !isImmaginaForProfessionalsValid ) SizedBox(height: 15),
              if ( widget.property!.projectType == 'Immagina' || widget.property!.projectType == 'cut' || !isImmaginaForProfessionalsValid )
                collapsableSection(
                    'Composizione Brochure',
                    6,
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        
                        Container(
                          width: double.infinity,
                          margin: EdgeInsets.only(top: 10),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(13),
                            border: Border.all(
                              width: 1,
                              color: Color(0xffE1E1E1),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Copertina',
                                        fontSize: 16,
                                        fontWeight: '700',
                                        textColor: Color.fromRGBO(0, 0, 0, 1),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 15,
                                  ),
                                  Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Expanded(
                                          flex: 2,
                                          child: NarBlockFilePickerWidget(
                                            dialogTitle: '',
                                            selectionType: 'singleImage',
                                            displayType: 'container',
                                            containerCount: 1,
                                            roomsSelection: false,
                                            onUploadCompleted: () {
                                              if (mounted) {
                                                setState(() {});
                                              }
                                            },
                                            onImageSelection: () {
                                              validateBrochureData();
                                              setState(() {});
                                            },
                                            containerHeight: 145,
                                            containerWidth: 145,
                                            containerBorderRadius: 7,
                                            renderImageList: brochureCoverImage,
                                            storageLocation: "newarcHomes/${widget.property!.firebaseId}/day-render/",
                                            gallery: renderImageDayList
                                                .map((e) => e.renderImageFirst!)
                                                .toList(),
                                            buttonTextColor: Colors.black,
                                            buttonNotAccent: true,
                                            buttonWidth: 125,
                                            buttonColor: Color(0xffE5E5E5),
                                          ),
                                        ),
                                      ])
                                ]),
                          ),
                        ),
                        if( widget.property!.projectType != 'cut' && widget.property!.projectType != 'Immagina for Professionals' ) Container(
                          width: double.infinity,
                          margin: EdgeInsets.only(top: 10),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(13),
                            border: Border.all(
                              width: 1,
                              color: Color(0xffE1E1E1),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Seleziona 6 foto attuali',
                                        fontSize: 16,
                                        fontWeight: '700',
                                        textColor: Color.fromRGBO(0, 0, 0, 1),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 15,
                                  ),
                                  Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Expanded(
                                          flex: 2,
                                          child: NarBlockFilePickerWidget(
                                            dialogTitle: '',
                                            selectionType: 'singleImage',
                                            displayType: 'container',
                                            containerCount: 6,
                                            roomsSelection: true,
                                            onUploadCompleted: () {
                                              if (mounted) {
                                                setState(() {});
                                              }
                                            },
                                            onImageSelection: () {
                                              validateBrochureData();
                                              setState(() {});
                                            },
                                            containerHeight: 100,
                                            containerWidth: 100,
                                            containerBorderRadius: 7,
                                            renderImageList:
                                                brochureActualImages,
                                            storageLocation:
                                                "newarcHomes/${widget.property!.firebaseId}/before-after-render/",
                                            gallery: renderImageBeforeAfterList
                                                .map((e) => e.renderImageFirst!)
                                                .toList(),
                                            buttonTextColor: Colors.black,
                                            buttonNotAccent: true,
                                            buttonWidth: 125,
                                            buttonColor: Color(0xffE5E5E5),
                                          ),
                                        ),
                                      ])
                                ]),
                          ),
                        ),
                        if( widget.property!.projectType != 'cut' && widget.property!.projectType != 'Immagina for Professionals' ) Container(
                          width: double.infinity,
                          margin: EdgeInsets.only(top: 10),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(13),
                            border: Border.all(
                              width: 1,
                              color: Color(0xffE1E1E1),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Descrizione stato attuale',
                                        fontSize: 16,
                                        fontWeight: '700',
                                        textColor: Color.fromRGBO(0, 0, 0, 1),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 15,
                                  ),
                                  NarFormLabelWidget(
                                    label: 'Scrivi qui la descrizione',
                                    fontSize: 13,
                                    fontWeight: '600',
                                    textColor: Colors.black,
                                  ),
                                  SizedBox(
                                    height: 10,
                                  ),
                                  NarTextareaWidget(
                                    hintText: "",
                                    maxLines: 8,
                                    minLines: 8,
                                    actionKeyboard: TextInputAction.done,
                                    controller: txtconBrochureActualDescription,
                                    // focusNode: _passwordControllerFocus,
                                    onSubmitField: () {
                                      validateBrochureData();
                                    },
                                    onChanged: (val){
                                      validateBrochureData();
                                    },
                                    parametersValidate: "Required!",
                                    validationType: 'required',
                                    // prefixIcon: Icon(Icons.keyboard_hide),
                                  ),
                                ]),
                          ),
                        ),
                        Container(
                          width: double.infinity,
                          margin: EdgeInsets.only(top: 10),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(13),
                            border: Border.all(
                              width: 1,
                              color: Color(0xffE1E1E1),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Seleziona 6 render',
                                        fontSize: 16,
                                        fontWeight: '700',
                                        textColor: Color.fromRGBO(0, 0, 0, 1),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 15,
                                  ),
                                  Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Expanded(
                                          flex: 2,
                                          child: NarBlockFilePickerWidget(
                                            dialogTitle: '',
                                            selectionType: 'singleImage',
                                            displayType: 'container',
                                            containerCount: 6,
                                            roomsSelection: true,
                                            onUploadCompleted: () {
                                              if (mounted) {
                                                setState(() {});
                                              }
                                            },
                                            onImageSelection: () {
                                              validateBrochureData();
                                              setState(() {});
                                            },
                                            containerHeight: 100,
                                            containerWidth: 100,
                                            containerBorderRadius: 7,
                                            renderImageList:
                                                brochureRenderImages,
                                            storageLocation:
                                                "newarcHomes/${widget.property!.firebaseId}/day-render/",
                                            gallery: renderImageDayList
                                                .map((e) => e.renderImageFirst!)
                                                .toList(),
                                            buttonTextColor: Colors.black,
                                            buttonNotAccent: true,
                                            buttonWidth: 125,
                                            buttonColor: Color(0xffE5E5E5),
                                          ),
                                        ),
                                      ])
                                ]),
                          ),
                        ),
                        Container(
                          width: double.infinity,
                          margin: EdgeInsets.only(top: 10),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(13),
                            border: Border.all(
                              width: 1,
                              color: Color(0xffE1E1E1),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Descrizione progetto',
                                        fontSize: 16,
                                        fontWeight: '700',
                                        textColor: Color.fromRGBO(0, 0, 0, 1),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 15,
                                  ),
                                  NarFormLabelWidget(
                                    label: 'Scrivi qui la descrizione',
                                    fontSize: 13,
                                    fontWeight: '600',
                                    textColor: Colors.black,
                                  ),
                                  SizedBox(
                                    height: 10,
                                  ),
                                  NarTextareaWidget(
                                    hintText: "",
                                    maxLines: 8,
                                    minLines: 8,
                                    actionKeyboard: TextInputAction.done,
                                    controller:
                                        txtconBrochureProjectDescription,
                                    // focusNode: _passwordControllerFocus,
                                    onSubmitField: () {
                                      validateBrochureData();
                                    },
                                    onChanged: (val){
                                      validateBrochureData();
                                    },
                                    // prefixIcon: Icon(Icons.keyboard_hide),
                                  ),
                                ]),
                          ),
                        ),
                        Container(
                          width: double.infinity,
                          margin: EdgeInsets.only(top: 10),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(13),
                            border: Border.all(
                              width: 1,
                              color: Color(0xffE1E1E1),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Immagine sfondo Virtual Tour',
                                        fontSize: 16,
                                        fontWeight: '700',
                                        textColor: Color.fromRGBO(0, 0, 0, 1),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 15,
                                  ),
                                  Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Expanded(
                                          flex: 2,
                                          child: NarBlockFilePickerWidget(
                                            dialogTitle: '',
                                            selectionType: 'singleImage',
                                            displayType: 'container',
                                            containerCount: 1,
                                            roomsSelection: false,
                                            onUploadCompleted: () {
                                              if (mounted) {
                                                setState(() {});
                                              }
                                            },
                                            onImageSelection: () {
                                              validateBrochureData();
                                              setState(() {});
                                            },
                                            containerHeight: 145,
                                            containerWidth: 145,
                                            containerBorderRadius: 7,
                                            renderImageList: brochureVTImage,
                                            storageLocation:
                                                "newarcHomes/${widget.property!.firebaseId}/day-render/",
                                            gallery: renderImageDayList
                                                .map((e) => e.renderImageFirst!)
                                                .toList(),
                                            buttonTextColor: Colors.black,
                                            buttonNotAccent: true,
                                            buttonWidth: 125,
                                            buttonColor: Color(0xffE5E5E5),
                                          ),
                                        ),
                                      ])
                                ]),
                          ),
                        ),
                      ],
                    )),
              SizedBox(
                height: 60,
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  NarFormLabelWidget(label: progressMessage),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Row(children: [
                        
                        if ( (isBrochureDataValid && projectType == 'Immagina' || (widget.property!.projectType == 'Immagina for Professionals' )) &&  widget.property!.projectType != 'cut' )
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 5, vertical: 3),
                            decoration: BoxDecoration(
                                border: Border.all(
                                    width: 1, color: Color(0xffD5D5D5)),
                                borderRadius: BorderRadius.circular(10)),
                            child: Row(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(left: 8.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Image.asset(
                                        'assets/immagina-logo-green.png',
                                        height: 15,
                                        // width: 292,
                                        fit: BoxFit.contain,
                                      ),
                                      SizedBox(height: 3),
                                      Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            NarFormLabelWidget(
                                              label: 'Invii effettuati:',
                                              textColor: Color(0xff8F8F8F),
                                              fontSize: 11,
                                            ),
                                            SizedBox(width: 3),
                                            NarFormLabelWidget(
                                              label: widget
                                                  .property!.sentToAgencyCounter
                                                  .toString(),
                                              textColor: Colors.black,
                                              fontSize: 13,
                                            ),
                                          ]),
                                    ],
                                  ),
                                ),
                                SizedBox(width: 10),
                                IconTextButtonWidget(
                                    text: 'Invia ad agenzia',
                                    icon: 'assets/icons/agency-action.svg',
                                    textColor: Colors.white,
                                    textStyle: TextStyle(
                                        fontSize: 14,
                                        fontFamily: 'Raleway-600',
                                        color: Colors.white,
                                        letterSpacing: 0.1),
                                    iconColor: Color(0xffffffff),
                                    backgroundColor: Color(0xff499B79),
                                    borderColor: Color(0xff499B79),
                                    onPressed: downloadingImage
                                        ? null
                                        : () async {
                                            
                                            openAgencyPopup();
                                          }),
                              ],
                            ),
                          ),
                      ]),
                      Row(
                        children: [
                          IconTextButtonWidget(
                              text: 'Download',
                              icon: 'assets/icons/download_brochure.svg',
                              textColor: Colors.black,
                              textStyle: TextStyle(
                                  fontSize: 14,
                                  fontFamily: 'Raleway-600',
                                  color: Colors.black,
                                  letterSpacing: 0.1),
                              iconColor: Color(0xff9D9D9D),
                              backgroundColor: Color(0xffEFEFEF),
                              onPressed: downloadingImage
                                  ? null
                                  : () async {
                                      // openAgencyPopup();
                                      openDownloadPopup();
                                    }),
                          SizedBox(width: 10),
                          isLoadingAmenities == true
                              ? NarFormLabelWidget(
                                  label: 'Please wait...',
                                  fontWeight: '800',
                                  fontSize: 15,
                                )
                              : BaseNewarcButton(
                                  buttonText: "Salva",
                                  height: 33,
                                  width: 120,
                                  onPressed: () async {
                                    if (optionalFeatures.length > 0) {
                                      optional.clear();
                                      optionalFeatures.forEach((key, opt) {
                                        if (opt == true) {
                                          optional.add(PropertyOptionalFeature(
                                              title: key,
                                              price: int.tryParse(
                                                  optionalFeaturesPrice[key]!
                                                      .text
                                                      .trim()),
                                              description:
                                                  optionalFeaturesDescription[
                                                      key]));
                                          // optional[key] =
                                          //     optionalFeaturesPrice[
                                          //             key]!
                                          //         .text;
                                        }
                                      });
                                    }

                                    List<String> selectedPropertyFeatures = [];
                                    propertyFeatures
                                        .forEach((featureName, selectStatus) {
                                      if (selectStatus == true) {
                                        selectedPropertyFeatures
                                            .add(featureName);
                                      }
                                    });

                                    List<String> selectedCurrentFeatures = [];
                                    currentFeatures
                                        .forEach((featureName, selectStatus) {
                                      if (selectStatus == true) {
                                        selectedCurrentFeatures
                                            .add(featureName);
                                      }
                                    });

                                    List<PropertyConfigStyles>
                                        configuredStyles = [];

                                    if (styleValuesRetained.length > 0) {
                                      styleValuesRetained.forEach((styleConfigIndex) {
                                        String price = styleValues[styleConfigIndex.toInt()]['price']!.text;
                                        price = price .replaceAll('.', '').replaceAll(',', '.');
                                        configuredStyles.add(
                                            PropertyConfigStyles(
                                                isDefault: styleConfigIndex == 0,
                                                description: predefinedStyleDescription[ styleValues[styleConfigIndex.toInt()] ['styleName']! .text],
                                                styleName: styleValues[styleConfigIndex.toInt()] ['styleName']! .text,
                                                // price: styleValues[styleConfigIndex.toInt()] ['price']!.text,
                                                price: price.toString(),
                                                // picturePaths: styleValues[styleConfigIndex.toInt()]['picturePaths'],
                                                picturePaths: styleValues[styleConfigIndex.toInt()]['pictures'] != null &&
                                                        styleValues[styleConfigIndex.toInt()]['pictures'].length >
                                                            0
                                                    ? styleValues[styleConfigIndex.toInt()]
                                                        ['pictures']
                                                    : [],
                                                pictures: styleValues[styleConfigIndex.toInt()] ['picturePaths'] .cast<XFile>()));
                                      });
                                    }

                                    String _error_message = '';
                                    String _error_title = '';

                                    hasErrorRenderDay = false;
                                    hasErrorRenderBeforeAfter = false;
                                    hasErrorFloorPlan = false;
                                    hasErrorCurrentFloorPlan = false;

                                    // if( renderImageDayList.length == 0 ) {
                                    //   _error_message = "Please upload 1 image!";
                                    //   _error_title = "Render Giorno!";
                                    //   hasErrorRenderDay = true;
                                    // } else if( projectPlanImages.length == 0 ) {
                                    //   _error_message = "Please upload 1 image!";
                                    //   _error_title = "Planimetria progetto!";
                                    //   hasErrorFloorPlan = true;
                                    // }

                                    // if( projectType != 'Newarc' ) {
                                    //
                                    //   if( renderImageBeforeAfterList.length == 0 ) {
                                    //     _error_message = "Please upload 1 image!";
                                    //     _error_title = "Stanze attuali!";
                                    //     hasErrorRenderBeforeAfter = true;
                                    //   } else if( currentPlan.length == 0 ) {
                                    //     _error_message = "Please upload 1 image!";
                                    //     _error_title = "Planimetria attuale!";
                                    //     hasErrorCurrentFloorPlan = true;
                                    //   }
                                    // }

                                    if (_error_title != '') {
                                      progressMessage = '';

                                      setState(() {});

                                      NarAlertDialog(context, _error_title, _error_message, []);

                                      return;
                                    }

                                    setState(() {
                                      isLoadingAmenities = true;
                                    });

                                    // Day render image
                                    // photoDayTimePaths
                                    photoDayTimePaths.clear();
                                    if (renderImageDayList.isNotEmpty) {
                                      for (var rd = 0; rd < renderImageDayList.length; rd++) {
                                        if (renderImageDayList[rd].renderImageFirst!.filename != null) {
                                          RenderImage _tmp = renderImageDayList[rd].renderImageFirst!;
                                          photoDayTimePaths.add({
                                            'filename': _tmp.filename,
                                            'isEnabledForWebsite':
                                                _tmp.isEnabledForWebsite,
                                            'isBrochure': _tmp.isBrochure,
                                            'room': _tmp.contRoom!.text,
                                            'location': _tmp.location
                                          });
                                        }
                                      }
                                    }

                                    // Night render image
                                    // photoNightTimePaths
                                    photoNightTimePaths.clear();
                                    if (renderImageNightList.isNotEmpty) {
                                      for (var rd = 0; rd < renderImageNightList.length; rd++) {
                                        if (renderImageNightList[rd].renderImageFirst!.filename != null) {
                                          RenderImage _dayTmp = renderImageNightList[rd].renderImageFirst!;
                                          RenderImage _nightTmp = renderImageNightList[rd].renderImageSecond!;

                                          photoNightTimePaths.add({
                                            'day': {
                                              'filename': _dayTmp.filename,
                                              'room': _dayTmp.room,
                                              'location': _dayTmp.location
                                            },
                                            'night': {
                                              'filename': _nightTmp.filename,
                                              'room': _dayTmp.room,
                                              'location': _nightTmp.location
                                            },
                                          });
                                        }
                                      }
                                    }

                                    photoBeforeAfterPaths.clear();
                                    if (renderImageBeforeAfterList.isNotEmpty) {
                                      for (var rd = 0; rd < renderImageBeforeAfterList.length; rd++) {
                                        if (renderImageBeforeAfterList[rd].renderImageFirst!.filename != null) {
                                          RenderImage _afterTmp = renderImageBeforeAfterList[rd].renderImageFirst!;
                                          RenderImage _beforeTmp = renderImageBeforeAfterList[rd].renderImageSecond!;

                                          photoBeforeAfterPaths.add({
                                            'after': {
                                              'isBrochure':
                                                  _afterTmp.isBrochure,
                                              'hasMatch': _afterTmp.hasMatch,
                                              'filename': _afterTmp.filename,
                                              'room': _afterTmp.contRoom!.text,
                                              'location': _afterTmp.location
                                            },
                                            'before': {
                                              'filename': _beforeTmp.filename,
                                              'room': _beforeTmp.room,
                                              'location': _beforeTmp.location
                                            },
                                          });
                                        }
                                      }
                                    }

                                    // Brochure cover image
                                    photoBrochureCoverPaths.clear();
                                    if (brochureCoverImage.isNotEmpty) {
                                      for (var rd = 0; rd < brochureCoverImage.length; rd++) {
                                        if (brochureCoverImage[rd].renderImageFirst!.filename != null) {
                                          RenderImage _tmp = brochureCoverImage[rd].renderImageFirst!;
                                          photoBrochureCoverPaths.add({
                                            'filename': _tmp.filename,
                                            'location': _tmp.location
                                          });
                                        }
                                      }
                                    }

                                    photoBrochureCoverActualPaths.clear();
                                    if (brochureActualImages.isNotEmpty) {
                                      for (var rd = 0; rd < brochureActualImages.length; rd++) {
                                        if (brochureActualImages[rd].renderImageFirst!.filename != null) {
                                          RenderImage _tmp = brochureActualImages[rd].renderImageFirst!;
                                          photoBrochureCoverActualPaths.add({
                                            'filename': _tmp.filename,
                                            'location': _tmp.location,
                                            'room': _tmp.contRoom!.text,
                                          });
                                        }
                                      }
                                    }

                                    photoBrochureRenderPaths.clear();
                                    if (brochureRenderImages.isNotEmpty) {
                                      for (var rd = 0; rd < brochureRenderImages.length; rd++) {
                                        if (brochureRenderImages[rd].renderImageFirst!.filename != null) {
                                          RenderImage _tmp = brochureRenderImages[rd].renderImageFirst!;
                                          photoBrochureRenderPaths.add({
                                            'filename': _tmp.filename,
                                            'location': _tmp.location,
                                            'room': _tmp.contRoom!.text,
                                          });
                                        }
                                      }
                                    }

                                    videoBrochureVTPaths.clear();
                                    if (brochureVTImage.isNotEmpty) {
                                      for (var rd = 0; rd < brochureVTImage.length; rd++) {
                                        if (brochureVTImage[rd].renderImageFirst!.filename != null) {
                                          RenderImage _tmp = brochureVTImage[rd].renderImageFirst!;
                                          videoBrochureVTPaths.add({
                                            'filename': _tmp.filename,
                                            'location': _tmp.location
                                          });
                                        }
                                      }
                                    }

                                    buildingPictures.clear();
                                    if (buildingPicturesImages.isNotEmpty) {
                                      for (var rd = 0; rd < buildingPicturesImages.length; rd++) {
                                        if (buildingPicturesImages[rd].renderImageFirst!.filename != null) {
                                          RenderImage _tmp = buildingPicturesImages[rd].renderImageFirst!;
                                          buildingPictures.add({
                                            'filename': _tmp.filename,
                                            'location': _tmp.location,
                                            'room': _tmp.room,
                                          });
                                        }
                                      }
                                    }

                                    // print(photoDayTimePaths);
                                    // return;

                                    // if (firebaseUser != null && firebaseUser! .emailVerified) {

                                    Property property = Property(
                                        code: widget.property!.code,
                                        codeCounter: widget.property!.codeCounter,
                                        year: widget.property!.year,
                                        firebaseId: widget.property!.firebaseId ?? '',
                                        zone: txtconPropertyZoneName!.text,
                                        type: txtconPropertyTypeName!.text,
                                        propertyName: txtconPropertyName!.text,
                                        addressInfo: widget.project.addressInfo,
                                        location: location,
                                        civic: txtconCivic!.text,
                                        city: txtconCity!.text,
                                        description: txtconDescription!.text,
                                        mq: txtconAreaMq!.text,
                                        baths: txtconBaths!.text,
                                        locals: txtconLocals!.text,
                                        publicationDate: publicationDate,
                                        bedrooms: txtconBedrooms!.text,
                                        floors: txtconFloors!.text,
                                        propertyFeatures: selectedPropertyFeatures,
                                        currentFeatures: selectedCurrentFeatures,
                                        styles: configuredStyles,
                                        insertUid: '',
                                        amenities: amenities,
                                        amenitiesCount: amenitiesCount,
                                        optional: optional,
                                        publicStatus: publicStatusOptions,
                                        isActive: widget.property != null
                                            ? widget.property!.isActive
                                            : true,
                                        virtualTour: txtconVirtualTour!.text,
                                        currentVirtualTour: txtconCurrentVirtualTour!.text,
                                        picturePaths: projectPlanImages,
                                        photoDayTimePaths: photoDayTimePaths,
                                        photoNightTimePaths: photoNightTimePaths,
                                        photoBeforeAfterPaths: photoBeforeAfterPaths,
                                        videoRenderPaths: videoRenderPaths,
                                        photographs: photographs,
                                        currentPlan: currentPlan,
                                        activateNightTimePicture: activateNightTimePicture,
                                        activateVirtualTourProject: activateVirtualTourProject,
                                        activateVideoRender: activateVideoRender,
                                        activateVirtualTourCurrent: activateVirtualTourCurrent,
                                        isArchived: widget.property!.isArchived ?? false,
                                        qrPaths: qrImages,
                                        bathsIns: txtconBathsIns!.text,
                                        localsIns: txtconLocalsIns!.text,
                                        fixtureIns: txtconFixtureIns!.text,
                                        InternalDoorsIns: txtconInteralDoorsIns!.text,
                                        WalkableIns: txtconWalkableIns!.text,
                                        qualityOfTheArea: txtconQualityOfTheAreaIns!.text,
                                        LighRenoIns: rangeResLight['actual'].toString(),
                                        LighRenoInsMin: rangeResLight['range_min'].toString(),
                                        LighRenoInsMax: rangeResLight['range_max'].toString(),

                                        FullRenoIns: rangeResFull['actual'].toString(),
                                        FullRenoInsMin: rangeResFull['range_min'].toString(),
                                        FullRenoInsMax: rangeResFull['range_max'].toString(),

                                        materialStandardLight: rangeResLight['material_standard'].toString(),
                                        materialStandardLightMin: rangeResLight['material_standard_min'].toString(),
                                        materialStandardLightMax: rangeResLight['material_standard_max'].toString(),
                                        
                                        materialPremiumLight: rangeResLight['material_permium'].toString(),
                                        materialPremiumLightMin: rangeResLight['material_permium_min'].toString(),
                                        materialPremiumLightMax: rangeResLight['material_permium_max'].toString(),
                                        
                                        materialStandardFull: rangeResFull['material_standard'].toString(),
                                        materialStandardFullMin: rangeResFull['material_standard_min'].toString(),
                                        materialStandardFullMax: rangeResFull['material_standard_max'].toString(),
                                        
                                        materialPremiumFull: rangeResFull['material_permium'].toString(),
                                        materialPremiumFullMin: rangeResFull['material_permium_min'].toString(),
                                        materialPremiumFullMax: rangeResFull['material_permium_max'].toString(),
                                        
                                        materialFixtureFull: rangeResFull['infissi'].toString(),
                                        materialFixtureFullMin: rangeResFull['infissi_min'].toString(),
                                        materialFixtureFullMax: rangeResFull['infissi_max'].toString(),
                                        
                                        photoBrochureCoverPaths: photoBrochureCoverPaths,
                                        photoBrochureCoverActualPaths: photoBrochureCoverActualPaths,
                                        photoBrochureRenderPaths: photoBrochureRenderPaths,
                                        videoBrochureVTPaths: videoBrochureVTPaths,
                                        brochureProjectDescription: txtconBrochureProjectDescription!.text,
                                        brochureActualDescription: txtconBrochureActualDescription!.text,
                                        actualEnergyClass: txtconActualEnergyClass!.text,
                                        projectEnergyClass: txtconProjectEnergyClass!.text,
                                        projectType: widget.property!.projectType,
                                        cutsCount: widget.property!.cutsCount,
                                        brochurePdfPath: brochurePdfPath,
                                        
                                        children: widget.property!.children!,
                                        buildingPictures: buildingPictures,
                                        sentToAgencyCounter: widget.property!.sentToAgencyCounter,
                                        projectStatus: txtconProjectStatus!.text,
                                    );

                                    setState(() {
                                      progressMessage = 'Salvataggio in corso';
                                    });

                                    String message = "";

                                    if (widget.property!.firebaseId == null) {
                                      property.insertTimestamp = Timestamp.now().millisecondsSinceEpoch;
                                      property.updateTimestamp = Timestamp.now().millisecondsSinceEpoch;

                                      await property
                                          .addProperty(property)
                                          .then((firebaseId) async {
                                            message = "Inserimento/aggiornamento avvenuto con successo.";

                                        
                                            widget.property!.firebaseId = firebaseId;

                                            setState(() {
                                              validateReadyForAgency();
                                              progressMessage = message;
                                              widget.isInputChangeDetected![0] = false;
                                            });

                                        /* Save the new created Property id
                                                  to the Project */
                                            if (projectType != 'Newarc') {
                                              NewarcProject project = widget.project!;
                                              project.propertyId = firebaseId;
                                              final FirebaseFirestore _db = FirebaseFirestore.instance;
                                              await _db.collection(appConfig.COLLECT_NEWARC_PROJECTS)
                                                  .doc(project.id)
                                                  .update(project.toMap());
                                            } else {

                                              ImmaginaProject project = widget.project!;
                                              project.propertyId = firebaseId;
                                              final FirebaseFirestore _db = FirebaseFirestore.instance;
                                              await _db.collection(appConfig.COLLECT_IMMAGINA_PROJECTS)
                                                  .doc(project.getid())
                                                  .update(project.toMap());
                                            }

                                          widget.property!.setFromObject(property);

                                        // widget.property = property;

                                        adImages.clear();
                                      }).onError((error, stackTrace) {
                                        message =
                                            "Si è verificato un errore: " +
                                                error.toString();

                                        print({error, stackTrace});

                                        setState(() {
                                          progressMessage = message;
                                        });
                                      });
                                    } else {
                                      // Update an existing document
                                      property.insertTimestamp =
                                          widget.property!.insertTimestamp;
                                      property.updateTimestamp = Timestamp.now()
                                          .millisecondsSinceEpoch;
                                      property.updateUid = '';
                                      property.firebaseId =
                                          widget.property!.firebaseId;
                                      await property
                                          .updateProperty()
                                          .then((value) {
                                        message = "Inserimento/aggiornamento avvenuto con successo.";

                                        setState(() {
                                          progressMessage = message;
                                          widget.isInputChangeDetected![0] =
                                              true;
                                        });

                                        widget.property!.setFromObject(property);
                                        adImages.clear();
                                        
                                      }).onError((error, stackTrace) {
                                        message = "Si è verificato un errore: " + error.toString();
                                        print({error, stackTrace});

                                        setState(() {
                                          progressMessage = message;
                                        });
                                      });
                                    }
                                    
                                    validateReadyForAgency();

                                    setState(() {
                                      isLoadingAmenities = false;
                                    });
                                  },
                                ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),

        ],
      ),
    );
  }


  Future _httpRequestViaServer({url}) async {
    HttpsCallable callable = FirebaseFunctions.instance.httpsCallable(
      'getDataFromUrl',
    );
    try {
      final HttpsCallableResult result = await callable.call(
        <String, dynamic>{
          'url': url,
        },
      );
      return (result.data);
    } on FirebaseFunctionsException catch (e) {
      // print('caught firebase functions exception');
      // print(e.code);
      // print(e.message);
      // print(e.details);
      return null;
    } catch (e) {
      // print('caught generic exception');
      // print(e);
      return null;
    }
  }

  Timer? _timer;
  
  getAmenities(lat, lng) async {
    amenities = [];
    var ame_types = ['school', 'market', 'park'];

    isLoadingAmenities = true;

    for (var i = 0; i < ame_types.length; i++) {
      dynamic result = await _httpRequestViaServer(
          url:
              "https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${lat},${lng}&radius=400&type=${ame_types[i]}&key=${googleApikey}");

      if (result['results'].length > 0) {
        List amenity = [];

        for (var j = 0; j < result['results'].length; j++) {
          tmpAmenities.add({
            'name': result['results'][j]['name'],
            'location': result['results'][j]['geometry']['location'],
            'vicinity': result['results'][j]['vicinity']
          });
        }

        if (result['next_page_token'] != 'undefined') {
          await fetchNextAmenity(
              i, lat, lng, ame_types[i], result['next_page_token']);
        }

        amenitiesCount[ame_types[i]] = tmpAmenities.length;
        // amenities.add({ame_types[i]: tmpAmenities});
        amenities.add({ame_types[i]: tmpAmenities});
        // print(ame_types[i]);
        // print(tmpAmenities);
        tmpAmenities = [];
      }
    }
    setState(() {
      isLoadingAmenities = false;
    });

    // print(amenities.length);
    // print(amenities_count);
    // print(amenities);
  }

  fetchNextAmenity(counter, lat, lng, amenity, token) async {
    await Future.delayed(Duration(seconds: 2));
    dynamic result = await _httpRequestViaServer(
        url:
            "https://maps.googleapis.com/maps/api/place/nearbysearch/json?pagetoken=${token}&key=${googleApikey}");

    if (result['results'].length > 0) {
      // List amenity = [];
      // amenities.addAll(other)
      for (var j = 0; j < result['results'].length; j++) {
        tmpAmenities.add({
          'name': result['results'][j]['name'],
          'location': result['results'][j]['geometry']['location'],
          'vicinity': result['results'][j]['vicinity']
        });
      }

      if (result['next_page_token'] != 'undefined' &&
          result['next_page_token'] != "") {
        await fetchNextAmenity(
            counter, lat, lng, amenity, result['next_page_token']);
      }

      // return amenity;
    }
  }

  enrichAddres() async {
    // print("geocoding for: " + txtconPropertyName!.text);
    dynamic result = await _httpRequestViaServer(
        url:
            "https://maps.googleapis.com/maps/api/place/json?address=${txtconPropertyName!.text}&key=${googleApikey}");
    //print(result);
    return result;
  }

  void _showDownloadingDialog(BuildContext context,{ValueNotifier<double>? progress}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black54,
      builder: (_) {
        return Scaffold(
          backgroundColor: Colors.transparent,
          body: Center(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              decoration: BoxDecoration(
                //color: Colors.black87,
                borderRadius: BorderRadius.circular(30),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: AppColor.white,),
                  SizedBox(height: 10,),
                  NarFormLabelWidget(
                    textAlign: TextAlign.center,
                    label: "Generazione in corso…",
                    fontSize: 18,
                    fontWeight: '700',
                    textColor: AppColor.white,
                  ),
                  SizedBox(height: 10,),
                  ValueListenableBuilder<double>(
                    valueListenable: progress!,
                    builder: (context, value, child) {
                      return Text(
                        '${value*100}%',
                        style: TextStyle(
                          color: AppColor.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
