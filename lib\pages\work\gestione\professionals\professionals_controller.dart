import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/professionals.dart';

class ProfessionalsController extends GetxController {
  bool loading = false;
  // suggested professionals to be shown in table
  List<Professional> professionals = [];
  TextEditingController professionFilterController = new TextEditingController();
  String professionSelectedFilter = "";

  TextEditingController professionalName = new TextEditingController();
  TextEditingController formationType = new TextEditingController();
  // List selectedActivities = [];
  TextEditingController contactName = new TextEditingController();
  TextEditingController contactSurname = new TextEditingController();
  TextEditingController contactPhone = new TextEditingController();
  TextEditingController contactEmail = new TextEditingController();

  TextEditingController professionalLegalEntity = new TextEditingController();
  TextEditingController professionalBillingCode = new TextEditingController();
  TextEditingController professionalVat = new TextEditingController();
  TextEditingController professionalFiscalCode = new TextEditingController();
  BaseAddressInfo professionalLegalAddress = BaseAddressInfo.empty();
  TextEditingController professionalPhone = new TextEditingController();
  TextEditingController professionalEmail = new TextEditingController();
  TextEditingController professionalProfession = new TextEditingController();
  TextEditingController professionalClientPhone = new TextEditingController();

  String validationMessage = "";
  String progressMessage = "";
  List<String> fileProgressMessage = [""];
  List<String> formMessages = [""];

  // Set non-TextEditingController values
  initInsideViewController(Professional professional){
    professionalLegalAddress = professional.legalAddressInfo;
    contactName.text = professional.contactPersonInfo.name ?? "";
    contactSurname.text = professional.contactPersonInfo.surname ?? "";
    contactPhone.text = professional.contactPersonInfo.phone ?? "";
    contactEmail.text = professional.contactPersonInfo.email ?? "";
    professionalName.text = professional.companyName ?? "";
    formationType.text = professional.formationType ?? "";
    professionalLegalEntity.text = professional.legalEntity ?? "";
    professionalFiscalCode.text = professional.fiscalCode ?? "";
    professionalEmail.text = professional.email ?? "";
    professionalProfession.text = professional.profession ?? "";
    professionalClientPhone.text = professional.phone ?? "";
    professionalBillingCode.text = professional.sdi ?? "";
    professionalVat.text = professional.vat ?? "";

    validationMessage = "";
    progressMessage = "";
    fileProgressMessage = [""];
    formMessages = [""];
  }

  clearInsideViewController(){
    contactName.clear();
    contactSurname.clear();
    contactPhone.clear();
    contactEmail.clear();
    professionalName.clear();
    formationType.clear();
    professionalLegalEntity.clear();
    professionalBillingCode.clear();
    professionalVat.clear();
    professionalFiscalCode.clear();
    professionalLegalAddress = BaseAddressInfo.empty();
    professionalClientPhone.clear();
    professionalEmail.clear();

    validationMessage = "";
    progressMessage = "";
    fileProgressMessage = [""];
    formMessages = [""];
  }

  clearFilter() {
    professionSelectedFilter = '';
    professionFilterController.clear();
  }
}
