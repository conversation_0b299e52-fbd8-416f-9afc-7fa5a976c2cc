


class NewarcMaterialTipologia{
  String? firebaseId;
  String? name; // type Name
  int? insertTimestamp;
  String? uid; //the Firebase Id of the logged in user
  int? codeCounter;

  Map<String, Object?> toMap() {
    return {
      'name': name,
      'insertTimestamp': insertTimestamp,
      'uid': uid,
      'codeCounter': codeCounter,
    };
  }

  NewarcMaterialTipologia.empty() {
    this.firebaseId = '';
    this.name = '';
    this.insertTimestamp = null;
    this.uid = '';
    this.codeCounter = 0;
  }

  NewarcMaterialTipologia.fromDocument(Map<String, dynamic> data,String id) {
    firebaseId = id;
    try {
      this.name = data['name'];
      this.insertTimestamp = data['insertTimestamp'];
      this.uid = data['uid'];
      this.codeCounter = data['codeCounter'];
    } catch (e, s) {
      print({ 'NewarcMaterialTipologia Class Error ------->', e, s});
    }
  }
}